# syntax=docker/dockerfile:1.4
FROM --platform=linux/amd64 eclipse-temurin:21-jre-alpine
WORKDIR /app
EXPOSE 8081

# Instala tzdata e configura o timezone para GMT-3
RUN apk add --no-cache tzdata
ENV TZ=GMT-3

RUN addgroup -S appgroup && adduser -S appuser -G appgroup
RUN chown -R appuser:appgroup /app
USER appuser

COPY --link api-application/target/api-application*.jar app.jar
RUN java -Djarmode=tools \
    -jar app.jar extract
#RUN java -Dspring.aot.enabled=true \
#    -Dspring.context.exit=onRefresh \
#    -Dspring.main.banner-mode=off \
#    -Dspring.profiles.active=aws-prd \
#    -Dspring.datasource.url=******************************************************* \
#    -Dspring.datasource.password='postgres' \
#    -XX:ArchiveClassesAtExit=app.jsa \
#    -jar app/app.jar

ENV PROFILE aws-prd
ENV SLACK_CHANNEL C06TFG29UQZ

ENTRYPOINT ["java", \
"-Djava.net.preferIPv4Stack=true", \
#"-XX:SharedArchiveFile=app.jsa", \
"-Duser.language=pt", "-Duser.country=BR", "-Duser.timezone=GMT-3", \
#"-Dspring.aot.enabled=true", \
"-Dspring.profiles.active=aws-prd", \
"-XX:MaxMetaspaceSize=600m", \
"-Xmx3g", "-Xms2g", \
"-jar", "app/app.jar" \
]