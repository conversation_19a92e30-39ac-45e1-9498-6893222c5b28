package br.com.nuvy.callface.config;

import com.amazon.sqs.javamessaging.ProviderConfiguration;
import com.amazon.sqs.javamessaging.SQSConnectionFactory;
import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import jakarta.jms.Session;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskExecutor;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.jms.support.destination.DynamicDestinationResolver;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;

@Slf4j
@EnableJms
@Configuration
@ConditionalOnProperty(name = "aws.sqs.enabled", havingValue = "true")
class AmazonSQSConfig {

  @Value("${aws.region}")
  private String region;
  @Value("${aws.credentials.access-key}")
  private String accessKey;
  @Value("${aws.credentials.secret-key}")
  private String secretKey;
  @Value("${aws.sqs.queue.callface.url}")
  private String uri;

  @Bean
  SQSConnectionFactory sqsConnectionFactory() {
    var pc = new ProviderConfiguration();
    var c = AwsBasicCredentials.create(accessKey, secretKey);
    var cp = StaticCredentialsProvider.create(c);
    var sqsClient = SqsClient.builder()
      .endpointOverride(URI.create(uri))
      .region(Region.of(region))
      .credentialsProvider(cp)
      .build();
    return new SQSConnectionFactory(pc, sqsClient);
  }

  @Bean
  AmazonSQS sqsClient() {
    var basicAWSCredentials = new BasicAWSCredentials(accessKey, secretKey);
    return AmazonSQSClientBuilder.standard()
      .withRegion(region)
      .withCredentials(new AWSStaticCredentialsProvider(basicAWSCredentials))
      .build();
  }

  static class CustomMessageListenerContainer extends DefaultMessageListenerContainer {

    public CustomMessageListenerContainer() {
      super();
    }

    @Override
    protected void rollbackOnExceptionIfNecessary(Session session, Throwable ex) {
      // do nothing, so that "visibilityTimeout" will stay same
    }
  }

  static class CustomJmsListenerContainerFactory extends DefaultJmsListenerContainerFactory {

    @Override
    protected DefaultMessageListenerContainer createContainerInstance() {
      return new CustomMessageListenerContainer();
    }
  }

  @Bean
  @Primary
  DefaultJmsListenerContainerFactory jmsListenerContainerFactory(
    TaskExecutor taskExecutor, SQSConnectionFactory connectionFactory
  ) {
    var factory = new CustomJmsListenerContainerFactory();
    factory.setTaskExecutor(taskExecutor);
    factory.setConnectionFactory(connectionFactory);
    factory.setDestinationResolver(new DynamicDestinationResolver());
    factory.setConcurrency("1-1");
    factory.setSessionAcknowledgeMode(SQSSession.UNORDERED_ACKNOWLEDGE);
    return factory;
  }

  @Bean
  DefaultJmsListenerContainerFactory jmsListenerContainerFactoryConcorrente(
    TaskExecutor taskExecutor, SQSConnectionFactory connectionFactory
  ) {
    var factory = new CustomJmsListenerContainerFactory();
    factory.setTaskExecutor(taskExecutor);
    factory.setConnectionFactory(connectionFactory);
    factory.setDestinationResolver(new DynamicDestinationResolver());
    factory.setConcurrency("1-1");
    factory.setSessionAcknowledgeMode(SQSSession.UNORDERED_ACKNOWLEDGE);
    return factory;
  }

  @Bean
  JmsTemplate defaultJmsTemplate(SQSConnectionFactory connectionFactory) {
    return new JmsTemplate(connectionFactory);
  }
}