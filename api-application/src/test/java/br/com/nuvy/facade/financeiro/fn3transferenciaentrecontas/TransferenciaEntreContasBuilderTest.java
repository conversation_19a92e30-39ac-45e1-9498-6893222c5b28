package br.com.nuvy.facade.financeiro.fn3transferenciaentrecontas;

import br.com.nuvy.facade.financeiro.fn3transferenciaentrecontas.model.TransferenciaInDto;
import com.github.javafaker.Faker;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Locale;

class TransferenciaEntreContasBuilderTest {

  protected Faker faker = new Faker(new Locale("pt-BR"));


  public TransferenciaInDto transferenciaInDto() {
    return TransferenciaInDto.builder()
      .contaOrigemId(999)
      .contaDestinoId(1000)
      .formaDeTransacaoId(1)
      .dataTransferencia(LocalDateTime.of(LocalDate.now(), LocalTime.of(0,0,1)))
      .valorTransferencia(new BigDecimal("0.00"))
      .numeroDocumento(faker.numerify("########"))
      .observacao(faker.lorem().characters())
      .build();
  }
  public TransferenciaInDto transferenciaInDtoMultiEmpresa() {
    return transferenciaInDto().toBuilder()
      .contaOrigemId(1005)
      .contaDestinoId(1006)
      .build();
  }

}
