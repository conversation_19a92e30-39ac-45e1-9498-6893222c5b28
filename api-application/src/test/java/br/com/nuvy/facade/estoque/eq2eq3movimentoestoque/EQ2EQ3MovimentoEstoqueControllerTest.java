package br.com.nuvy.facade.estoque.eq2eq3movimentoestoque;

import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.contains;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import br.com.nuvy.NuvyApiTestBase;
import br.com.nuvy.api.enums.OrigemMovimentacao;
import br.com.nuvy.facade.estoque.eq2eq3movimentoestoque.model.RastreabilidadeOutDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;


/**
 * Para os testes ficaram atomicos deletar sempre a movimentação ao final dos casos de teste <p>
 * Utilizar o produto id 1000 para os testes sem lote <p> Utilizar o produto id 1001 para os testes
 * com lote <p> Utilizar o estoque id 999 para Origem <p> Utilizar o estoque 1000 para Destino
 */

class EQ2EQ3MovimentoEstoqueControllerTest extends NuvyApiTestBase {

  @Autowired
  private MockMvc mvc;

  private final MovimentoEstoqueBuilderTest movimentoEstoqueBuilderTest = new MovimentoEstoqueBuilderTest();


  @Nested
  @DisplayName("Testa a movimentação de entrada e saida do estoque sem lote")
  class TesteMovimentoDeEntradaESaida {

    @Nested
    @DisplayName("Testa a movimentação de entrada e saida do estoque sem lote")
    class TesteMovimentoDeEntradaESaidaSemLote {

      @ParameterizedTest
      @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
      @DisplayName("Deve retornar 201 quando fizer um POST movimentacao de entrada")
      void TestPostMovimentacoes(OrigemMovimentacao origemMovimentacao) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(new BigDecimal("12.22"));
        entradaSaida.setOrigemMovimentacao(origemMovimentacao);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andExpect(status().isCreated())
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }
      @ParameterizedTest
      @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
      @DisplayName("Deve retornar 200 quando fizer um PUT movimentacao de entrada")
      void TestPutMovimentacoes(OrigemMovimentacao origemMovimentacao) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(new BigDecimal("12.22"));
        entradaSaida.setOrigemMovimentacao(origemMovimentacao);
        var entradaSaidaEdit = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entradaSaidaEdit.setValorCusto(new BigDecimal("500.00"));
        entradaSaidaEdit.setQuantidade(new BigDecimal("12.22"));
        entradaSaidaEdit.setOrigemMovimentacao(origemMovimentacao);

        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andExpect(status().isCreated())
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(put("/estoque/eq4/movimento/"+id, entradaSaidaEdit))
          .andExpect(status().isOk());
        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }


      @ParameterizedTest
      @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
      @DisplayName("Deve retornar 204 quando fizer um DEL de entrada e saida")
      void TestDelMovimentacao(OrigemMovimentacao origemMovimentacao) throws Exception {
        var entrada = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entrada.setValorCusto(new BigDecimal("500.00"));
        entrada.setQuantidade(new BigDecimal("12.22"));
        entrada.setOrigemMovimentacao(origemMovimentacao);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entrada))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(del("/estoque/eq4/movimento/" + id))
          .andExpect(status().isNoContent());

      }
      @ParameterizedTest
      @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
      @DisplayName("Deve retornar 200 quando fizer um GET na lista de movimentos")
      void TestGetListaMovimento(OrigemMovimentacao origemMovimentacao) throws Exception {
        var entradaEsaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entradaEsaida.setValorCusto(new BigDecimal("500.00"));
        entradaEsaida.setQuantidade(new BigDecimal("12.22"));
        entradaEsaida.setOrigemMovimentacao(origemMovimentacao);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaEsaida))
          .andReturn().getResponse().getHeader("X-ID");

        mvc.perform(get("/estoque/eq4/movimento/")
            .queryParam("idProduto", "1000"))
          .andExpect(status().isOk())
          .andExpect(jsonPath("[*].id", notNullValue()))
          .andExpect(
            jsonPath("[*].dataMovimentacao", hasItem(entradaEsaida.getDataMovimentacao().toString())))
          .andExpect(jsonPath("[*].origemMovimentacao", hasItem(origemMovimentacao.name())))
          .andExpect(jsonPath("[*].produtoCodigo", hasItem("76614")))
          .andExpect(jsonPath("[*].produtoOrigemMercadoriaNome", hasItem("Nacional: exceto as indicadas nos códigos 3, 4, 5 e 8")))
          .andExpect(jsonPath("[*].produtoDescricao", hasItem("Produto Movimentacao")))
          .andExpect(jsonPath("[*].depositoNome", hasItem("Estoque Origem")))
          .andExpect(jsonPath("[*].quantidade", hasItem(notNullValue())))
          .andExpect(jsonPath("[*].valorCusto", hasItem(notNullValue())))
          .andExpect(jsonPath("[*].produtoValorCustoMedio", hasItem(notNullValue())))

        ;
        mvc.perform(del("/estoque/eq4/movimento/" + id));

      }

      @ParameterizedTest
      @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
      @DisplayName("Deve retornar 200 quando fizer um Get id de movimentacao")
      void TestGetIdMovimento(OrigemMovimentacao origemMovimentacao) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(new BigDecimal("10.00"));
        entradaSaida.setOrigemMovimentacao(origemMovimentacao);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/estoque/eq4/movimento/" + id))
          .andExpect(status().isOk())
          .andExpect(jsonPath("id", notNullValue()))
          .andExpect(
            jsonPath("dataMovimentacao", is(entradaSaida.getDataMovimentacao().toString())))
          .andExpect(jsonPath("origemMovimentacao", is(origemMovimentacao.name())))
          .andExpect(jsonPath("produtoId", is(1000)))
          .andExpect(jsonPath("produtoDescricao", is("Produto Movimentacao")))
          .andExpect(jsonPath("produtoUnidadeMedidaDescricao", is("UNIDADE")))
          .andExpect(jsonPath("depositoEmpresaId", is(1)))
          .andExpect(jsonPath("depositoId", is(999)))
          .andExpect(jsonPath("depositoNome", is("Estoque Origem")))
          .andExpect(jsonPath("quantidade", is(10.0)))
          .andExpect(jsonPath("valorCusto", is(500.00)))
          .andExpect(jsonPath("observacao", is(entradaSaida.getObservacao())));

        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }

      @ParameterizedTest
      @CsvSource({"10.00", "15.50", "20.75"})
      @DisplayName("Dado que crio uma movimentação de entrada/saida, o saldo deve retornar 200 e os valores dos deposítos verificados")
      void TestSaldoMovimentacaoEntrada(BigDecimal quantidade) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        String produtoId = "1000";
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(quantidade);
        entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/estoque/eq4/saldo-estoque/" + produtoId))
          .andExpect(status().isOk())
          .andExpect(
            jsonPath("totalProdutos", closeTo(entradaSaida.getQuantidade().doubleValue(), 0.01)));

        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }

      @ParameterizedTest
      @CsvSource({"10.00", "15.50", "20.75"})
      @DisplayName("Dado que crio uma movimentação de saida e o depósito permite estoque negativo, o saldo deve retornar 200 e os valores dos deposítos verificados")
      void TestSaldoMovimentacaoSaida(BigDecimal quantidade) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        String produtoId = "1000";
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(quantidade);
        entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.SAIDA);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/estoque/eq4/saldo-estoque/" + produtoId))
          .andExpect(status().isOk())
          .andExpect(
            jsonPath("totalProdutos",
              closeTo(entradaSaida.getQuantidade().negate().doubleValue(), 0.01)));

        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }


      @ParameterizedTest
      @CsvSource({"10.00", "15.50", "20.75"})
      @DisplayName("Dado que crio uma movimentação de entrada/saida, a quantidade de saldo de estoque atual em proudutos deve ser alterada")
      void TestQuantidadeEstoqueAtualProduto(BigDecimal quantidade) throws Exception {
        var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
        String produtoId = "1000";
        entradaSaida.setValorCusto(new BigDecimal("500.00"));
        entradaSaida.setQuantidade(quantidade);
        entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/v1/cadastro/produto/" + produtoId))
          .andExpect(jsonPath("quantidadeEstoqueAtual",
            closeTo(entradaSaida.getQuantidade().doubleValue(), 0.01)));
        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }


    }


  }

  @Nested
  @DisplayName("Testa a movimentação de entrada e saida do estoque com lote")
  class TesteMovimentoDeEntradaESaidaComLote {

    @Autowired
    private ObjectMapper objectMapper;
    private RastreabilidadeOutDto rastreabilidadeOutDto;

    @BeforeEach
    void criaRastreabilidade() throws Exception {
      var rastreabilidade = movimentoEstoqueBuilderTest.rastreabilidadeInDto();
      rastreabilidade.setQuantidade(new BigDecimal("10.00"));
      var responseBody = mvc.perform(post("/estoque/eq2-eq3/rastreabilidade/", rastreabilidade))
        .andReturn().getResponse().getContentAsString();
      rastreabilidadeOutDto = objectMapper.readValue(responseBody,
        RastreabilidadeOutDto.class);

    }


    @ParameterizedTest
    @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
    @DisplayName("Deve retornar 201 quando fizer um POST movimentacao de entrada/saida com rastreabilidade")
    void TestPostMovimentacoes(OrigemMovimentacao origemMovimentacao) throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setProdutoId(1001);
      entradaSaida.setOrigemMovimentacao(origemMovimentacao);
      entradaSaida.setRastreabilidadeId(rastreabilidadeOutDto.getId());
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andExpect(status().isCreated())
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }
    @ParameterizedTest
    @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
    @DisplayName("Deve retornar 200 quando fizer um GET nos saldos dos produtos por lote")
    void TestGetSaldoEstoqueLote(OrigemMovimentacao origemMovimentacao) throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setProdutoId(1001);
      entradaSaida.setOrigemMovimentacao(origemMovimentacao);
      entradaSaida.setRastreabilidadeId(rastreabilidadeOutDto.getId());
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andExpect(status().isCreated())
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/estoque/eq2-eq3/saldo-estoque/"+1001+"/lote/"+rastreabilidadeOutDto.getLote()))
        .andExpect(status().isOk());
      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }


    @ParameterizedTest
    @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
    @DisplayName("Deve retornar 204 quando fizer um DEL de entrada e saida com lote")
    void TestDelMovimentacao(OrigemMovimentacao origemMovimentacao) throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setProdutoId(1001);
      entradaSaida.setOrigemMovimentacao(origemMovimentacao);
      entradaSaida.setRastreabilidadeId(rastreabilidadeOutDto.getId());
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andExpect(status().isCreated())
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(del("/estoque/eq4/movimento/" + id))
        .andExpect(status().isNoContent());

    }

    @ParameterizedTest
    @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
    @DisplayName("Deve retornar 200 quando fizer um Get id de movimentacao")
    void TestGetIdMovimento(OrigemMovimentacao origemMovimentacao) throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setProdutoId(1001);
      entradaSaida.setRastreabilidadeId(rastreabilidadeOutDto.getId());
      entradaSaida.setOrigemMovimentacao(origemMovimentacao);
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/estoque/eq4/movimento/" + id))
        .andExpect(status().isOk())
        .andExpect(jsonPath("id", notNullValue()))
        .andExpect(
          jsonPath("dataMovimentacao", is(entradaSaida.getDataMovimentacao().toString())))
        .andExpect(jsonPath("origemMovimentacao", is(origemMovimentacao.name())))
        .andExpect(jsonPath("produtoId", is(entradaSaida.getProdutoId())))
        .andExpect(jsonPath("produtoDescricao", is("Produto Movimentacao com lote")))
        .andExpect(jsonPath("produtoUnidadeMedidaDescricao", is("UNIDADE")))
        .andExpect(jsonPath("depositoEmpresaId", is(1)))
        .andExpect(jsonPath("depositoId", is(999)))
        .andExpect(jsonPath("depositoNome", is("Estoque Origem")))
        .andExpect(jsonPath("quantidade", is(10.0)))
        .andExpect(jsonPath("valorCusto", is(500.00)))
        .andExpect(jsonPath("rastreabilidadeId", is(rastreabilidadeOutDto.getId())))
        .andExpect(jsonPath("rastreabilidadeLote", is(rastreabilidadeOutDto.getLote())))
        .andExpect(jsonPath("rastreabilidadeDataValidade",
          is(rastreabilidadeOutDto.getDataValidade().toString())))
        .andExpect(jsonPath("observacao", is(entradaSaida.getObservacao())))
        .andExpect(jsonPath("tipoMovimentacao", is(origemMovimentacao.name())));

      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }

    @ParameterizedTest

    @CsvSource({"10.00", "15.50", "20.75"})
    @DisplayName("Dado que crio uma movimentação de entrada, o saldo deve retornar 200 e os valores dos deposítos verificados")
    void TestSaldoMovimentacaoEntrada(BigDecimal quantidade) throws Exception {
      var entrada = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entrada.setValorCusto(new BigDecimal("500.00"));
      entrada.setQuantidade(quantidade);
      entrada.setProdutoId(1001);
      entrada.setRastreabilidadeId(rastreabilidadeOutDto.getId());
      entrada.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entrada))
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/estoque/eq4/saldo-estoque/" + entrada.getProdutoId().toString()))
        .andExpect(status().isOk())
        .andExpect(
          jsonPath("totalProdutos", closeTo(entrada.getQuantidade().doubleValue(), 0.01)));
      mvc.perform(del("/estoque/eq4/movimento/" + id));

    }
  }


  @Nested

    /**
     <p>Para facilitar a manutenção desse teste criei dois Hooks do Junit. </p>
     <p>O beforeEach é para sempre criar uma movimentação de Entrada antes de ser feita a transferencia</p>
     <p>O AfterEach é para sempre deletar automaticamente essa mesma movimentação depois que é executado o caso de teste</p>
     <p>Isso garante que os testes sempre vão ficar atômicos. </p>
     */
  class TesteMovimentoDeTransferencia {

    @Nested
    class TesteMovimentoDeTransferenciaSemLote {

      private String idEntrada;

      @BeforeEach
      void criaMovimentoDeEntrada() throws Exception {
        var entrada = movimentoEstoqueBuilderTest.criarMovimentoDto();
        entrada.setValorCusto(new BigDecimal("500.00"));
        entrada.setQuantidade(new BigDecimal("10.00"));
        entrada.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
        idEntrada = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entrada))
          .andReturn().getResponse().getHeader("X-ID");
      }

      @AfterEach
      void deletaEntrada() throws Exception {
        mvc.perform(del("/estoque/eq4/movimento/" + idEntrada));
      }

      @Test
      @DisplayName("Deve retornar 201 quando fizer um POST de transferencia")
      void TestPostMovimentacaoTransferencia() throws Exception {

        var transferencia = movimentoEstoqueBuilderTest.criarMovimentoDto();
        transferencia.setValorCusto(new BigDecimal("500.00"));
        transferencia.setQuantidade(new BigDecimal("5.00"));
        transferencia.setDepositoDestinoId(1000);
        transferencia.setOrigemMovimentacao(OrigemMovimentacao.TRANSFERENCIA);
        var idTransferencia = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", transferencia))
          .andExpect(status().isCreated()).andReturn().getResponse().getHeader("X-ID");

        mvc.perform(del("/estoque/eq4/movimento/" + idTransferencia));
      }

      @Test
      @DisplayName("Deve retornar 204 quando fizer um DEL de transferencia")
      void TestDelMovimentacaoTransferencia() throws Exception {

        var transferencia = movimentoEstoqueBuilderTest.criarMovimentoDto();
        transferencia.setValorCusto(new BigDecimal("500.00"));
        transferencia.setQuantidade(new BigDecimal("5.00"));
        transferencia.setDepositoDestinoId(1000);
        transferencia.setOrigemMovimentacao(OrigemMovimentacao.TRANSFERENCIA);
        var idTransferencia = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", transferencia))
          .andReturn().getResponse().getHeader("X-ID");

        mvc.perform(del("/estoque/eq4/movimento/" + idTransferencia))
          .andExpect(status().isNoContent());
      }

      @Test
      @DisplayName("Deve retornar 200 quando fizer um Get id de transferencia")
      void TestGetIdMovimentacaoTransferencia() throws Exception {

        var transferencia = movimentoEstoqueBuilderTest.criarMovimentoDto();
        transferencia.setValorCusto(new BigDecimal("500.00"));
        transferencia.setQuantidade(new BigDecimal("5.00"));
        transferencia.setDepositoDestinoId(1000);
        transferencia.setOrigemMovimentacao(OrigemMovimentacao.TRANSFERENCIA);
        var idTransferencia = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", transferencia))
          .andReturn().getResponse().getHeader("X-ID");

        mvc.perform(get("/estoque/eq4/movimento/" + idTransferencia))
          .andExpect(status().isOk())
          .andExpect(jsonPath("id", notNullValue()))
          .andExpect(
            jsonPath("dataMovimentacao", is(transferencia.getDataMovimentacao().toString())))
          .andExpect(
            jsonPath("origemMovimentacao", is(transferencia.getOrigemMovimentacao().toString())))
          .andExpect(jsonPath("produtoId", is(1000)))
          .andExpect(jsonPath("produtoDescricao", is("Produto Movimentacao")))
          .andExpect(jsonPath("produtoUnidadeMedidaDescricao", is("UNIDADE")))
          .andExpect(jsonPath("depositoEmpresaId", is(1)))
          .andExpect(jsonPath("depositoId", is(999)))
          .andExpect(jsonPath("depositoNome", is("Estoque Origem")))
          .andExpect(jsonPath("depositoDestinoId", is(1000)))
          .andExpect(jsonPath("depositoDestinoNome", is("Estoque Destino")))
          .andExpect(
            jsonPath("quantidade", closeTo(transferencia.getQuantidade().doubleValue(), 0.01)))
          .andExpect(
            jsonPath("valorCusto", closeTo(transferencia.getValorCusto().doubleValue(), 0.01)))
          .andExpect(jsonPath("observacao", is(transferencia.getObservacao())));

        mvc.perform(del("/estoque/eq4/movimento/" + idTransferencia));

      }


    }
  }


  @Nested
  class TesteMovimentoDeAjuste {

    private String idEntrada;

    @BeforeEach
    void criaMovimentoDeEntrada() throws Exception {
      var entrada = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entrada.setValorCusto(new BigDecimal("500.00"));
      entrada.setQuantidade(new BigDecimal("10.00"));
      entrada.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
      idEntrada = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entrada))
        .andReturn().getResponse().getHeader("X-ID");
    }

    @AfterEach
    void deletaEntrada() throws Exception {
      mvc.perform(del("/estoque/eq4/movimento/" + idEntrada));
    }

    @Nested
    @DisplayName("Testa a movimentação de ajuste do estoque sem lote")
    class TesteMovimentoDeAjusteSemLote {

      @Test
      @DisplayName("Deve retornar 201 quando fizer um POST de ajuste")
      void TestPostAjuste() throws Exception {
        var ajuste = movimentoEstoqueBuilderTest.criarMovimentoDto();
        ajuste.setValorCusto(new BigDecimal("500.00"));
        ajuste.setQuantidade(new BigDecimal("6.00"));
        ajuste.setOrigemMovimentacao(OrigemMovimentacao.AJUSTE);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", ajuste))
          .andExpect(status().isCreated())
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }


      @Test
      @DisplayName("Deve retornar 204 quando fizer um DEL de ajuste")
      void TestDelMovimentoAjuste() throws Exception {
        var ajuste = movimentoEstoqueBuilderTest.criarMovimentoDto();
        ajuste.setValorCusto(new BigDecimal("500.00"));
        ajuste.setQuantidade(new BigDecimal("6.00"));
        ajuste.setOrigemMovimentacao(OrigemMovimentacao.AJUSTE);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", ajuste))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(del("/estoque/eq4/movimento/" + id))
          .andExpect(status().isNoContent());

      }

      @Test
      @DisplayName("Deve retornar 200 quando fizer um Get id de ajuste")
      void TestGetIdAjuste() throws Exception {
        var ajuste = movimentoEstoqueBuilderTest.criarMovimentoDto();
        ajuste.setValorCusto(new BigDecimal("500.00"));
        ajuste.setQuantidade(new BigDecimal("6.00"));
        ajuste.setOrigemMovimentacao(OrigemMovimentacao.AJUSTE);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", ajuste))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/estoque/eq4/movimento/" + id))
          .andExpect(status().isOk())
          .andExpect(jsonPath("id", notNullValue()))
          .andExpect(
            jsonPath("dataMovimentacao", is(ajuste.getDataMovimentacao().toString())))
          .andExpect(
            jsonPath("origemMovimentacao", is(ajuste.getOrigemMovimentacao().toString())))
          .andExpect(jsonPath("produtoId", is(1000)))
          .andExpect(jsonPath("produtoDescricao", is("Produto Movimentacao")))
          .andExpect(jsonPath("produtoUnidadeMedidaDescricao", is("UNIDADE")))
          .andExpect(jsonPath("depositoEmpresaId", is(1)))
          .andExpect(jsonPath("depositoId", is(999)))
          .andExpect(jsonPath("depositoNome", is("Estoque Origem")))
          .andExpect(jsonPath("quantidade", is(4.0)))
          .andExpect(jsonPath("valorCusto", is(500.00)))
          .andExpect(jsonPath("observacao", is(ajuste.getObservacao())));

        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }

      @Test
      @DisplayName("Dado que crio uma movimentação de entrada/saida, o saldo deve retornar 200 e os valores dos deposítos verificados")
      void TestSaldoMovimentacaoAjuste() throws Exception {
        var ajuste = movimentoEstoqueBuilderTest.criarMovimentoDto();
        String produtoId = "1000";
        ajuste.setValorCusto(new BigDecimal("500.00"));
        ajuste.setQuantidade(new BigDecimal("6.00"));
        ajuste.setOrigemMovimentacao(OrigemMovimentacao.AJUSTE);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", ajuste))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/estoque/eq4/saldo-estoque/" + produtoId))
          .andExpect(status().isOk())
          .andExpect(
            jsonPath("totalProdutos", closeTo(ajuste.getQuantidade().doubleValue(), 0.01)));

        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }

      @Test
      @DisplayName("Dado que faço um ajuste, a quantidade de saldo de estoque atual em produtos deve ser alterada")
      void TestQuantidadeEstoqueAtualProduto() throws Exception {
        var ajuste = movimentoEstoqueBuilderTest.criarMovimentoDto();
        String produtoId = "1000";
        ajuste.setValorCusto(new BigDecimal("500.00"));
        ajuste.setQuantidade(new BigDecimal("6.00"));
        ajuste.setOrigemMovimentacao(OrigemMovimentacao.AJUSTE);
        var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", ajuste))
          .andReturn().getResponse().getHeader("X-ID");
        mvc.perform(get("/v1/cadastro/produto/" + produtoId))
          .andExpect(jsonPath("quantidadeEstoqueAtual",
            closeTo(ajuste.getQuantidade().doubleValue(), 0.01)));
        mvc.perform(del("/estoque/eq4/movimento/" + id));
      }


    }

  }

  @Nested
  @DisplayName("Testa os Gets de eq2-eq3")
  class TestGets {

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET dos depósitos")
    void testGetDeposito() throws Exception {

      mvc.perform(get("/estoque/eq2-eq3/depositos"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("[*].id").exists())
        .andExpect(jsonPath("[*].id", notNullValue()))
        .andExpect(jsonPath("[*].nome").exists())
        .andExpect(jsonPath("[*].nome", notNullValue()))
        .andExpect(jsonPath("[*].tipo").exists())
        .andExpect(jsonPath("[*].tipo", notNullValue()))
        .andExpect(jsonPath("[*].venda").exists())
        .andExpect(jsonPath("[*].venda", notNullValue()))
        .andExpect(jsonPath("[*].situacao").exists())
        .andExpect(jsonPath("[*].situacao", notNullValue()))
        .andExpect(jsonPath("[*].empresaId").exists())
        .andExpect(jsonPath("[*].empresaId", notNullValue()))
        .andExpect(jsonPath("[*].empresaNome").exists())
        .andExpect(jsonPath("[*].empresaNome", notNullValue()))
        .andExpect(jsonPath("[*].sistema").exists())
        .andExpect(jsonPath("[*].sistema", notNullValue()))
        .andExpect(jsonPath("[*].permiteEstoqueNegativo").exists())
        .andExpect(jsonPath("[*].permiteEstoqueNegativo", notNullValue()))
        .andExpect(jsonPath("[*].ativo").exists())
        .andExpect(jsonPath("[*].ativo", notNullValue()));

    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET dos produtos")
    void testGetProdutos() throws Exception {

      mvc.perform(get("/estoque/eq2-eq3/produtos"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("[*].id").exists())
        .andExpect(jsonPath("[*].id", notNullValue()))
        .andExpect(jsonPath("[*].codigo").exists())
        .andExpect(jsonPath("[*].codigo", notNullValue()));


    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET dos produtos código")
    void testGetProdutosCodigo() throws Exception {

      mvc.perform(get("/estoque/eq2-eq3/produtos/codigo"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("[*].id").exists())
        .andExpect(jsonPath("[*].id", notNullValue()))
        .andExpect(jsonPath("[*].codigo").exists())
        .andExpect(jsonPath("[*].codigo", notNullValue()));


    }
  }

  @Nested
  @DisplayName("Testa rastreabilidade")
  class TestRastreabilidade {

    @Test
    @DisplayName("Deve retornar 201 quando fizer um POST de rastreabilidade")
    void testPostRastreabilidade() throws Exception {
      var rastreabilidade = movimentoEstoqueBuilderTest.rastreabilidadeInDto();
      rastreabilidade.setQuantidade(new BigDecimal("10.00"));
      mvc.perform(post("/estoque/eq2-eq3/rastreabilidade/", rastreabilidade))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("id").exists())
        .andExpect(jsonPath("id", notNullValue()))
        .andExpect(jsonPath("dataValidade", is(rastreabilidade.getDataValidade().toString())))
        .andExpect(jsonPath("numeroLote", is(rastreabilidade.getLote())));

    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET de rastreabilidade")
    void testGetRastreabilidade() throws Exception {
      var rastreabilidade = movimentoEstoqueBuilderTest.rastreabilidadeInDto();
      rastreabilidade.setQuantidade(new BigDecimal("10.00"));
      mvc.perform(post("/estoque/eq2-eq3/rastreabilidade/", rastreabilidade)).andReturn()
        .getResponse().getHeader("X-ID");
      mvc.perform(get("/estoque/eq2-eq3/rastreabilidade/"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("[*].id").exists())
        .andExpect(jsonPath("[*].id", notNullValue()))
        .andExpect(jsonPath("[*].dataValidade").exists())
        .andExpect(jsonPath("[*].dataValidade", notNullValue()))
        .andExpect(jsonPath("[*].numeroLote").exists())
        .andExpect(jsonPath("[*].numeroLote", notNullValue()));
    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET de rastreabilidade por id do produto")
    void testGetRastreabilidadeIdProduto() throws Exception {
      var rastreabilidade = movimentoEstoqueBuilderTest.rastreabilidadeInDto();
      rastreabilidade.setQuantidade(new BigDecimal("10.00"));
      var id = mvc.perform(post("/estoque/eq2-eq3/rastreabilidade/", rastreabilidade)).andReturn()
        .getResponse().getHeader("X-ID");
      // Usar o produto 1001 para fazer o GET
      mvc.perform(get("/estoque/eq2-eq3/rastreabilidade/1001"))
        .andExpect(status().isOk())
        .andExpect(jsonPath(String.format("$[?(@.id == %s)]", id), notNullValue()))
        .andExpect(jsonPath(String.format("$[?(@.id == %s)].dataValidade", id),
          Matchers.contains(rastreabilidade.getDataValidade().toString())))
        .andExpect(jsonPath(String.format("$[?(@.id == %s)].numeroLote", id),
          Matchers.contains(rastreabilidade.getLote())));
    }


  }

  @Nested
  class MultiEmpresaTest {

    @ParameterizedTest
    @EnumSource(value = OrigemMovimentacao.class, names = {"ENTRADA", "SAIDA"})
    @DisplayName("Deve retornar 201 quando fizer um POST movimentacao de entrada")
    void TestPostMovimentacoes(OrigemMovimentacao origemMovimentacao) throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoMultiEmpresaDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setOrigemMovimentacao(origemMovimentacao);
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida, "2"))
        .andExpect(status().isCreated())
        .andReturn().getResponse().getHeader("X-ID");

      mvc.perform(get("/estoque/eq4/movimento/" + id))
        .andExpect(status().isOk())
        .andExpect(jsonPath("id", notNullValue()))
        .andExpect(
          jsonPath("dataMovimentacao", is(entradaSaida.getDataMovimentacao().toString())))
        .andExpect(jsonPath("origemMovimentacao", is(origemMovimentacao.name())))
        .andExpect(jsonPath("produtoId", is(1000)))
        .andExpect(jsonPath("produtoDescricao", is("Produto Movimentacao")))
        .andExpect(jsonPath("produtoUnidadeMedidaDescricao", is("UNIDADE")))
        .andExpect(jsonPath("depositoEmpresaId", is(2)))
        .andExpect(jsonPath("depositoId", is(1004)))
        .andExpect(jsonPath("depositoNome", is("Estoque Origem empresa 2")))
        .andExpect(jsonPath("quantidade", is(10.0)))
        .andExpect(jsonPath("valorCusto", is(500.00)))
        .andExpect(jsonPath("observacao", is(entradaSaida.getObservacao())));

      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }

  }

  @Nested
  @DisplayName("Teste de relatorios de estoque")
  class TesteRelatorios {

    @Test
    @Disabled("Incompatibilidade da Query com o h2")
    @DisplayName("Deve retornar 200 quando fizer um GET em relatorio de estoque minimo")
    void TestRelatorioEstoqueMinimo() throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
      var produtoCodigo = "76614";
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/relatorio/rl2/estoque-minimo")
          .queryParam("empresasId", "1")

        )
        .andExpect(status().isOk())
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].empresaNome",
          "76614"), contains("Nuvy")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].depositoNome",
          "76614"), contains("Estoque Origem")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoCodigo",
          "76614"), contains("76614")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoDescricao",
          "76614"), contains("Produto Movimentacao")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoUnidadeMedida",
          "76614"), contains("UNIDADE")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoSaldoAtual",
          "76614"), contains(10.00)))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoSaldoMinimo",
          "76614"), contains(1000)))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].estoqueMinimoXSaldoEstoque",
          "76614"), contains(-990)))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoTipoMercadoria",
          "76614"), contains("Mercadoria para Revenda")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoNcmCodigo",
          "76614"), contains("01")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoNcmDescricao",
          "76614"), contains("Animais vivos")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoPesoLiquido",
          "76614"), contains(850.4400)))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoPesoBruto",
          "76614"), contains(265.0100)))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoLote",
          "76614"), contains("-")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoQualificacao",
          "76614"), contains("-")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoCategoria",
          "76614"), contains("-")))
        .andExpect(jsonPath(String.format("$[?(@.produtoCodigo == %s)].produtoSituacao",
          "76614"), contains("ATIVO")))
      ;

      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET em relatorio de estoque detalhado")
    void TestRelatorioEstoqueDetalhado() throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoDto();
      String produtoId = "1000";
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida))
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/relatorio/rl2/estoque-detalhado")
          .queryParam("empresasId", "1")
          .queryParam("idDeposito", "999")
          .queryParam("dataMovimentacaoInicial", LocalDate.now().minusDays(1).toString())
          .queryParam("dataMovimentacaoFinal", LocalDate.now().toString())
          .queryParam("idProduto", produtoId)

        )
        .andExpect(status().isOk())
        .andExpect(jsonPath("$[0].codigoProduto", is("76614")))
        .andExpect(jsonPath("$[0].descricaoProduto", is("Produto Movimentacao")))
        .andExpect(
          jsonPath("$[0].dataMovimentacao", is(entradaSaida.getDataMovimentacao().toString())))
        .andExpect(
          jsonPath("$[0].origemMovimentacao", is(entradaSaida.getOrigemMovimentacao().toString())))
        .andExpect(jsonPath("$[0].deposito.id", is(999)))
        .andExpect(jsonPath("$[0].deposito.nome", is("Estoque Origem")))
        .andExpect(jsonPath("$[0].deposito.tipo", is("PROPRIO")))
        .andExpect(jsonPath("$[0].deposito.situacao", is("ATIVO")))
        .andExpect(jsonPath("$[0].quantidade",
          is(closeTo(entradaSaida.getQuantidade().doubleValue(), 0.01))))
        .andExpect(jsonPath("$[0].valorCusto",
          is(closeTo(entradaSaida.getValorCusto().doubleValue(), 0.01))))
        .andExpect(jsonPath("$[0].valorCustoTotal", is(5000.00)))
        .andExpect(jsonPath("$[0].observacao", is(entradaSaida.getObservacao())))
        .andExpect(jsonPath("$[0].usuario.id", is("293bb980-293c-4be7-80a9-590776864eb1")))
        .andExpect(jsonPath("$[0].usuario.email", is("<EMAIL>")))
        .andExpect(jsonPath("$[0].usuario.nome", is("Nuvy")))
        .andExpect(jsonPath("$[0].usuario.situacao", is("ATIVO")))
        .andExpect(jsonPath("$[0].ncmProduto.id", is("01")))
        .andExpect(jsonPath("$[0].ncmProduto.descricao", is("Animais vivos")))
        .andExpect(jsonPath("$[0].pesoLiquidoProduto", is(850.4400)))
        .andExpect(jsonPath("$[0].pesoBrutoProduto", is(265.0100)))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.id", is(64)))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.unidade", is("UN")))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.descricao", is("UNIDADE")))
      ;

      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }

    @Test
    @DisplayName("Deve retornar 200 quando fizer um GET em relatorio de estoque detalhado multiempresa")
    void TestRelatorioEstoqueDetalhadoMultiEmpresa() throws Exception {
      var entradaSaida = movimentoEstoqueBuilderTest.criarMovimentoMultiEmpresaDto();
      String produtoId = "1000";
      entradaSaida.setValorCusto(new BigDecimal("500.00"));
      entradaSaida.setQuantidade(new BigDecimal("10.00"));
      entradaSaida.setOrigemMovimentacao(OrigemMovimentacao.ENTRADA);
      var id = mvc.perform(post("/estoque/eq2-eq3/movimentacao/", entradaSaida, "2"))
        .andReturn().getResponse().getHeader("X-ID");
      mvc.perform(get("/relatorio/rl2/estoque-detalhado", "2")
          .queryParam("empresasId", "2")
          .queryParam("idDeposito", "1004")
          .queryParam("dataMovimentacaoInicial", LocalDate.now().minusDays(1).toString())
          .queryParam("dataMovimentacaoFinal", LocalDate.now().toString())
          .queryParam("idProduto", produtoId)

        )
        .andExpect(status().isOk())
        .andExpect(jsonPath("$[0].codigoProduto", is("76614")))
        .andExpect(jsonPath("$[0].descricaoProduto", is("Produto Movimentacao")))
        .andExpect(
          jsonPath("$[0].dataMovimentacao", is(entradaSaida.getDataMovimentacao().toString())))
        .andExpect(
          jsonPath("$[0].origemMovimentacao", is(entradaSaida.getOrigemMovimentacao().toString())))
        .andExpect(jsonPath("$[0].deposito.id", is(1004)))
        .andExpect(jsonPath("$[0].deposito.nome", is("Estoque Origem empresa 2")))
        .andExpect(jsonPath("$[0].deposito.tipo", is("PROPRIO")))
        .andExpect(jsonPath("$[0].deposito.situacao", is("ATIVO")))
        .andExpect(jsonPath("$[0].quantidade",
          is(closeTo(entradaSaida.getQuantidade().doubleValue(), 0.01))))
        .andExpect(jsonPath("$[0].valorCusto",
          is(closeTo(entradaSaida.getValorCusto().doubleValue(), 0.01))))
        .andExpect(jsonPath("$[0].valorCustoTotal", is(5000.00)))
        .andExpect(jsonPath("$[0].observacao", is(entradaSaida.getObservacao())))
        .andExpect(jsonPath("$[0].usuario.id", is("293bb980-293c-4be7-80a9-590776864eb1")))
        .andExpect(jsonPath("$[0].usuario.email", is("<EMAIL>")))
        .andExpect(jsonPath("$[0].usuario.nome", is("Nuvy")))
        .andExpect(jsonPath("$[0].usuario.situacao", is("ATIVO")))
        .andExpect(jsonPath("$[0].ncmProduto.id", is("01")))
        .andExpect(jsonPath("$[0].ncmProduto.descricao", is("Animais vivos")))
        .andExpect(jsonPath("$[0].pesoLiquidoProduto", is(850.4400)))
        .andExpect(jsonPath("$[0].pesoBrutoProduto", is(265.0100)))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.id", is(64)))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.unidade", is("UN")))
        .andExpect(jsonPath("$[0].unidadeMedidaProduto.descricao", is("UNIDADE")))
      ;

      mvc.perform(del("/estoque/eq4/movimento/" + id));
    }
  }
}






