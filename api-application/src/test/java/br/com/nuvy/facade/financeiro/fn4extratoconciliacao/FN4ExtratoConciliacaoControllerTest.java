package br.com.nuvy.facade.financeiro.fn4extratoconciliacao;

import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import br.com.nuvy.NuvyApiTestBase;
import br.com.nuvy.facade.financeiro.fn1contaspagar.ContasPagarBuilder;
import br.com.nuvy.facade.financeiro.fn2contasreceber.ContasReceberBuilder;
import com.jayway.jsonpath.JsonPath;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.web.servlet.MockMvc;

class FN4ExtratoConciliacaoControllerTest extends NuvyApiTestBase {

  // TODO REFAZER TESTES DE CONCILIAÇÃO

  @Autowired
  private MockMvc mvc;

  private final ContasPagarBuilder contasPagarBuilder = new ContasPagarBuilder();
  private final ContasReceberBuilder contasReceberBuilder = new ContasReceberBuilder();

  @Test
  @Disabled
  @DisplayName("Deve retornar 201 quando fizer uma conciliação de conta a pagar")
  void testConciliacaoContaPagar() throws Exception {
    var contaPagar = contasPagarBuilder.tituloDtoSemCnab();
    var baixa = contasPagarBuilder.tituloBaixaDto();
    baixa.setDataBaixa(LocalDate.now());
    baixa.setValorBaixa(new BigDecimal("1000.00"));
    baixa.setContaBancariaId(1001);
    baixa.setFormaPagamentoId(1);

    var id = mvc.perform(post("/financeiro/fn1/conta-pagar/", contaPagar))
      .andReturn().getResponse().getHeader("X-ID");

    var idBaixa = mvc.perform(post("/financeiro/fn1/conta-pagar/" + id + "/baixa/", baixa))
      .andReturn().getResponse().getHeader("X-ID");


    /*para fazer o POST conciliacao, é necessário ter o id do movimento não conciliado. A unica api que retorna esse id é a de fn4/extrato
    Por isso estou extraindo o id dessa api para usar depois no Post.
    * */
    var response = mvc.perform(get("/financeiro/fn4/extrato?contaBancariaId=1001&dataBaixaInicial=2020-01-01&dataBaixaFinal=2099-01-01&page=0&size=100000")).andReturn().getResponse().getContentAsString();

    System.out.println("==================OIOI" + response);

    List<Integer> idsNaoConciliados = JsonPath.read(response, "$[*].[?(@.status == 'NAO_CONCILIADO')].id" );

    mvc.perform(post("/financeiro/fn4/conciliacao/extrato/"+idsNaoConciliados.stream().findFirst().get(), ""))
      .andExpect(status().isCreated());


    mvc.perform(del("/financeiro/fn1/conta-pagar/baixa/" + idBaixa));
  }

  @Test
  @Disabled
  @DisplayName("Deve retornar 201 quando fizer uma desconciliação de conta a pagar")
  void testDesconciliacaoContaPagar() throws Exception {
    var contaPagar = contasPagarBuilder.tituloDtoSemCnab();
    var baixa = contasPagarBuilder.tituloBaixaDto();
    baixa.setDataBaixa(LocalDate.now());
    baixa.setValorBaixa(new BigDecimal("1000.00"));
    baixa.setContaBancariaId(1001);
    baixa.setFormaPagamentoId(1);

    var id = mvc.perform(post("/financeiro/fn1/conta-pagar/", contaPagar))
      .andReturn().getResponse().getHeader("X-ID");

    var idBaixa = mvc.perform(post("/financeiro/fn1/conta-pagar/" + id + "/baixa/", baixa))
      .andReturn().getResponse().getHeader("X-ID");


    /*para fazer o POST conciliacao, é necessário ter o id do movimento não conciliado. A unica api que retorna esse id é a de fn4/extrato
    Por isso estou extraindo o id dessa api para usar depois no Post.
    * */
    var response = mvc.perform(get("/financeiro/fn4/extrato?contaBancariaId=1001&dataBaixaInicial=2020-01-01&dataBaixaFinal=2099-01-01&page=0&size=100000")).andReturn().getResponse().getContentAsString();
    List<Integer> idsNaoConciliados = JsonPath.read(response, "$[*].[?(@.status == 'NAO_CONCILIADO' && @.tipo == 'DESPESA')].id" );


    mvc.perform(post("/financeiro/fn4/conciliacao/extrato/"+idsNaoConciliados.stream().findFirst().get(), ""));

    var response2 = mvc.perform(get("/financeiro/fn4/extrato?contaBancariaId=1001&dataBaixaInicial=2020-01-01&dataBaixaFinal=2099-01-01&page=0&size=100000")).andReturn().getResponse().getContentAsString();
    List<Integer> idsConciliados = JsonPath.read(response2, "$[*].[?(@.status == 'CONCILIADO' && @.tipo == 'DESPESA')].id" );


    mvc.perform(post("/financeiro/fn4/desconciliacao/extrato/"+idsConciliados.stream().findFirst().get(), ""))
      .andExpect(status().isCreated());


    mvc.perform(del("/financeiro/fn1/conta-pagar/baixa/" + idBaixa));
  }

  @Test
  @Disabled
  @DisplayName("Deve retornar 201 quando fizer uma desconciliação de conta a receber")
  void testDesconciliacaoContaReceber() throws Exception {
    var contaReceber = contasReceberBuilder.tituloDtoSemCnab();
    var baixa = contasReceberBuilder.tituloBaixaDto();
    baixa.setDataBaixa(LocalDate.now());
    baixa.setValorBaixa(new BigDecimal("1000.00"));
    baixa.setContaBancariaId(1001);
    baixa.setFormaPagamentoId(1);

    var id = mvc.perform(post("/financeiro/fn2/conta-receber/", contaReceber))
      .andReturn().getResponse().getHeader("X-ID");

    var idBaixa = mvc.perform(post("/financeiro/fn2/conta-receber/" + id + "/baixa/", baixa))
      .andReturn().getResponse().getHeader("X-ID");


    /*para fazer o POST conciliacao, é necessário ter o id do movimento não conciliado. A unica api que retorna esse id é a de fn4/extrato
    Por isso estou extraindo o id dessa api para usar depois no Post.
    * */
    var response = mvc.perform(get("/financeiro/fn4/extrato?contaBancariaId=1001&dataBaixaInicial=2020-01-01&dataBaixaFinal=2099-01-01&page=0&size=100000")).andReturn().getResponse().getContentAsString();
    List<Integer> idsNaoConciliados = JsonPath.read(response, "$[*].[?(@.status == 'NAO_CONCILIADO' && @.tipo == 'RECEITA')].id" );


    mvc.perform(post("/financeiro/fn4/conciliacao/extrato/"+idsNaoConciliados.stream().findFirst().get(), ""));

    var response2 = mvc.perform(get("/financeiro/fn4/extrato?contaBancariaId=1001&dataBaixaInicial=2020-01-01&dataBaixaFinal=2099-01-01&page=0&size=100000")).andReturn().getResponse().getContentAsString();
    List<Integer> idsConciliados = JsonPath.read(response2, "$[*].[?(@.status == 'CONCILIADO' && @.tipo == 'RECEITA')].id" );


    mvc.perform(post("/financeiro/fn4/desconciliacao/extrato/"+idsConciliados.stream().findFirst().get(), ""))
      .andExpect(status().isCreated());


    mvc.perform(del("/financeiro/fn1/conta-pagar/baixa/" + idBaixa));
  }

  @Test
  @Disabled
  @DisplayName("Deve retornar 201 quando fizer uma conciliação de contas a receber")
  void testConciliacaoContaReceber() throws Exception {
    var contaReceber = contasReceberBuilder.tituloDtoSemCnab();
    var baixa = contasReceberBuilder.tituloBaixaDto();
    baixa.setDataBaixa(LocalDate.now());
    baixa.setValorBaixa(new BigDecimal("1000.00"));
    baixa.setContaBancariaId(1001);
    baixa.setFormaPagamentoId(1);

    var id = mvc.perform(post("/financeiro/fn2/conta-receber/", contaReceber))
      .andReturn().getResponse().getHeader("X-ID");

    var idBaixa = mvc.perform(post("/financeiro/fn2/conta-receber/" + id + "/baixa/", baixa))
      .andReturn().getResponse().getHeader("X-ID");


    /*para fazer o POST conciliacao, é necessário ter o id do movimento não conciliado. A unica api que retorna esse id é a de fn4/extrato
    Por isso estou extraindo o id dessa api para usar depois no Post.
    * */
    var response = mvc.perform(get("/financeiro/fn4/extrato/")).andReturn().getResponse().getContentAsString();
    List<Integer> idsNaoConciliados = JsonPath.read(response, "$[*].[?(@.status == 'NAO_CONCILIADO')].id" );


    mvc.perform(post("/financeiro/fn4/conciliacao/extrato/"+idsNaoConciliados.stream().findFirst().get(), ""))
      .andExpect(status().isCreated());


    mvc.perform(del("/financeiro/fn2/conta-receber/baixa/" + idBaixa));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um get saldo")
  void testGetFornecedor() throws Exception {
    mvc.perform(get("/financeiro/fn4/extrato/saldo")
        .param("contaBancariaId", "1001"))
      .andExpect(status().isOk())
      .andExpect(jsonPath("$.saldo").exists())
      .andExpect(jsonPath("$.saldo", notNullValue()));
  }



}