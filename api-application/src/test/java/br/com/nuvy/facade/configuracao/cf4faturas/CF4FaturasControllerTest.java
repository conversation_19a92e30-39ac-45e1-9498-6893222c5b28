package br.com.nuvy.facade.configuracao.cf4faturas;

import static org.hamcrest.Matchers.closeTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import br.com.nuvy.NuvyApiTestBase;
import br.com.nuvy.api.enums.BucketsS3;
import br.com.nuvy.api.enums.FaturaStatus;
import br.com.nuvy.api.financeiro.dto.FaturamentoClienteDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;

class CF4FaturasControllerTest extends NuvyApiTestBase {

  @Autowired
  private MockMvc mvc;

  @Test
  @DisplayName("Deve retornar 200 quando fizer um GET faturas dos clientes")
  void testGetFaturas() throws Exception {


    var faturasExpected = List.of(FaturamentoClienteDto.builder()
      .caminhoBoletoFatura("urlboleto")
      .caminhoNotaFatura("urlnotafaturada")
      .valor(new BigDecimal("99.00"))
      .data(LocalDateTime.now())
      .status(FaturaStatus.PAGO)
      .descricao("Iniciante")
      .build());

    Mockito.when(amazonS3Service.generateSignedUrl(faturasExpected.get(0).getCaminhoBoletoFatura(),
      BucketsS3.SAP)).thenReturn("urlboleto");
    Mockito.when(amazonS3Service.generateSignedUrl(faturasExpected.get(0).getCaminhoNotaFatura(),
      BucketsS3.SAP)).thenReturn("urlnotafaturada");

    mvc.perform(get("/configuracao/cf4/faturas"))
      .andExpect(status().isOk())
      .andExpect(jsonPath("[0].data", is(notNullValue())))
      .andExpect(jsonPath("[0].descricao", is(faturasExpected.get(0).getDescricao())))
      .andExpect(jsonPath("[0].valor", closeTo(faturasExpected.get(0).getValor().doubleValue(), 0.01)))
      .andExpect(jsonPath("[0].caminhoBoletoFatura", is(faturasExpected.get(0).getCaminhoBoletoFatura())))
      .andExpect(jsonPath("[0].caminhoNotaFatura", is(faturasExpected.get(0).getCaminhoNotaFatura())))
      .andDo(print())
    ;
  }
}