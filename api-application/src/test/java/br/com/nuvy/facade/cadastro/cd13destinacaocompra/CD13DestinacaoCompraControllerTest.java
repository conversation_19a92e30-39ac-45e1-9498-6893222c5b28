package br.com.nuvy.facade.cadastro.cd13destinacaocompra;

import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import br.com.nuvy.NuvyApiTestBase;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;


class CD13DestinacaoCompraControllerTest extends NuvyApiTestBase {

  @Autowired
  private MockMvc mvc;

  private final DestinacaoCompraBuilder destinacaoCompraBuilder = new DestinacaoCompraBuilder();

  @Test
  @DisplayName("Deve retornar 201 quando fizer um POST de destinacao compra")
  void testPostDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andExpect(status().isCreated());
  }

  @Test
  @DisplayName("Deve retornar 204 quando fizer um DEL de destinacao compra")
  void testDelDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    var id = mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andReturn().getResponse().getHeader("X-ID");
    mvc.perform(del("/cadastro/cd13/destinacao-compra/" + id)).andExpect(status().isNoContent());
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get id de destinacao compra")
  void testGetIdDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    var id = mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andReturn().getResponse().getHeader("X-ID");
    mvc.perform(get("/cadastro/cd13/destinacao-compra/" + id))
      .andExpect(status().isOk())
      .andExpect(jsonPath("nome", is(destinacao.getNome())))
      .andExpect(jsonPath("tipo", is(destinacao.getTipo().toString())))
      .andExpect(jsonPath("geraContasPagar", is(destinacao.getGeraContasPagar())))
      .andExpect(jsonPath("geraMovimentacaoEstoque", is(destinacao.getGeraMovimentacaoEstoque())))
      .andExpect(jsonPath("custoIcms", is(destinacao.getCustoIcms())))
      .andExpect(jsonPath("custoPis", is(destinacao.getCustoPis())))
      .andExpect(jsonPath("custoCofins", is(destinacao.getCustoCofins())))
      .andExpect(jsonPath("icmsSt", is(destinacao.getIcmsSt())))
      .andExpect(jsonPath("custoFrete", is(destinacao.getCustoFrete())))
      .andExpect(jsonPath("custoOutrasDespesas", is(destinacao.getCustoOutrasDespesas())))
      .andExpect(jsonPath("custoSeguro", is(destinacao.getCustoSeguro())))
      .andExpect(jsonPath("situacao", is(destinacao.getSituacao().toString())))
      .andExpect(jsonPath("regimeTributario.id", is(2)))
      .andExpect(jsonPath("regimeTributario.nome", is("Simples Nacional")))
      .andExpect(jsonPath("regimeTributario.situacao", is("ATIVO")))
      .andExpect(jsonPath("empresas[0].id", is(1)))
      .andExpect(jsonPath("empresas[0].cpfCnpj", is("25103676000131")))
      .andExpect(jsonPath("empresas[0].nome", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].nomeFantasia", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].situacao", is("ATIVO")))
      .andExpect(jsonPath("cfops[0].id").exists())
      .andExpect(
        jsonPath("cfops[0].cfopOrigem.id", is(destinacao.getCfops().get(0).getCfopOrigemId())))
      .andExpect(jsonPath("cfops[0].cfopOrigem.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipoOperacao", is("INTERNA")))
      .andExpect(
        jsonPath("cfops[0].cfopEntrada.id", is(destinacao.getCfops().get(0).getCfopEntradaId())))
      .andExpect(jsonPath("cfops[0].cfopEntrada.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.id",
        is((destinacao.getCfops().get(0).getCfopDevolucaoId()))))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("pisCsts[0].id").exists())
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.id",
        is(destinacao.getPisCsts().get(0).getCstPisOrigemId())))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.id",
        is(destinacao.getPisCsts().get(0).getCstPisEntradaId())))
      .andExpect(
        jsonPath("pisCsts[0].cstPisEntrada.descricao", is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.id",
        is(destinacao.getPisCsts().get(0).getCstPisDevolucaoId())))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].id").exists())
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsOrigemId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsEntradaId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.descricao",
        is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsDevolucaoId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("icmsCsosns[0].id").exists())
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsts[0].id").exists())
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.id",
        is(destinacao.getIcmsCsts().get(0).getCstIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.descricao", is("Tributada integralmente")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.id",
        is(destinacao.getIcmsCsts().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.id",
        is(destinacao.getIcmsCsts().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("ipiCsts[0].id").exists())
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiOrigemId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.tipo", is("SAIDA")));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get All de destinacao compra")
  void testGetAllDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    var id = mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andReturn().getResponse().getHeader("X-ID");
    mvc.perform(get("/cadastro/cd13/destinacao-compra/"))
      .andExpect(status().isOk())
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].id", id)).exists())
      .andExpect(
        jsonPath(String.format("$[?(@.id == '%s')].id", id), Matchers.contains(notNullValue())))
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].nome", id),
        Matchers.contains(destinacao.getNome())))
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].tipo", id),
        Matchers.contains(destinacao.getTipo().toString())))
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].geraContasPagar", id),
        Matchers.contains(destinacao.getGeraContasPagar())))
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].situacao", id),
        Matchers.contains(destinacao.getSituacao().toString())))
      .andExpect(jsonPath(String.format("$[?(@.id == '%s')].empresasNomeFantasia[0]", id),
        Matchers.contains("Nuvy")));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Patch de destinacao compra")
  void testPatchDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    var id = mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andReturn().getResponse().getHeader("X-ID");
    mvc.perform(
        patchWithQueryParam("/cadastro/cd13/destinacao-compra/" + id, "situacao", "INATIVO"))
      .andExpect(status().isOk());
    mvc.perform(get("/cadastro/cd13/destinacao-compra/" + id))
      .andExpect(status().isOk())
      .andExpect(jsonPath("nome", is(destinacao.getNome())))
      .andExpect(jsonPath("tipo", is(destinacao.getTipo().toString())))
      .andExpect(jsonPath("geraContasPagar", is(destinacao.getGeraContasPagar())))
      .andExpect(jsonPath("geraMovimentacaoEstoque", is(destinacao.getGeraMovimentacaoEstoque())))
      .andExpect(jsonPath("custoIcms", is(destinacao.getCustoIcms())))
      .andExpect(jsonPath("custoPis", is(destinacao.getCustoPis())))
      .andExpect(jsonPath("custoCofins", is(destinacao.getCustoCofins())))
      .andExpect(jsonPath("icmsSt", is(destinacao.getIcmsSt())))
      .andExpect(jsonPath("custoFrete", is(destinacao.getCustoFrete())))
      .andExpect(jsonPath("custoOutrasDespesas", is(destinacao.getCustoOutrasDespesas())))
      .andExpect(jsonPath("custoSeguro", is(destinacao.getCustoSeguro())))
      .andExpect(jsonPath("situacao", is("INATIVO")))
      .andExpect(jsonPath("regimeTributario.id", is(2)))
      .andExpect(jsonPath("regimeTributario.nome", is("Simples Nacional")))
      .andExpect(jsonPath("regimeTributario.situacao", is("ATIVO")))
      .andExpect(jsonPath("empresas[0].id", is(1)))
      .andExpect(jsonPath("empresas[0].cpfCnpj", is("25103676000131")))
      .andExpect(jsonPath("empresas[0].nome", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].nomeFantasia", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].situacao", is("ATIVO")))
      .andExpect(jsonPath("cfops[0].id").exists())
      .andExpect(
        jsonPath("cfops[0].cfopOrigem.id", is(destinacao.getCfops().get(0).getCfopOrigemId())))
      .andExpect(jsonPath("cfops[0].cfopOrigem.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipoOperacao", is("INTERNA")))
      .andExpect(
        jsonPath("cfops[0].cfopEntrada.id", is(destinacao.getCfops().get(0).getCfopEntradaId())))
      .andExpect(jsonPath("cfops[0].cfopEntrada.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.id",
        is((destinacao.getCfops().get(0).getCfopDevolucaoId()))))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("pisCsts[0].id").exists())
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.id",
        is(destinacao.getPisCsts().get(0).getCstPisOrigemId())))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.id",
        is(destinacao.getPisCsts().get(0).getCstPisEntradaId())))
      .andExpect(
        jsonPath("pisCsts[0].cstPisEntrada.descricao", is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.id",
        is(destinacao.getPisCsts().get(0).getCstPisDevolucaoId())))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].id").exists())
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsOrigemId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsEntradaId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.descricao",
        is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.id",
        is(destinacao.getCofinsCsts().get(0).getCstCofinsDevolucaoId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("icmsCsosns[0].id").exists())
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.id",
        is(destinacao.getIcmsCsosns().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsts[0].id").exists())
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.id",
        is(destinacao.getIcmsCsts().get(0).getCstIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.descricao", is("Tributada integralmente")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.id",
        is(destinacao.getIcmsCsts().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.id",
        is(destinacao.getIcmsCsts().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("ipiCsts[0].id").exists())
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiOrigemId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.id",
        is(destinacao.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.tipo", is("SAIDA")));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um PUT de destinacao compra")
  void testPutDestinacaoCompra() throws Exception {
    var destinacao = destinacaoCompraBuilder.destinacaoCompraDto();
    var id = mvc.perform(post("/cadastro/cd13/destinacao-compra", destinacao))
      .andReturn().getResponse().getHeader("X-ID");
    var destinacaoNew =  mvc.perform(get("/cadastro/cd13/destinacao-compra/" + id)).andReturn().getResponse().getContentAsString();
    var destinacaoEdit = destinacaoCompraBuilder.destinacaoCompraDtoWithId(destinacaoNew);
    destinacaoEdit.setNome("Nome Edit");
    mvc.perform(put("/cadastro/cd13/destinacao-compra/" + id, destinacaoEdit));
    mvc.perform(get("/cadastro/cd13/destinacao-compra/" + id))
      .andExpect(status().isOk())
      .andExpect(jsonPath("nome", is(destinacaoEdit.getNome())))
      .andExpect(jsonPath("tipo", is(destinacaoEdit.getTipo().toString())))
      .andExpect(jsonPath("geraContasPagar", is(destinacaoEdit.getGeraContasPagar())))
      .andExpect(
        jsonPath("geraMovimentacaoEstoque", is(destinacaoEdit.getGeraMovimentacaoEstoque())))
      .andExpect(jsonPath("custoIcms", is(destinacaoEdit.getCustoIcms())))
      .andExpect(jsonPath("custoPis", is(destinacaoEdit.getCustoPis())))
      .andExpect(jsonPath("custoCofins", is(destinacaoEdit.getCustoCofins())))
      .andExpect(jsonPath("icmsSt", is(destinacaoEdit.getIcmsSt())))
      .andExpect(jsonPath("custoFrete", is(destinacaoEdit.getCustoFrete())))
      .andExpect(jsonPath("custoOutrasDespesas", is(destinacaoEdit.getCustoOutrasDespesas())))
      .andExpect(jsonPath("custoSeguro", is(destinacaoEdit.getCustoSeguro())))
      .andExpect(jsonPath("situacao", is(destinacaoEdit.getSituacao().toString())))
      .andExpect(jsonPath("regimeTributario.id", is(2)))
      .andExpect(jsonPath("regimeTributario.nome", is("Simples Nacional")))
      .andExpect(jsonPath("regimeTributario.situacao", is("ATIVO")))
      .andExpect(jsonPath("empresas[0].id", is(1)))
      .andExpect(jsonPath("empresas[0].cpfCnpj", is("25103676000131")))
      .andExpect(jsonPath("empresas[0].nome", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].nomeFantasia", is("Nuvy")))
      .andExpect(jsonPath("empresas[0].situacao", is("ATIVO")))
      .andExpect(jsonPath("cfops[0].id").exists())
      .andExpect(
        jsonPath("cfops[0].cfopOrigem.id", is(destinacaoEdit.getCfops().get(0).getCfopOrigemId())))
      .andExpect(jsonPath("cfops[0].cfopOrigem.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopOrigem.tipoOperacao", is("INTERNA")))
      .andExpect(
        jsonPath("cfops[0].cfopEntrada.id",
          is(destinacaoEdit.getCfops().get(0).getCfopEntradaId())))
      .andExpect(jsonPath("cfops[0].cfopEntrada.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopEntrada.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.id",
        is((destinacaoEdit.getCfops().get(0).getCfopDevolucaoId()))))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.descricao",
        is("Venda de mercadoria, adquirida ou recebida de terceiros, destinada à ZFM ou ALC")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfops[0].cfopDevolucao.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("pisCsts[0].id").exists())
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.id",
        is(destinacaoEdit.getPisCsts().get(0).getCstPisOrigemId())))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.id",
        is(destinacaoEdit.getPisCsts().get(0).getCstPisEntradaId())))
      .andExpect(
        jsonPath("pisCsts[0].cstPisEntrada.descricao", is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("pisCsts[0].cstPisEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.id",
        is(destinacaoEdit.getPisCsts().get(0).getCstPisDevolucaoId())))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("pisCsts[0].cstPisDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].id").exists())
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.id",
        is(destinacaoEdit.getCofinsCsts().get(0).getCstCofinsOrigemId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.id",
        is(destinacaoEdit.getCofinsCsts().get(0).getCstCofinsEntradaId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.descricao",
        is("Operação Tributável a Alíquota Zero")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsEntrada.tipo", is("SAIDA")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.id",
        is(destinacaoEdit.getCofinsCsts().get(0).getCstCofinsDevolucaoId())))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.descricao",
        is("Operação Tributável com Alíquota Básica")))
      .andExpect(jsonPath("cofinsCsts[0].cstCofinsDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("icmsCsosns[0].id").exists())
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.id",
        is(destinacaoEdit.getIcmsCsosns().get(0).getCsosnIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsOrigem.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.id",
        is(destinacaoEdit.getIcmsCsosns().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsEntrada.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.id",
        is(destinacaoEdit.getIcmsCsosns().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsosns[0].csosnIcmsDevolucao.possuiAliquotaCredito", is(true)))
      .andExpect(jsonPath("icmsCsts[0].id").exists())
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.id",
        is(destinacaoEdit.getIcmsCsts().get(0).getCstIcmsOrigemId())))
      .andExpect(jsonPath("icmsCsts[0].cstIcmsOrigem.descricao", is("Tributada integralmente")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.id",
        is(destinacaoEdit.getIcmsCsts().get(0).getCsosnIcmsEntradaId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsEntrada.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.id",
        is(destinacaoEdit.getIcmsCsts().get(0).getCsosnIcmsDevolucaoId())))
      .andExpect(jsonPath("icmsCsts[0].csosnIcmsDevolucao.descricao",
        is("Tributada pelo Simples Nacional com permissão de crédito")))
      .andExpect(jsonPath("ipiCsts[0].id").exists())
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.id",
        is(destinacaoEdit.getIpiCsts().get(0).getCstIpiOrigemId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.id",
        is(destinacaoEdit.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.id",
        is(destinacaoEdit.getIpiCsts().get(0).getCstIpiDevolucaoId())))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.descricao", is("Saída Tributada")))
      .andExpect(jsonPath("ipiCsts[0].cstIpiEntrada.tipo", is("SAIDA")))

    ;
  }


  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get em listagem-cfop")
  void testeGetListagemCfops() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/cfops"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].tipoOperacao").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()))
      .andExpect(jsonPath("[*].tipo", notNullValue()))
      .andExpect(jsonPath("[*].tipoOperacao", notNullValue()));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get em listagem icms csosn")
  void testeGetListagemIcmsCsosn() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/icms/csosn"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].possuiAliquotaCredito").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()))
      .andExpect(jsonPath("[*].possuiAliquotaCredito", notNullValue()));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get em listagem icmscst")
  void testeGetListagemIcmsSt() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/icms/cst"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()));

  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get de CFOP")
  void testeGetCfop() throws Exception {

    mvc.perform(get("/cadastro/cd13/cfop"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].tipoOperacao").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()))
      .andExpect(jsonPath("[*].tipoOperacao", notNullValue()));
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get de sugestao CFOP")
  void testeGetSugestaoCfop() throws Exception {

    mvc.perform(get("/cadastro/cd13/sugestao-cfop/"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].cfopOrigem.id").exists())
      .andExpect(jsonPath("[*].cfopOrigem.descricao").exists())
      .andExpect(jsonPath("[*].cfopOrigem.tipo").exists())
      .andExpect(jsonPath("[*].cfopOrigem.tipoOperacao").exists())
      .andExpect(jsonPath("[*].cfopEntrada.id").exists())
      .andExpect(jsonPath("[*].cfopEntrada.descricao").exists())
      .andExpect(jsonPath("[*].cfopEntrada.tipo").exists())
      .andExpect(jsonPath("[*].cfopEntrada.tipoOperacao").exists())
      .andExpect(jsonPath("[*].cfopDevolucao.id").exists())
      .andExpect(jsonPath("[*].cfopDevolucao.descricao").exists())
      .andExpect(jsonPath("[*].cfopDevolucao.tipo").exists())
      .andExpect(jsonPath("[*].cfopDevolucao.tipoOperacao").exists())
      .andExpect(jsonPath("[*].cfopOrigem.id", notNullValue()))
      .andExpect(jsonPath("[*].cfopOrigem.descricao", notNullValue()))
      .andExpect(jsonPath("[*].cfopOrigem.tipo", notNullValue()))
      .andExpect(jsonPath("[*].cfopOrigem.tipoOperacao", notNullValue()))
      .andExpect(jsonPath("[*].cfopEntrada.id", notNullValue()))
      .andExpect(jsonPath("[*].cfopEntrada.descricao", notNullValue()))
      .andExpect(jsonPath("[*].cfopEntrada.tipo", notNullValue()))
      .andExpect(jsonPath("[*].cfopEntrada.tipoOperacao", notNullValue()))
      .andExpect(jsonPath("[*].cfopDevolucao.id", notNullValue()))
      .andExpect(jsonPath("[*].cfopDevolucao.descricao", notNullValue()))
      .andExpect(jsonPath("[*].cfopDevolucao.tipo", notNullValue()))
      .andExpect(jsonPath("[*].cfopDevolucao.tipoOperacao", notNullValue()))

    ;
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get Id de sugestao CFOP")
  void testeGetIdSugestaoCfop() throws Exception {

    //consultar os registros da tabela cd_sugestao_cfop
    var id = "11";
    mvc.perform(get("/cadastro/cd13/sugestao-cfop/" + id))
      .andExpect(status().isOk())
      .andExpect(jsonPath("id", is(11)))
      .andExpect(jsonPath("tipo", is("INDUSTRIALIZACAO")))
      .andExpect(jsonPath("cfopOrigem.id", is("5101")))
      .andExpect(jsonPath("cfopOrigem.descricao", is("Venda de produção do estabelecimento")))
      .andExpect(jsonPath("cfopOrigem.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfopOrigem.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("cfopEntrada.id", is("1551")))
      .andExpect(jsonPath("cfopEntrada.descricao", is("Compra de bem p/ o ativo imobilizado")))
      .andExpect(jsonPath("cfopEntrada.tipo", is("ENTRADA")))
      .andExpect(jsonPath("cfopEntrada.tipoOperacao", is("INTERNA")))
      .andExpect(jsonPath("cfopDevolucao.id", is("5553")))
      .andExpect(jsonPath("cfopDevolucao.descricao",
        is("Devolução de compra de bem p/ o ativo imobilizado")))
      .andExpect(jsonPath("cfopDevolucao.tipo", is("SAIDA")))
      .andExpect(jsonPath("cfopDevolucao.tipoOperacao", is("INTERNA")))

    ;
  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get de listagem Ipi cst")
  void testeGetListagemIpiCst() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/ipi/cst"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()));

  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get de listagem Pis cst")
  void testeGetListagemPisCst() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/pis/cst"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()));

  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get de listagem Pis cst")
  void testeGetListagemCofinsCst() throws Exception {

    mvc.perform(get("/cadastro/cd13/listagem/cst/cofins"))
      .andExpect(status().isOk())
      .andExpect(header().string("TotalPages", notNullValue()))
      .andExpect(header().string("TotalElements", notNullValue()))
      .andExpect(jsonPath("[*].id").exists())
      .andExpect(jsonPath("[*].descricao").exists())
      .andExpect(jsonPath("[*].tipo").exists())
      .andExpect(jsonPath("[*].id", notNullValue()))
      .andExpect(jsonPath("[*].descricao", notNullValue()));

  }

  @Test
  @DisplayName("Deve retornar 200 quando fizer um Get em iniciar")
  void testeIniciar() throws Exception {

    mvc.perform(get("/cadastro/cd13/iniciar"))
      .andExpect(status().isOk())
      .andExpect(jsonPath("csosnIcmsList[*].id").exists())
      .andExpect(jsonPath("csosnIcmsList[*].descricao").exists())
      .andExpect(jsonPath("csosnIcmsList[*].possuiAliquotaCredito").exists())
      .andExpect(jsonPath("cstCofinsList[*].id").exists())
      .andExpect(jsonPath("cstCofinsList[*].descricao").exists())
      .andExpect(jsonPath("cstCofinsList[*].tipo").exists())
      .andExpect(jsonPath("cstIcmsList[*].id").exists())
      .andExpect(jsonPath("cstIcmsList[*].descricao").exists())
      .andExpect(jsonPath("cstIpiList[*].id").exists())
      .andExpect(jsonPath("cstIpiList[*].descricao").exists())
      .andExpect(jsonPath("cstIpiList[*].tipo").exists())
      .andExpect(jsonPath("regimeTributarioList[*].id").exists())
      .andExpect(jsonPath("regimeTributarioList[*].nome").exists())
      .andExpect(jsonPath("regimeTributarioList[*].situacao").exists())
      .andExpect(jsonPath("tipoDespesaList[*].id").exists())
      .andExpect(jsonPath("tipoDespesaList[*].nome").exists())
      .andExpect(jsonPath("tipoDespesaList[*].descricao").exists())
      .andExpect(jsonPath("tipoDestinacaoList").exists());

  }

  @Nested
  class DestinacaoCompraMultiempresaTest {

    @Test
    @DisplayName("Quando fizer um GET All e filtrar deve verificar se a destinacao de compra possui duas empresas cadastradas")
    void testVerificaSeOMultiempresaRetornaDuasEmpresasNaMesmaContaBancaria() throws Exception {
      var destinacaoCompraEmpresa1 = destinacaoCompraBuilder.destinacaoCompraDto();
      var destinacaoCompraEmpresa2 = destinacaoCompraBuilder.destinacaoCompraDto();

      destinacaoCompraEmpresa2.getEmpresas().add(2);

      var idDestinacaoEmpresa1 = mvc.perform(post("/cadastro/cd13/destinacao-compra/", destinacaoCompraEmpresa1))
        .andReturn().getResponse().getHeader("X-ID");
      var idDestinacaoEmpresa2 = mvc.perform(post("/cadastro/cd13/destinacao-compra/", destinacaoCompraEmpresa2))
        .andReturn().getResponse().getHeader("X-ID");

      assert idDestinacaoEmpresa1 != null;
      assert idDestinacaoEmpresa2 != null;

      mvc.perform(get("/cadastro/cd13/destinacao-compra/"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$[*].id", hasItem(Integer.valueOf(idDestinacaoEmpresa1))))
        .andExpect(jsonPath("$[*].id", hasItem(Integer.valueOf(idDestinacaoEmpresa2))))
        .andExpect(jsonPath(String.format("$[?(@.id == %s)].empresasNomeFantasia[0]", idDestinacaoEmpresa2),hasItem("Nuvy")))
        .andExpect(jsonPath(String.format("$[?(@.id == %s)].empresasNomeFantasia[1]", idDestinacaoEmpresa2),hasItem("Nuvy 2")));


    }

  }


}