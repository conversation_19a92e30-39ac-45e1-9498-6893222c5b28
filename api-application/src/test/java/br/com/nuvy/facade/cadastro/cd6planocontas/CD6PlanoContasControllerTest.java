package br.com.nuvy.facade.cadastro.cd6planocontas;

import static br.com.nuvy.facade.cadastro.cd6planocontas.PlanoDeContasEndpoint.ALTERAR_SITUACAO_PLANO_CONTAS;
import static br.com.nuvy.facade.cadastro.cd6planocontas.PlanoDeContasEndpoint.CADASTRAR_PLANO_CONTAS;
import static br.com.nuvy.facade.cadastro.cd6planocontas.PlanoDeContasEndpoint.DELETAR_PLANO_CONTAS;
import static br.com.nuvy.facade.cadastro.cd6planocontas.PlanoDeContasEndpoint.EDITAR_PLANO_CONTAS;
import static br.com.nuvy.facade.cadastro.cd6planocontas.PlanoDeContasEndpoint.PESQUISAR_PLANO_CONTAS;
import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import br.com.nuvy.NuvyApiTestBase;
import br.com.nuvy.api.enums.Situacao;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;

@RequiredArgsConstructor
class CD6PlanoContasControllerTest extends NuvyApiTestBase {

  @Autowired
  private MockMvc mvc;

  private PlanoContaBuilderTest planoContaBuilderTest = new PlanoContaBuilderTest();


  @Test
  @DisplayName("Deve retornar status 201 quando fizer um POST de plano de contas")
  void testPostPlanoDeContas() throws Exception {
    mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoContaBuilderTest.planoContaDto()))
      .andExpect(status().isCreated());

  }

  @Test
  @DisplayName("Deve retornar status 200 quando fizer um GET All de plano de contas")
  void testGetPlanoDeContas() throws Exception {
    var planoConta = planoContaBuilderTest.planoContaDto();
    mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoConta));
    mvc.perform(get(PESQUISAR_PLANO_CONTAS))
      .andExpect(status().isOk());

  }

  @Test
  @DisplayName("Deve retornar status 200 quando fizer um GET Id de plano de contas e verificar retorno dos campos")
  void testGetPlanoDeContasId() throws Exception {
    var planoConta = planoContaBuilderTest.planoContaDto();
    var id = mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoConta)).andReturn().getResponse()
      .getHeader("X-ID");
    mvc.perform(get(PESQUISAR_PLANO_CONTAS + id))
      .andExpect(status().isOk())
      .andExpect(jsonPath("nome", is(planoConta.getNome())))
      .andExpect(jsonPath("situacao", is(planoConta.getSituacao().toString())))
      .andExpect(jsonPath("tipo", is(planoConta.getTipo().toString())))
      .andExpect(jsonPath("padrao", is(planoConta.getPadrao())));

  }

  @Test
  @DisplayName("Deve retornar status 204 quando fizer um DEL de plano de contas")
  void testDelPlanoDeContas() throws Exception {
    var planoConta = planoContaBuilderTest.planoContaDto();
    var id = mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoConta)).andReturn().getResponse()
      .getHeader("X-ID");
    mvc.perform(del(DELETAR_PLANO_CONTAS + id))
      .andExpect(status().isNoContent());
  }

  @Test
  @DisplayName("Deve retornar status 200 quando fizer um PATCH de plano de contas e verificar se o status é inativo")
  void testPatchPlanoDeContas() throws Exception {
    var planoConta = planoContaBuilderTest.planoContaDto();
    var id = mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoConta)).andReturn().getResponse()
      .getHeader("X-ID");
    planoConta.setSituacao(Situacao.INATIVO);
    mvc.perform(patch(ALTERAR_SITUACAO_PLANO_CONTAS + id, planoConta))
      .andExpect(status().isOk());
    mvc.perform(get(PESQUISAR_PLANO_CONTAS + id))
      .andExpect(jsonPath("situacao", is("INATIVO")));
  }

  @Test
  @DisplayName("Deve retornar status 200 quando fizer um PUT de plano de contas e verificar retorno dos campos")
  void testPutPlanoDeContas() throws Exception {
    var planoConta = planoContaBuilderTest.planoContaDto();
    var id = mvc.perform(post(CADASTRAR_PLANO_CONTAS, planoConta)).andReturn().getResponse()
      .getHeader("X-ID");
    var planoContaEditado = planoContaBuilderTest.planoContaDto();
    mvc.perform(put(EDITAR_PLANO_CONTAS + id, planoContaEditado))
      .andExpect(status().isOk());
    mvc.perform(get(PESQUISAR_PLANO_CONTAS + id))
      .andExpect(jsonPath("nome", is(planoContaEditado.getNome())));
  }
}