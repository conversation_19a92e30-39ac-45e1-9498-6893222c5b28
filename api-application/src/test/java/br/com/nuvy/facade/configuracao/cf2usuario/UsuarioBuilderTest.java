package br.com.nuvy.facade.configuracao.cf2usuario;

import br.com.nuvy.api.enums.SituacaoRecebedorNfe;
import br.com.nuvy.api.enums.SituacaoUsuario;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuarioDto;
import com.github.javafaker.Faker;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Locale;

public class UsuarioBuilderTest {

  protected Faker faker = new Faker(new Locale("pt-BR"));


  public UsuarioDto usuarioDto() {
    return UsuarioDto.builder()
      .nome(faker.name().fullName())
      .situacao(SituacaoUsuario.PENDENTE)
      .email(faker.internet().safeEmailAddress())
      .dataNascimento(LocalDate.of(1989, 1, 1))
      .perfilIds(Arrays.asList(999))
      .situacaoRecebedorNfe(SituacaoRecebedorNfe.ATIVO)
      .build();
  }

}
