package br.com.nuvy.facade.portalcontador.pc3fechamentoperiodo;

import br.com.nuvy.facade.portalcontador.pc1contador.dtos.ContadorDto;
import br.com.nuvy.facade.portalcontador.pc3fechamentoperiodo.dtos.FechamentoDtoIn;
import br.com.nuvy.utils.GeraCpfCnpj;
import com.github.javafaker.Faker;

import java.util.Locale;

public class FechamentoBuilder {

  protected Faker faker = new Faker(new Locale("pt-BR"));


  public FechamentoDtoIn fechamentoDtoIn() {
    return FechamentoDtoIn.builder()
      .bloqueio(true)
      .build();
  }

}
