package br.com.nuvy.api.cadastro.controller;

import br.com.nuvy.api.cadastro.dto.VariacaoProdutoResumoDto;
import com.github.javafaker.Faker;
import java.util.Locale;

class VariacaoBuilder {

  protected Faker faker = new Faker(new Locale("pt-BR"));

  public VariacaoProdutoResumoDto variacaoDto() {
    return VariacaoProdutoResumoDto.builder()
      .nome(faker.commerce().material())
      .build();
  }

}
