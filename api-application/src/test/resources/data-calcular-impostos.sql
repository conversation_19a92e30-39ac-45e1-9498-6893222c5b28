INSERT INTO cd_produto
(id_produto, id_aplicacao, cod_produto, cod_ean, descricao, descricao_completa, vl_preco_venda,
 vl_preco_custo_ini, dias_crossdocking, dias_garantia, observacao, profundidade, altura, largura,
 peso_liquido, peso_bruto, marca, modelo, situacao, fci, qtd_min_estoque, qtd_multipla,
 tp_aquisicao, dt_cadastro, dt_alteracao, ind_kit, ind_variacao, id_cest, id_ncm, ind_lote_validade,
 ind_outros_controles, id_origem_mercadoria, id_tp_mercadoria, id_unidade_medida,
 id_categoria_produto, ind_variacao_pai, id_produto_pai_variacao)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', '7661446523', '************',
        'Produto teste variacao 1', 'Produto para teste de variacao', 2000.00, 500.00, 2, 8,
        '<PERSON><PERSON><PERSON> the White', NULL, 70.2100, 5780.6100, 850.4400, 265.0100,
        'Aerodynamic Aluminum Plate', 'Mediocre Steel Knife', 'ATIVO', '', 1000, NULL, 0, NULL,
        NULL, false, true, '0100700', '01', false, false, 1, 1, 64, NULL, false, null);

INSERT INTO cd_pessoa(id_pessoa, id_aplicacao, tp_pessoa, cpf_cnpj, situacao, nome, funcao,
                      telefone, dt_nascimento, foto, tp_ie)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'FISICA', '22734517094', 'ATIVO',
        'Cliente teste pedido', 'Tester', '16999999999', '1989-11-30', 'https://nuvy.com.br/',
        'CONTRIBUINTE');
INSERT INTO cd_pessoa_endereco(id_aplicacao, cep, uf, uf_nome, cidade, cidade_cod, bairro, endereco,
                               numero, tp_endereco, id_pessoa, uf_cod)
VALUES ('c3961619-a92a-49ac-9a13-4827b17bd18a', '89160-932', 'SC', 'SANTA CATARINA', 'RIO DO SUL',
        '4214805', 'JARDIM AMÉRICA', 'RUA DOUTOR GUILHERME GEMBALLA 13', '13', 0, 1, '42');

INSERT INTO cd_regra_icms
  (id_regra_icms, id_aplicacao, nome, situacao)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'Regra ICMS teste pedido', 'ATIVO');

INSERT INTO cd_regra_icms_item
(id_regra_icms_item, id_aplicacao, id_regra_icms,
 cod_csosn_icms, aliquota_interestadual_icms_st, perc_mva_st, aliquota_fcp_st,
 aliquota_interna_icms_st, perc_reducao_bc_st,
 perc_mva_estrangeiro_st, aliquota_fcp_estrangeiro_st, aliquota_icms_estrangeiro_st,
 perc_reducao_bc_estrangeiro_st, cod_csosn_cf,
 cod_csosn_cf_nc, aliquota_fcp_difal_interestadual_cf_nc, aliquota_interna_uf_destino_cf_nc,
 aliquota_interestadual_icms_cf_nc,
 perc_reducao_bc_destino_cf_nc, aliquota_interestadual_icms_estrangeiro_cf_nc, info_complementar,
 situacao, ind_zera_mva_st_cf,
 tp_regra_calculo_cf_nc, ind_calc_difal_st_cf, aliquota_fcp_difal_interestadual_cf,
 aliquota_interna_uf_destino_cf,
 aliquota_interestadual_cf, perc_reducao_bc_destino_cf, aliquota_interestadual_estrangeiro_cf,
 regra_calculo_cf, modalide_bc_cf,
 aliquota_interestadual_icms_st_cf, perc_mva_st_cf, aliquota_fcp_st_cf, aliquota_interna_icms_st_cf,
 perc_reducao_bc_st_cf,
 perc_mva_estrangeiro_st_cf, aliquota_fcp_estrangeiro_st_cf, aliquota_icms_estrangeiro_st_cf,
 perc_reducao_bc_estrangeiro_st_cf,
 modalide_bc, modalide_bc_st, aliquota_fcp, aliquota_icms, aliquota_icms_estrangeiro, cod_cst_icms,
 ind_icms_desonerado,
 motivo_desoneracao, percentual_deferimento, perc_reducao_bc, modalide_bc_st_cf, cod_cst_cf,
 cod_cst_cf_nc, aliquota_fcp_cf,
 aliquota_fcp_cf_nc, aliquota_fcp_estrangeiro_st_cf_nc, aliquota_fcp_st_cf_nc, aliquota_icms_cf,
 aliquota_icms_cf_nc,
 aliquota_icms_estrangeiro_cf, aliquota_icms_estrangeiro_cf_nc, aliquota_icms_estrangeiro_st_cf_nc,
 aliquota_interestadual_icms_st_cf_nc,
 aliquota_interna_icms_st_cf_nc, ind_icms_desonerado_cf, ind_icms_desonerado_cf_nc,
 modalide_bc_cf_nc, modalide_bc_st_cf_nc,
 motivo_desoneracao_cf, motivo_desoneracao_cf_nc, percentual_deferimento_cf,
 percentual_deferimento_cf_nc, perc_mva_estrangeiro_st_cf_nc,
 perc_mva_st_cf_nc, perc_reducao_bc_cf, perc_reducao_bc_cf_nc, perc_reducao_bc_estrangeiro_st_cf_nc,
 perc_reducao_bc_st_cf_nc)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 1,
        '900', 0, 0, 0, 0, 0,
        0, 0, 0, 0, '900',
        '900', 0, 0, 0,
        0, 0, '', 'ATIVO', false,
        'CALCULO_POR_DENTRO', false, 0, 0,
        0, 0, 0, 'CALCULO_POR_DENTRO', null,
        0, 0, 0, 0, 0,
        0, 0, 0, 0,
        null, 'MARGEM_VALOR_AGREGADO', 0, 0, 0, null, false,
        null, 0, 0, 'MARGEM_VALOR_AGREGADO', null, null, 0,
        0, 0, 0, 0, 0,
        0, 0, 0, 0,
        0, false, false, null, 'MARGEM_VALOR_AGREGADO',
        null, null, 0, 0, 0,
        0, 0, 0, 0, 0);

INSERT INTO cd_regra_item_uf
  (id_regra_icms_item, uf)
VALUES (1, 'SP');

INSERT INTO cd_regra_ipi
(id_regra_ipi, id_aplicacao, nome,
 cod_cst_ipi, aliquota_ipi, vl_minimo_ipi, info_complementar, id_origem_mercadoria,
 cod_enquadramento_ipi, situacao, vl_ipi)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'Regra IPI teste pedido',
        '99', 0, 0, '', 1, '999', 'ATIVO', 0);

INSERT INTO cd_regra_pis
(id_regra_pis, id_aplicacao, nome, cod_cst_pis, aliquota, ind_reduz_icms_base_pis,
 info_complementar, situacao, vl_pis)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'Regra PIS teste pedido', '49', 0, false, '',
        'ATIVO', 0);

INSERT INTO cd_regra_cofins
(id_regra_cofins, id_aplicacao, nome, cod_cst_cofins, aliquota_cofins, ind_reduz_icms_base_cofins,
 info_complementar, situacao, vl_cofins)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'Regra PIS teste pedido', '49', 0, false, '',
        'ATIVO', 0);

INSERT INTO cd_nop
(id_nop, id_aplicacao, id_regime_tributario, nome, descricao, situacao, ind_comprado, ind_fabricado,
 ind_gera_cr, ind_baixa_estoque, id_conta_receita,
 id_regra_icms, id_regra_ipi, id_regra_pis, id_regra_cofins)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 2, 'NOP teste pedido', 'NOP teste pedido',
        'ATIVO', true, true, false, false, 999,
        1, 1, 1, 1);

INSERT INTO cd_nop_empresa
  (id_empresa, id_nop)
VALUES (1, 1);

INSERT INTO cd_nop_cfop (id_nop_cfop, id_aplicacao, id_nop, cod_cfop, cod_cfop_st,
                         cod_cfop_cf_contrib,
                         cod_cfop_cf_contrib_st, cod_cfop_nao_conttrib, situacao, tp_aquisicao,
                         tp_operacao)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 1, '5102', '5102', '5102',
        '5102', '5102', 'ATIVO', 'COMPRADO', 'INTERNA');
INSERT INTO cd_nop_cfop (id_nop_cfop, id_aplicacao, id_nop, cod_cfop, cod_cfop_st,
                         cod_cfop_cf_contrib,
                         cod_cfop_cf_contrib_st, cod_cfop_nao_conttrib, situacao, tp_aquisicao,
                         tp_operacao)
VALUES (2, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 1, '6102', '6102', '6102',
        '6102', '6102', 'ATIVO', 'COMPRADO', 'INTERESTADUAL');
INSERT INTO cd_nop_cfop (id_nop_cfop, id_aplicacao, id_nop, cod_cfop, cod_cfop_st,
                         cod_cfop_cf_contrib,
                         cod_cfop_cf_contrib_st, cod_cfop_nao_conttrib, situacao, tp_aquisicao,
                         tp_operacao)
VALUES (3, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 1, '5101', '5101', '5101',
        '5101', '5101', 'ATIVO', 'FABRICADO', 'INTERNA');
INSERT INTO cd_nop_cfop (id_nop_cfop, id_aplicacao, id_nop, cod_cfop, cod_cfop_st,
                         cod_cfop_cf_contrib,
                         cod_cfop_cf_contrib_st, cod_cfop_nao_conttrib, situacao, tp_aquisicao,
                         tp_operacao)
VALUES (4, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 1, '6101', '6101', '6101',
        '6101', '6101', 'ATIVO', 'FABRICADO', 'INTERESTADUAL');

INSERT INTO fn_banco_conta
(id_banco_conta, id_aplicacao, nome, vl_limite_credito, agencia, numero_conta, saldo_inicial,
 dt_saldo_inicial, ind_controle, nome_gerente, telefone_gerente, email, ind_integracao_direta,
 situacao, ind_excluido, id_banco, id_tp_banco_conta, ind_configurar_pagamento,
 ind_configurar_boleto, qtd_dias_protesto, id_empresa_boleto, carteira_cobranca, tp_remessa_cnab,
 cod_convenio, modalidade_cobranca, variacao_carteira, cod_transmissao, ind_faixa_numeracao,
 ult_nro_faixa, vl_juros_dia, vl_multa, protesto_negativacao, instrucoes_boleto_1,
 instrucoes_boleto_2, instrucoes_boleto_3, instrucoes_boleto_4, nr_remessa, ind_protesto,
 ultima_remessa, ind_aceite)
VALUES (1, 'c3961619-a92a-49ac-9a13-4827b17bd18a', 'Conta Teste pedido', 0.0000000000, '455666',
        '12123', 1000.00, '2023-04-28', true, 'Gerente 6', '1699999988', '<EMAIL>',
        NULL, 'ATIVO', false, 17, 999, false, false, NULL, NULL, NULL, 'CNAB_240', NULL, NULL, NULL,
        NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, false);

-- INSERT INTO cd_tipo_frete
-- (id_tipo_frete, nm_tipo_frete, tp_tipo_frete, compoe_valor_icms, cod_modalidade_frete_nf)
-- VALUES (6, 'Sem Ocorrência de Transporte', NULL, false, '9');

-- INSERT INTO cd_cond_pagamento (id_cond_pagamento, nome, qtd_parcelas, ordem) VALUES (1, '1 Parcela (à vista)', 0, 1);
