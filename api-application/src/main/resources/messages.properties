# http://programandosemcafeina.blogspot.com/2007/04/caracteres-especiais-representados-em.html
meta.anual-ja-cadastrada=Este ano j\u00e1 possui uma meta definida.
incompatible.passwords=A senha e contra-senha precisam ser iguais.
usuario=Usu\u00e1rio.
vendedor.pessoa.nao.vendedor=Este parceiro precisa ser Vendedor.
fornecedor.pessoa.nao.fornecedor=Este parceiro precisa ser Fornecedor.
expired.token=Token expirado.
impossivel.excluir.usuario.ja.acessou=Este Usu\u00e1rio n\u00e3o pode ser exclu\u00eddo pois j\u00e1 acessou o sistema. Mas voc\u00ea pode alterar seu status.
codigo.empresa.invalido=C\u00f3digo da empresa inv\u00e1lido.
invalid.delete.qualificao=N\u00e3o \u00e9 poss\u00edvel excluir qualifica\u00e7\u00e3o em uso!
deposito.padrao.nao.pode.ser.excluido=O dep\u00f3sito padr\u00e3o n\u00e3o pode ser exclu\u00eddo!
nota.fiscal.padrao.nao.pode.ser.excluido=A Nota Fiscal padr\u00e3o n\u00e3o pode ser exclu\u00edda!
troca.empresa.nao.permitida=Troca de empresa n\u00e3o permitida.
planilha.fora.padrao=O arquivo importado n\u00e3o \u00e9 compat\u00edvel. Utilize o template!
comissao.excecao.vendedor.unique=Vendedor j\u00e1 cont\u00e9m uma exce\u00e7\u00e3o para comiss\u00e3o cadastrada.
troca.tipo.nao.permitida=Troca de Tipo n\u00e3o permitida.
pessoa.fornecedor.vinculada.deposito=Fornecedor n\u00e3o pode ser exclu\u00eddo pois est\u00e1 vinculado a um dep\u00f3sito!
tabela.preco.existente=Nome da tabela de pre\u00e7o j\u00e1 foi usado!
erro.formato.certificado=O arquivo que importou n\u00e3o est\u00e1 no formato correto! Ele precisa estar no formato .PFX e ser do tipo A1 (e-PJ, e-NF-e ou e-CNPJ).
nao.permitido.excluir.movimento=Este movimento n\u00e3o pode ser exclu\u00eddo, pois j\u00e1 foi realizada uma opera\u00e7\u00e3o com este n\u00famero de lote.
nao.permitido.editar.movimento=Este movimento n\u00e3o pode ser editado, pois j\u00e1 foi realizada uma opera\u00e7\u00e3o com este n\u00famero de lote.
nao.permitido.deletar.conta=Esta conta n\u00e3o pode ser exclu\u00edda, pois possui movimenta\u00e7\u00f5es, mas voc\u00ea pode inativ\u00e1-la!
deposito.inativo.nao.deve.alterar=Dep\u00f3sito inativado pelo backoffice, n\u00e3o pode ser editado.
empresa.inativa.nao.deve.alterar=Empresa est\u00e1 inativada pelo backoffice, n\u00e3o pode ser editada.
deposito.inativo.nao.deve.deletar=Dep\u00f3sito inativado pelo backoffice, n\u00e3o pode ser exclu\u00eddo.
empresa.inativa.nao.deve.deletar=Empresa Inativa n\u00e3o pode ser exclu\u00edda
certificado.digital.obrigatorio=Certificado digital \u00e9 obrigat\u00f3rio.
numero.documento.numerico=Utilize apenas n\u00fameros.
situacao.titulo.impedido.alterar=Esta situa\u00e7\u00e3o n\u00e3o pode ser alterada.
cliente.pessoa.nao.cliente=Este parceiro precisa ser Cliente.
cliente.pessoa.sem.endereco=Este cliente precisa ter um endere\u00e7o cadastrado.
pedido.faturado.nao.deve.alterar.status=Este pedido j\u00e1 foi faturado e n\u00e3o pode retornar para as etapas anteriores.
quantidade.item.maior.que.quantidade.deposito=Estoque insuficiente! A quantidade de itens \u00e9 maior que a quantidade dispon\u00edvel em estoque.
depositos.nao.deve.estar.vazio=Informe o dep\u00f3sito.
faturar.pedido=Faturar pedido!
senha.atual.incompativel=A senha n\u00e3o confere!
tipo.arquivo.nao.zip=O arquivo para upload deve estar no formato .ZIP.
veiculo.nao.encontrado=Ve\u00edculo n\u00e3o encontrado.
tipo.carroceria.nao.encontrado=Tipo de carroceria n\u00e3o encontrado.
tipo.veiculo.nao.encontrado=Tipo de ve\u00edculo n\u00e3o encontrado.
nota.ja.importada=Nota fiscal j\u00e1 importada!
comissao.nao.pode.ser.deletada.pois.existe.excecao.vinculada=Comiss\u00e3o n\u00e3o pode ser deletada pois existe exce\u00e7\u00e3o vinculada!
vendedor.nao.encontrado=Vendedor n\u00e3o encontrado.
comissao.padrao.nao.cadastrada=Comiss\u00e3o padr\u00e3o n\u00e3o cadastrada.
apenas.uma.comissao.padrao.permitida.no.sistema=Apenas uma comiss\u00e3o padr\u00e3o \u00e8 permitida.
empresa.faturamento.mensal.invalido=Informe o faturamento dos \u00faltimos 12 meses de forma decrescente.
empresa.nao.optante.simples.nacional=Empresa n\u00e3o se enquadra no Simples Nacional.
fn_movto_banco_id_banco_conta_fkey=Esta conta n\u00e3o pode ser exclu\u00edda, pois j\u00e1 foi realizada movimenta\u00e7\u00f5es.
fn_titulo_id_banco_conta_fkey=Esta conta n\u00e3o pode ser exclu\u00edda, pois j\u00e1 foi realizada movimenta\u00e7\u00f5es.
plano.conta.sistema.excluir=Este plano de conta n\u00e3o pode ser exclu\u00eddo, pois \u00e9 um plano de conta do sistema.
plano.conta.sistema.editar=Este plano de conta n\u00e3o pode ser editado, pois \u00e9 um plano de conta do sistema.
ccusto_cetegoria_unique=O nome da Categoria j\u00e1 est\u00e1 cadastrado no sistema.
ccusto_grupo_unique=O nome do Grupo j\u00e1 est\u00e1 cadastrado no sistema.
plano_conta_cetegoria_unique=O nome da Categoria j\u00e1 est\u00e1 cadastrado no sistema.
plano_conta_grupo_unique=O nome do Grupo j\u00e1 est\u00e1 cadastrado no sistema.
empresa.simples.nacional.sublimite=Aten\u00e7\u00e3o! Voc\u00ea entrou no excesso de sublimite do Simples Nacional, altere o seu regime tribut\u00e1rio.
endereco.fiscal.obrigatorio=O endere\u00e7o fiscal e cobran\u00e7a \u00e9 obrigat\u00f3rio.
endereco.entrega.obrigatorio=O endere\u00e7o de entrega \u00e9 obrigat\u00f3rio.
titulo.ja.esta.com.status.de.pago=Titulo j\u00e1 est\u00e1 com o status de pago.
meta.geral.vendedores=Meta geral menor que a meta dos vendedores.
percentual.maior=Distribui\u00e7\u00e3o total de {0} maior que 100%.
canal.venda.nao.encontrado=Canal de venda n\u00e3o encontrado.
meta.geral.canal.de.vendas=Meta Geral menor que a soma dos canais de venda existentes.
meta.geral.inexistente=Meta Geral inexistente.
meta.geral.menor.que.meta.do.canal=Meta Geral menor que meta estipulada para o canal.
meta.geral.menor.que.meta.do.vendedor=Meta Geral menor que meta estipulada para o vendedor.
validation.error=Erro de valida\u00e7\u00e3o
atleastone.notnull=Pelo menos um dos campos deve ser informado
erro.ao.processar.a.planilha=Erro ao processar a planilha.
meta.vendedor.existente=Vendedor j\u00e1 possui meta cadastrada.
meta.geral.menor.que.meta.dos.vendedores=Meta Geral menor que meta atribuida aos vendedores.
meta.canal.existente=Canal j\u00e1 possui meta cadastrada para o ano.
meta.geral.menor.que.meta.dos.canais=Meta Geral menor que meta dos canais.
produto_codigo_unique=O c\u00f3digo do produto j\u00e1 est\u00e1 cadastrado no sistema.
formato.invalido=Formato inv\u00e1lido
pelo.menos.uma.relacao.comercial.necessario=Pelo menos uma rela\u00e7\u00e3o comercial \u00e9 necess\u00e1ria.
apenas.numeros.sao.aceito=Apenas n\u00fameros s\u00e3o aceito
existe.algum.campo.obrigatorio=Existe algum campo obrigat\u00f3rio que n\u00e3o foi preenchido que o sistema n\u00e3o consegue identificar
imagem.muito.grande=O tamanho da imagem {0} \u00e9 muito grande para ser processada. O m\u00e1ximo permitido \u00e9 de 10MB.
ultrapassou.quantidade.fotos=Foi ultrapassado o m\u00e1ximo de fotos permitida. O m\u00e1ximo permitido \u00e9 de 6 fotos.
endereco.entrega.unico=N\u00e3o \u00e9 poss\u00edvel cadastrar o endere\u00e7o de entrega quando a op\u00e7\u00e3o endere\u00e7o \u00fanico \u00e9 habilitado.
arquivo.invalido=Formato de arquivo inv\u00e1lido.
categoria.nao.pode.deletar=Aten\u00e7\u00e3o! Voc\u00ea n\u00e3o pode excluir este grupo, pois existem produtos vinculados a uma das categorias. Primeiro voc\u00ea deve remover os v\u00ednculos!
categorias.iguais=Existe mais de uma categoria com o mesmo nome {0}.
categoria.pai.nao.pode.deletar=Voc\u00ea n\u00e3o pode excluir esta categoria, pois ela \u00e9 uma categoria pai.
nao.pode.cadastrar.plano.conta=N\u00e3o \u00e9 poss\u00edvel cadastrar uma categoria para este grupo!
campo.maximo.caracteres=Campo m\u00e1ximo de {0} caracteres.
valor.informado.invalido=Valor informado inv\u00e1lido.
perfil.nao.pode.ser.excluido=Perfil n\u00e3o pode ser exclu\u00eddo, pois est\u00e1 vinculado com um ou mais usu\u00e1rios.
usuario.nao.pode.ser.removido=Este Usu\u00e1rio n\u00e3o pode ser exclu\u00eddo pois j\u00e1 est\u00e1 vinculado com um ou mais perfil(s) o sistema.
empresa.nao.encontrada=Empresa n\u00e3o encontrada.
modulo.nao.encontrado=MÃ³dulo n\u00e3o encontrado.
aplicacao.expirada=Aplica\u00e7\u00e3o expirada
aplicacao.inativa=Aplica\u00e7\u00e3o inativa
produto.contem.variacao=N\u00e3o \u00e9 poss\u00edvel excluir um produto que possui varia\u00e7\u00f5es.
produto.eh.variacao=N\u00e3o \u00e9 poss\u00edvel excluir/atualizar um produto que seja uma varia\u00e7\u00e3o.
notificacao.mark.received=Notifica\u00e7\u00e3o j\u00e1 est\u00e1 marcada como recebida.
notificacao.mark.read=Notifica\u00e7\u00e3o j\u00e1 est\u00e1 marcada como lida.
notificacao.mark.received.and.read=NotificaÃ§Ã£o j\u00e1 est\u00e1 marcada como recebida/lida.
soma.percentuais.ultrapassou.limite=A soma dos percentuais ultrapassou o limite de {0}%
soma.percentuais.nao.ultrapassou.limite=A soma dos percentuais n\u00e3o ultrapassou o limite de {0}%
pedido.nao.pode.conter.produto.pai=N\u00e3o \u00e9 poss\u00edvel vincular um produto que cont\u00e9m grade a um item de pedido, vincule uma das varia\u00e7\u00f5es deste produto.
sistema.configuracao.pix=O sistema tem configura\u00e7\u00e3o pix apenas para o banco Ita\u00fa
invalid.token.expiration=Token inv\u00e1lido ou expirado.
invalid.password=Senha fora do padr\u00e3o.
comissao.deve.ser.maior.que.zero=A porcentagem da comiss\u00e3o deve ser maior que zero.
comissao.deve.ser.positiva=A porcentagem da comiss\u00e3o deve ser positiva.
remessa.com.titulos.quitados=Esta remessa n\u00e3o pode ser cancelada, pois tem lan\u00e7amentos quitados.
remessa.ja.cancelada=Esta remessa j\u00e1 est\u00e1 cancelada.
qualificacao.nao.encontrada=Qualifica\u00e7\u00e3o n\u00e3o cadastrada no sistema.
empresa.inativa.nao.pode.atualizar.deposito=N\u00e3o \u00e9 permitido editar dep\u00f3sito de uma empresa inativa.
empresa.inativa.nao.pode.cadastrar.deposito=N\u00e3o \u00e9 permitido cadastrar dep\u00f3sito para uma empresa inativa.
deposito.padrao.nao.pode.ser.alterado=Dep\u00f3sito padr\u00e3o n\u00e3o pode ser editado.
empresa.contatos.required=N\u00e3o \u00e9 poss\u00edvel realizar a opera\u00e7\u00e3o, pois \u00e9 obrigat\u00f3rio existir ao menos um contato.
empresa.contatos.delete.invalid=Contato n\u00e3o pode ser exclu\u00eddo, pois \u00e9 obrigat\u00f3rio existir ao menos um contato.
nao.permitido.realizar.tranferencia.mesma.conta=N\u00e3o \u00e9 poss\u00edvel realizar transfer\u00eancia para a mesma conta
nao.pode.excluir.registro.com.vinculo=Opera\u00e7\u00e3o n\u00e3o pode ser realizada, existem diverg\u00eancias nas entidades de relacionamento.
cfopnopitem.cfopcodigo.nao.deve.estar.vazio=Informe o c\u00f3digo cfop.
produto.id.nao.encontrado=Produto n\u00e3o foi encontrado.
cliente.id.nao.encontrado=Cliente n\u00e3o foi encontrado.
cliente.com.endereco.vazio=N\u00e3o foi encontrado endere\u00e7os para este Cliente.
cliente.tipo-ie.required=O cliente precisa ter o campo Indicador de IE informado.
nop.id.nao.encontrado=NOP n\u00e3o foi encontrado.
regra.ipi.nao.encontrada=Regra IPI n\u00e3o foi encontrada.
regra.cofins.nao.encontrada=Regra COFINS n\u00e3o foi encontrada.
regra.pis.nao.encontrada=Regra PIS n\u00e3o foi encontrada.
regra.icms.uf.nao.cadastrada=Regra ICMS para a UF {0} não foi encontrada.
regra.icms.nao.encontrada=Regra ICMS n\u00e3o foi encontrada.
cfop.nop.required=Informe ao menos um cfop.
pessoa_cpf_cnpj_unique=CPF/CNPJ j\u00e1 cadastrado.
regra.nao.faz.parte.da.nop=Essa Regra n\u00e3o faz parte na NOP.
movimentacao.nao.encontrada=Movimenta\u00e7\u00e3o bancaria n\u00e3o encontrada.
titulo.rateio.centro.custo.igual=N\u00e3o pode ser feito rateio entre o mesmo centro de custo.
cd_produto_kit_id_produto_fkey=Produto cont\u00e9m v\u00ednculo com kit
titulo.originado.venda=Este registro n\u00e3o pode ser exclu\u00eddo, pois foi criado a partir de uma venda.
titulo.originado.venda.atualizar=Este registro n\u00e3o pode ser atualizado, pois foi criado a partir de uma venda.
titulo.existe.em.cnab.item=Este registro n\u00e3o pode ser exclu\u00eddo, pois existe titulo vinculado a cnab.
busca.sem.resultados.validos=Busca realizada com os filtros n\u00e3o retornou nenhum resultado v\u00e1lido.
excecao_regra_pis_produto_unique=Produto n\u00e3o pode ser cadastrado em duplicidade para mesma regra fiscal.
excecao_regra_pis_ncm_unique=Origem da mercadoria n\u00e3o pode ser cadastrada em duplicidade para mesma regra fiscal.
excecao_regra_ipi_produto_unique=Produto n\u00e3o pode ser cadastrado em duplicidade para mesma regra fiscal.
excecao_regra_ipi_ncm_unique=Origem da mercadoria n\u00e3o pode ser cadastrada em duplicidade para mesma regra fiscal.
excecao_regra_cofins_ncm_unique=Origem da mercadoria n\u00e3o pode ser cadastrada em duplicidade para mesma regra fiscal.
excecao_regra_cofins_produto_unique=Produto n\u00e3o pode ser cadastrado em duplicidade para mesma regra fiscal.
excecao_regra_icms_item_produto_unique=Produto n\u00e3o pode ser cadastrado em duplicidade para mesma regra fiscal.
excecao_regra_icms_item_ncm_unique=NCM n\u00e3o pode ser cadastrado em duplicidade para mesma regra fiscal.
cd_regra_icms_item_excecao_id_produto_fkey=Produto n\u00e3o pode ser exclu\u00eddo pois est\u00e1 vinculado a uma exce\u00e7\u00e3o de regra fiscal.
cd_produto_grade_id_produto_pai_fkey=Produto n\u00e3o pode ser exclu\u00eddo pois est\u00e1 vinculado a uma ou mais varia\u00e7\u00f5es.
parceiro.vinculo.outras.operacoes=N\u00e3o \u00e9 poss\u00edvel excluir esse parceiro, pois esta vinculado a outras opera\u00e7\u00f5es no sistema!
valor.total.nao.pode.ser.nulo=O campo valor total n\u00e3o pode ser nulo.
insc_estadual_uf_empresa_unique=A inscri\u00e7\u00e3o estadual j\u00e1 est\u00e1 em uso.
historico.inexistente=Hist\u00f3rico do t\u00edtulo n\u00e3o encontrado.
registro.sem.vinculo.com.anexos=Registro sem arquivos anexados.
nop.nao.pode.ser.excluida.existem.pedidos.vinculados=Nop n\u00e3o pode ser exclu\u00edda pois existem pedidos vinculados.
usuario.ja.ativo=S\u00f3 \u00e9 poss\u00edvel excluir usu\u00e1rios com situa\u00e7\u00e3o pendente!
impossivel.inativar.usuario.logado=Imposs\u00edvel inativar usu\u00e1rio logado!
operacao.nao.suportada=Opera\u00e7\u00e3o n\u00e3o suportada.
estoque.nao.permite.estoque.negativo=O estoque selecionado n\u00e3o permite estoque negativo!
titulo.contem.nota.recebida=Aten\u00e7\u00e3o! Esta nota n\u00e3o pode ser exclu\u00edda pois foi importada a partir de uma nota fiscal. Voc\u00ea deve reverter o recebimento desta nota!
titulo.contem.nota.emitida=Aten\u00e7\u00e3o! Esta nota n\u00e3o pode ser exclu\u00edda pois tem nota fiscal emitida. Voc\u00ea deve realizar o cancelamento da nota fiscal emitida!
produto.vinculo.outras.operacoes=N\u00e3o \u00e9 poss\u00edvel excluir esse produto, pois est\u00e1 vinculado a outras opera\u00e7\u00f5es no sistema!
limite.credito.insuficiente=Limite de cr\u00e9dito insuficiente para realizar a transfer\u00eancia
pedido.deve.ser.faturado=Aten\u00e7\u00e3o! Esse pedido ainda n\u00e3o foi faturado.
pedido.deve.ser.enviado=Aten\u00e7\u00e3o! Esse pedido ainda n\u00e3o foi enviado.
pedido.consumidor-final.required=O pedido precisa ter o campo Consumidor final informado.
saldo.insuficiente=Saldo insuficiente
certificado.nao.pertence.empresa=Certificado n\u00e3o pertence a essa matriz ou filial.
empresa.contem.certificado=Empresa j\u00e1 possui certificado digital cadastrado.
usuario.nao.encontrado=Usu\u00e1rio n\u00e3o encontrado.
titulo.pago.cem.porcento=O t\u00edtulo j\u00e1 foi pago em sua totalidade.
data.final.deve.ser.futura=A data de validade deve ser maior que a data atual.
data.nascimento.nao.deve.ser.futura=A data de nascimento não deve ser uma data futura.
perfil.sistema.nao.pode.ser.alterado=N\u00e3o \u00e9 poss\u00edvel alterar perfis padr\u00f5es do sistema.
perfil.sistema.nao.pode.ser.excluido=N\u00e3o \u00e9 poss\u00edvel excluir perfis padr\u00f5es do sistema.
percentual.menor=Distribui\u00e7\u00e3o total de {0} menor que 100%.
titulo.pago.ou.recebido=O t\u00edtulo j\u00e1 foi {0}.
valor.baixa.maior.que.valor.total=O Valor da baixa n\u00e3o pode ser maior que o valor restante.
anexo.inexistente=Anexo do Hist\u00f3rico n\u00e3o encontrado.
cd_pessoa_qualificacao_pessoa_id_qualificacao_pessoa_fkey=N\u00E3o \u00E9 poss\u00EDvel deletar uma qualifica\u00E7\u00E3o que est\u00E1 em uso no sistema!
numero.lote.ja.cadastrado=N\u00famero de lote j\u00e1 cadastrado
nao.permitido.deletar.conta.com.titulo=Esta conta n\u00e3o pode ser exclu\u00edda, pois possui conta a pagar/receber, mas voc\u00ea pode inativ\u00e1-la!
nao.possivel.excluir.centro.custo=N\u00e4o \u00e9 poss\u00edvel excluir este centro de custo, pois esta vinculado a outras opera\u00e7\u00f5es no sistema!
variacao.produto.invalida=N\u00e3o foi poss\u00edvel criar as varia\u00e7\u00f5es, pois existe mais produto que varia\u00e7\u00e3o.
produto.variacao.operacao.invalida=Produto varia\u00e7\u00e3o n\u00e3o pode ser gravado pela mesma rotina que produto pai.
campo.required=O campo {0} \u00e9 obrigat\u00f3rio!
tenant.callface.nao.contratado=Voc\u00ea n\u00e4o possui um plano para liga\u00e7\u00f5es ativo.
aplicacao.callface.ja.ativa=Empresa j\u00e1 possui callface ativado.
vd_pedido_item_id_tab_preco_fkey=N\u00e3o \u00e9 poss\u00edvel excluir a tabela de pre\u00e7o pois ela est\u00e1 vinculada a item de pedido. Voc\u00ea pode inativ\u00e1-la!
vd_pedido_id_tab_preco_fkey=N\u00e3o \u00e9 poss\u00edvel excluir a tabela de pre\u00e7o pois ela est\u00e1 vinculada a um pedido, mas voc\u00ea pode inativ\u00e1-la!
cd_produto_kit_id_tab_preco_fkey=N\u00e3o \u00e9 poss\u00edvel excluir a tabela de pre\u00e7o pois ela est\u00e1 vinculada a um kit de produto, mas voc\u00ea pode inativ\u00e1-la!
conta.contem.baixa=Esta conta n\u00e3o pode ser exclu\u00edda pois possui baixa e/ou j\u00e1 foi conciliada!
ambiente.nota.fiscal.ja.cadastrado=N\u00e3o \u00e9 poss\u00edvel cadastrar mais de um ambiente de nota fiscal!
orcamento.emails.duplicados=N\u00e3o \u00e9 poss\u00edvel enviar para o mesmo e-mail mais de uma vez. Analise os e-mails informados!
orcamento.emails.obrigatorio=Informe pelo menos um e-mail para enviar o or\u00e7amento!
invalid.fci=FCI fora do padr\u00e3o.
invalid.codigo.barras=C\u00f3digo EAN fora do padr\u00e3o.
invalid.license.plate=Placa veiculo fora do padr\u00e3o.
invalid.state.registration=Inscri\u00e7\u00e3o Estadual fora do padr\u00e3o.
invalid.phone=Telefone fora do padr\u00e3o.
not.numeric.string=String deve conter somente n\u00fameros.
required.tipo.operacao=Tipo opera\u00e7\u00e3o \u00e9 obrigat\u00f3rio em Pedido.
required.numero.nota.fiscal=N\u00famero nota fiscal \u00e9 obrigat\u00f3rio em Pedido.
required.serie.nota.fiscal=Serie nota fiscal \u00e9 obrigat\u00f3rio em Pedido.
mva.deve.ser.nulo=Para a Modalidade Base Calculo {0}, o mva deve estar vazio.
codigo.municipio.diferente={0} difere do CEP informado!
pedido.situacao.igual=N\u00e3o \u00e9 poss\u00edvel alterar a situa\u00e7\u00e3o do pedido, pois a situa\u00e7\u00e3o nova \u00e9 igual a atual.
pedido.sem.itens=N\u00e3o \u00e9 poss\u00edvel alterar a situa\u00e7\u00e3o do pedido sem adicionar pelo menos um item.
pedido.nfe.sem.notafiscal=N\u00e3o foi poss\u00edvel encontrar numero da nota fiscal junto ao pedido.
data.vencimento.maior.que.emissao=Data de vencimento n\u00e3o pode ser anterior a data de emiss\u00e3o
pedido.nao.encontrado=N\u00e3o foi poss\u00edvel encontrar o pedido!
nao.existe.carta.correcao.pendente.pedido=N\u00e3o existe carta de corre\u00e7\u00e3o pendente para este pedido!
existe.carta.correcao.pendente.pedido=Existe carta de corre\u00e7\u00e3o pendente para este pedido!
produto.nao.existe=N\u00e3o existe produto com o c\u00f3digo informado!
regra.nao.pode.ser.excluida.pois.existe.excecao=Essa regra n\u00e3o pode ser exclu\u00edda pois possui uma exce\u00e7\u00e3o.
conta.bancaria.nao.encontrada=Conta banc\u00e1ria n\u00e3o encontrada!
baixa.titulo.nao.encontrado=Baixa do t\u00edtulo n\u00e3o encontrada!
titulo.nao.encontrado=T\u00edtulo n\u00e3o encontrado!
sistema.titulo.conciliado=Esta conta j\u00e1 foi conciliada!, para editar voc\u00ea deve reverter a concilia\u00e7\u00e3o ou excluir.
vinculo.categoria.ja.cadastrado=J\u00e1 existe um v\u00ednculo para esta categoria!
produto.variacao.existente=J\u00e1 existe uma varia\u00e7\u00e3o com esses dados.
carta.correcao.descricao=Carta de corre\u00e7\u00e3o precisa esta entre 15 a 1000 caracteres.
pedido.notafiscal.reenvio.invalido=N\u00e3o foi poss\u00edvel reenviar nota fiscal do pedido com situa\u00e7\u00e3o({0}).
produto.kit.quantidade.minima.um=Quando informado mais de um produto, a quantidade m\u00ednima deve ser 1.
produto.kit.quantidade.minima.dois=Quando informado apenas um produto, a quantidade m\u00ednima deve ser 2.
senha.atual.igual.nova=A senha j\u00e1 foi utilizada anteriormente. Utilize outra senha.
ncm.nao.encontrado=Ncm {0} n�o encontrado.
nao.possivel.remover.rastreabilidade=V\u00ednculo com rastreabilidade n\u00e3o pode ser removido, pois o lote est\u00e1 sendo utilizado em outras opera\u00e7\u00f5es.
eq_saldo_id_rastreabilidade_fkey=Lote est\u00e1 sendo utilizado em outras opera\u00e7\u00f5es.
lote.em.vinculo.com.produto=Este lote j\u00e1 est\u00e1 vinculado a outro produto!
saldo.insuficiente.deposito=Estoque insuficiente! A quantidade informada para produto({0}) \u00e9 maior que a quantidade dispon\u00edvel em estoque. Verifique o(s) dep\u00f3sito(s) e tente novamente.
deposito.reserva.estoque.invalido=Existem pedido(s) sem faturar para este dep\u00f3sito. Fature e depois ative a reserva.
eq_movto_estoque_id_deposito_fkey=Este dep\u00f3sito n\u00e3o ser exclu\u00eddo. H\u00e1 movimenta\u00e7\u00e3o de estoque para este dep\u00f3sito.
deposito.invalid.venda=Dep\u00f3sito({0}) n\u00e3o esta configurado para venda. Realiza a configura\u00e7\u00e3o do mesmo e tente novamente.
deposito.estoque.negativo.invalido=N\u00e3o \u00e9 poss\u00edvel alterar o dep\u00f3sito devido \u00e0 exist\u00eancia de um ou mais pedidos configurados para permitir estoque negativo.
deposito.pedido.em.aberto.invalido=N\u00e3o \u00e9 poss\u00edvel alterar o dep\u00f3sito devido \u00e0 exist\u00eancia de um ou mais pedidos em aberto para mesmo. Fature o(s) pedido(s) e tente novamente.
invalid.delete.variacao=N\u00e3o \u00e9 poss\u00edvel excluir varia\u00e7\u00e3o em uso!
invalid.delete.variacao.atributo=N\u00e3o \u00e9 poss\u00edvel excluir atributo de varia\u00e7\u00e3o em uso!
pedido.nao.pode.ser.alterado=O pedido j\u00e1 faturado n\u00e3o pode ser alterado!
pedido.faturamento.invalido=Ainda faltam preencher alguns campos para poder faturar.
pedido.faturado.alteracao.invalida=Esse pedido j\u00e1 foi faturado!
pedido.item.configuracao.deposito.invalida=N\u00e3o foi informada nenhuma configura\u00e7\u00e3o de dep\u00f3sito para o item {0}. Informe o dep\u00f3sito e tente novamente.
pedido.item.configuracao.deposito.quantidade.invalida=Quantidade informada na configura\u00e7\u00e3o de dep\u00f3sito para item {0} \u00e9 inv\u00e1lida. Verifique e tente novamente.
aviso.nao.encontrado=Aviso n\u00e3o encontrado.
amazon.s3.url.incorreta=Url informada para gerar o acesso \u00e9 incorreta.
ambiente.emissao.required=Ambiente de emiss\u00e3o n\u00e3o encontrado.
catalogo.nao.encontrado=Cat\u00e1logo n\u00e3o encontrado.
nf.rejeitada.ou.emitida=Nota fiscal {0} somente pode ser cancelada.
cancelamento.de.pedido.possui.baixas=N\u00e3o \u00e9 poss\u00edvel cancelar pois existem baixas de parcelas.
nao.e.possivel.editar.movimentacao.automatica=Esta movimenta\u00e7\u00e3o de estoque n\u00e3o pode ser editada pois foi originada de um pedido.
nao.e.possivel.excluir.movimentacao.automatica=Esta movimenta\u00e7\u00e3o de estoque n\u00e3o pode ser exclu\u00edda pois foi originada de um pedido.
transmitir.nota.pedido.normal=Nota fiscal de pedido de venda deve ser transmitida pelo m\u00f3dulo de vendas.
pedido.contem.contas.baixadas=Esse pedido j\u00e1 possui contas baixadas.
pedido.de.venda.nao.encontrado=N\u00e3o foi poss\u00edvel encontrar o pedido de venda!
recurso.nao.encontrado=Recurso n\u00e3o encontrado.
data.final.maior.que.data.atual=Data final n\u00e3o pode ser maior que a data atual.
data.inicial.maior.que.data.final=Data inicial n\u00e3o pode ser maior que a data final.
nota.recebida.nao.encontrada=Nota recebida n\u00e3o encontrada.
certificado.empresa.nao.encontrado=N\u00e3o foi poss\u00edvel encontrar nenhum certificado para esta empresa.
certificado.vencido=Certificado A1 vencido. Atualize o certificado no sistema!
pedido.inutilizacao.warning=A nota fiscal desse pedido j\u00e1 foi inutilizada. N\u00e3o \u00e9 poss\u00edvel alterar a situa\u00e7\u00e3o.
item.nota.nao.encontrado=Item da nota recebida n\u00e3o encontrado.
estoque.item.nao.encontrado=Estoque do item n\u00e3o encontrado.
destinacao.compra.cfop.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de CFOP.
destinacao.compra.cstpis.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de PIS CST.
destinacao.compra.cstcofins.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de COFINS CST.
destinacao.compra.csosnicms.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de ICMS CSOSN.
destinacao.compra.csticms.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de ICMS CST.
destinacao.compra.cstipi.origem.duplicado=J\u00e1 existe uma destina\u00e7\u00e3o de compra com esse codigo de origem de IPI CST.
nota.recebida.conferida=Nota recebida j\u00e1 foi conferida.
dados.pagamento.faltantes=Existem dados obrigat\u00f3rios para o pagamento que n\u00e3o foram informados.
dados.itens.nao.conferidos=Existem itens que n\u00e3o foram conferidos.
produto.nao.relacionado.ao.item=Item {0} n\u00e3o est\u00e1 relacionado a nenhum produto.
item.nao.foi.distribuido.para.estoque=Item {0} n\u00e3o foi distribu\u00eddo para nenhum estoque.
arquivo.deve.ser.nfe=Importe somente o XML de NF-e (DANFE).
nota.nao.pertence.empresa=A NF-e importada n\u00e3o \u00e9 para o CNPJ desta empresa. Deseja importar mesmo assim?
nota.venda.nao.pode.importar.como.compra=NF-e de Venda n\u00e3o pode ser importada como uma Compra.
nota.recebida.nao.pode.ser.excluida=N\u00e3o \u00e9 poss\u00edvel excluir a nota recebida pois ela j\u00e1 foi conferida.
nao.pode.excluir.pessoa.com.vinculo=Este parceiro n\u00e3o pode ser exclu\u00eddo, pois est\u00e1 sendo utilizado em outras opera\u00e7\u00f5es!
nao.pode.excluir.produto.com.vinculo=Este produto n\u00e3o pode ser exclu\u00eddo, pois est\u00e1 sendo utilizado em outras opera\u00e7\u00f5es!
nop.cfop.required=\u00c9 necess\u00e1rio informar o CFOP na NOP {0}.
nop.cfop.venda.required=\u00c9 necess\u00e1rio informar o CFOP de venda de {0} na NOP {1} na aba Opera\u00e7\u00f5es {2}.
nop.cfop.st.required=\u00c9 necess\u00e1rio informar o CFOP quando houver ST de {0} na NOP {1} na aba Opera\u00e7\u00f5es {2}.
nop.cfop.consumidor.st.required=\u00c9 necess\u00e1rio informar o CFOP consumidor final contribuinte quando houver ST de {0} na NOP {1} na aba Opera\u00e7\u00f5es {2}.
nop.cfop.consumidor.contribuinte.required=\u00c9 necess\u00e1rio informar o CFOP consumidor final contribuinte de {0} na NOP {1} na aba Opera\u00e7\u00f5es {2}.
nop.cfop.consumidor.nao.contribuinte.required=\u00c9 necess\u00e1rio informar o CFOP consumidor final n\u00e3o contribuinte de {0} na NOP {1} na aba Opera\u00e7\u00f5es {2}.
nop.icms.required=\u00c9 necess\u00e1rio informar o ICMS na NOP {0}.
nop.ipi.required=\u00c9 necess\u00e1rio informar o IPI na NOP {0}.
nop.pis.required=\u00c9 necess\u00e1rio informar o PIS na NOP {0}.
nop.cofins.required=\u00c9 necess\u00e1rio informar o COFINS na NOP {0}.
tipo.operacao.obrigatorio=Tipo de opera\u00e7\u00e3o \u00e9 obrigat\u00f3rio.
nota.fiscal.ja.manifestada=Nota fiscal j\u00e1 foi manifestada.
nota.fiscal.cnpj.empresa.diferente=Voc\u00ea n\u00e3o pode manifestar uma nota fiscal de outra empresa!
todos.itens.devolvidos=Todos os itens j\u00e1  foram devolvidos. N\u00e3o \u00e9 poss\u00edvel criar um novo pedido de devolu\u00e7\u00e3o.
nenhum.pedido.valido.encontrado=Nenhum {0} v\u00e1lido encontrado.
contador.nao.encontrado=Contador n\u00e3o encontrado!
fechamento.contabil.nao.encontrado=Fechamento cont\u00e1bil n\u00e3o encontrado!
fechamento.contabil.em.processamento=Existe um fechamento cont\u00e1bil em processamento. Aguarde a finaliza\u00e7\u00e3o do processamento para realizar um novo fechamento.
boleto.deve.ser.cancelado.exclusao.titulo=Essa conta a receber possui um boleto gerado, o mesmo deve ser cancelado antes da exclus\u00e3o da conta a receber.
nenhum.arquivo.disponivel.para.gerar=Nenhum dos arquivos selecionados est\u00e1 dispon\u00edvel para gerar!
nota.fiscal.em.processamento.sefaz=Nota fiscal em processamento no SEFAZ.
banco.nao.encontrado=Banco n\u00e3o encontrado.
agencia.e.conta.nao.encontrado=N\u00e3o foram encontradas conta banc\u00e1ria com esse n\u00famero de ag\u00eancia e conta cadastradas no sistema.
attribute.format.error=O atributo {1} com o conte\u00fado {0} n\u00e3o est\u00e1 no formato correto.
valor.numerico.muito.grande=Valor num\u00e9rico excede o limite do banco de dados.
catalogo.em.uso=Cat\u00e1logo n\u00e3o pode ser inativado pois est\u00e1 vinculado a um ou mais marketplaces.
endereco.nao.encontrado=Endere\u00e7o n\u00e3o encontrado.
pedido.data.vencimento.menor.data.faturamento=A data do vencimento da parcela deve ser igual ou maior que a data do faturamento.
titulo.originado.ordem.servico.excluir=Este registro n\u00e3o pode ser exclu\u00eddo, pois foi criado a partir de uma ordem de servi\u00e7o.
titulo.originado.ordem.servico.duplicar=Este registro n\u00e3o pode ser duplicado, pois foi criado a partir de uma ordem de servi\u00e7o.
titulo.originado.ordem.servico.atualizar=Este registro n\u00e3o pode ser atualizado, pois foi criado a partir de uma ordem de servi\u00e7o.
fechamento.contabil.bloqueado=O per\u00edodo solicitado est\u00e1 bloqueado no Portal do Contador!
nota.devolucao.nao.pode.ser.deletada=Nota de devolu\u00e7\u00e3o s\u00f3 pode ser exclu\u00edda se estiver com a situa\u00e7\u00e3o pendente!
pedido.origem.canal.venda.padrao=Esse pedido foi criado a partir de canal de venda padr\u00e3o.
produto.kit.nao.encontrado=Produto kit n\u00e3o encontrado.
nota.recebida.sem.itens=Nota recebida n\u00e3o possui itens.
forma.pagamento.nao.configurada=Forma de pagamento da devolu\u00e7\u00e3o n\u00e3o configurada.
forma.pagamento.nao.encontrada=Forma de pagamento n\u00e3o encontrada.
conta.bancaria.nao.configurada=Conta banc\u00e1ria da devolu\u00e7\u00e3o n\u00e3o configurada.
produto.nao.vinculado=Existem itens que n\u00e3o est\u00e3o vinculados a nenhum produto.
cfop.nao.configurado=CFOP da devolu\u00e7\u00e3o n\u00e3o configurado.
nao.existem.itens.a.devolver=N\u00e3o existem itens que possam ser devolvidos.
nota.deve.ser.entrada=Nota Fiscal a ser devolvida deve ser do tipo 'Entrada'.
nao.pode.gerar.devolucao.empresa.diferente=Voc\u00ea n\u00e3o pode gerar uma devolu\u00e7\u00e3o de uma nota emitida para outra empresa!
endereco.sem.codigo.cidade=O endere\u00e7o do pedido est\u00e1 sem o c\u00f3digo de IBGE da cidade do endere\u00e7o.
empresa.mandante.token.invalid=Token NFe est\u00e1 inv\u00e1lido!
empresa.id.nao.encontrado=Empresa {0} não encontrada.
empresa.regime.tributario.required=O regime tributário da empresa {0} não foi informado.
modelo.dre.padrao.excluir=N\u00e3o \u00e9 poss\u00edvel excluir o modelo padr\u00e3o de DRE.
modelo.dre.padrao.alterar=N\u00e3o \u00e9 poss\u00edvel alterar o modelo padr\u00e3o de DRE.
deposito.nao.encontrado=Dep\u00f3sito de id {0} n\u00e3o encontrado.
pos.pdv.id.integracao.nao.encontrada=Id {0} de integra\u00e7\u00e3o com o POS PDV n\u00e3o encontrado.
pos.pdv.nao.encontradada.para.empresa=POS PDV n\u00e3o encontrado para a empresa {0}.
pos.pdv.integracao.inativa=Integra\u00e7\u00e3o com o POS PDV est\u00e1 inativa.
pedido.numero.item.cliente.numerico=O n\u00famero do item do cliente deve ser num\u00e9rico.
pedido.verificar.detalhes.erro.historico=Verifique os detalhes do erro no hist\u00f3rico do pedido.
conta.bancaria.nao.informada=Conta banc\u00e1ria n\u00e3o informada.
conta.bancaria.nao.encontrada.empresa=Conta banc\u00e1ria n\u00e3o encontrada para a empresa informada.
pos.pdv.id.nao.encontrado=Id {0} do POS PDV n\u00e3o encontrado.
nop.nao.encontrado.empresa=NOP informada n\u00e3o foi encontrada para a empresa.
deposito.nao.encontrado.empresa=Dep\u00f3sito informado n\u00e3o foi encontrado para a empresa.
jornada.nao.encontrada=Jornada n\u00e3o encontrada.
jornada.nao.permite.inativar=Jornada n\u00e3o permite inativa\u00e7\u00e3o.
baixa.data.maior.atual=Data da baixa n\u00e3o pode ser maior que a data atual.
empresa.id.nao.encontrada=Empresa {0} n\u00e3o encontrada.
nota.devolucao.nao.pode.ser.cancelada=Nota de devolu\u00e7\u00e3o s\u00f3 pode ser cancelada se estiver emitida ou rejeitada!
nao.e.possivel.excluir.remessa.titulo.baixado=N\u00e3o \u00e9 poss\u00edvel excluir uma remessa de um t\u00edtulo baixado.
numero.inicial.nota.maior.zero=N\u00famero inicial deve ser maior que zero.
numero.final.nota.maior.zero=N\u00famero final deve ser maior que zero.
numero.final.nota.maior.igual.numero.inicial=N\u00famero final deve ser maior ou igual ao n\u00famero inicial.
inutilizacao.numeros.ainda.nao.utilizados=Voc\u00ea n\u00e3o pode inutilizar essa faixa pois existem n\u00fameros ainda n\u00e3o utilizados.
inutilizacao.numeros.utilizados=Voc\u00ea n\u00e3o pode inutilizar essa faixa, pois existe n\u00famero de nota fiscal entre essa faixa.
inutilizacao.nota.somente.rejeitada=Voc\u00ea n\u00e3o pode inutilizar essa nota, pois ela n\u00e3o est\u00e1 rejeitada.
inutilizacao.nao.encontrada=Inutiliza\u00e7\u00e3o n\u00e3o encontrada.
saldo.estoque.nao.encontrado=Saldo de estoque n\u00e3o encontrado.
enviando.email.para.retry=Enviando e-mail para nova tentativa;
categoria.produto.nao.encontrada=Categoria do produto n\u00e3o encontrada.
variacao.produto.nao.encontrada=Varia\u00e7\u00e3o do produto n\u00e3o encontrada.
valor.variacao.nao.encontrado=Valor da varia\u00e7\u00e3o n\u00e3o encontrado.
endereco.nao.encontrado.base.dados=Endere\u00e7o n\u00e3o encontrado na base de dados.
pos.nao.pode.alterar.senha=Senha n\u00e3o pode ser alterada para integra\u00e7\u00e3o POS CONTROLE.
pos.nao.pode.alterar.usuario=Usu\u00e1rio n\u00e3o pode ser alterado para integra\u00e7\u00e3o POS CONTROLE.
soma.parcelas.difere.total.pedido=Soma das parcelas difere do total do pedido!
nenhuma.ordem.servico.informada=Nenhuma ordem de servi\u00e7o informada.
deposito.nao.pertence.empresa=Dep\u00f3sito n\u00e3o pertence a empresa informada.
nfs.erro.buscar.pessoa=Erro ao buscar pessoa! Tente novamente mais tarde!
cnpj.invalido=CNPJ inv\u00e1lido.
empresa.cnpj.alterado.exluir.certificado=Antes de alterar o CNPJ voc\u00ea precisa excluir o Certificado A1 cadastrado.
certificado.erro.leitura.arquivo=Erro ao ler arquivo do certificado.
certificado.senha.incorreta=Senha do certificado inv\u00e1lida.
pedido.email.nota.fiscal.obrigatorio=O e-mail para envio da nota fiscal \u00e9 obrigat\u00f3rio.
condicao.pagamento.nao.encontrada=Condi\u00e7\u00e3o de pagamento n\u00e3o encontrada.
nome.interno.obrigatorio=Nome interno \u00e9 obrigat\u00f3rio.
nop.nao.pode.selecionar.opcoes=N\u00E3o \u00E9 poss\u00EDvel selecionar op\u00E7\u00F5es em 'Pedido' e 'Emiss\u00E3o de NF-e' simultaneamente. Por favor, desmarque as op\u00E7\u00F5es em uma das se\u00E7\u00F5es para continuar.
nop.deve.selecionar.opcoes=Selecione uma op\u00E7\u00E3o em 'Pedido' ou 'Emiss\u00E3o de NF-e' para continuar.
nota.fiscal.entrada.tipo.despesa.obrigatorio=Tipo de despesa \u00e9 obrigat\u00f3rio.
nota.fiscal.entrada.parcelas.obrigatorio=Parcelas \u00e9 obrigat\u00f3rio.
nota.fiscal.entrada.forma.pagamento.obrigatorio=Forma de pagamento \u00e9 obrigat\u00f3rio.
nota.fiscal.entrada.conta.bancaria.obrigatorio=Conta banc\u00e1ria \u00e9 obrigat\u00f3rio.
nota.fiscal.entrada.condicao.pagamento.obrigatorio=Condi\u00e7\u00e3o de pagamento \u00e9 obrigat\u00f3rio.
nota.fiscal.entrada.destinacao.obrigatorio=Destina\u00e7\u00e3o \u00e9 obrigat\u00f3rio.
configuracao.boleto.fornecedor.cpf.cnpj.obrigatorio=CPF/CNPJ \u00e9 obrigat\u00f3rio.
quantidade.nao.informada=Quantidade n\u00e3o informada.
categoria.pai.nao.pode.alterar=Voc\u00ea n\u00e3o pode vincular produtos na categoria pai.

