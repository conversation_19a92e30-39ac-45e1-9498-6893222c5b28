package br.com.nuvy.integracao.plug4market.aplicacao.processador;

import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.integracao.plug4market.dominio.utilitarios.Plug4marketUtils.getExpiracaoToken;
import static br.com.nuvy.integracao.plug4market.dominio.utilitarios.Plug4marketUtils.isTokenValido;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import br.com.nuvy.api.anuncio.CategoriaAnuncio;
import br.com.nuvy.api.cadastro.model.CategoriaProduto;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.ProdutoVariacaoProduto;
import br.com.nuvy.api.cadastro.repository.ProdutoRepository;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.api.estoque.service.SaldoService;
import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.api.venda.model.CanalVendaPlug4Market;
import br.com.nuvy.api.venda.model.CanalVendaProduto;
import br.com.nuvy.api.venda.repository.CanalVendaPlug4marketRepository;
import br.com.nuvy.api.venda.repository.CanalVendaProdutoRepository;
import br.com.nuvy.api.venda.repository.CanalVendaRepository;
import br.com.nuvy.api.venda.repository.CategoriaAnuncioRepository;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.AtualizaTokenAcessoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnfileiraProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnviaProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.ExtraiTokenAcessoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.SincronizaProdutosPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.porta.saida.rest.ClienteRestPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.ProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.RetornoTokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.TokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.evento.ProdutoRecebidoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.EntidadeNaoEncontradaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.ErroComunicacaoPlug4market;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Stream;
import br.com.nuvy.multitenent.TenantContext;
import lombok.RequiredArgsConstructor;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionTemplate;

@Service
@RequiredArgsConstructor
class ProcessadorPlug4market implements
  ProcessadorAutenticacaoPlug4market,
  ProcessadorProdutoPlug4market {

  private static final Logger log = LoggerFactory.getLogger(ProcessadorPlug4market.class);

  private final TransactionTemplate transacao;
  private final ApplicationEventPublisher eventPublisher;

  private final ClienteRestPlug4market clienteRest;

  private final SaldoService saldoService;

  private final ProdutoRepository repositorioProduto;
  private final CanalVendaRepository repositorioCanalVenda;
  private final CanalVendaProdutoRepository repositorioCanalVendaProduto;
  private final CanalVendaPlug4marketRepository repositorioToken;
  private final CategoriaAnuncioRepository repositorioMapeamentoCategoria;

  @Override
  public void processaComando(ExtraiTokenAcessoPlug4market comando) {
    repositorioToken.findAllByEmpresaId(comando.getIdEmpresa())
      .forEach((CanalVendaPlug4Market token) -> {
        try {
          token.setExpiraEm(getExpiracaoToken(token.getAccessToken()));
          token.setUpdatedAt(LocalDateTime.now());
          repositorioToken.saveAndFlush(token);
        } catch (Exception ex) {
          log.error(
            "Plug4market - Erro ao extrair token de acesso da empresa {}.",
            comando.getIdEmpresa(), ex);
        }
      });
  }

  @Override
  public void processaComando(AtualizaTokenAcessoPlug4market comando) {
    repositorioToken.findAllByEmpresaId(comando.getIdEmpresa())
      .forEach((CanalVendaPlug4Market token) -> {
        TokenPlug4market entidade = new TokenPlug4market(token);
        try {
          RetornoTokenPlug4market retorno = clienteRest.postAtualizacaoToken(entidade);
          if (retorno != null) {
            token.setAccessToken(retorno.getTokenAcesso());
            token.setRefreshToken(retorno.getTokenAtualizacao());
            token.setUpdatedAt(LocalDateTime.now());
            token.setExpiraEm(getExpiracaoToken(token.getAccessToken()));
            repositorioToken.saveAndFlush(token);
          }
        } catch (ErroComunicacaoPlug4market ex) {
          log.error(
            "Plug4market - Erro ao atualizar token de acesso da empresa {}.",
            comando.getIdEmpresa(), ex);
        }
      });
  }

  @Override
  public void processaComando(EnfileiraProdutoPlug4market comando) {
    int configs = repositorioCanalVenda.coundPlug4Market(comando.getIdProduto());
    if (configs == 0) {
      log.debug(
        "Plug4market - Produto não tem canal de venda configurado ou não está no catalogo!");
      return;
    }
    eventPublisher.publishEvent(new ProdutoRecebidoPlug4market(
      UUID.randomUUID(), comando.getIdAplicacao(), comando.getIdEmpresa(), comando.getIdUsuario(),
      comando.getIdProduto(), comando.getEmitidoAs()
    ));
  }

  @Override
  public void processaComando(EnviaProdutoPlug4market comando) {
    List<CanalVendaProduto> canaisCadastradosAplicacao
      = repositorioCanalVendaProduto.findAllByProdutoId(comando.getIdProduto());

    List<CanalVenda> canaisAplicacao = repositorioCanalVenda.findAllPlug4Market(
      comando.getIdProduto()
    );
    if (canaisAplicacao.isEmpty() && canaisCadastradosAplicacao.isEmpty()) {
      log.debug(
        "Plug4market - Produto não tem canal de venda configurado ou não está no catalogo!");
      return;
    }

    Map<Empresa, List<CanalVenda>> empresaCanais = canaisAplicacao.stream()
      .collect(groupingBy(CanalVenda::getEmpresa, collectingAndThen(toList(),
        (List<CanalVenda> canais) -> canais.stream()
          .sorted(comparing(CanalVenda::getId))
          .toList()
      )));
    Map<Empresa, List<CanalVenda>> empresaCanaisCadastrados = canaisCadastradosAplicacao.stream()
      .collect(groupingBy(CanalVendaProduto::getEmpresa, mapping(CanalVendaProduto::getCanalVenda,
        collectingAndThen(toList(), (List<CanalVenda> canais) -> canais.stream()
          .sorted(comparing(CanalVenda::getId))
          .toList()))));

    Set<Empresa> empresas = new HashSet<>(empresaCanais.keySet());
    empresas.addAll(empresaCanaisCadastrados.keySet());

    Map<Integer, Optional<CanalVendaPlug4Market>> tokenCache = new HashMap<>();

    List<ProdutoPlug4market> produtos = getOrElse(transacao.execute((TransactionStatus s) ->
      repositorioProduto.findProdutoPlug4marketById(comando.getIdProduto()).stream()
        .flatMap((Produto p) -> {
          Hibernate.initialize(p.getVariacoes());
          if (p.isVariacaoPai()) {
            return p.getProdutosVariacao().stream().map((Produto pv) -> {
              Hibernate.initialize(pv.getVariacoes());
              pv.getVariacoes().forEach((ProdutoVariacaoProduto pvp) -> {
                Hibernate.initialize(pvp.getVariacaoProdutoValor());
              });
              return new ProdutoPlug4market(p, pv, p.getCategoriaProduto());
            });
          }
          return Stream.of(new ProdutoPlug4market(null, p, p.getCategoriaProduto()));
        }).toList()),
      List.of());

    if (produtos.isEmpty()) {
      log.warn("Plug4market - Produto não encontrado pelo id: {}", comando.getIdProduto());
      return;
    }

    produtos.forEach((ProdutoPlug4market produtoPlug4market) -> {
      Produto produto = produtoPlug4market.getProduto();
      CategoriaProduto categoriaProduto = produtoPlug4market.getCategoriaErp();

      if (categoriaProduto == null) {
        log.debug(
          "Plug4market - Produto {} - {} não tem categoria informada!",
          produto.getCodigo(), produto.getDescricao());
        return;
      }

      empresas.forEach((Empresa empresa) -> {
        Optional<CanalVendaPlug4Market> token = tokenCache.computeIfAbsent(
          empresa.getId(),
          (Integer empresaId) -> repositorioToken.findAllByEmpresaId(empresaId).stream()
            .filter((CanalVendaPlug4Market t) -> isTokenValido(t.getAccessToken()))
            .findFirst());
        if (token.isEmpty()) {
          log.debug(
            "Plug4market - Token expirado ou não cadastrado para empresa {}-{}!",
            empresa.getId(), empresa.getNome());
          return;
        }

        List<CanalVenda> canaisEmpresa = empresaCanais.getOrDefault(
          empresa, List.of());
        List<CanalVenda> canaisCadastradosEmpresa = empresaCanaisCadastrados.getOrDefault(
          empresa, List.of());

        Optional<CanalVenda> primeiroCanal = canaisEmpresa.stream()
          .filter(Objects::nonNull)
          .findFirst().or(() -> canaisCadastradosEmpresa.stream()
            .filter(Objects::nonNull)
            .findFirst());
        if (primeiroCanal.isEmpty()) {
          log.debug(
            "Plug4market - Canal de venda não configurado para empresa {} - {}!",
            empresa.getId(), empresa.getNome());
          return;
        }
        Optional<CategoriaAnuncio> categoriaAnuncio
          = repositorioMapeamentoCategoria.findByCanalVendaIdAndCategoriaErpId(
          primeiroCanal.get().getId(), categoriaProduto.getId());
        if (categoriaAnuncio.isEmpty()) {
          log.debug(
            "Plug4market - Categoria {} - {} não mapeada para o produto: {} - {}",
            categoriaProduto.getId(), categoriaProduto.getNome(),
            produto.getCodigo(), produto.getDescricao());
          return;
        }
        produtoPlug4market.setCategoriaPlug4market(categoriaAnuncio.get());

        Optional<Deposito> deposito = canaisEmpresa.stream()
          .map(CanalVenda::getDeposito).filter(Objects::nonNull)
          .findFirst().or(() -> canaisCadastradosEmpresa.stream()
            .map(CanalVenda::getDeposito).filter(Objects::nonNull)
            .findFirst());
        if (deposito.isEmpty()) {
          log.debug(
            "Plug4market - Depósito não configurado para empresa {} - {}!",
            empresa.getId(), empresa.getNome());
          return;
        }
        BigDecimal estoque = saldoService.calculaEstoque(produto, deposito.get());
        produtoPlug4market.setEstoque(estoque);

        List<ProdutoPlug4market.CanalVendaPlug4market> canaisVenda = canaisEmpresa.stream()
          .map((CanalVenda cv) -> new ProdutoPlug4market.CanalVendaPlug4market(
            cv.getOrigem(), produto, cv.getTabelaPreco()))
          .toList();
        produtoPlug4market.setCanais(canaisVenda);

        List<String> restricoesEnvio = produtoPlug4market.getRestricoesEnvio();
        if (!restricoesEnvio.isEmpty()) {
          log.debug(
            "Plug4market - Produto {} - {} não pode ser importado! Restrições: {}",
            produto.getCodigo(), produto.getDescricao(), restricoesEnvio);
          return;
        }

        produtoPlug4market.setAtivo(
          Situacao.ATIVO.equals(produto.getSituacao())
            && !canaisEmpresa.isEmpty());

        if (canaisCadastradosEmpresa.isEmpty()) {
          try {
            criarProduto(
              comando, empresa,
              canaisCadastradosEmpresa, canaisEmpresa,
              token.get(), produtoPlug4market
            );
          } catch (ErroComunicacaoPlug4market ex) {
            log.error(
              "Plug4market - Erro ao criar produto {} - {}!",
              produto.getCodigo(), produto.getDescricao(), ex);
          }
        } else {
          try {
            alterarProduto(
              comando, empresa,
              canaisCadastradosEmpresa, canaisEmpresa,
              token.get(), produtoPlug4market
            );
          } catch (EntidadeNaoEncontradaPlug4market ex) {
            log.debug(
              "Plug4market - Produto {} - {} não encontrado no Plug4market! criando...",
              produto.getCodigo(), produto.getDescricao());
            try {
              criarProduto(
                comando, empresa,
                canaisCadastradosEmpresa, canaisEmpresa,
                token.get(), produtoPlug4market
              );
            } catch (ErroComunicacaoPlug4market ex1) {
              log.error(
                "Plug4market - Erro ao criar produto {} - {}!",
                produto.getCodigo(), produto.getDescricao(), ex1);
            }
          } catch (ErroComunicacaoPlug4market ex) {
            log.error(
              "Plug4market - Erro ao alterar produto {} - {}!",
              produto.getCodigo(), produto.getDescricao(), ex);
          }
        }
      });
    });
  }

  private void salvarCanalVendaProduto(
    EnviaProdutoPlug4market comando, Empresa empresa,
    List<CanalVenda> jaCadastrados, List<CanalVenda> canaisEmpresa,
    Produto produto
  ) {
    List<CanalVenda> remover = jaCadastrados.stream()
      .filter((CanalVenda cv) -> !canaisEmpresa.contains(cv))
      .toList();
    List<CanalVenda> criar = canaisEmpresa.stream()
      .filter((CanalVenda canalVenda) -> jaCadastrados.stream().noneMatch(
        (CanalVenda cv) -> cv.getId().equals(canalVenda.getId())
      ))
      .toList();
    if (!remover.isEmpty()) {
      repositorioCanalVendaProduto.deleteByProdutoIdAndCanalVendaIn(produto.getId(), remover);
    }
    if (!criar.isEmpty()) {
      repositorioCanalVendaProduto.saveAllAndFlush(criar.stream()
        .map((CanalVenda canalVenda) -> CanalVendaProduto.builder()
          .id(UUID.randomUUID())
          .canalVenda(canalVenda)
          .produto(produto)
          .idExterno(produto.getCodigo())
          .aplicacao(comando.getIdAplicacao().toString())
          .empresa(empresa)
          .createdAt(LocalDateTime.now())
          .build()).toList());
    }
  }

  private void criarProduto(
    EnviaProdutoPlug4market comando, Empresa empresa,
    List<CanalVenda> jaCadastrados, List<CanalVenda> canaisEmpresa,
    CanalVendaPlug4Market token, ProdutoPlug4market produtoPlug4market
  ) throws ErroComunicacaoPlug4market {
    Produto produto = produtoPlug4market.getProduto();
    try {
      if (!Situacao.ATIVO.equals(produto.getSituacao())) {
        log.debug(
          "Plug4market - Produto {} - {} não está ativo, não enviado!",
          produto.getCodigo(), produto.getDescricao());
        return;
      }
      transacao.executeWithoutResult((TransactionStatus s) -> {
        salvarCanalVendaProduto(comando, empresa, jaCadastrados, canaisEmpresa, produto);
        try {
          clienteRest.postProduto(token.getAccessToken(), produtoPlug4market);
        } catch (ErroComunicacaoPlug4market ex) {
          throw new RuntimeException(ex);
        }
      });
    } catch (Exception ex) {
      if (ex.getCause() instanceof ErroComunicacaoPlug4market checkedEx) {
        throw checkedEx;
      } else {
        throw ex;
      }
    }
  }

  private void alterarProduto(
    EnviaProdutoPlug4market comando, Empresa empresa,
    List<CanalVenda> jaCadastrados, List<CanalVenda> canaisEmpresa,
    CanalVendaPlug4Market token, ProdutoPlug4market produtoPlug4market
  ) throws EntidadeNaoEncontradaPlug4market, ErroComunicacaoPlug4market {
    Produto produto = produtoPlug4market.getProduto();
    try {
      transacao.executeWithoutResult((TransactionStatus s) -> {
        salvarCanalVendaProduto(comando, empresa, jaCadastrados, canaisEmpresa, produto);
        try {
          clienteRest.putProduto(token.getAccessToken(), produtoPlug4market);
        } catch (ErroComunicacaoPlug4market | EntidadeNaoEncontradaPlug4market ex) {
          throw new RuntimeException(ex);
        }
      });
    } catch (Exception ex) {
      if (ex.getCause() instanceof ErroComunicacaoPlug4market checkedEx) {
        throw checkedEx;
      } else if (ex.getCause() instanceof EntidadeNaoEncontradaPlug4market checkedEx) {
        throw checkedEx;
      } else {
        throw ex;
      }
    }
  }

  @Override
  public void processaComando(SincronizaProdutosPlug4market comando) {
    Optional<CanalVendaPlug4Market> token
      = repositorioToken.findAllByEmpresaId(comando.getIdEmpresa()).stream()
      .filter((CanalVendaPlug4Market t) -> isTokenValido(t.getAccessToken()))
      .findFirst();
    if (token.isEmpty()) {
      log.warn(
        "Plug4market - Token expirado ou não cadastrado para empresa {}!",
        comando.getIdEmpresa());
      return;
    }

    repositorioProduto.findProdutosSincronizarPlug4market(comando.getIdCanalVenda())
      .forEach((Integer idProduto) -> eventPublisher.publishEvent(
        new ProdutoRecebidoPlug4market(
          UUID.randomUUID(),
          comando.getIdAplicacao(), comando.getIdEmpresa(), comando.getIdUsuario(),
          idProduto, comando.getEmitidoAs()
        )));
  }

//  @Bean
//  ApplicationListener<ApplicationReadyEvent> testExtraiTokenAcesso(
//    ProcessadorAutenticacaoPlug4market processadorAutenticacao
//  ) {
//    return (ApplicationReadyEvent event) -> {
//      TenantContext.setTenant("f65d852e-c8f1-475b-b6ba-41d1cdc7d764");
//      BadgeContext.generateBadgeContext("f65d852e-c8f1-475b-b6ba-41d1cdc7d764", 5);
//      processadorAutenticacao.processaComando(new ExtraiTokenAcessoPlug4market(
//        UUID.randomUUID(),
//        UUID.fromString("f65d852e-c8f1-475b-b6ba-41d1cdc7d764"),
//        5,
//        null,
//        LocalDateTime.now()
//      ));
//    };
//  }

//  @Bean
//  ApplicationListener<ApplicationReadyEvent> testAtualizaTokenAcesso(
//    ProcessadorAutenticacaoPlug4market processadorAutenticacao
//  ) {
//    return (ApplicationReadyEvent event) -> {
//      TenantContext.setTenant("f65d852e-c8f1-475b-b6ba-41d1cdc7d764");
//      BadgeContext.generateBadgeContext("f65d852e-c8f1-475b-b6ba-41d1cdc7d764", 5);
//      processadorAutenticacao.processaComando(new AtualizaTokenAcessoPlug4market(
//        UUID.randomUUID(),
//        UUID.fromString("f65d852e-c8f1-475b-b6ba-41d1cdc7d764"),
//        5,
//        null,
//        LocalDateTime.now()
//      ));
//    };
//  }

//  @Bean
//  ApplicationListener<ApplicationReadyEvent> testEnfileiraProduto(
//    ProcessadorProdutoPlug4market processadorProduto
//  ) {
//    return (ApplicationReadyEvent event) -> {
//      TenantContext.setTenant("f65d852e-c8f1-475b-b6ba-41d1cdc7d764");
//      BadgeContext.generateBadgeContext("f65d852e-c8f1-475b-b6ba-41d1cdc7d764", 5);
//      List.of(1659, 3292).forEach((Integer idProduto) -> {
//        processadorProduto.processaComando(new EnfileiraProdutoPlug4market(
//          UUID.randomUUID(),
//          UUID.fromString("f65d852e-c8f1-475b-b6ba-41d1cdc7d764"),
//          5,
//          UUID.fromString("92812bf5-c023-4f5c-be89-ba7a6bc99867"),
//          idProduto,
//          LocalDateTime.now()
//        ));
//      });
//    };
//  }

//  @Bean
//  ApplicationListener<ApplicationReadyEvent> testSincronizaProdutos(
//    ProcessadorProdutoPlug4market processadorProduto
//  ) {
//    return (ApplicationReadyEvent event) -> {
//      TenantContext.setTenant("f65d852e-c8f1-475b-b6ba-41d1cdc7d764");
//      BadgeContext.generateBadgeContext("f65d852e-c8f1-475b-b6ba-41d1cdc7d764", 5);
//      processadorProduto.processaComando(new SincronizaProdutosPlug4market(
//        UUID.randomUUID(),
//        UUID.fromString("f65d852e-c8f1-475b-b6ba-41d1cdc7d764"),
//        5,
//        UUID.fromString("92812bf5-c023-4f5c-be89-ba7a6bc99867"),
//        1511,
//        LocalDateTime.now()
//      ));
//    };
//  }
}