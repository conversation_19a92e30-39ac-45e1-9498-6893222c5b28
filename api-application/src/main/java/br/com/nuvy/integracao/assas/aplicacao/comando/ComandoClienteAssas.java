package br.com.nuvy.integracao.assas.aplicacao.comando;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class ComandoClienteAssas extends ComandoAssas {

  private final Integer idCliente;

  protected ComandoClienteAssas(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCliente,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, emitidoAs);
    this.idCliente = idCliente;
  }
}
