package br.com.nuvy.integracao.plug4market.aplicacao.comando;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EnviaCategoriaPlug4market extends ComandoCategoriaPlug4market {

  public EnviaCategoriaPlug4market(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCategoria,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCategoria, emitidoAs);
  }
}
