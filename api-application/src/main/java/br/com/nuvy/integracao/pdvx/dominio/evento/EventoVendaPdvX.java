package br.com.nuvy.integracao.pdvx.dominio.evento;

import br.com.nuvy.base.NuvyErpEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class EventoVendaPdvX extends NuvyErpEvent {

  private final String idVenda;
  private final String jsonVenda;

  protected EventoVendaPdvX(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, String idVenda, String jsonVenda,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, ocorridoAs);
    this.idVenda = idVenda;
    this.jsonVenda = jsonVenda;
  }
}
