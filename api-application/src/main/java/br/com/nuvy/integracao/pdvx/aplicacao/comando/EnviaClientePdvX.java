package br.com.nuvy.integracao.pdvx.aplicacao.comando;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EnviaClientePdvX extends ComandoClientePdvX {

  public EnviaClientePdvX(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCliente,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCliente, emitidoAs);
  }
}
