package br.com.nuvy.integracao.plug4market.dominio.entidade;

import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.integracao.plug4market.dominio.utilitarios.Plug4marketUtils.converteUnidadeMedidaPlug4market;

import br.com.nuvy.api.anuncio.CategoriaAnuncio;
import br.com.nuvy.api.cadastro.model.CategoriaProduto;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.model.OrigemMercadoria;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.UnidadeMedida;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.venda.model.CanalOrigem;
import br.com.nuvy.api.venda.model.TabelaPreco;
import br.com.nuvy.common.utils.StringUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

public class ProdutoPlug4market {

  @Getter
  private final Produto produtoPai;

  @Getter
  private final Produto produto;

  @Getter
  private final CategoriaProduto categoriaErp;

  @Setter
  private CategoriaAnuncio categoriaPlug4market;

  @Setter
  private BigDecimal estoque;

  @Setter
  private List<CanalVendaPlug4market> canais = List.of();

  @Setter
  private Boolean ativo = Boolean.FALSE;

  public ProdutoPlug4market(
    Produto produtoPai, Produto produto,
    CategoriaProduto categoriaErp
  ) {
    this.produtoPai = produtoPai;
    this.produto = produto;
    this.categoriaErp = categoriaErp;
  }

  public String getProdutoId() {
    return produto.getId().toString();
  }

  public String getProdutoNome() {
    return produto.getDescricaoVariacao();
  }

  public String getSku() {
    return produto.getCodigo();
  }

  public String getNome() {
    return produto.getDescricaoVariacao();
  }

  public String getDescricao() {
    return produto.getDescricaoVariacao();
  }

  public String getEan() {
    return StringUtils.getGtinValido(produto.getCodigoEan());
  }

  public String getCategoriaErpId() {
//    return categoriaErp.getId().toString();
    return null;
  }

  public String getCategoriaPlug4marketId() {
    return get(categoriaPlug4market, CategoriaAnuncio::getCategoriaIntegracao);
  }

  public String getMarca() {
    return getOrElse(
      produtoPai, Produto::getMarca,
      produto.getMarca());
  }

  public String getModelo() {
    return getOrElse(
      produtoPai, Produto::getModelo,
      produto.getModelo());
  }

  public BigDecimal getLargura() {
    return getOrElse(
      produtoPai, Produto::getLargura,
      produto.getLargura());
  }

  public BigDecimal getAltura() {
    return getOrElse(
      produtoPai, Produto::getAltura,
      produto.getAltura());
  }

  public BigDecimal getComprimento() {
    return getOrElse(
      produtoPai, Produto::getProfundidade,
      produto.getProfundidade());
  }

  public BigDecimal getPeso() {
    BigDecimal peso = getOrElse(
      produto.getPesoBruto(),
      produto.getPesoLiquido());
    if (peso == null && produtoPai != null) {
      peso = getOrElse(
        produtoPai.getPesoBruto(),
        produtoPai.getPesoLiquido());
    }
    return peso;
  }

  public BigDecimal getPreco() {
    return getOrElse(
      produtoPai, Produto::getPrecoVenda,
      produto.getPrecoVenda());
  }

  public BigDecimal getPrecoCusto() {
    return getOrElse(
      produtoPai, Produto::getValorCustoMedio,
      produto.getValorCustoMedio());
  }

  public String getOrigem() {
    OrigemMercadoria origemMercadoria = getOrElse(
      produtoPai, Produto::getOrigemMercadoria,
      produto.getOrigemMercadoria());
    if (origemMercadoria != null) {
      return origemMercadoria.isEstrangeiroIcms()
        ? "importado" : "nacional";
    }
    return null;
  }

  public Integer getGarantia() {
    return getOrElse(
      get(produtoPai, Produto::getDiasGarantia, (Integer dias) -> dias / 30),
      get(produto.getDiasGarantia(), (Integer dias) -> dias / 30));
  }

  public Integer getDiasCrossDocking() {
    return getOrElse(
      produtoPai, Produto::getDiasCrossdocking,
      produto.getDiasCrossdocking());
  }

  public String getUnidadeMedida() {
    UnidadeMedida um = getOrElse(
      produtoPai, Produto::getUnidadeMedida,
      produto.getUnidadeMedida());
    if (um != null) {
      return converteUnidadeMedidaPlug4market(um);
    }
    return null;
  }

  public String getNcm() {
    return getOrElse(
      get(produtoPai, Produto::getNcm, Ncm::getId),
      get(produto.getNcm(), Ncm::getId));
  }

  public BigDecimal getEstoque() {
    return estoque;
  }

  public Boolean getAtivo() {
    return ativo;
  }

  public List<CanalVendaPlug4market> getCanaisVenda() {
    return canais;
  }

  public String getCor() {
    return null;
  }

  public String getTamanho() {
    return null;
  }

  public String getSabor() {
    return null;
  }

  public String getPotencia() {
    return null;
  }

  public String getVoltagem() {
    return null;
  }

  public String getGenero() {
    return null;
  }

  public Integer getMultiplicadorUnidade() {
    return null;
  }

  public static class CanalVendaPlug4market {

    private final CanalOrigem canalOrigem;
    private final Produto produto;
    private final TabelaPreco tabelaPreco;

    public CanalVendaPlug4market(
      CanalOrigem canalOrigem, Produto produto, TabelaPreco tabelaPreco
    ) {
      this.canalOrigem = canalOrigem;
      this.produto = produto;
      this.tabelaPreco = tabelaPreco;
    }

    public Integer getId() {
      return canalOrigem.getIdIntegrador();
    }

    public BigDecimal getPreco() {
      if (tabelaPreco != null && tabelaPreco.getSituacao() == Situacao.ATIVO) {
        return tabelaPreco.calcularPreco(produto);
      }
      return produto.getPrecoVenda();
    }
  }

  public List<String> getRestricoesEnvio() {
    List<String> restricoes = new ArrayList<>();
    if (getCategoriaPlug4marketId() == null) {
      restricoes.add("Produto não possui categoria!");
    }
    if (getPreco() == null) {
      restricoes.add("Produto não possui preço!");
    }
    if (getAltura() == null) {
      restricoes.add("Produto não possui altura!");
    }
    if (getLargura() == null) {
      restricoes.add("Produto não possui largura!");
    }
    if (getComprimento() == null) {
      restricoes.add("Produto não possui comprimento!");
    }
    if (getPeso() == null) {
      restricoes.add("Produto não possui peso!");
    }
    return restricoes;
  }
}
