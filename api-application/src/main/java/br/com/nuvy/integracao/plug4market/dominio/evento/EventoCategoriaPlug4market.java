package br.com.nuvy.integracao.plug4market.dominio.evento;

import br.com.nuvy.base.NuvyErpEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class EventoCategoriaPlug4market extends NuvyErpEvent {

  private final Integer idCategoria;

  protected EventoCategoriaPlug4market(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCategoria,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, ocorridoAs);
    this.idCategoria = idCategoria;
  }
}
