package br.com.nuvy.integracao.plug4market.aplicacao.porta.saida.rest;

import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.CategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.ProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.TokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.RetornoTokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.ErroComunicacaoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.EntidadeNaoEncontradaPlug4market;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public interface ClienteRestPlug4market {

  ObjectMapper getJsonMapper();

  @Nullable
  RetornoTokenPlug4market postAtualizacaoToken(
    @NonNull TokenPlug4market entidade
  ) throws ErroComunicacaoPlug4market;

  void postProduto(
    @NonNull String token,
    @NonNull ProdutoPlug4market entidade
  ) throws ErroComunicacaoPlug4market;

  void putProduto(
    @NonNull String token,
    @NonNull ProdutoPlug4market entidade
  ) throws ErroComunicacaoPlug4market, EntidadeNaoEncontradaPlug4market;

  void postCategoria(
    @NonNull String token,
    @NonNull CategoriaPlug4market entidade
  ) throws ErroComunicacaoPlug4market;

  void putCategoria(
    @NonNull String token,
    @NonNull CategoriaPlug4market entidade
  ) throws ErroComunicacaoPlug4market, EntidadeNaoEncontradaPlug4market;
}
