package br.com.nuvy.integracao.plug4market.aplicacao.comando;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SincronizaProdutosPlug4market extends ComandoPlug4market {

  private final Integer idCanalVenda;

  public SincronizaProdutosPlug4market(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCanalVenda,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, emitidoAs);
    this.idCanalVenda = idCanalVenda;
  }
}
