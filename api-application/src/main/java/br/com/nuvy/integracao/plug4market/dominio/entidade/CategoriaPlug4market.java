package br.com.nuvy.integracao.plug4market.dominio.entidade;

import br.com.nuvy.api.cadastro.model.CategoriaProduto;
import lombok.Getter;

@Getter
public class CategoriaPlug4market {

  private final CategoriaProduto categoria;

  public CategoriaPlug4market(CategoriaProduto categoria) {
    this.categoria = categoria;
  }

  public String getCategoriaId() {
    return categoria.getId().toString();
  }

  public String getNome() {
    return categoria.getNome();
  }

  public String getAlternativeId() {
    return categoria.getId().toString();
  }

  public String getFatherAlternativeId() {
    if (categoria.getCategoriaPai() != null) {
      return categoria.getCategoriaPai().getId().toString();
    }
    return null;
  }

  public boolean isRaiz() {
    return categoria.getCategoriaPai() == null;
  }
}
