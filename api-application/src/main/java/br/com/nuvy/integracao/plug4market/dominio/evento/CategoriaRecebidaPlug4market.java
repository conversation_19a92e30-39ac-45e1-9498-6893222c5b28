package br.com.nuvy.integracao.plug4market.dominio.evento;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CategoriaRecebidaPlug4market extends EventoCategoriaPlug4market {

  public CategoriaRecebidaPlug4market(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCategoria,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCategoria, ocorridoAs);
  }
}
