package br.com.nuvy.integracao.assas.aplicacao.comando;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EnfileiraEnvioBoletoAssas extends ComandoBoletoAssas {

  public EnfileiraEnvioBoletoAssas(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idTitulo,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idTitulo, emitidoAs);
  }
}
