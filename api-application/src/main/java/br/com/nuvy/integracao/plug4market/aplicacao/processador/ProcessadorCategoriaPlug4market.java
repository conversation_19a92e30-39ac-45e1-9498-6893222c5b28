package br.com.nuvy.integracao.plug4market.aplicacao.processador;

import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnfileiraCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnviaCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.SincronizaCategoriasPlug4market;

public interface ProcessadorCategoriaPlug4market {

  void processaComando(EnfileiraCategoriaPlug4market comando);

  void processaComando(EnviaCategoriaPlug4market comando);

  void processaComando(SincronizaCategoriasPlug4market comando);
}
