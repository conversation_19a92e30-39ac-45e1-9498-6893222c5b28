package br.com.nuvy.integracao.pdvx.aplicacao.processador;

import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnfileiraEstoquePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnfileiraProdutoPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaEstoquePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaProdutoPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.SincronizaEstoqueProdutoPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.SincronizaProdutosPdvX;

public interface ProcessadorProdutoPdvX {

  void processaComando(EnfileiraProdutoPdvX comando);

  void processaComando(EnviaProdutoPdvX comando);

  void processaComando(SincronizaProdutosPdvX comando);

  void processaComando(SincronizaEstoqueProdutoPdvX comando);

  void processaComando(EnfileiraEstoquePdvX comando);

  void processaComando(EnviaEstoquePdvX comando);
}
