package br.com.nuvy.integracao.pdvx.aplicacao.comando;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EnviaVendedorPdvX extends ComandoVendedorPdvX {

  private final boolean paraInclusao;

  public EnviaVendedorPdvX(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idVendedor,
    boolean paraInclusao, LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idVendedor, emitidoAs);
    this.paraInclusao = paraInclusao;
  }

  public EnviaVendedorPdvX(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idVendedor,
    LocalDateTime emitidoAs
  ) {
    this(id, idAplicacao, idEmpresa, idUsuario, idVendedor, false, emitidoAs);
  }
}
