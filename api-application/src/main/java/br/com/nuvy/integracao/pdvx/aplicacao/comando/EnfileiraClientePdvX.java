package br.com.nuvy.integracao.pdvx.aplicacao.comando;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EnfileiraClientePdvX extends ComandoClientePdvX {

  public EnfileiraClientePdvX(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCliente,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCliente, emitidoAs);
  }
}
