package br.com.nuvy.notificacao.dominio.usuario;

import java.util.UUID;

public record UsuarioNotificacaoRecord(
  UUID id,
  String email,
  String nome,
  String senha
) implements UsuarioNotificacao {

  public UUID getId() {
    return id();
  }
  public String getEmail() {
    return email();
  }
  public String getNome() {
    return nome();
  }
  public String getSenha() {
    return senha();
  }
}
