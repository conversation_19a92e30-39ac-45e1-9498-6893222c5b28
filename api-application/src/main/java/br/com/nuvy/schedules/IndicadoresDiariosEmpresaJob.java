package br.com.nuvy.schedules;

import br.com.nuvy.api.cadastro.dto.projection.IndicadoresEmpresaViewProjection;
import br.com.nuvy.api.cadastro.repository.IndicadoresEmpresaViewRepository;
import br.com.nuvy.api.seguranca.repository.UsuarioRepository;
import br.com.nuvy.common.utils.NumberUtils;
import br.com.nuvy.events.IndicadoresEmpresaEmailEvent;
import br.com.nuvy.notification.DestinatarioEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@ConditionalOnProperty(
  name = "nuvy.jobs.indicadores-diarios-empresa.enabled",
  havingValue = "true"
)
public class IndicadoresDiariosEmpresaJob {

    private final IndicadoresEmpresaViewRepository indicadoresEmpresaViewRepository;
    private final UsuarioRepository usuarioRepository;
    private final ApplicationEventPublisher indicadoresEmpresaEmailEventPublisher;

    public IndicadoresDiariosEmpresaJob(
            IndicadoresEmpresaViewRepository indicadoresEmpresaViewRepository,
            UsuarioRepository usuarioRepository,
            ApplicationEventPublisher indicadoresEmpresaEmailEventPublisher
    ) {
        this.indicadoresEmpresaViewRepository = indicadoresEmpresaViewRepository;
        this.usuarioRepository = usuarioRepository;
        this.indicadoresEmpresaEmailEventPublisher = indicadoresEmpresaEmailEventPublisher;
        log.info("Job Scheduled {} habilitado", IndicadoresDiariosEmpresaJob.class.getSimpleName());
    }

    @Scheduled(cron = "${nuvy.jobs.indicadores-diarios-empresa.cron}", zone = "America/Sao_Paulo")
    @Transactional
    public void start() {
        log.info("Cron Gerador Indicadores Diarios Empresa");

        var indicadores = indicadoresEmpresaViewRepository.findAllWithRowNumber();

        Map<Integer, Map<String, List<IndicadoresEmpresaViewProjection>>> indicadoresAgrupados = indicadores.stream()
                .collect(Collectors.groupingBy(
                        IndicadoresEmpresaViewProjection::getId,
                        Collectors.groupingBy(IndicadoresEmpresaViewProjection::getAplicacaoId)
                ));

        indicadoresAgrupados.forEach((idEmpresa, aplicacaoMap) ->
                aplicacaoMap.forEach((idAplicacao, listaIndicadores) -> usuarioRepository.findUsuarioNomeUsuarioEmailByAplicacaoId(idAplicacao)
                        .stream()
                        .forEach(usuario -> {

                            DestinatarioEmail destinatario = getDestinatarios(usuario.getEmail(), usuario.getNome());

                            var data = new HashMap<String, String>();
                            data.put("data", LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
                            data.put("nome_usuario", usuario.getNome());

                            String contasPagar = "R$ 0,00";
                            String contasReceber = "R$ 0,00";
                            String contasPagas = "R$ 0,00";
                            String contasRecebidas = "R$ 0,00";
                            String inadimplencia = "R$ 0,00";
                            String nomeEmpresa = "";
                            BigDecimal valorFaturamento = BigDecimal.ZERO;
                            String valorFaturamentoTotal = "R$ 0,00";

                            for (IndicadoresEmpresaViewProjection indicador : listaIndicadores) {
                                String tipo = indicador.getTipo();
                                nomeEmpresa = indicador.getNomeEmpresa();
                                switch (tipo) {
                                    case "CONTA_PAGAR_DIA":
                                        contasPagar = NumberUtils.formataValor(indicador.getValor());
                                        break;
                                    case "CONTA_RECEBER_DIA":
                                        contasReceber = NumberUtils.formataValor(indicador.getValor());
                                        break;
                                    case "CONTA_PAGAR_DIA_ANTERIOR":
                                        contasPagas = NumberUtils.formataValor(indicador.getValor());
                                        break;
                                    case "CONTA_RECEBIDA_DIA_ANTERIOR":
                                        contasRecebidas = NumberUtils.formataValor(indicador.getValor());
                                        break;
                                    case "INADIMPLENCIA":
                                        inadimplencia = NumberUtils.formataValor(indicador.getValor());
                                        break;
                                    case "FATURAMENTO_PEDIDO":
                                    case "FATURAMENTO_SERVICOS":
                                        valorFaturamento = valorFaturamento.add(indicador.getValor());
                                        valorFaturamentoTotal = NumberUtils.formataValor(valorFaturamento);
                                        break;
                                    default:
                                        break;
                                }
                            }

                            data.put("contas_pagar", contasPagar);
                            data.put("contas_receber", contasReceber);
                            data.put("contas_pagas", contasPagas);
                            data.put("contas_recebidas", contasRecebidas);
                            data.put("inadimplencia", inadimplencia);
                            data.put("valor_faturamento", valorFaturamentoTotal);
                            data.put("empresa", nomeEmpresa);

                            indicadoresEmpresaEmailEventPublisher.publishEvent(
                                    new IndicadoresEmpresaEmailEvent(this, destinatario, data)
                            );
                        })));
    }

    private DestinatarioEmail getDestinatarios(
            String emailUsuario, String nomeUsuario
    ) {
        HashSet<String> emails = new HashSet<>();
        emails.add(emailUsuario);
        return DestinatarioEmail.builder()
                .emails(emails)
                .copiasOcultas(null)
                .nomeCliente(nomeUsuario)
                .anexos(null)
                .build();
    }

}
