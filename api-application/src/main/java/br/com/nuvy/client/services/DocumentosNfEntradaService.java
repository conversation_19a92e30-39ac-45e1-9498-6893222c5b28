package br.com.nuvy.client.services;

import static br.com.nuvy.api.enums.SituacaoNotaEntrada.NF_DEVOLUCAO_EMITIDA;
import static br.com.nuvy.api.enums.SituacaoNotaEntrada.NF_INUTILIZADA;
import static br.com.nuvy.api.enums.SituacaoNotaEntrada.NF_REJEITADA;
import static br.com.nuvy.api.enums.TipoArquivoNfEntrada.AMBOS;
import static br.com.nuvy.api.enums.TipoArquivoNfEntrada.DANFE;
import static br.com.nuvy.api.enums.TipoArquivoNfEntrada.XML;
import static br.com.nuvy.api.enums.TipoNotaFiscal.DEVOLUCAO;
import static br.com.nuvy.client.dto.PedidoDanfeDto.toNotaEntrada;

import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.enums.BucketsS3;
import br.com.nuvy.api.enums.SituacaoNotaEntrada;
import br.com.nuvy.api.enums.TipoArquivoNfEntrada;
import br.com.nuvy.api.estoque.model.CartaCorrecaoCompra;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.estoque.repository.NotaFiscalEntradaRepository;
import br.com.nuvy.api.estoque.service.CartaCorrecaoCompraService;
import br.com.nuvy.client.dto.EntradaCCeDto;
import br.com.nuvy.client.dto.EntradaDanfeDto;
import br.com.nuvy.client.dto.OrcamentoAnexosDto;
import br.com.nuvy.client.dto.RetornoDanfeDto;
import br.com.nuvy.client.enums.TipoArquivoDownload;
import br.com.nuvy.client.enums.TipoDanfe;
import br.com.nuvy.client.interfaces.DocumentosClient;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.AmazonS3Service;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentosNfEntradaService {

  private final DocumentosClient client;
  private final NotaFiscalEntradaRepository notaFiscalEntradaRepository;
  private final AmazonS3Service s3Service;
  private final CartaCorrecaoCompraService cartaCorrecaoCompraService;
  private final DocumentosService documentosService;
  private final DocumentosConstructors constructors;
  private final FileService fileService;

  @Transactional
  public ResponseEntity<InputStreamResource> geraPdfDanfeEntrada(List<UUID> idsNotas,
    Boolean isEntrada) {

    var content = createPdfDanfeEntrada(idsNotas, isEntrada);
    return ResponseEntity.ok()
      .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=danfes.pdf")
      .contentType(MediaType.parseMediaType("application/pdf"))
      .body(new InputStreamResource(content));
  }

  @Transactional
  public ResponseEntity<InputStreamResource> createZipDanfeEntrada(List<UUID> idsNotas,
    TipoArquivoNfEntrada tipoArquivo, Boolean isEntrada) throws IOException {
    List<RetornoDanfeDto> retornoDanfeDtos = new ArrayList<>();
    for (UUID id : idsNotas) {
      var notaFiscalEntrada = notaFiscalEntradaRepository.findById(id)
        .orElseThrow(() -> new ResourceNotFoundException("nota.recebida.nao.encontrada"));
      var retornoBuilder = RetornoDanfeDto.builder()
        .caminhoXmlNfe(notaFiscalEntrada.getCaminhoXml())
        .chaveNfe(notaFiscalEntrada.getChaveAcesso() == null?"NFe_":notaFiscalEntrada.getChaveAcesso());
      if (List.of(DANFE, AMBOS).contains(tipoArquivo)) {
        var contentDanfePDf = createPdfDanfeEntrada(List.of(id), isEntrada);
        retornoBuilder.danfe(contentDanfePDf.readAllBytes());
      }
      if (List.of(XML, AMBOS).contains(tipoArquivo)) {
        var situacao = notaFiscalEntrada.getSituacao();
        if (notaFiscalEntrada.getTipoNota().equals(DEVOLUCAO) && (
          situacao.equals(NF_DEVOLUCAO_EMITIDA) || situacao.equals(
            SituacaoNotaEntrada.NF_CANCELADA))) {
          var entradaDanfe = EntradaDanfeDto.builder()
            .pedidoDanfeDto(toNotaEntrada(notaFiscalEntrada))
            .tipoDanfe(TipoDanfe.NORMAL)
            .isEntrada(isEntrada)
            .build();
          retornoDanfeDtos.add(client.createDanfePdf(entradaDanfe));
        } else if (List.of(NF_REJEITADA, NF_INUTILIZADA).contains(situacao)
          && notaFiscalEntrada.getTipoNota().equals(DEVOLUCAO)) {
          retornoBuilder.xmlNfe(getBucketFile(BucketsS3.NFE, notaFiscalEntrada.getCaminhoXml()));
        } else {
          retornoBuilder.xmlNfe(getBucketFile(BucketsS3.ERP, notaFiscalEntrada.getCaminhoXml()));
        }
      }
      retornoDanfeDtos.add(retornoBuilder.build());
    }
    ByteArrayInputStream content;
    ByteArrayOutputStream out = new ByteArrayOutputStream();
    ZipOutputStream zipOutputStream = new ZipOutputStream(out);

    if (List.of(XML, AMBOS).contains(tipoArquivo)) {
      geraZipXml(retornoDanfeDtos, zipOutputStream, AMBOS.equals(tipoArquivo));
    }
    if (List.of(DANFE, AMBOS).contains(tipoArquivo)) {
      geraZipPdf(retornoDanfeDtos, zipOutputStream, AMBOS.equals(tipoArquivo));
    }
    zipOutputStream.closeEntry();
    zipOutputStream.close();
    content = new ByteArrayInputStream(out.toByteArray());

    var inputStreamResource = new InputStreamResource(content);
    return ResponseEntity.ok()
      .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=nf-entrada.zip")
      .contentType(MediaType.APPLICATION_OCTET_STREAM)
      .body(inputStreamResource);
  }

  /**
   * Cria um PDF com as DANFEs de uma lista de notas fiscais de entrada.
   *
   * @param idsNotas Lista de UUIDs das notas fiscais de entrada.
   * @param isEntrada Booleano indicando se as notas são de entrada.
   * @return Um ByteArrayInputStream contendo o PDF com as DANFEs.
   */
  public ByteArrayInputStream createPdfDanfeEntrada(List<UUID> idsNotas,
    Boolean isEntrada) {
    List<OrcamentoAnexosDto> dtos = new LinkedList<>();

    for (UUID notaId : idsNotas) {
      var notaFiscalEntrada = notaFiscalEntradaRepository.findById(notaId)
        .orElseThrow(
          () -> new ResourceNotFoundException("nota.recebida.nao.encontrada"));
      OrcamentoAnexosDto dto = new OrcamentoAnexosDto();
      var pedidoDanfe = toNotaEntrada(notaFiscalEntrada);
      pedidoDanfe.setEmpresaLogotipo(constructors.trataLogoEmpresa(pedidoDanfe.getEmpresaLogotipo()));
      dto.setEntradaDanfeDto(EntradaDanfeDto.builder()
        .pedidoDanfeDto(pedidoDanfe)
        .isEntrada(isEntrada)
        .build());
      dtos.add(dto);
    }
    var content = new ByteArrayInputStream(
      client.createOrcamentoArquivosPdf(List.of(TipoArquivoDownload.DANFE), dtos, Boolean.FALSE));

    return content;
  }

  public void geraZipXml(List<RetornoDanfeDto> retornoDanfeDtos, ZipOutputStream zipOutputStream,
    Boolean isAmbos) throws IOException {
    var folder = isAmbos ? "xml/" : "";

    int numeroArquivos = 0;
    for (RetornoDanfeDto retornoDanfeDto : retornoDanfeDtos) {
      if (retornoDanfeDto.getXmlNfe() == null) {
        continue;
      }
      var nomeArquivo = "";
      if (retornoDanfeDtos.stream()
        .filter(each -> each.getChaveNfe().equals(retornoDanfeDto.getChaveNfe())).count() > 1) {
        nomeArquivo = retornoDanfeDto.getChaveNfe().concat("(" + ++numeroArquivos + ")")
          .concat(".xml").replace(" ", "");
      } else {
        nomeArquivo = retornoDanfeDto.getChaveNfe().concat(".xml").replace(" ", "");
      }
      zipOutputStream.putNextEntry(
        new ZipEntry(folder.concat(nomeArquivo)));
      zipOutputStream.write(retornoDanfeDto.getXmlNfe());
    }
  }

  public void geraZipPdf(List<RetornoDanfeDto> retornoDanfeDtos, ZipOutputStream zipOutputStream,
    Boolean isAmbos) throws IOException {
    var folder = isAmbos ? "pdf/" : "";
    int numeroArquivos = 0;
    for (RetornoDanfeDto retornoDanfeDto : retornoDanfeDtos) {
      if (retornoDanfeDto.getDanfe() == null) {
        continue;
      }
      var nomeArquivo = "";
      if (retornoDanfeDtos.stream()
        .filter(each -> each.getChaveNfe().equals(retornoDanfeDto.getChaveNfe())).count() > 1) {
        nomeArquivo = retornoDanfeDto.getChaveNfe().concat("(" + ++numeroArquivos + ")")
          .concat(".pdf").replace(" ", "");
      } else {
        nomeArquivo = retornoDanfeDto.getChaveNfe().concat(".pdf").replace(" ", "");
      }
      zipOutputStream.putNextEntry(new ZipEntry(
        folder.concat(nomeArquivo)));
      zipOutputStream.write(retornoDanfeDto.getDanfe());
    }
  }

  public RetornoDanfeDto createDanfePdf(NotaFiscalEntrada notaFiscalEntrada, TipoDanfe tipoDanfe) {
    EntradaDanfeDto entradaDanfeDto = EntradaDanfeDto.builder()
      .tipoDanfe(tipoDanfe)
      .pedidoDanfeDto(toNotaEntrada(notaFiscalEntrada))
      .isEntrada(Boolean.FALSE)
      .build();
    return client.createDanfePdf(entradaDanfeDto);
  }

  /**
   * Cria uma carta de correção para uma notaFiscalEntrada com base nas informações de
   * notaFiscalEntrada.
   *
   * @param notaFiscalEntrada A notaFiscalEntrada para o qual a carta de correção será criada.
   * @return Um RetornoDanfeDto contendo informações sobre a carta de correção criada.
   */
  public ResponseEntity<InputStreamResource> createCartaCorrecao(
    NotaFiscalEntrada notaFiscalEntrada) {
    var carta = cartaCorrecaoCompraService.findCartaCorrecaoTofileByNotaEntradaId(
        notaFiscalEntrada.getId())
      .orElse(new CartaCorrecaoCompra());
    EntradaCCeDto entradaCCeDto = constructors.generateCCeFromNotaEntrada(notaFiscalEntrada, carta);
    var result = documentosService.createCartaCorrecao(entradaCCeDto);

    return ResponseEntity.ok()
      .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=carta-correcao.pdf")
      .contentType(MediaType.APPLICATION_PDF)
      .body(new InputStreamResource(new ByteArrayInputStream(result.getDanfe())));
  }

  public byte[] getBucketFile(BucketsS3 bucket, String path) {
    try{
      var contentXml =  fileService.readBucket(bucket,path);
      return contentXml.readAllBytes();
    } catch (Exception e) {
      log.error("Erro ao buscar arquivo no bucket: {}", e.getMessage());
      return null;
    }
  }
}
