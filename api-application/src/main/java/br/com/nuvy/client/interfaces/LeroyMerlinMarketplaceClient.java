package br.com.nuvy.client.interfaces;

import br.com.nuvy.client.dto.ConfirmImportProductResponseDto;
import br.com.nuvy.client.dto.EtiquetaResponse;
import br.com.nuvy.client.dto.SearchImportProductResponseDto;
import br.com.nuvy.config.FeignSupportConfig;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoControleEntregaDtoOut;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoControleRastreamentoDtoOut;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoFaturadoDtoOut;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.hub.NuvyHubResponseDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "leroyMerlinMarketplaceClient", url = "${nuvy.service.marketplace.leroy-merlin.url}", configuration = {
  FeignSupportConfig.class, BaseMarketplaceClient.CustomErrorDecoder.class})
public interface LeroyMerlinMarketplaceClient extends BaseMarketplaceClient {

  @PutMapping(value = "/TryShipOrderAsync", headers = {"Content-Type=application/json"})
  NuvyHubResponseDto tryShipOrderAsync(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody PedidoControleRastreamentoDtoOut pedidoControleRastreamentoDto);

  @PutMapping(value = "/TryDeliveryOrderAsync", headers = {"Content-Type=application/json"})
  NuvyHubResponseDto tryDeliveryOrderAsync(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody PedidoControleEntregaDtoOut pedidoControleEntregaDtoOut);

  @PutMapping(value = "/TryInvoiceOrderAsync", headers = {"Content-Type=application/json"})
  NuvyHubResponseDto tryInvoiceOrderAsync(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody PedidoFaturadoDtoOut pedidoFaturadoDtoOut);

  @PostMapping(value = "/ExecuteProductCommand", headers = {"Content-Type=application/json",
    "accept=text/plain"})
  Object executeProductComand(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody Object productContractHub);

  @PostMapping(value = "/ExecuteProductInventoryCommand", headers = {
    "Content-Type=application/json"})
  Object executeProductInventoryCommand(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody Object productContractHub);

  @PostMapping(value = "/SaveAccountConfiguration", headers = {"Content-Type=application/json",
    "Accept=application/json"})
  Object saveAccountConfiguration(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestHeader("accessToken") String accessToken,
    @RequestHeader("email") String email,
    @RequestHeader("user") String username,
    @RequestHeader("password") String password,
    @RequestHeader("storeId") String storeId,
    @RequestHeader("integrationKey") String integrationKey);

  @GetMapping(value = "/GetShipmentLabelAsync", headers = {"Content-Type=application/json",
    "Accept=text/plain"})
  EtiquetaResponse getShipmentLabelAsync(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestParam String orderId);

  @PostMapping(value = "/EnqueueProductCommand", headers = {"Content-Type=application/json",
    "accept=text/plain"})
  Object enqueueProductComand(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody Object productContractHub);

  @PostMapping(value = "/EnqueueProductInventoryCommand", headers = {
    "Content-Type=application/json"})
  Object enqueueProductInventoryCommand(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody Object productContractHub);

  @PostMapping(value = "/EnqueueProductPriceCommand", headers = {
    "Content-Type=application/json"})
  Object enqueueProductPriceCommand(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestBody Object productContractHub);

  @PostMapping(value = "/SearchImportProduct", headers = {"Content-Type=application/json"})
  SearchImportProductResponseDto searchImportProduct(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @RequestParam(value = "offset", required = false) Integer offset,
    @RequestParam(value = "limit", required = false) Integer limit);

  @PutMapping(value = "/ConfirmImportProduct/{id}", headers = {"Content-Type=application/json"})
  ConfirmImportProductResponseDto confirmImportProduct(
    @RequestHeader("x-VendorId") String vendorId,
    @RequestHeader("x-TenantId") String tenantId,
    @RequestHeader("x-AccountId") String accountId,
    @PathVariable("id") String id);
}
