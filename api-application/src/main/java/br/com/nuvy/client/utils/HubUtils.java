package br.com.nuvy.client.utils;

import br.com.nuvy.api.enums.OrigemEventoHub;
import br.com.nuvy.api.integracaohub.enums.MarketplaceHub;
import br.com.nuvy.api.integracaohub.enums.ServiceOperation;
import br.com.nuvy.common.exception.BusinessException;

public class HubUtils {

  public static ServiceOperation mapOrigemToOperation(OrigemEventoHub origemEventoHub) {
    ServiceOperation serviceOperation;
    switch (origemEventoHub) {
      case PRODUTO_SALDO_ALTERADO, CN_VENDA_ALTERADO_DEPOSITO, CN_VENDA_ALTERADO_PRECO,
           PRODUTO_EDITADO, TABELA_PRECO_ALTERADO, SINCRONIZAR_PRODUTOS,
           CN_VENDA_ALTERADO_CATALOGO -> serviceOperation = ServiceOperation.Update;
      case PRODUTO_CRIADO, CAT_CATEG_CRIADO, CAT_PROD_CRIADO ->
        serviceOperation = ServiceOperation.Insert;
      case PRODUTO_EXCLUIDO, CAT_CATEG_EXCLUIDO, CAT_PROD_EXCLUIDO ->
        serviceOperation = ServiceOperation.Delete;
      default -> serviceOperation = null;
    }
    return serviceOperation;
  }

  public static MarketplaceHub mapOrigemCanalToMarketplaceHub(Integer canalOrigemId) {
    MarketplaceHub marketplace;
    switch (canalOrigemId) {
      case 1 ->
        throw new BusinessException("Canal de origem Nuvy não permite integração com o HUB");
      case 2 -> marketplace = MarketplaceHub.MercadoLivre;
      case 3 -> marketplace = MarketplaceHub.B2W;
      case 4 -> marketplace = MarketplaceHub.Magalu;
      case 5 -> marketplace = MarketplaceHub.ViaVarejo;
      case 6 -> marketplace = MarketplaceHub.Amazon;
      case 8 -> marketplace = MarketplaceHub.Netshoes;
      case 16 -> marketplace = MarketplaceHub.LeroyMerlin;
      case 10 -> marketplace = MarketplaceHub.Nuvemshop;
      case 17 -> marketplace = MarketplaceHub.Virtuol;
      default -> throw new BusinessException("Canal de origem não mapeado para o HUB");
    }
    return marketplace;
  }

}
