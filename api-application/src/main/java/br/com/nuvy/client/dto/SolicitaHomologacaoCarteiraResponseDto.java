package br.com.nuvy.client.dto;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

@Getter
@Setter
public class SolicitaHomologacaoCarteiraResponseDto {

  private Integer id;
  private String bank_contract_slug;
  private Integer next_our_number;
  private String agency_number;
  private String agency_digit;
  private String account_number;
  private String account_digit;
  private String extra1;
  private String extra1_digit;
  private String extra2;
  private String extra2_digit;
  private String contract;
  private String contract_type;
  private String extra3;
  private String beneficiary_name;
  private String beneficiary_cnpj_cpf;
  private String beneficiary_address_street;
  private String beneficiary_address_street_number;
  private String beneficiary_address_complement;
  private String beneficiary_address_neighborhood;
  private String beneficiary_address_city;
  private String beneficiary_address_state;
  private String beneficiary_address_zipcode;
  private String name;
  private String status;
  private LocalDateTime homologated_at;
  private Integer next_remittance_number;
  private String configuration;
  private String custom_name;
  private String kind;
  private String allow_expiration_on_weekends;
  private LocalDateTime created_at;
  private LocalDateTime updated_at;

}
