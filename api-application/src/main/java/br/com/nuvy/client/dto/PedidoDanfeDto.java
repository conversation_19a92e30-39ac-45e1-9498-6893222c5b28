package br.com.nuvy.client.dto;

import static br.com.nuvy.api.enums.SituacaoPedido.situacaoNotaToSituacaoPedido;
import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.venda.model.Pedido;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedidoDanfeDto implements Serializable {

  private String aplicacao;
  private Integer empresaId;
  private String empresaLogotipo;
  private String xmlNotaFiscal;
  private SituacaoPedido situacao;
  private String nopPedidoDescricao;
  private String numeroNotaFiscal;

  public static PedidoDanfeDto fromEntidade(Pedido pedido) {
    return PedidoDanfeDto.builder()
      .aplicacao(pedido.getAplicacao())
      .empresaId(pedido.getEmpresa().getId())
      .empresaLogotipo(get(pedido.getEmpresa(), Empresa::getLogotipo))
      .xmlNotaFiscal(pedido.getXmlNotaFiscal())
      .situacao(pedido.getSituacao())
      .nopPedidoDescricao(pedido.getNopPedidoDescricao())
      .numeroNotaFiscal(getOrElse(pedido.getNumeroNotaFiscal(), String::valueOf, ""))
      .build();
  }

  public static PedidoDanfeDto toNotaEntrada(NotaFiscalEntrada notaFiscalEntrada) {
    return PedidoDanfeDto.builder()
      .aplicacao(notaFiscalEntrada.getAplicacao())
      .empresaId(notaFiscalEntrada.getEmpresa().getId())
      .empresaLogotipo(get(notaFiscalEntrada.getEmpresa(), Empresa::getLogotipo))
      .xmlNotaFiscal(notaFiscalEntrada.getCaminhoXml())
      .situacao(situacaoNotaToSituacaoPedido(notaFiscalEntrada.getSituacao()))
      .nopPedidoDescricao(notaFiscalEntrada.getNopDescricao())
      .numeroNotaFiscal(getOrElse(notaFiscalEntrada.getNumeroNota(), String::valueOf, ""))
      .build();
  }

}
