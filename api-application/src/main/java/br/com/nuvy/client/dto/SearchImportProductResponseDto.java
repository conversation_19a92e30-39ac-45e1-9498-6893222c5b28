package br.com.nuvy.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchImportProductResponseDto {
  private Data data;
  private List<String> errors;
  private Identity identity;
  private boolean hasBusinessError;
  private boolean isValid;

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Data {
    private List<ProductData> docs;
    private int count;
    private int offset;
    private int limit;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Identity {
    private Claims claims;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Claims {
    private String ClaimType;
    private String vendorId;
    private String tenantId;
    private String accountId;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ProductData {
    private String vendorId;
    private String tenantId;
    private String accountId;
    private String integrationType;
    private String importId;
    private String externalId;
    private Product product;
    private String timestamp;
    private boolean imported;
    private String id;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Product {
    private List<Sku> skus;
    private String id;
    private String code;
    private String name;
    private String integrationId;
    private String description;
    private String brand;
    private int warrantyTime;
    private String warrantyType;
    private List<Attribute> attributes;
    private List<Image> images;
    private Category category;
    private boolean hasVariations;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Sku {
    private String productId;
    private String id;
    private String skuCode;
    private String integrationId;
    private String name;
    private Price price;
    private Inventory inventory;
    private Dimension dimension;
    private List<Attribute> skuAttributes;
    private List<Variation> variations;
    private List<Image> images;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Price {
    private double list;
    private double retail;
    private Double salePrice;
    private String salePriceFrom;
    private String salePriceTo;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Inventory {
    private int balance;
    private Integer handlingDays;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Dimension {
    private String height;
    private String width;
    private String length;
    private String weight;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Attribute {
    private String id;
    private String value;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Variation {
    private String id;
    private String valueId;
    private String value;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Image {
    private String url;
    private int width;
    private int height;
    private boolean isMain;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Category {
    private int id;
    private List<CategoryPath> path;
    private String name;
    private String marketplaceId;
    private String parentId;
  }

  @lombok.Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class CategoryPath {
    private int parentId;
    private int id;
    private String name;
  }
}