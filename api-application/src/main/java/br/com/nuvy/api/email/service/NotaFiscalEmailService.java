package br.com.nuvy.api.email.service;

import static br.com.nuvy.api.email.utils.OrdemServicoConstants.ASSUNTO_NF_EMITIDA;
import static br.com.nuvy.api.email.utils.OrdemServicoConstants.ASSUNTO_RECIBO;
import static br.com.nuvy.api.enums.TipoEmail.CARTA_CORRECAO;
import static br.com.nuvy.api.enums.TipoEmail.NOTA_FISCAL;
import static br.com.nuvy.api.enums.TipoEmail.NOTA_FISCAL_CANCELADA;
import static br.com.nuvy.api.enums.TipoEmail.PEDIDO;
import static br.com.nuvy.client.enums.TipoDanfe.NORMAL;
import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.common.utils.StringUtils.formatCpfCnpj;
import static br.com.nuvy.notification.Constants.TEMPLATE_EMAIL_BOLETO_CHEGOU;
import static br.com.nuvy.notification.Constants.TEMPLATE_EMAIL_OS_NFS_CHEGOU;
import static br.com.nuvy.notification.Constants.TEMPLATE_EMAIL_OS_RECIBO_CHEGOU;

import br.com.nuvy.api.cadastro.model.EmissaoNotaServico;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.repository.EmissaoNotaServicoRepository;
import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.email.utils.OrdemServicoConstants;
import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.api.enums.TipoComprovante;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.titulo.TituloValorVencimentoInfo;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.financeiro.service.TituloBoletoService;
import br.com.nuvy.api.notafiscal.model.NotaFiscalSerie;
import br.com.nuvy.api.notafiscal.repository.NotaFiscalSerieRepository;
import br.com.nuvy.api.notafiscal.service.NotaFiscalSerieService;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoRepository;
import br.com.nuvy.api.servico.service.OrdemServicoEmailService;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoContato;
import br.com.nuvy.api.venda.model.PedidoHistoricoEmailArquivo;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.api.venda.service.PedidoHistoricoEmailService;
import br.com.nuvy.client.dto.RetornoDanfeDto;
import br.com.nuvy.client.services.DocumentosNfEntradaService;
import br.com.nuvy.client.services.DocumentosOrcamentoPedidoService;
import br.com.nuvy.client.services.DocumentosOsService;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.file.File;
import br.com.nuvy.common.utils.NumberUtils;
import br.com.nuvy.events.CartaCorrecaoEmailEvent;
import br.com.nuvy.events.PedidoAutorizadoEmailEvent;
import br.com.nuvy.events.PedidoCanceladoEmailEvent;
import br.com.nuvy.events.PedidoEmailEvent;
import br.com.nuvy.events.PedidoReciboFaturadoEmailEvent;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.EnviarEmailFaturadoSqs;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoHistoricoEmailDto;
import br.com.nuvy.notification.DestinatarioEmail;
import br.com.nuvy.notification.service.BrevoService;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotaFiscalEmailService {

  private final DocumentosOrcamentoPedidoService documentosPedidoService;
  private final ApplicationEventPublisher pedidoEvent;
  private final PedidoHistoricoEmailService pedidoHistoricoEmailService;
  private final DocumentosNfEntradaService documentosNfEntradaService;
  private final FileService fileService;
  private final TituloBoletoService tituloBoletoService;
  private final PedidoRepository pedidoRepository;
  private final OrdemServicoNotaFiscalRepository ordemServicoNotaRepository;
  private final BrevoService brevoService;
  private final OrdemServicoEmailService ordemServicoEmailService;
  private final OrdemServicoRepository ordemServicoRepository;
  private final DocumentosOsService documentosOsService;
  private final NotaFiscalSerieRepository notaFiscalSerieRepository;
  private final EmissaoNotaServicoRepository emissaoNotaServicoRepository;
  private final TituloRepository tituloRepository;

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void envioPedidoEmail(EnviarEmailFaturadoSqs envioEmailDto) throws Exception {
    Pedido pedido = pedidoRepository.findById(envioEmailDto.getPedidoId())
      .orElseThrow(() -> new ResourceNotFoundException("pedido.nao.encontrado"));
    if(TipoComprovante.RECIBO.equals(pedido.getTipoComprovante())){
      envioPedidoReciboAutorizado(pedido);
    }else{
      envioPedidoNotaAutorizada(pedido);
    }
  }

  private void envioPedidoNotaAutorizada(Pedido pedido) {
    RetornoDanfeDto retornoDanfeDto = documentosPedidoService.createDanfePdf(pedido, NORMAL);

    File xmlNfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-nfe.xml")
      .bytes(retornoDanfeDto.getXmlNfe())
      .type("xml")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getXmlNfe()))
      .build();

    PedidoHistoricoEmailArquivo xml = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoXmlNfe())
      .build();

    File danfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-nfe.pdf")
      .bytes(retornoDanfeDto.getDanfe())
      .type("pdf")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getDanfe()))
      .build();

    PedidoHistoricoEmailArquivo pdf = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoDanfe())
      .build();

    List<File> anexos = new ArrayList<>();
    boolean isBoletosCobranca = FormaPagamento.ID_FORMA_PAGAMENTO_BOLETO
      .equals(pedido.getFormaRecebimento().getId());

    boolean isCarnesCobranca = FormaPagamento.ID_FORMA_PAGAMENTO_CARNE
      .equals(pedido.getFormaRecebimento().getId());

    Optional<NotaFiscalSerie> optionalNotaFiscalSerie = notaFiscalSerieRepository.findByEmpresaId(pedido.getEmpresa().getId());
    optionalNotaFiscalSerie.ifPresentOrElse(
        serie -> {
          if (Boolean.TRUE.equals(serie.getEmailNfe())) {
            anexos.add(xmlNfe);
            anexos.add(danfe);
          } else {
            log.debug("Envio de XML e DANFE desabilitado.");
          }
          if (isBoletosCobranca && Boolean.TRUE.equals(serie.getEmailBoleto())) {
            log.debug("Adicionando boletos ao email, se houver");
            tituloBoletoService.adicionaBoletosParaEnvioEmail(anexos, pedido);
          } else if (isCarnesCobranca && Boolean.TRUE.equals(serie.getEmailBoleto())) {
            log.debug("Adicionando carnês ao email, se houver");
            tituloBoletoService.adicionarCarnesParaEnvioEmail(anexos, pedido);
          } else {
            log.debug("Envio de boletos/carnês desabilitado.");
          }
        },
        () -> log.debug("Cadastro de nota fiscal não encontrado")
      );

    DestinatarioEmail destinatario = getDestinatarios(pedido, anexos, isBoletosCobranca);

    HashMap<String, String> data = getVariaveisTemplate(pedido, retornoDanfeDto);
    var titulo = tituloRepository.findTitulosValorVencimentoByPedidoId(pedido.getId());

    if (titulo.isPresent()) {
      data.put("valor", NumberUtils.formataValor(titulo.get().getValorLiquido()));
      data.put("data_venc", titulo.get().getDataVencimento().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
    }
    optionalNotaFiscalSerie.ifPresent(serie -> {
      if (Boolean.TRUE.equals(serie.getEmailBoleto())
        && !Boolean.TRUE.equals(serie.getEmailNfe())
        && !Boolean.TRUE.equals(serie.getEmailRecibo()))
      {
        log.debug("Somente a configuração de boleto está ativa, enviando PedidoReciboFaturadoEmailEvent.");
        pedidoEvent.publishEvent(new PedidoReciboFaturadoEmailEvent(this, destinatario, data, pedido.getEmpresa().getNome()));
      } else {
        log.debug("Existem outras configurações ativas, enviando PedidoAutorizadoEmailEvent.");
        pedidoEvent.publishEvent(new PedidoAutorizadoEmailEvent(this, destinatario, data, pedido.getEmpresa().getNome()));
      }
    });

    List<PedidoHistoricoEmailArquivo> arquivosEmail = new ArrayList<>();
    arquivosEmail.add(xml);
    arquivosEmail.add(pdf);

    PedidoHistoricoEmailDto pedidoHistorico = PedidoHistoricoEmailDto.builder()
      .dataHistorico(LocalDateTime.now())
      .assunto(NOTA_FISCAL.getAssuntoEmail())
      .pedidoId(pedido.getId())
      .remetenteId(pedido.getUsuario().getId())
      .arquivos(arquivosEmail)
      .destinatarios(getAllDestinatarios(pedido))
      .tipoEmail(NOTA_FISCAL)
      .build();

    pedidoHistoricoEmailService.create(pedidoHistorico.toEntity());
  }

  public void envioNotaCancelada(Pedido pedido) throws IOException {
    InputStream xmlS3 = fileService.readNfeBucket(pedido.getXmlNotaFiscal());

    RetornoDanfeDto retornoDanfeDto = documentosPedidoService.createDanfePdf(
      pedido, NORMAL
    );
    File xmlNfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-canc.xml")
      .bytes(xmlS3.readAllBytes())
      .type("xml")
      .inputStream(xmlS3)
      .build();
    PedidoHistoricoEmailArquivo xml = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoXmlNfe())
      .build();

    File danfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-canc.pdf")
      .bytes(retornoDanfeDto.getDanfe())
      .type("pdf")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getDanfe()))
      .build();
    PedidoHistoricoEmailArquivo pdf = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoDanfe())
      .build();

    List<File> anexos = new ArrayList<>();
    anexos.add(xmlNfe);
    anexos.add(danfe);
    DestinatarioEmail destinatario = getDestinatarios(pedido, anexos, false);
    HashMap<String, String> data = getVariaveisTemplate(pedido, retornoDanfeDto);
    pedidoEvent.publishEvent(new PedidoCanceladoEmailEvent(
      this, destinatario, data, pedido.getEmpresa().getNome()
    ));

    List<PedidoHistoricoEmailArquivo> arquivosEmail = new ArrayList<>();
    arquivosEmail.add(pdf);
    arquivosEmail.add(xml);
    PedidoHistoricoEmailDto pedidoHistorico = PedidoHistoricoEmailDto.builder()
      .dataHistorico(LocalDateTime.now())
      .assunto(NOTA_FISCAL_CANCELADA.getAssuntoEmail())
      .pedidoId(pedido.getId())
      .remetenteId(pedido.getUsuario().getId())
      .arquivos(arquivosEmail)
      .destinatarios(getAllDestinatarios(pedido))
      .tipoEmail(NOTA_FISCAL_CANCELADA)
      .build();

    pedidoHistoricoEmailService.create(pedidoHistorico.toEntity());
  }

  public void envioNotaCartaCorrecao(Pedido pedido, String uri) {
    RetornoDanfeDto retornoDanfeDto = documentosPedidoService.createCartaCorrecao(pedido, uri);

    File xmlNfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-cce.xml")
      .bytes(retornoDanfeDto.getXmlNfe())
      .type("xml")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getXmlNfe()))
      .build();
    PedidoHistoricoEmailArquivo xml = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoXmlNfe())
      .build();

    File danfe = File.builder()
      .fileName(pedido.getChaveNotaFiscal() + "-cce.pdf")
      .bytes(retornoDanfeDto.getDanfe())
      .type("pdf")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getDanfe()))
      .build();
    PedidoHistoricoEmailArquivo pdf = PedidoHistoricoEmailArquivo.builder()
      .arquivo(retornoDanfeDto.getCaminhoDanfe())
      .build();

    List<File> anexos = new ArrayList<>();
    anexos.add(xmlNfe);
    anexos.add(danfe);

    DestinatarioEmail destinatario = getDestinatarios(pedido, anexos, false);

    HashMap<String, String> data = getVariaveisTemplate(pedido, retornoDanfeDto);
    pedidoEvent.publishEvent(new CartaCorrecaoEmailEvent(this, destinatario, data, pedido.getEmpresa().getNome()));

    List<PedidoHistoricoEmailArquivo> arquivosEmail = new ArrayList<>();
    arquivosEmail.add(pdf);
    arquivosEmail.add(xml);
    PedidoHistoricoEmailDto pedidoHistorico = PedidoHistoricoEmailDto.builder()
      .dataHistorico(LocalDateTime.now())
      .assunto(CARTA_CORRECAO.getAssuntoEmail())
      .pedidoId(pedido.getId())
      .remetenteId(pedido.getUsuario().getId())
      .arquivos(arquivosEmail)
      .destinatarios(getAllDestinatarios(pedido))
      .tipoEmail(CARTA_CORRECAO)
      .build();
    pedidoHistoricoEmailService.create(pedidoHistorico.toEntity());
  }

  private static HashMap<String, String> getVariaveisTemplate(
    Pedido pedido, RetornoDanfeDto retornoDanfeDto
  ) {
    Pessoa cliente = pedido.getCliente();
    var data = new HashMap<String, String>();
    data.put("nome_empresa_cliente", cliente.getNome());
    data.put("numero_pedido", String.valueOf(pedido.getCodigoPedido()));
    data.put("numero_cnpj_cpf", formatCpfCnpj(cliente.getCpfCnpj()));
    data.put("numero_nfe", String.valueOf(pedido.getNumeroNotaFiscal()));
    data.put("data_emissao", retornoDanfeDto.getDataEmissao());
    data.put("chave_acesso", retornoDanfeDto.getChaveNfe());
    var emailContato = pedido.getVendedor() != null ? pedido.getVendedor().getEmailCobranca()
      : pedido.getUsuario().getEmail();
    data.put("email_usuario", emailContato);
    data.put("nome_empresa", pedido.getEmpresa().getNome());
    return data;
  }

  private DestinatarioEmail getDestinatarios(
    Pedido pedido, List<File> anexos, boolean isEmailCobranca
  ) {
    HashSet<String> emails = new HashSet<>();
    if (!StringUtils.isBlank(pedido.getEmailNotaFiscal())) {
      emails.add(pedido.getEmailNotaFiscal());
    }
    if (isEmailCobranca) {
      if (!StringUtils.isBlank(pedido.getEmailCobranca())) {
        emails.add(pedido.getEmailCobranca());
      }
    }
    HashSet<String> cco = new HashSet<>();
    for (PedidoContato contato : pedido.getContatos()) {
      if (!StringUtils.isBlank(contato.getEmail())) {
        cco.add(contato.getEmail());
      }
    }
    return DestinatarioEmail.builder()
      .emails(emails)
      .copiasOcultas(!cco.isEmpty() ? cco : null)
      .nomeCliente(pedido.getCliente().getNome())
      .anexos(!anexos.isEmpty() ? anexos : null)
      .build();
  }

  private String getAllDestinatarios(Pedido pedido) {
    var emailCliente = pedido.getCliente().getEmailNotaFiscal();
    StringBuilder allDestinatarios = new StringBuilder(emailCliente);
    for (PedidoContato contato : pedido.getContatos()) {
      if (!StringUtils.isBlank(contato.getEmail()) && !emailCliente.equals(contato.getEmail())) {
        allDestinatarios.append("; ").append(contato.getEmail());
      }
    }
    return allDestinatarios.toString();
  }

  public void envioNotaDevolucaoCompraAutorizada(NotaFiscalEntrada notaFiscalEntrada) {
    var retornoDanfeDto = documentosNfEntradaService.createDanfePdf(notaFiscalEntrada, NORMAL);
    File xmlNfe = File.builder()
      .fileName(notaFiscalEntrada.getChaveAcesso() + "-devol.xml")
      .bytes(retornoDanfeDto.getXmlNfe())
      .type("xml")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getXmlNfe()))
      .build();

    File danfe = File.builder()
      .fileName(notaFiscalEntrada.getChaveAcesso() + "-devol.pdf")
      .bytes(retornoDanfeDto.getDanfe())
      .type("pdf")
      .inputStream(new ByteArrayInputStream(retornoDanfeDto.getDanfe()))
      .build();

    Pessoa cliente = notaFiscalEntrada.getFornecedor();
    HashSet<String> emails = new HashSet<>();
    emails.add(cliente.getEmailNotaFiscal());
    List<File> anexos = new ArrayList<>();
    anexos.add(xmlNfe);
    anexos.add(danfe);
    HashSet<String> cco = new HashSet<>();
    if (notaFiscalEntrada.getUsuario() != null) {
      cco.add(notaFiscalEntrada.getUsuario().getEmail());
    }
    var destinatario = DestinatarioEmail.builder()
      .emails(emails)
      .copiasOcultas(cco.isEmpty() ? null : cco)
      .anexos(anexos)
      .build();

    var data = new HashMap<String, String>();
    data.put("nome_empresa_cliente", cliente.getNome());
    data.put("numero_pedido", String.valueOf(notaFiscalEntrada.getCodigoTela()));
    data.put("numero_cnpj_cpf", formatCpfCnpj(cliente.getCpfCnpj()));
    data.put("numero_nfe", String.valueOf(notaFiscalEntrada.getNumeroNota()));
    data.put("data_emissao", retornoDanfeDto.getDataEmissao());
    data.put("chave_acesso", retornoDanfeDto.getChaveNfe());
    data.put("email_usuario", notaFiscalEntrada.getUsuario().getEmail());
    data.put("nome_empresa", notaFiscalEntrada.getEmpresa().getNome());
    pedidoEvent.publishEvent(new PedidoAutorizadoEmailEvent(
      this, destinatario, data, notaFiscalEntrada.getEmpresa().getNome()
    ));
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void enviarOrdemServicoEmail(EnviarEmailFaturadoSqs envioEmailDto) throws IOException {
    var ordemServico = ordemServicoRepository.findById(envioEmailDto.getOrdemServicoId())
      .orElseThrow(() -> new ResourceNotFoundException("ordem.servico.nao.encontrado"));
    if (ordemServico.getSituacao().equals(SituacaoOrdemServico.FATURADO)) {
      if (ordemServico.getTipoComprovante().equals(OrdemServicoConstants.TIPO_COMPROVANTE_NF)) {
        envioOrdemServicoNfEmitida(ordemServico);
      } else {
        envioOrdemServicoFaturado(ordemServico); // Caso contrario é recibo
      }
    }
  }

  private void envioOrdemServicoNfEmitida(OrdemServico ordemServico) {
    log.debug("Enviando email de NFS emitida");
    OrdemServicoNotaFiscal ordemServicoNota = ordemServicoNotaRepository.findFirstByOrdemServico(ordemServico);

    HashSet<String> emails = new HashSet<>();
    String emailNota = ordemServicoNota.getEmail();
    if (StringUtils.isNotBlank(emailNota)) {
      emails.add(emailNota);
    }
    String emailNfse = get(ordemServico.getCliente(), Pessoa::getEmailNotaFiscal, String::trim);
    emailNfse = getOrElse(ordemServico.getEmailEnvioNfse(), String::trim, emailNfse);
    if (StringUtils.isNotBlank(emailNfse)) {
      emails.add(emailNfse);
    }

    log.debug("Adicionando anexos ao email");
    List<File> anexos = new ArrayList<>();
    byte[] xmlFile = fileService.readNfsBucket(ordemServicoNota.getCaminhoXml());
    File fileXml = new File();
    fileXml.setFileName("nfs_" + ordemServicoNota.getNumeroNfse() + ".xml");
    fileXml.setType("application/xml");
    fileXml.setBytes(xmlFile);
    fileXml.setInputStream(new ByteArrayInputStream(xmlFile));

    byte[] pdfFile = fileService.readNfsBucket(ordemServicoNota.getCaminhoPdf());
    File filePdf = new File();
    filePdf.setFileName("nfs_" + ordemServicoNota.getNumeroNfse() + ".pdf");
    filePdf.setType("application/pdf");
    filePdf.setBytes(pdfFile);
    filePdf.setInputStream(new ByteArrayInputStream(pdfFile));

    Optional<EmissaoNotaServico> optionalEmissaoNotaServico = emissaoNotaServicoRepository.findByIdEmpresa(ordemServico.getEmpresa().getId());
    optionalEmissaoNotaServico.ifPresentOrElse(
        (EmissaoNotaServico emissaoNotaServico) -> {
          if (Boolean.TRUE.equals(emissaoNotaServico.getEmailNfe())) {
            anexos.add(fileXml);
            anexos.add(filePdf);
          } else {
            log.debug("Envio de XML e NFS desabilitado.");
          }
          if (Boolean.TRUE.equals(emissaoNotaServico.getEmailBoleto())) {
            log.debug("Verificando forma de pagamento para anexos");
            FormaPagamento formaPagamento = get(ordemServico, OrdemServico::getFormaRecebimento);
            if (formaPagamento != null) {
              Integer formaPagamentoId = formaPagamento.getId();
              if (FormaPagamento.ID_FORMA_PAGAMENTO_BOLETO.equals(formaPagamentoId)) {
                log.debug("Adicionando boletos ao email, se houver");
                tituloBoletoService.adicionaBoletosParaEnvioEmail(anexos, ordemServico);
              } else if (FormaPagamento.ID_FORMA_PAGAMENTO_CARNE.equals(formaPagamentoId)) {
                log.debug("Adicionando carnês ao email, se houver");
                tituloBoletoService.adicionarCarnesParaEnvioEmail(anexos, ordemServico);
              }

              String emailBoleto = get(
                ordemServico.getCliente(), Pessoa::getEmailCobranca, String::trim);
              emailBoleto = getOrElse(
                ordemServico.getEmailEnvioCobranca(), String::trim, emailBoleto);
              if (StringUtils.isNotBlank(emailBoleto)) {
                emails.add(emailBoleto);
              }
            }
          } else {
            log.debug("Envio de boletos/carnês desabilitado.");
          }
        },
      () -> log.debug("Cadastro de emissão de nota de serviço não encontrado")
    );

    HashSet<String> cc = new HashSet<>();
    for(var contato : ordemServico.getContatos()) {
      if (StringUtils.isNotBlank(contato.getEmail()) && !emails.contains(contato.getEmail())) {
        cc.add(contato.getEmail());
      }
    }

    DestinatarioEmail notaFiscalDestinatario = new DestinatarioEmail();
    notaFiscalDestinatario.setEmails(emails);
    notaFiscalDestinatario.setAnexos(anexos);
    notaFiscalDestinatario.setCopias(cc.isEmpty() ? null : cc);

    HashMap<String, String> anexosS3 = new HashMap<>();
    anexosS3.put("nfs_" + ordemServicoNota.getNumeroNfse() + ".pdf",
      ordemServicoNota.getCaminhoPdf());
    anexosS3.put("nfs_" + ordemServicoNota.getNumeroNfse() + ".xml",
      ordemServicoNota.getCaminhoXml());

    HashMap<String, Object> data = new HashMap<>();

    String dataEmissao =
      ordemServicoNota.getDataEmissao() != null ? ordemServicoNota.getDataEmissao()
        .format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "";
    String numeroNfe = getOrElse(ordemServicoNota.getNumeroNfse(), "Sem informação de NF-e");

    data.put("nome_empresa_cliente", ordemServicoNota.getRazaoSocial());
    data.put("numero_os", getOrElse(ordemServico.getCodigo(), ordemServico.getId()) + "");
    data.put("numero_nfse", numeroNfe);
    data.put("numero_nfe", numeroNfe);
    data.put("data_emissao", dataEmissao);
    data.put("email_usuario", ordemServico.getUsuario().getEmail());
    data.put("numero_cnpj_cpf", ordemServico.getCliente().getCpfCnpj());
    data.put("nome_empresa", ordemServico.getEmpresa().getNome());
    data.put("chave_acesso", getOrElse(ordemServicoNota.getCodigoVerificacao(), "Sem informação de Chave de Acesso"));
    Optional<TituloValorVencimentoInfo> titulo = tituloRepository.findTitulosValorVencimentoByOrdemServicoId(ordemServico.getId());
    if (titulo.isPresent()) {
      data.put("valor", NumberUtils.formataValor(titulo.get().getValorLiquido()));
      data.put("data_venc", titulo.get().getDataVencimento().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
    }

    optionalEmissaoNotaServico.ifPresent(emissaoNotaServico -> {
      if (Boolean.TRUE.equals(emissaoNotaServico.getEmailBoleto())
        && !Boolean.TRUE.equals(emissaoNotaServico.getEmailRecibo())
        && !Boolean.TRUE.equals(emissaoNotaServico.getEmailNfe()))
      {
        log.debug("Somente a configuração de boleto está ativa, enviando Enviando email de de NFS emitida - somente boleto");
        brevoService.preparaAndSendNotaFiscal(notaFiscalDestinatario, data,
          TEMPLATE_EMAIL_BOLETO_CHEGOU, ordemServico.getEmpresa().getNome());
      } else {
        log.debug("Existem outras configurações ativas, enviando email de NFS emitida");
        brevoService.preparaAndSendNotaFiscal(notaFiscalDestinatario, data,
          TEMPLATE_EMAIL_OS_NFS_CHEGOU, ordemServico.getEmpresa().getNome());
      }
    });

    log.debug("Salvando email enviado");
    log.debug(String.valueOf(notaFiscalDestinatario.getEmails()));
    ordemServicoEmailService.salvarEmailEnviado(ASSUNTO_NF_EMITIDA,
      ordemServicoNota.getOrdemServico().getId(),
      notaFiscalDestinatario, anexosS3);
  }

  private void envioOrdemServicoFaturado(OrdemServico ordemServico) throws IOException {
    byte[] reciboPdf = documentosOsService.createAnexoReciboOsByteArray(ordemServico.getId());
    InputStream reciboPdfInputStream = new ByteArrayInputStream(reciboPdf);

    Set<String> emails = new HashSet<>();
    String emailNfse = get(ordemServico.getCliente(), Pessoa::getEmailNotaFiscal, String::trim);
    emailNfse = getOrElse(ordemServico.getEmailEnvioNfse(), String::trim, emailNfse);
    if (StringUtils.isNotBlank(emailNfse)) {
      emails.add(emailNfse);
    }
    List<File> anexos = new ArrayList<>();

    File filePdf = new File();
    filePdf.setFileName("recibo_" + ordemServico.getNumeroRps() + ".pdf");
    filePdf.setType("application/pdf");
    filePdf.setBytes(reciboPdf);
    filePdf.setInputStream(reciboPdfInputStream);

    Optional<EmissaoNotaServico> optionalEmissaoNotaServico = emissaoNotaServicoRepository.findByIdEmpresa(ordemServico.getEmpresa().getId());
    optionalEmissaoNotaServico.ifPresentOrElse(
        (EmissaoNotaServico emissaoNotaServico) -> {
          if (Boolean.TRUE.equals(emissaoNotaServico.getEmailRecibo())) {
            anexos.add(filePdf);
          } else {
            log.debug("Envio de Recibo desabilitado.");
          }
          if (Boolean.TRUE.equals(emissaoNotaServico.getEmailBoleto())) {
            log.debug("Verificando forma de pagamento para anexos");
            FormaPagamento formaPagamento = getOrElse(ordemServico, OrdemServico::getFormaRecebimento, null);
            if (formaPagamento != null) {
              Integer formaPagamentoId = formaPagamento.getId();
              if (FormaPagamento.ID_FORMA_PAGAMENTO_BOLETO.equals(formaPagamentoId)) {
                log.debug("Adicionando boletos ao email, se houver");
                tituloBoletoService.adicionaBoletosParaEnvioEmail(anexos, ordemServico);
              } else if (FormaPagamento.ID_FORMA_PAGAMENTO_CARNE.equals(formaPagamentoId)) {
                log.debug("Adicionando carnês ao email, se houver");
                tituloBoletoService.adicionarCarnesParaEnvioEmail(anexos, ordemServico);
              }

              String emailBoleto = get(
                ordemServico.getCliente(), Pessoa::getEmailCobranca, String::trim);
              emailBoleto = getOrElse(
                ordemServico.getEmailEnvioCobranca(), String::trim, emailBoleto);
              if (StringUtils.isNotBlank(emailBoleto)) {
                emails.add(emailBoleto);
              }
            }
          } else {
            log.debug("Envio de boletos/carnês desabilitado.");
          }
        },
      () -> log.debug("Cadastro de emissão de nota de serviço não encontrado")
    );

    HashSet<String> cc = new HashSet<>();
    for (var contato : ordemServico.getContatos()) {
      if (StringUtils.isNotBlank(contato.getEmail()) && !emails.contains(contato.getEmail())) {
        cc.add(contato.getEmail());
      }
    }

    DestinatarioEmail notaFiscalDestinatario = new DestinatarioEmail();
    notaFiscalDestinatario.setAnexos(anexos);
    notaFiscalDestinatario.setEmails(emails);
    notaFiscalDestinatario.setCopias(cc.isEmpty() ? null : cc);

    HashMap<String, String> anexosS3 = new HashMap<>();

    String nomeArquivo = "recibo_" + ordemServico.getNumeroRps() + ".pdf";
    String resultAnexo = fileService.write(reciboPdfInputStream, ordemServico.getAplicacao(),
      String.valueOf(ordemServico.getEmpresa().getId()), "email-recibo", nomeArquivo);

    anexosS3.put(filePdf.getFileName(), resultAnexo);

    Map<String, Object> data = new HashMap<>();
    String dataEmissao =
      ordemServico.getDataFaturamentoRps() != null ? ordemServico.getDataFaturamentoRps()
        .format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "";
    Pessoa cliente = ordemServico.getCliente();
    data.put("nome_empresa_cliente", cliente.getNome());
    data.put("numero_os", getOrElse(ordemServico.getCodigo(), ordemServico.getId()) + "");
    data.put("numero_cnpj_cpf", formatCpfCnpj(cliente.getCpfCnpj()));
    data.put("numero_recibo", ordemServico.getNumeroRps() + "");
    data.put("data_emissao", dataEmissao);
    data.put("email_usuario", ordemServico.getUsuario().getEmail());
    data.put("nome_empresa", ordemServico.getEmpresa().getNome());
    var titulo = tituloRepository.findTitulosValorVencimentoByOrdemServicoId(ordemServico.getId());
    if (titulo.isPresent()) {
      data.put("valor", NumberUtils.formataValor(titulo.get().getValorLiquido()));
      data.put("data_venc", titulo.get().getDataVencimento().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
    }

    optionalEmissaoNotaServico.ifPresent(emissaoNotaServico -> {
      if (Boolean.TRUE.equals(emissaoNotaServico.getEmailBoleto())
        && !Boolean.TRUE.equals(emissaoNotaServico.getEmailRecibo())
        && !Boolean.TRUE.equals(emissaoNotaServico.getEmailNfe()))
      {
        log.debug("Somente a configuração de boleto está ativa, enviando Enviando email de Recibo emitido.");
        brevoService.preparaAndSendNotaFiscal(notaFiscalDestinatario, data,
          TEMPLATE_EMAIL_BOLETO_CHEGOU, ordemServico.getEmpresa().getNome());
      } else {
        log.debug("Existem outras configurações ativas, enviando email de Recibo emitido");
        brevoService.preparaAndSendNotaFiscal(notaFiscalDestinatario, data,
          TEMPLATE_EMAIL_OS_RECIBO_CHEGOU, ordemServico.getEmpresa().getNome());
      }
    });

    log.debug("Salvando email enviado");
    ordemServicoEmailService.salvarEmailEnviado(ASSUNTO_RECIBO, ordemServico.getId(),
      notaFiscalDestinatario, anexosS3);
  }

  private void envioPedidoReciboAutorizado(Pedido pedido) {
    var reciboByte = documentosPedidoService.createComprovantePdf(pedido, false, false);

    var filePdf = new File();
    filePdf.setFileName("pedido_" + pedido.getCodigoPedido() + ".pdf");
    filePdf.setType("application/pdf");
    filePdf.setBytes(reciboByte);
    filePdf.setInputStream(new ByteArrayInputStream(reciboByte));

    List<File> anexos = new ArrayList<>();
    boolean isBoletosCobranca = FormaPagamento.ID_FORMA_PAGAMENTO_BOLETO
      .equals(pedido.getFormaRecebimento().getId());

    boolean isCarnesCobranca = FormaPagamento.ID_FORMA_PAGAMENTO_CARNE
      .equals(pedido.getFormaRecebimento().getId());

    Optional<NotaFiscalSerie> optionalNotaFiscalSerie = notaFiscalSerieRepository.findByEmpresaId(pedido.getEmpresa().getId());
    optionalNotaFiscalSerie.ifPresentOrElse(
        serie -> {
          if (Boolean.TRUE.equals(serie.getEmailRecibo())) {
            anexos.add(filePdf);
          } else {
            log.debug("Envio de Recibo desabilitado.");
          }
          if (isBoletosCobranca && Boolean.TRUE.equals(serie.getEmailBoleto())) {
            log.debug("Adicionando boletos ao email, se houver");
            tituloBoletoService.adicionaBoletosParaEnvioEmail(anexos, pedido);
          } else if (isCarnesCobranca && Boolean.TRUE.equals(serie.getEmailBoleto())) {
            log.debug("Adicionando carnês ao email, se houver");
            tituloBoletoService.adicionarCarnesParaEnvioEmail(anexos, pedido);
          } else if (!Boolean.TRUE.equals(serie.getEmailBoleto())) {
            log.debug("Envio de boletos/carnês desabilitado.");
          }
        },
        () -> log.debug("Série de nota fiscal não encontrada")
      );

    Pessoa cliente = pedido.getCliente();
    DestinatarioEmail destinatario = getDestinatarios(pedido, anexos, isBoletosCobranca);

    var emailDuvidas = pedido.getVendedor() != null
      ? pedido.getVendedor().getEmailCobranca()
      : pedido.getUsuario().getEmail();

    var dataEmissao =
      pedido.getDataFaturamento() != null ? pedido.getDataFaturamento()
        .format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) : "";

    var data = new HashMap<String, String>();
    data.put("nome_cliente", getOrElse(cliente.getNomeFantasia(), cliente.getNome()));
    data.put("nome_empresa", getOrElse(pedido.getEmpresa().getNomeFantasia(), pedido.getEmpresa().getNome()));
    data.put("nome_empresa_cliente", cliente.getNome());
    data.put("numero_cnpj_cpf", formatCpfCnpj(cliente.getCpfCnpj()));
    data.put("data_emissao", dataEmissao);
    data.put("email_usuario", emailDuvidas);
    data.put("numero_pedido", String.valueOf(pedido.getCodigoPedido()));
    var titulo = tituloRepository.findTitulosValorVencimentoByPedidoId(pedido.getId());
    if (titulo.isPresent()) {
      data.put("valor", NumberUtils.formataValor(titulo.get().getValorLiquido()));
      data.put("data_venc", titulo.get().getDataVencimento().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
    }
    optionalNotaFiscalSerie.ifPresent(serie -> {
      if (Boolean.TRUE.equals(serie.getEmailBoleto())
        && !Boolean.TRUE.equals(serie.getEmailRecibo())
        && !Boolean.TRUE.equals(serie.getEmailNfe()))
      {
        log.debug("Somente a configuração de boleto está ativa, enviando PedidoReciboFaturadoEmailEvent.");
        pedidoEvent.publishEvent(new PedidoReciboFaturadoEmailEvent(this, destinatario, data, pedido.getEmpresa().getNome()));
      } else {
        log.debug("Existem outras configurações ativas, enviando PedidoEmailEvent.");
        pedidoEvent.publishEvent(new PedidoEmailEvent(this, destinatario, data, pedido.getEmpresa().getNome()));
      }
    });

    var pedidoHistorico = PedidoHistoricoEmailDto.builder()
      .dataHistorico(LocalDateTime.now())
      .assunto(String.format(PEDIDO.getAssuntoEmail(), pedido.getCodigoPedido(),getOrElse(pedido, Pedido::getEmpresa, Empresa::getNome, "")))
      .pedidoId(pedido.getId())
      .remetenteId(pedido.getUsuario().getId())
      .destinatarios(getAllDestinatarios(pedido))
      .tipoEmail(PEDIDO)
      .build();

    pedidoHistoricoEmailService.create(pedidoHistorico.toEntity());
  }
}
