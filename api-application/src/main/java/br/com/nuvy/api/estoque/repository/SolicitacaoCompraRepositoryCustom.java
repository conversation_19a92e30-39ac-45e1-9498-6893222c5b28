package br.com.nuvy.api.estoque.repository;

import br.com.nuvy.api.estoque.repository.specification.SolicitacaoCompraSpecification;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.SolicitacaoCompraListagemDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public interface SolicitacaoCompraRepositoryCustom {

  Page<SolicitacaoCompraListagemDto> findSolicitacaoCompraBuscaLista(SolicitacaoCompraSpecification spec,
    Pageable pageable);

}
