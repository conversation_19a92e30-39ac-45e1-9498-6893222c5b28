package br.com.nuvy.api.cadastro.filter;

import br.com.nuvy.api.enums.TipoCst;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DestinacaoCompraCstCofinsFilter {
  private String id;
  private String descricao;
  private String criterio;
  @NotNull
  private TipoCst tipo;
  private List<String> idsCstCofinsIgnorados;
}
