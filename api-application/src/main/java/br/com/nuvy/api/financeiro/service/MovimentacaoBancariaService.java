package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.config.BadgeContext.getEmpresa;

import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.FormaPagamentoService;
import br.com.nuvy.api.enums.SituacaoMovimentacaoBancaria;
import br.com.nuvy.api.enums.TipoExtratoTitulo;
import br.com.nuvy.api.enums.TipoTransacao;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoDto;
import br.com.nuvy.api.financeiro.dto.FormaPagamentoDto;
import br.com.nuvy.api.financeiro.dto.TranferenciaContasDto;
import br.com.nuvy.api.financeiro.dto.TransferenciaContasResumoDto;
import br.com.nuvy.api.financeiro.filter.ExtratoConciliacaoFilter;
import br.com.nuvy.api.financeiro.filter.LancamentoManualFilter;
import br.com.nuvy.api.financeiro.filter.MovimentacaoBancariaFilter;
import br.com.nuvy.api.financeiro.filter.TransacaoFilter;
import br.com.nuvy.api.financeiro.mapper.LancamentoManualMapper;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancaria;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancariaCentroCusto;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.api.financeiro.model.Transacao;
import br.com.nuvy.api.financeiro.model.TransacaoTransferencia;
import br.com.nuvy.api.financeiro.repository.ExtratoConciliacaoViewRepository;
import br.com.nuvy.api.financeiro.repository.MovimentacaoBancariaRepository;
import br.com.nuvy.api.financeiro.repository.TransacaoRepository;
import br.com.nuvy.api.financeiro.repository.TransacaoTransferenciaRepository;
import br.com.nuvy.api.financeiro.repository.specification.ExtratoConciliacaoSpecification;
import br.com.nuvy.api.financeiro.repository.specification.LancamentoManualSpecification;
import br.com.nuvy.api.financeiro.repository.specification.MovimentacaoBancariaSpecification;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.facade.financeiro.fn3transferenciaentrecontas.model.MovimentacaoBancariaDto;
import br.com.nuvy.facade.financeiro.fn3transferenciaentrecontas.model.TransferenciaInDto;
import br.com.nuvy.facade.financeiro.fn3transferenciaentrecontas.model.TransferenciaOutDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.ExtratoConciliacaoDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.ExtratoSaldo;
import br.com.nuvy.facade.financeiro.fn9lancamentomanual.model.FN9LancamentoManualDtoIn;
import br.com.nuvy.facade.financeiro.fn9lancamentomanual.model.FN9LancamentoManualResumoDto;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Order;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MovimentacaoBancariaService extends
  PageableServiceAdapterDto<MovimentacaoBancaria, MovimentacaoBancariaDto, Integer, MovimentacaoBancariaFilter, MovimentacaoBancariaRepository> {

  private final EmpresaService empresaService;
  private final TransacaoService transacaoService;
  private final ContaBancariaService contaBancariaService;
  private final FormaPagamentoService formaPagamentoService;
  private final TransacaoTransferenciaRepository transferenciaRepository;
  private final TransacaoRepository transacaoRepository;
  private final ExtratoConciliacaoViewRepository extratoConciliacaoViewRepository;
  private final CentroCustoService centroCustoService;

  private final LancamentoManualMapper lancamentoManualMapper;

  @Override
  protected MovimentacaoBancariaSpecification configureSpecification(
    MovimentacaoBancariaFilter filter) {
    return MovimentacaoBancariaSpecification.from(filter);
  }

  public Integer createMovimentacaoBancaria(TransferenciaInDto transferencia) {
    if (
      !contaBancariaService.existsEmpresaIdAndContaBancariaId(
        getEmpresa().getId(), transferencia.getContaDestinoId())
        ||
        !contaBancariaService.existsEmpresaIdAndContaBancariaId(
          getEmpresa().getId(), transferencia.getContaOrigemId())
    ) {
      throw new ResourceNotFoundException("conta.bancaria.nao.encontrada.para.empresa");
    }

    Transacao transacao = transacaoService.createTransacaoEntreContas(transferencia);
    ContaBancaria contaOrigem = contaBancariaService.findById(transferencia.getContaOrigemId())
      .orElseThrow(ResourceNotFoundException::new);

    ContaBancaria contaDestino = contaBancariaService.findById(transferencia.getContaDestinoId())
      .orElseThrow(ResourceNotFoundException::new);

    MovimentacaoBancaria movimentacaoOrigem = criarMovimentacaoDto(transferencia,
      transferencia.getValorTransferencia().negate(),
      contaOrigem, transacao);

    MovimentacaoBancaria movimentacaoDestino = criarMovimentacaoDto(transferencia,
      transferencia.getValorTransferencia(),
      contaDestino, transacao);

    TransacaoTransferencia transacaoTransferencia = new TransacaoTransferencia();
    transacaoTransferencia.setMovimentacaoOrigem(movimentacaoOrigem);
    transacaoTransferencia.setMovimentacaoDestino(movimentacaoDestino);
    transacaoTransferencia.setTransacao(transacaoRepository.getReferenceById(transacao.getId()));
    transferenciaRepository.save(transacaoTransferencia);

    return transacao.getId();
  }

  private MovimentacaoBancaria criarMovimentacaoDto(
    TransferenciaInDto transferencia, BigDecimal valorMovimento,
    ContaBancaria contaBancaria, Transacao transacao
  ) {
    MovimentacaoBancaria movimentacaoBancaria = repository.save(MovimentacaoBancaria.builder()
      .valorMovimento(valorMovimento)
      .dataMovimento(transferencia.getDataTransferencia())
      .numeroDocumento(transferencia.getNumeroDocumento())
      .observacao(transferencia.getObservacao())
      .contaBancaria(contaBancariaService.getReference(contaBancaria.getId()))
      .transacao(transacaoRepository.getReferenceById(transacao.getId()))
      .formaPagamento(get(transferencia.getFormaDeTransacaoId(),
        formaPagamentoService::getReference))
      .build());
    contaBancariaService.atualizaSaldoBanco(contaBancaria.getId(), valorMovimento,
      false, transferencia.getDataTransferencia().toLocalDate());
    return movimentacaoBancaria;
  }

  public TransferenciaOutDto findMovimentacaoBancariaTransferenciaById(
    Integer idMovimentacaoBancariaOrigem) {
    var transferencia = this.transferenciaRepository.findByMovimentacaoOrigemId(
      idMovimentacaoBancariaOrigem).orElseThrow(ResourceNotFoundException::new);

    var movimentoOrigem = transferencia.getMovimentacaoOrigem();
    var movimentoDestino = transferencia.getMovimentacaoDestino();
    this.validaEmpresaContaBancaria(movimentoOrigem, movimentoDestino);

    return TransferenciaOutDto.builder()
      .id(movimentoOrigem.getId())
      .contaOrigem(ContaBancariaResumoDto.from(movimentoOrigem.getContaBancaria()))
      .contaDestino(ContaBancariaResumoDto.from(movimentoDestino.getContaBancaria()))
      .formaDeTransacao(FormaPagamentoDto.from(movimentoOrigem.getFormaPagamento()))
      .dataTransferencia(movimentoOrigem.getDataMovimento())
      .numeroDocumento(movimentoOrigem.getNumeroDocumento())
      .valorTransferencia(transferencia.getTransacao().getValorTransacao())
      .observacao(movimentoOrigem.getObservacao())

      .build();
  }

  public Page<TransferenciaContasResumoDto> findTransferencia(
    TransacaoFilter filter, Pageable pageable
  ) {
    var empresaCurrent = empresaService.findById(getEmpresa().getId())
      .orElseThrow(ResourceNotFoundException::new);
    var contasBancariasByEmpresa = contaBancariaService.findAllContaCorrenteByEmpresa(
      empresaCurrent);

    var movimentosResumo = new ArrayList<TransferenciaContasResumoDto>();
    var ptransacoes = this.transacaoService.findAll(filter, this.toPageable(pageable));
    var transacoes = ptransacoes.getContent();

    for (Transacao umaTransacao : transacoes) {
      var transferencia = umaTransacao.getTransferencia();
      var movimentacaoOrigem = transferencia.getMovimentacaoOrigem();
      var movimentacaoDestino = transferencia.getMovimentacaoDestino();

      Predicate<ContaBancaria> filtroEmpresaContaBancaria =
        item -> (item.getId().equals(movimentacaoOrigem.getContaBancaria().getId())
          || item.getId().equals(movimentacaoDestino.getContaBancaria().getId()));

      Optional<ContaBancaria> contaBancariaMatchOpt = contasBancariasByEmpresa.stream()
        .filter(filtroEmpresaContaBancaria).findFirst();

      if (contaBancariaMatchOpt.isPresent()) {
        var transferenciaResumo = new TransferenciaContasResumoDto();
        transferenciaResumo.setId(movimentacaoOrigem.getId());
        transferenciaResumo.setValor(umaTransacao.getValorTransacao());
        transferenciaResumo.setData(movimentacaoOrigem.getDataMovimento());

        transferenciaResumo.setContaOrigem(
          TranferenciaContasDto.from(movimentacaoOrigem.getContaBancaria()));
        transferenciaResumo.setContaDestino(
          TranferenciaContasDto.from(movimentacaoDestino.getContaBancaria()));
        movimentosResumo.add(transferenciaResumo);
      }
    }
    return new PageImpl<>(movimentosResumo, pageable, ptransacoes.getTotalElements());
  }

  private void validaEmpresaContaBancaria(MovimentacaoBancaria origem,
    MovimentacaoBancaria destino) {
    var empresaCurrent = empresaService.findById(getEmpresa().getId())
      .orElseThrow(ResourceNotFoundException::new);
    var contasBancariasByEmpresa = contaBancariaService.findAllContaCorrenteByEmpresa(
      empresaCurrent);

    Predicate<ContaBancaria> filtroEmpresaContaBancaria =
      item -> (item.getId().equals(origem.getContaBancaria().getId())
        || item.getId().equals(destino.getContaBancaria().getId()));

    var contaBancariaEmpresaMatchOpt = contasBancariasByEmpresa.stream()
      .filter(filtroEmpresaContaBancaria).findFirst();
    if (contaBancariaEmpresaMatchOpt.isEmpty()) {
      throw new ResourceNotFoundException("movimentacao.nao.encontrada");
    }
  }

  public void updateMovimentacaoTransferenciaBancaria(Integer idMovimentacaoBancariaOrigem,
    TransferenciaInDto payload) {
    Boolean deletarMovimentacao = false;
    var transferencia = this.transferenciaRepository.findByMovimentacaoOrigemId(
      idMovimentacaoBancariaOrigem).orElseThrow(ResourceNotFoundException::new);

    var formaPagamento = this.formaPagamentoService.findById(payload.getFormaDeTransacaoId())
      .orElseThrow(ResourceNotFoundException::new);

    var movimentoOrigem = transferencia.getMovimentacaoOrigem();
    var movimentoDestino = transferencia.getMovimentacaoDestino();
    var transacao = transferencia.getTransacao();
    this.validaEmpresaContaBancaria(movimentoOrigem, movimentoDestino);

    boolean isContaBancariaOrigemEquals = payload.getContaOrigemId()
      .equals(movimentoOrigem.getContaBancaria().getId());
    boolean isContaBancariaDestinoEquals = payload.getContaDestinoId()
      .equals(movimentoDestino.getContaBancaria().getId());
    boolean isMudouValorTransacao =
      transferencia.getTransacao().getValorTransacao().compareTo(payload.getValorTransferencia())
        != 0;

    if (isMudouValorTransacao) {
      this.contaBancariaService.atualizaSaldoBanco(movimentoOrigem.getContaBancaria().getId(),
        transacao.getValorTransacao(), deletarMovimentacao,
        transacao.getDataTransacao().toLocalDate());
      this.contaBancariaService.atualizaSaldoBanco(movimentoDestino.getContaBancaria().getId(),
        transacao.getValorTransacao().negate(), deletarMovimentacao,
        transacao.getDataTransacao().toLocalDate());

      if (isContaBancariaOrigemEquals) {
        this.contaBancariaService.atualizaSaldoBanco(movimentoOrigem.getContaBancaria().getId(),
          payload.getValorTransferencia().negate(), deletarMovimentacao,
          transacao.getDataTransacao().toLocalDate());
      }
      if (isContaBancariaDestinoEquals) {
        this.contaBancariaService.atualizaSaldoBanco(movimentoDestino.getContaBancaria().getId(),
          payload.getValorTransferencia(), deletarMovimentacao,
          transacao.getDataTransacao().toLocalDate());
      }
    }

    if (!isContaBancariaOrigemEquals) {
      var contaBancaria = contaBancariaService.findById(payload.getContaOrigemId())
        .orElseThrow(ResourceNotFoundException::new);
      if (!isMudouValorTransacao) {
        this.contaBancariaService.atualizaSaldoBanco(movimentoOrigem.getContaBancaria().getId(),
          movimentoDestino.getValorMovimento(), deletarMovimentacao,
          movimentoDestino.getDataMovimento().toLocalDate());
      }

      movimentoOrigem.setContaBancaria(contaBancaria);
      this.contaBancariaService.atualizaSaldoBanco(movimentoOrigem.getContaBancaria().getId(),
        payload.getValorTransferencia().negate(), deletarMovimentacao,
        transacao.getDataTransacao().toLocalDate());
    }

    if (!isContaBancariaDestinoEquals) {
      var contaBancaria = contaBancariaService.findById(payload.getContaDestinoId())
        .orElseThrow(ResourceNotFoundException::new);
      if (!isMudouValorTransacao) {
        this.contaBancariaService.atualizaSaldoBanco(movimentoDestino.getContaBancaria().getId(),
          movimentoDestino.getValorMovimento().negate(), deletarMovimentacao,
          movimentoDestino.getDataMovimento().toLocalDate());
      }

      movimentoDestino.setContaBancaria(contaBancaria);
      this.contaBancariaService.atualizaSaldoBanco(movimentoDestino.getContaBancaria().getId(),
        payload.getValorTransferencia(), deletarMovimentacao,
        transacao.getDataTransacao().toLocalDate());
    }

    movimentoOrigem.setFormaPagamento(formaPagamento);
    movimentoOrigem.setObservacao(payload.getObservacao());
    movimentoOrigem.setDataMovimento(payload.getDataTransferencia());
    movimentoOrigem.setNumeroDocumento(payload.getNumeroDocumento());
    movimentoOrigem.setValorMovimento(payload.getValorTransferencia().negate());

    movimentoDestino.setFormaPagamento(formaPagamento);
    movimentoDestino.setObservacao(payload.getObservacao());
    movimentoDestino.setDataMovimento(payload.getDataTransferencia());
    movimentoDestino.setNumeroDocumento(payload.getNumeroDocumento());
    movimentoDestino.setValorMovimento(payload.getValorTransferencia());

    transacao.setValorTransacao(payload.getValorTransferencia());
  }

  public void deleteMovimentacaoTransferenciaBancaria(Integer idMovimentacaoContaBancaria) {
    Boolean deletarMovimentacao = true;
    var transferencia = this.transferenciaRepository.findByMovimentacaoOrigemId(
      idMovimentacaoContaBancaria).orElseThrow(ResourceNotFoundException::new);

    var movimentacaoOrigem = transferencia.getMovimentacaoOrigem();
    var movimentacaoDestino = transferencia.getMovimentacaoDestino();

    this.validaEmpresaContaBancaria(movimentacaoOrigem, movimentacaoDestino);

    this.contaBancariaService.atualizaSaldoBanco(movimentacaoOrigem.getContaBancaria().getId(),
      transferencia.getTransacao().getValorTransacao(), deletarMovimentacao,
      transferencia.getTransacao().getDataTransacao().toLocalDate());
    this.contaBancariaService.atualizaSaldoBanco(movimentacaoDestino.getContaBancaria().getId(),
      transferencia.getTransacao().getValorTransacao().negate(), deletarMovimentacao,
      transferencia.getTransacao().getDataTransacao().toLocalDate());

    this.transferenciaRepository.delete(transferencia);
    this.repository.delete(movimentacaoOrigem);
    this.repository.delete(movimentacaoDestino);
    this.transacaoRepository.delete(transferencia.getTransacao());
  }

  private Pageable toPageable(Pageable pageable) {
    if (pageable.getSort().isSorted()) {
      Map<String, String> propertys = new HashMap<>();
      propertys.put("contaBancariaOrigem", "transferencia.movimentacaoOrigem.contaBancaria.nome");
      propertys.put("contaBancariaDestino", "transferencia.movimentacaoDestino.contaBancaria.nome");
      propertys.put("dataMovimento", "transferencia.movimentacaoOrigem.dataMovimento");
      propertys.put("valor", "valorTransacao");
      Optional<Order> order = pageable.getSort().get().findFirst();
      if (order.isPresent() && (StringUtils.isNotBlank(propertys.get(order.get().getProperty())))) {
        pageable = ((PageRequest) pageable).withSort(order.get().getDirection(),
          propertys.get(order.get().getProperty()));
      }
    }
    return pageable;
  }

  public List<MovimentacaoBancaria> findByTituloBaixaAndTransacao(BigDecimal valorBaixa,
    LocalDateTime dataBaixa, ContaBancaria contaBancaria, Transacao transacao,
    FormaPagamento formaPagamento) {
    var filter = new MovimentacaoBancariaFilter();
    filter.setValorMovimento(valorBaixa);
    filter.setDataMovimento(dataBaixa);
    filter.setContaBancariaId(contaBancaria.getId());
    filter.setTransacaoId(transacao.getId());
    filter.setFormaPagamentoId(formaPagamento.getId());
    return find(filter);
  }

  public Page<ExtratoConciliacaoDto> findExtratoConciliacao(
    ExtratoConciliacaoFilter filter, Pageable pageable
  ) {
    filter.setTipo(Arrays.asList(
      TipoExtratoTitulo.DESPESA,
      TipoExtratoTitulo.RECEITA,
      TipoExtratoTitulo.TRANSFERENCIA_CONTAS,
      TipoExtratoTitulo.SALDO_INICIAL,
      TipoExtratoTitulo.LANCAMENTO_MANUAL
    ));
    filter.setEmpresaId(getEmpresa().getId());
    ExtratoConciliacaoSpecification specification = new ExtratoConciliacaoSpecification(filter);
    return extratoConciliacaoViewRepository.findAll(specification, toPageableExtrato(pageable))
      .map(ExtratoConciliacaoDto::from);
  }

  public void conciliacaoExtrato(MovimentacaoBancaria movimentacaoBancaria) {
    movimentacaoBancaria.setSituacao(SituacaoMovimentacaoBancaria.CONCILIADO);
    repository.save(movimentacaoBancaria);
  }

  public void desconciliacaoExtrato(MovimentacaoBancaria movimentacaoBancaria) {
    movimentacaoBancaria.setSituacao(SituacaoMovimentacaoBancaria.NAO_CONCILIADO);
    movimentacaoBancaria.setFitId(null);
    movimentacaoBancaria.setMemo(null);
    repository.save(movimentacaoBancaria);
  }

  public void conciliacaoTransferenciaPorUploadDeExtrato(
    MovimentacaoBancaria movimentacaoBancaria,
    LocalDateTime dataTransferencia,
    String fitId, String memo
  ) {
    movimentacaoBancaria.setFitId(fitId);
    movimentacaoBancaria.setMemo(memo);
    movimentacaoBancaria.setSituacao(SituacaoMovimentacaoBancaria.CONCILIADO);
    if (Objects.nonNull(dataTransferencia)) {
      movimentacaoBancaria.setDataMovimento(dataTransferencia);
    }
    repository.save(movimentacaoBancaria);
  }

  public ExtratoSaldo findExtratoSaldo(MovimentacaoBancariaFilter filter) {
    filter.setTipoTransacao(List.of(
      TipoTransacao.BAIXA,
      TipoTransacao.SALDO_INICIAL,
      TipoTransacao.TRANSFERENCIA_CONTAS,
      TipoTransacao.LANCAMENTO_MANUAL
    ));
    var movimentacoes = repository.findAll(
      MovimentacaoBancariaSpecification.from(filter));

    BigDecimal valorSaldo = movimentacoes.stream()
      .filter(movimentacao -> {
        var tituloBaixas = movimentacao.getTransacao().getTituloBaixas();
        return tituloBaixas == null || tituloBaixas.isEmpty() ||
          tituloBaixas.stream()
            .allMatch(tituloBaixa -> Boolean.FALSE.equals(
              tituloBaixa.getTitulo().getIndTituloExcluido()));
      })
      .map(MovimentacaoBancaria::getValorMovimento)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return ExtratoSaldo.builder()
      .saldo(valorSaldo)
      .build();
  }

  public MovimentacaoBancaria findByTransacaoId(Integer transacaoId) {
    return repository.findByTransacaoId(transacaoId);
  }

  public MovimentacaoBancaria realizaLancamentoManual(FN9LancamentoManualDtoIn payload) {
    if (payload.getDataLancamento().isAfter(LocalDate.now())) {
      throw new PreconditionException("A data de lançamento não pode ser maior que a data atual");
    }

    var contaBancaria = contaBancariaService.findById(payload.getContaBancariaId())
      .orElseThrow(ResourceNotFoundException::new);
    var transacao = transacaoService.create(TipoTransacao.LANCAMENTO_MANUAL);

    List<MovimentacaoBancariaCentroCusto> centrosCusto = Optional.ofNullable(
        payload.getCentrosCusto())
      .orElse(Collections.emptyList())
      .stream()
      .map(dto -> MovimentacaoBancariaCentroCusto.builder()
        .percentualRateio(dto.getPercentualRateio())
        .centroCusto(centroCustoService.findById(dto.getCentroCustoId())
          .orElseThrow(ResourceNotFoundException::new))
        .build())
      .collect(Collectors.toList());

    var movimentacao = MovimentacaoBancaria.builder()
      .transacao(transacao.toEntity())
      .valorMovimento(payload.getValorLancamento())
      .dataMovimento(payload.getDataLancamento().atStartOfDay())
      .numeroDocumento(payload.getNumeroDocumento())
      .observacao(payload.getObservacao())
      .contaBancaria(contaBancaria)
      .planoConta(PlanoConta.of(payload.getTipoReceitaDespesaId()))
      .centrosCusto(centrosCusto.isEmpty() ? null : centrosCusto)
      .build();
    if (!centrosCusto.isEmpty()) {
      centrosCusto.forEach(cc -> cc.setMovimentacaoBancaria(movimentacao));
    }
    repository.save(movimentacao);

    contaBancariaService.atualizaSaldoBanco(payload.getContaBancariaId(),
      movimentacao.getValorMovimento(), false, true, payload.getDataLancamento(), false);

    return movimentacao;
  }

  public Page<FN9LancamentoManualResumoDto> buscaLancamentosManuais(
    LancamentoManualFilter filter, Pageable pageable
  ) {
    LancamentoManualSpecification specification = new LancamentoManualSpecification(filter);
    filter.setEmpresaId(getEmpresa().getId());
    filter.setTipoTransacao(TipoTransacao.LANCAMENTO_MANUAL);
    return repository.findAll(specification, toPageableLancamentoManual(pageable))
      .map(lancamentoManualMapper::toDto);
  }

  public FN9LancamentoManualResumoDto buscaLancamentosManuaisPorId(Integer idLancamento) {
    return repository.findById(idLancamento).map(lancamentoManualMapper::toDto)
      .orElseThrow(() -> new ResourceNotFoundException("Lançamento manual não encontrado"));
  }

  public void atualizarLancamentoManual(Integer lancamentoManualId,
    FN9LancamentoManualDtoIn payload) {
    log.debug("Atualizando lançamento manual com id {}", lancamentoManualId);

    var movimentacao = repository.findById(lancamentoManualId)
      .orElseThrow(() -> new ResourceNotFoundException("Lançamento manual não encontrado"));

    if (payload.getDataLancamento().isAfter(LocalDate.now())) {
      throw new PreconditionException("A data de lançamento não pode ser maior que a data atual");
    }

    boolean isContaBancariaEquals = payload.getContaBancariaId()
      .equals(movimentacao.getContaBancaria().getId());
    boolean isDataMovimentoEquals = payload.getDataLancamento()
      .isEqual(movimentacao.getDataMovimento().toLocalDate());
    boolean isMudouValorTransacao =
      movimentacao.getValorMovimento().compareTo(payload.getValorLancamento())
        != 0;

    LocalDate verificaAlteracaoData = isDataMovimentoEquals
      ? movimentacao.getDataMovimento().toLocalDate()
      : payload.getDataLancamento();

    if (isMudouValorTransacao) {
      this.contaBancariaService.atualizaSaldoBanco(movimentacao.getContaBancaria().getId(),
        movimentacao.getValorMovimento().negate(), false,
        movimentacao.getDataMovimento().toLocalDate());

      if (isContaBancariaEquals) {
        this.contaBancariaService.atualizaSaldoBanco(movimentacao.getContaBancaria().getId(),
          payload.getValorLancamento(), false, verificaAlteracaoData);
      }
    }

    if (!isContaBancariaEquals) {
      var contaBancaria = contaBancariaService.findById(payload.getContaBancariaId())
        .orElseThrow(ResourceNotFoundException::new);
      if (!isMudouValorTransacao) {
        this.contaBancariaService.atualizaSaldoBanco(movimentacao.getContaBancaria().getId(),
          movimentacao.getValorMovimento().negate(), false,
          movimentacao.getDataMovimento().toLocalDate());
      }

      movimentacao.setContaBancaria(contaBancaria);
      this.contaBancariaService.atualizaSaldoBanco(movimentacao.getContaBancaria().getId(),
        payload.getValorLancamento(), true, verificaAlteracaoData);
    }

    movimentacao.setValorMovimento(payload.getValorLancamento());
    movimentacao.setDataMovimento(payload.getDataLancamento().atStartOfDay());
    movimentacao.setNumeroDocumento(payload.getNumeroDocumento());
    movimentacao.setObservacao(payload.getObservacao());
    movimentacao.setPlanoConta(PlanoConta.of(payload.getTipoReceitaDespesaId()));
    movimentacao.getTransacao().setValorTransacao(payload.getValorLancamento());

    repository.save(movimentacao);
  }

  public void excluirLancamentoManual(Integer lancamentoManualId) {
    log.debug("Excluindo lançamento manual com id {}", lancamentoManualId);

    var movimentacao = repository.findById(lancamentoManualId)
      .orElseThrow(() -> new ResourceNotFoundException("Lançamento manual não encontrado"));

    this.contaBancariaService.atualizaSaldoBanco(movimentacao.getContaBancaria().getId(),
      movimentacao.getValorMovimento().negate(), true,
      movimentacao.getDataMovimento().toLocalDate());

    this.repository.delete(movimentacao);
    this.transacaoRepository.delete(movimentacao.getTransacao());
  }

  public Pageable toPageableExtrato(Pageable pageable) {
    if (pageable.getSort().isSorted()) {
      Map<String, String> propertys = new HashMap<>();
      propertys.put("valorBaixa", "valorBaixa");
      propertys.put("tipo", "tipo");
      propertys.put("dataBaixa", "dataBaixa");
      propertys.put("status", "status");
      propertys.put("bancoId", "bancoId");
      propertys.put("bancoCodigo", "bancoCodigo");
      propertys.put("bancoNome", "bancoNome");
      propertys.put("parceiroId", "parceiroId");
      propertys.put("parceiroNome", "parceiroNome");
      propertys.put("parceiroCpfCnpj", "parceiroCpfCnpj");
      propertys.put("dataVencimento", "vencimento");
      Optional<Order> order = pageable.getSort().get().findFirst();
      if (order.isPresent() && (StringUtils.isNotBlank(propertys.get(order.get().getProperty())))) {
        pageable = ((PageRequest) pageable).withSort(order.get().getDirection(),
          propertys.get(order.get().getProperty()));
      }
    }
    return pageable;
  }

  public Pageable toPageableLancamentoManual(Pageable pageable) {
    if (pageable.getSort().isSorted()) {
      Map<String, String> propertys = new HashMap<>();
      propertys.put("lancamentoManualId", "id");
      propertys.put("contaBancariaId", "contaBancaria.id");
      propertys.put("contaBancariaNome", "contaBancaria.nome");
      propertys.put("valorLancamento", "valorMovimento");
      propertys.put("dataLancamento", "dataMovimento");
      propertys.put("numeroDocumento", "numeroDocumento");
      propertys.put("observacao", "observacao");
      propertys.put("tipoReceitaDespesaId", "planoConta.id");
      propertys.put("tipoReceitaDespesaDescricao", "planoConta.nome");
      propertys.put("tipoPlanoConta", "planoConta.tipo");
      Optional<Order> order = pageable.getSort().get().findFirst();
      if (order.isPresent() && (StringUtils.isNotBlank(propertys.get(order.get().getProperty())))) {
        pageable = ((PageRequest) pageable).withSort(order.get().getDirection(),
          propertys.get(order.get().getProperty()));
      }
    }
    return pageable;
  }

  public void updateSituacao(Integer id, SituacaoMovimentacaoBancaria situacao) {
    repository.updateSituacao(id, situacao);
  }
}