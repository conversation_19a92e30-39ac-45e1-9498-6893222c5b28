package br.com.nuvy.api.adaptadores.entrada.evento.integracao.assas;

import br.com.nuvy.api.financeiro.event.titulo.TituloAlterado;
import br.com.nuvy.api.financeiro.event.titulo.TituloBoletoCancelamentoSolicitado;
import br.com.nuvy.api.financeiro.event.titulo.TituloInserido;
import br.com.nuvy.integracao.assas.aplicacao.comando.EnfileiraEnvioBoletoAssas;
import br.com.nuvy.integracao.assas.aplicacao.comando.EnfileiraExclusaoBoletoAssas;
import br.com.nuvy.integracao.assas.aplicacao.processador.ProcessadorBoletoAssas;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
class TituloEntradaEventoAssas {

  private final Logger log = LoggerFactory.getLogger(TituloEntradaEventoAssas.class);

  private final ProcessadorBoletoAssas processadorBoleto;

  TituloEntradaEventoAssas(
    ProcessadorBoletoAssas processadorBoleto
  ) {
    this.processadorBoleto = processadorBoleto;
  }

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(TituloInserido evento) {
    processadorBoleto.processaComando(new EnfileiraEnvioBoletoAssas(
      UUID.randomUUID(), evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdTitulo(), evento.getOcorridoAs()
    ));
  }

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(TituloAlterado evento) {
    processadorBoleto.processaComando(new EnfileiraEnvioBoletoAssas(
      UUID.randomUUID(), evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdTitulo(), evento.getOcorridoAs()
    ));
  }

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(TituloBoletoCancelamentoSolicitado evento) {
    processadorBoleto.processaComando(new EnfileiraExclusaoBoletoAssas(
      UUID.randomUUID(), evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdTitulo(), evento.getOcorridoAs()
    ));
  }
}
