package br.com.nuvy.api.venda.service;

import static br.com.nuvy.api.enums.AcaoHistorico.NF_INUTILIZADA;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.config.BadgeContext.getEmpresa;
import static br.com.nuvy.config.BadgeContext.getUsuario;

import br.com.nuvy.api.cadastro.importacaonfe.models.retInutNFe;
import br.com.nuvy.api.cadastro.importacaonfe.service.ImportacaoNfeService;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.enums.SituacaoNotaEntrada;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.StatusInutilizacao;
import br.com.nuvy.api.enums.TipoNotaFiscal;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaHistorico;
import br.com.nuvy.api.estoque.repository.NotaFiscalEntradaRepository;
import br.com.nuvy.api.estoque.service.MovimentoEstoquePorPedidoService;
import br.com.nuvy.api.estoque.service.NotaFiscalEntradaHistoricoService;
import br.com.nuvy.api.estoque.service.NotaFiscalEntradaSefazService;
import br.com.nuvy.api.notafiscal.model.NotaFiscalSerie;
import br.com.nuvy.api.notafiscal.service.NotaFiscalSerieService;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.sqs.dto.InutilizacaoSqs;
import br.com.nuvy.api.sqs.dto.InutilizacaoSqs.Documento;
import br.com.nuvy.api.sqs.dto.InutilizacaoSqs.Identificacao;
import br.com.nuvy.api.venda.model.Inutilizacao;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoHistorico;
import br.com.nuvy.api.venda.repository.InutilizacaoRepository;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.common.exception.BusinessException;
import br.com.nuvy.config.AmazonS3Service;
import br.com.nuvy.facade.estoque.eq5outrasnotas.model.InutilizacaoNotaCompraInDto;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.InutilizacaoDto;
import br.com.nuvy.facade.venda.vd5outrasnotas.model.InutilizacaoPedidoInDto;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class InutilizacaoService {

  private final InutilizacaoRepository repository;
  private final NotaFiscalSerieService notaFiscalConfig;
  private final PedidoRepository pedidoRepository;
  private final MovimentoEstoquePorPedidoService movimentoEstoquePorPedidoService;
  private final AmazonS3Service amazonS3Service;
  private final PedidoHistoricoService pedidoHistoricoService;
  private final FileService fileService;
  private final EmpresaService empresaService;
  private final ImportacaoNfeService importacaoNfeService;
  private final NotaFiscalEntradaRepository notaFiscalEntradaRepository;
  private final NotaFiscalEntradaHistoricoService notaEntradaHistoricoService;
  private final NotaFiscalEntradaSefazService notaFiscalEntradaSefazService;

  public InutilizacaoDto obter(UUID inutilizacaoId) {
    InutilizacaoDto inutilizacaoDto = InutilizacaoDto.from(
      repository.required(inutilizacaoId, "inutilizacao.nao.encontrada"));
    String url = amazonS3Service.generateSignedUrl(inutilizacaoDto.getCaminhoXml());
    inutilizacaoDto.setCaminhoXml(url);
    return inutilizacaoDto;
  }

  public Inutilizacao inserir(InutilizacaoPedidoInDto payload) {
    Empresa empresa = empresaService.required(getEmpresa().getId());
    NotaFiscalSerie configNfe = notaFiscalConfig.findNotaFiscalByEmpresaId(empresa.getId());

    validaRangeNumerosAInutilizar(
      payload.getNumeroInicial(), payload.getNumeroFinal(),
      payload.getSerieNota(), configNfe);

    Inutilizacao inutilizacao = new Inutilizacao();
    inutilizacao.setStatus(StatusInutilizacao.PENDENTE);
    inutilizacao.setMotivo(payload.getMotivo().trim());
    inutilizacao.setNumeroInicial(payload.getNumeroInicial());
    inutilizacao.setNumeroFinal(payload.getNumeroFinal());
    inutilizacao.setAmbiente(configNfe.getAmbiente());
    inutilizacao.setDataHoraRegistro(LocalDateTime.now());
    inutilizacao.setUsuario(getUsuario());
    inutilizacao.setEmpresa(empresa);
    inutilizacao.setSerieNota(payload.getSerieNota());

    boolean isNumeroInicialIgualNumeroFinal =
      payload.getNumeroInicial().compareTo(payload.getNumeroFinal()) == 0;

    if (payload.getPedidoId() != null) {
      pedidoRepository.findById(payload.getPedidoId())
        .ifPresentOrElse(inutilizacao::setPedido,
          () -> {
            if (isNumeroInicialIgualNumeroFinal) {
              pedidoRepository.findFirstBySerieNotaFiscalAndNumeroNotaFiscalAndEmpresa(
                payload.getSerieNota(), payload.getNumeroInicial(), empresa)
                .ifPresent(inutilizacao::setPedido);
            }
          });
    } else {
      if (isNumeroInicialIgualNumeroFinal) {
        pedidoRepository.findFirstBySerieNotaFiscalAndNumeroNotaFiscalAndEmpresa(
            payload.getSerieNota(), payload.getNumeroInicial(), empresa)
          .ifPresent(inutilizacao::setPedido);
      }
    }

    if (inutilizacao.getPedido() != null) {
      Pedido pedido = inutilizacao.getPedido();
      if (!pedido.getSituacao().equals(SituacaoPedido.NF_REJEITADA)) {
        throw new BusinessException("inutilizacao.nota.somente.rejeitada");
      }
      pedido.setSituacao(SituacaoPedido.NF_INUTILIZADA);
      inutilizacao.setSerieNota(pedido.getSerieNotaFiscal());
      pedidoRepository.save(pedido);
    }

    validaRangeUtilizadoEmPedidosENotas(
      payload.getNumeroInicial(), payload.getNumeroFinal(),
      payload.getSerieNota(), payload.getPedidoId());

    repository.save(inutilizacao);
    return inutilizacao;
  }

  public Inutilizacao inserir(InutilizacaoNotaCompraInDto payload) {
    Empresa empresa = empresaService.required(getEmpresa().getId());
    NotaFiscalSerie configNfe = notaFiscalConfig.findNotaFiscalByEmpresaId(empresa.getId());

    validaRangeNumerosAInutilizar(
      payload.getNumeroInicial(), payload.getNumeroFinal(),
      payload.getSerieNota(), configNfe);

    Inutilizacao inutilizacao = new Inutilizacao();
    inutilizacao.setStatus(StatusInutilizacao.PENDENTE);
    inutilizacao.setMotivo(payload.getMotivo());
    inutilizacao.setNumeroInicial(payload.getNumeroInicial());
    inutilizacao.setNumeroFinal(payload.getNumeroFinal());
    inutilizacao.setAmbiente(configNfe.getAmbiente());
    inutilizacao.setDataHoraRegistro(LocalDateTime.now());
    inutilizacao.setUsuario(getUsuario());
    inutilizacao.setEmpresa(empresa);
    inutilizacao.setSerieNota(payload.getSerieNota());

    boolean isNumeroInicialIgualNumeroFinal =
      payload.getNumeroInicial().compareTo(payload.getNumeroFinal()) == 0;

    if (payload.getNotaFiscalId() != null) {
      notaFiscalEntradaRepository.findById(payload.getNotaFiscalId())
        .ifPresentOrElse(inutilizacao::setNotaFiscalEntrada,
          () -> {
            if (isNumeroInicialIgualNumeroFinal) {
              notaFiscalEntradaRepository.findBySerieNotaFiscalAndNumeroNotaAndEmpresa(
                payload.getSerieNota(), payload.getNumeroInicial().toString(), empresa)
                .ifPresent(inutilizacao::setNotaFiscalEntrada);
            }
          });
    } else {
      if (isNumeroInicialIgualNumeroFinal) {
        notaFiscalEntradaRepository.findBySerieNotaFiscalAndNumeroNotaAndEmpresa(
            payload.getSerieNota(), payload.getNumeroInicial().toString(), empresa)
          .ifPresent(inutilizacao::setNotaFiscalEntrada);
      }
    }

    if (inutilizacao.getNotaFiscalEntrada() != null) {
      NotaFiscalEntrada notaFiscalEntrada = inutilizacao.getNotaFiscalEntrada();
      if (!notaFiscalEntrada.getSituacao().equals(SituacaoNotaEntrada.NF_REJEITADA)) {
        throw new BusinessException("inutilizacao.nota.somente.rejeitada");
      }
      notaFiscalEntrada.setSituacao(SituacaoNotaEntrada.NF_INUTILIZADA);
      inutilizacao.setSerieNota(notaFiscalEntrada.getSerieNotaFiscal());
      notaFiscalEntradaRepository.save(notaFiscalEntrada);
    }

    validaRangeUtilizadoEmPedidosENotas(
      payload.getNumeroInicial(), payload.getNumeroFinal(),
      payload.getSerieNota(), payload.getNotaFiscalId());

    repository.save(inutilizacao);
    return inutilizacao;
  }

  private void validaRangeUtilizadoEmPedidosENotas(
    Integer numeroInicial, Integer numeroFinal,
    String serie, UUID uuid
  ) {
    boolean validaNotas = notaFiscalEntradaRepository.existsByFaixaNumerosAndSerie(
      serie, numeroInicial.toString(), numeroFinal.toString(), TipoNotaFiscal.DEVOLUCAO, uuid);
    boolean validaPedidos = pedidoRepository.existsByFaixaNumerosAndSerie(
      serie, numeroInicial, numeroFinal, uuid);
    if (validaPedidos || validaNotas) {
      throw new BusinessException("inutilizacao.numeros.utilizados");
    }
  }

  private static void validaRangeNumerosAInutilizar(
    Integer numeroInicial, Integer numeroFinal,
    String serie, NotaFiscalSerie configNfe
  ) {
    if (numeroInicial.compareTo(0) <= 0) {
      throw new BusinessException("numero.inicial.nota.maior.zero");
    }
    if (numeroFinal.compareTo(0) <= 0) {
      throw new BusinessException("numero.final.nota.maior.zero");
    }
    if (numeroInicial.compareTo(numeroFinal) > 0) {
      throw new BusinessException("numero.final.nota.maior.igual.numero.inicial");
    }
    if (configNfe.getSerie().equals(serie)) {
      if (numeroFinal.compareTo(configNfe.getNumero()) > 0) {
        throw new BusinessException("inutilizacao.numeros.ainda.nao.utilizados");
      }
    }
  }

  @Transactional
  public void confirmar(InutilizacaoSqs request) {
    log.info("confirmando inutilizacao: {}", request.getInutilizacao().getInutilizacaoId());
    Identificacao identificacao = request.getInutilizacao();
    var uriDocumento = getOrElse(request.getDocumento(), Documento::getUri, "");

    UUID inutilizacaoId = UUID.fromString(identificacao.getInutilizacaoId());
    log.info("Id inutilizacao: {}", inutilizacaoId);

    repository.findById(inutilizacaoId).ifPresentOrElse(inutilizacao -> {
      inutilizacao.setStatus(identificacao.getStatus());
      inutilizacao.setCaminhoXml(uriDocumento);
      inutilizacao.setMotivoRetorno(identificacao.getMotivoRetorno());

      if (identificacao.getStatus().equals(StatusInutilizacao.TRANSMITIDO)) {
        Pedido pedido = inutilizacao.getPedido();
        if (pedido != null) {
          movimentoEstoquePorPedidoService.estornaEstoque(pedido);
          criarHistoricoInutilizacaoPedido(inutilizacao);
          if (StringUtils.isNotEmpty(inutilizacao.getCaminhoXml())) {
            pedidoRepository.alterarCaminhoXmlInutilizacao(pedido.getId(),
              inutilizacao.getCaminhoXml());
          }
        } else {
          NotaFiscalEntrada notaFiscalEntrada = inutilizacao.getNotaFiscalEntrada();
          if (notaFiscalEntrada != null) {
            notaFiscalEntradaSefazService.geraEstornoEstoqueReflexo(notaFiscalEntrada);
            criarHistoricoInutilizacaoNotaEntrada(inutilizacao);
            if (StringUtils.isNotEmpty(inutilizacao.getCaminhoXml())) {
              notaFiscalEntradaRepository.alterarCaminhoXmlInutilizacao(notaFiscalEntrada.getId(),
                inutilizacao.getCaminhoXml());
            }
          }
        }
        String numeroProtocolo = lerXml(uriDocumento);
        inutilizacao.setProtocolo(numeroProtocolo);
      }
      repository.save(inutilizacao);
    }, () -> log.warn("Não encontrado inutilizacao para o Id: {}", inutilizacaoId));
    log.info("inutilização confirmada.");
  }

  private String lerXml(String uri) {
    InputStream inputStreamXml = fileService.readNfeBucket(uri);
    String numeroProtocolo;
    try {
      retInutNFe nFeProcs = importacaoNfeService.parserToInutNFe(inputStreamXml);
      numeroProtocolo = nFeProcs.getInfInut().getNProt();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
    return numeroProtocolo;
  }

  private void criarHistoricoInutilizacaoPedido(Inutilizacao inutilizacao) {
    PedidoHistorico historico = new PedidoHistorico();
    historico.setDataHistorico(LocalDateTime.now());
    historico.setSituacao(inutilizacao.getPedido().getSituacao());
    historico.setPedido(inutilizacao.getPedido());

    var usuarioHistorico = inutilizacao.getPedido().getUpdatedBy() != null
      ? inutilizacao.getPedido().getUpdatedBy()
      : inutilizacao.getPedido().getCreatedBy();
    historico.setUsuario(Usuario.of(UUID.fromString(usuarioHistorico)));
    historico.setCaminhoXml(inutilizacao.getCaminhoXml());
    historico.setDescricao("Inutilização da nota");

    if (inutilizacao.getStatus().equals(StatusInutilizacao.TRANSMITIDO)) {
      historico.setDetalhe(
        "A nota " + inutilizacao.getPedido().getNumeroNotaFiscal() + " foi inutilizada");
    } else {
      historico.setDetalhe(inutilizacao.getMotivoRetorno());
    }
    pedidoHistoricoService.create(historico);
  }

  private void criarHistoricoInutilizacaoNotaEntrada(Inutilizacao inutilizacao) {
    NotaFiscalEntradaHistorico historico = new NotaFiscalEntradaHistorico();
    historico.setDataHistorico(LocalDateTime.now());
    historico.setAcao(NF_INUTILIZADA);
    historico.setNotaFiscalEntrada(inutilizacao.getNotaFiscalEntrada());

    var usuarioHistorico = inutilizacao.getNotaFiscalEntrada().getUsuario() != null
      ? inutilizacao.getPedido().getUpdatedBy()
      : inutilizacao.getPedido().getCreatedBy();
    historico.setUsuario(Usuario.of(UUID.fromString(usuarioHistorico)));
    historico.setCaminhoXml(inutilizacao.getCaminhoXml());

    if (inutilizacao.getStatus().equals(StatusInutilizacao.TRANSMITIDO)) {
      historico.setDescricao(
        "A nota " + inutilizacao.getNotaFiscalEntrada().getNumeroNota() + " foi inutilizada");
    } else {
      historico.setDescricao(inutilizacao.getMotivoRetorno());
    }
    notaEntradaHistoricoService.create(historico);
  }
}
