package br.com.nuvy.api.compras.service;

import br.com.nuvy.api.cadastro.model.Cfop;
import br.com.nuvy.api.cadastro.model.nop.CsosnIcms;
import br.com.nuvy.api.cadastro.model.nop.CstCofins;
import br.com.nuvy.api.cadastro.model.nop.CstIcms;
import br.com.nuvy.api.cadastro.model.nop.CstIpi;
import br.com.nuvy.api.cadastro.model.nop.CstPis;
import br.com.nuvy.api.cadastro.repository.DestinacaoCfopRepository;
import br.com.nuvy.api.cadastro.repository.DestinacaoCofinsCstRepository;
import br.com.nuvy.api.cadastro.repository.DestinacaoRepository;
import br.com.nuvy.api.compras.filter.DestinacaoCompraFilter;
import br.com.nuvy.api.compras.model.DestinacaoCfop;
import br.com.nuvy.api.compras.model.DestinacaoCompra;
import br.com.nuvy.api.compras.model.DestinacaoCsosnIcms;
import br.com.nuvy.api.compras.model.DestinacaoCstCofins;
import br.com.nuvy.api.compras.model.DestinacaoCstIcms;
import br.com.nuvy.api.compras.model.DestinacaoCstIpi;
import br.com.nuvy.api.compras.model.DestinacaoCstPis;
import br.com.nuvy.api.compras.repository.DestinacaoIcmsCstRepository;
import br.com.nuvy.api.compras.repository.DestinacaoIpiCstRepository;
import br.com.nuvy.api.compras.repository.DestinacaoPisCstRepository;
import br.com.nuvy.api.compras.repository.specification.DestinacaoCompraSpecification;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.SituacaoNotaEntrada;
import br.com.nuvy.api.enums.TipoNotaFiscal;
import br.com.nuvy.api.estoque.service.NotaFiscalEntradaItemService;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.dtossaida.DestinacaoCompraOutDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import javax.security.auth.login.AccountExpiredException;

@Service
public class DestinacaoCompraService extends
  PageableServiceAdapterDto<DestinacaoCompra, DestinacaoCompraOutDto, Integer, DestinacaoCompraFilter, DestinacaoRepository> {

  private final DestinacaoCfopRepository destinacaoCfopRepository;
  private final DestinacaoIcmsCstRepository destinacaoIcmsCstRepository;
  private final DestinacaoIpiCstRepository destinacaoIpiCstRepository;
  private final DestinacaoPisCstRepository destinacaoPisCstRepository;
  private final DestinacaoCofinsCstRepository destinacaoCofinsCstRepository;
  private final NotaFiscalEntradaItemService notaFiscalEntradaItemService;


  public DestinacaoCompraService(DestinacaoCfopRepository destinacaoCfopRepository,
    DestinacaoIcmsCstRepository destinacaoIcmsCstRepository,
    DestinacaoIpiCstRepository destinacaoIpiCstRepository,
    DestinacaoPisCstRepository destinacaoPisCstRepository,
    DestinacaoCofinsCstRepository destinacaoCofinsCstRepository,
    NotaFiscalEntradaItemService notaFiscalEntradaItemService) {
    this.destinacaoCfopRepository = destinacaoCfopRepository;
    this.destinacaoIcmsCstRepository = destinacaoIcmsCstRepository;
    this.destinacaoIpiCstRepository = destinacaoIpiCstRepository;
    this.destinacaoPisCstRepository = destinacaoPisCstRepository;
    this.destinacaoCofinsCstRepository = destinacaoCofinsCstRepository;
    this.notaFiscalEntradaItemService = notaFiscalEntradaItemService;
  }

  @Override
  protected DestinacaoCompraSpecification configureSpecification(DestinacaoCompraFilter filter) {
    return DestinacaoCompraSpecification.from(filter);
  }

  @Override
  protected DestinacaoCompra beforeCreate(DestinacaoCompra entity) {
    if (Objects.nonNull(entity.getCfops())) {
      Map<String, Long> cfopOrigemCountMap = entity.getCfops().stream()
        .map(DestinacaoCfop::getCfopOrigem)
        .filter(Objects::nonNull)
        .map(Cfop::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      cfopOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.cfop.origem.duplicado");
        }
      });
      for (DestinacaoCfop cfop : entity.getCfops()) {
        cfop.setDestinacaoCompra(entity);
      }
    }
    if (Objects.nonNull(entity.getPisCsts())) {
      Map<String, Long> pisCstOrigemCountMap = entity.getPisCsts().stream()
        .map(DestinacaoCstPis::getCstPisOrigem)
        .filter(Objects::nonNull)
        .map(CstPis::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      pisCstOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.cstpis.origem.duplicado");
        }
      });
      for (DestinacaoCstPis pis : entity.getPisCsts()) {
        pis.setDestinacaoCompra(entity);
      }
    }
    if (Objects.nonNull(entity.getCofinsCsts())) {
      Map<String, Long> cofinsCstOrigemCountMap = entity.getCofinsCsts().stream()
        .map(DestinacaoCstCofins::getCstCofinsOrigem)
        .filter(Objects::nonNull)
        .map(CstCofins::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      cofinsCstOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.cstcofins.origem.duplicado");
        }
      });
      for (DestinacaoCstCofins cofins : entity.getCofinsCsts()) {
        cofins.setDestinacaoCompra(entity);
      }
    }
    if (Objects.nonNull(entity.getIcmsCsosns())) {
      Map<String, Long> icmsCsosnOrigemCountMap = entity.getIcmsCsosns().stream()
        .map(DestinacaoCsosnIcms::getCsosnIcmsOrigem)
        .filter(Objects::nonNull)
        .map(CsosnIcms::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      icmsCsosnOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.csosnicms.origem.duplicado");
        }
      });
      for (DestinacaoCsosnIcms csodIcms : entity.getIcmsCsosns()) {
        csodIcms.setDestinacaoCompra(entity);
      }
    }
    if (Objects.nonNull(entity.getIcmsCsts())) {
      Map<String, Long> icmsCstOrigemCountMap = entity.getIcmsCsts().stream()
        .map(DestinacaoCstIcms::getCstIcmsOrigem)
        .filter(Objects::nonNull)
        .map(CstIcms::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      icmsCstOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.csticms.origem.duplicado");
        }
      });
      for (DestinacaoCstIcms icms : entity.getIcmsCsts()) {
        icms.setDestinacaoCompra(entity);
      }
    }
    if (Objects.nonNull(entity.getIpiCsts())) {
      Map<String, Long> ipiCstOrigemCountMap = entity.getIpiCsts().stream()
        .map(DestinacaoCstIpi::getCstIpiOrigem)
        .filter(Objects::nonNull)
        .map(CstIpi::getId)
        .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

      ipiCstOrigemCountMap.forEach((cfopCode, count) -> {
        if (count > 1) {
          throw new PreconditionException("destinacao.compra.cstipi.origem.duplicado");
        }
      });
      for (DestinacaoCstIpi ipi : entity.getIpiCsts()) {
        ipi.setDestinacaoCompra(entity);
      }
    }
    if (entity.getSituacao() != Situacao.ATIVO) {
      entity.setSituacao(Situacao.ATIVO);
    }
    if(Objects.isNull(entity.getGeraContasPagar())) {
      entity.setGeraContasPagar(false);
    }
    return super.beforeCreate(entity);
  }

  @Override
  protected DestinacaoCompra save(DestinacaoCompra entity) {
    var isAtualizaItensRelacionados = entity.getIsAtualizaItensRelacionados();
    var entitySaved =  super.save(entity);
    entitySaved.setIsAtualizaItensRelacionados(isAtualizaItensRelacionados);
    return entitySaved;
  }

  @Override
  protected DestinacaoCompra beforeUpdate(DestinacaoCompra oldEntity, DestinacaoCompra newEntity) {
    if(Objects.isNull(newEntity.getGeraContasPagar())) {
      newEntity.setGeraContasPagar(false);
    }
    verificaSeMudouCustoImpostos(oldEntity, newEntity);
    deleteDestinacaoCfop(oldEntity, newEntity);
    limpaReistrosImpostos(oldEntity, newEntity);
    return super.beforeUpdate(oldEntity, newEntity);
  }

  @Override
  protected DestinacaoCompra afterUpdate(DestinacaoCompra entity) {
    if(entity.getIsAtualizaItensRelacionados()) {
      notaFiscalEntradaItemService.atualizaDestinacaoItens(entity, List.of(SituacaoNotaEntrada.PENDENTE), List.of(
        TipoNotaFiscal.ENTRADA));
    }
    return super.afterUpdate(entity);
  }

  private void verificaSeMudouCustoImpostos(DestinacaoCompra oldEntity,
    DestinacaoCompra newEntity) {
    var isMudou = false;
    if (!Objects.equals(oldEntity.getCustoIcms(), newEntity.getCustoIcms())) {
      isMudou = true;
    } else if (!Objects.equals(oldEntity.getCustoIpi(), newEntity.getCustoIpi())) {
      isMudou = true;
    } else if (!Objects.equals(oldEntity.getCustoPis(), newEntity.getCustoPis())) {
      isMudou = true;
    } else if (!Objects.equals(oldEntity.getCustoCofins(), newEntity.getCustoCofins())) {
      isMudou = true;
    }
    newEntity.setIsAtualizaItensRelacionados(isMudou);
  }

  private void deleteDestinacaoCfop(DestinacaoCompra oldEntity, DestinacaoCompra newEntity){
    var cfopsOld = oldEntity.getCfops();
    var cfopsNew = newEntity.getCfops();
    if(cfopsOld != null) {
      for (DestinacaoCfop cfop : cfopsOld) {
        if (cfopsNew == null || cfopsNew.stream().noneMatch(c -> cfop.getId().equals(c.getId()))) {
          destinacaoCfopRepository.delete(cfop);
        }
      }
    }
  }

  private void limpaReistrosImpostos(DestinacaoCompra oldEntity, DestinacaoCompra newEntity) {
    limpaRegistrosIcms(oldEntity, newEntity);
    limpaRegistrosIpi(oldEntity, newEntity);
    limpaRegistrosPis(oldEntity, newEntity);
    limpaRegistrosCofins(oldEntity, newEntity);
  }

  private void limpaRegistrosIcms(DestinacaoCompra oldEntity,
    DestinacaoCompra newEntity) {
    var icmsOldList = oldEntity.getIcmsCsts();
    var icmsNewList = newEntity.getIcmsCsts();
    var icmsResult = new ArrayList<DestinacaoCstIcms>();
    if (icmsOldList != null) {
      for (DestinacaoCstIcms icmsOld : icmsOldList) {
        if (icmsNewList != null) {
          if(icmsResult.stream().anyMatch(c -> c.getCstIcmsOrigem().getId().equals(icmsOld.getCstIcmsOrigem().getId()))){
            destinacaoIcmsCstRepository.delete(icmsOld);
            continue;
          }
          var icms = icmsNewList.stream().filter(c -> icmsOld.getId().equals(c.getId()))
            .findFirst();
          if (icms.isPresent()) {
            icmsResult.add(icms.get());
          } else {
            icmsOld.setCsosnIcmsEntrada(null);
            icmsOld.setCsosnIcmsDevolucao(null);
            icmsResult.add(icmsOld);
          }
        } else {
          icmsResult.add(icmsOld);
        }
      }
      newEntity.setIcmsCsts(icmsResult);
    }
  }

  private void limpaRegistrosIpi(DestinacaoCompra oldEntity,
    DestinacaoCompra newEntity) {
    var ipiOldList = oldEntity.getIpiCsts();
    var ipiNewList = newEntity.getIpiCsts();
    var ipiResult = new ArrayList<DestinacaoCstIpi>();
    if (ipiOldList != null) {
      for (DestinacaoCstIpi ipiOld : ipiOldList) {
        if (ipiNewList != null) {
          if (ipiResult.stream().anyMatch(c -> c.getCstIpiOrigem().getId().equals(ipiOld.getCstIpiOrigem().getId()))){
            destinacaoIpiCstRepository.delete(ipiOld);
            continue;
          }
          var ipi = ipiNewList.stream().filter(c -> ipiOld.getId().equals(c.getId()))
            .findFirst();
          if (ipi.isPresent()) {
            ipiResult.add(ipi.get());
          } else {
            ipiOld.setCstIpiEntrada(null);
            ipiOld.setCstIpiDevolucao(null);
            ipiResult.add(ipiOld);
          }
        } else {
          ipiResult.add(ipiOld);
        }
      }
      newEntity.setIpiCsts(ipiResult);
    }
  }

private void limpaRegistrosPis(DestinacaoCompra oldEntity,
    DestinacaoCompra newEntity) {
    var pisOldList = oldEntity.getPisCsts();
    var pisNewList = newEntity.getPisCsts();
    var pisResult = new ArrayList<DestinacaoCstPis>();
    if (pisOldList != null) {
      for (DestinacaoCstPis pisOld : pisOldList) {
        if (pisResult.stream().anyMatch(c -> c.getCstPisOrigem().getId().equals(pisOld.getCstPisOrigem().getId()))){
          destinacaoPisCstRepository.delete(pisOld);
          continue;
        }
        if (pisNewList != null) {
          var pis = pisNewList.stream().filter(c -> pisOld.getId().equals(c.getId()))
            .findFirst();
          if (pis.isPresent()) {
            pisResult.add(pis.get());
          } else {
            pisOld.setCstPisEntrada(null);
            pisOld.setCstPisDevolucao(null);
            pisResult.add(pisOld);
          }
        } else {
          pisResult.add(pisOld);
        }
      }
      newEntity.setPisCsts(pisResult);
    }
  }

  private void limpaRegistrosCofins(DestinacaoCompra oldEntity,
    DestinacaoCompra newEntity) {
    var cofinsOldList = oldEntity.getCofinsCsts();
    var cofinsNewList = newEntity.getCofinsCsts();
    var cofinsResult = new ArrayList<DestinacaoCstCofins>();
    if (cofinsOldList != null) {
      for (DestinacaoCstCofins cofinsOld : cofinsOldList) {
        if (cofinsResult.stream().anyMatch(c -> c.getCstCofinsOrigem().getId().equals(cofinsOld.getCstCofinsOrigem().getId()))){
          destinacaoCofinsCstRepository.delete(cofinsOld);
          continue;
        }
        if (cofinsNewList != null) {
          var cofins = cofinsNewList.stream().filter(c -> cofinsOld.getId().equals(c.getId()))
            .findFirst();
          if (cofins.isPresent()) {
            cofinsResult.add(cofins.get());
          } else {
            cofinsOld.setCstCofinsEntrada(null);
            cofinsOld.setCstCofinsDevolucao(null);
            cofinsResult.add(cofinsOld);
          }
        } else {
          cofinsResult.add(cofinsOld);
        }
      }
      newEntity.setCofinsCsts(cofinsResult);
    }
  }

}
