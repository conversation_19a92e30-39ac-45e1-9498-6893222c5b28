package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.enums.TipoIE;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.common.utils.StringUtils;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParceiroDto implements Dto<Pessoa> {

  private Integer id;
  private TipoPessoa tipo;
  @NotNull
  private String cpfCnpj;
  private SituacaoParceiro situacao;
  @NotNull
  private String nome;
  private String nomeFantasia;
  private String inscricaoEstadual;
  private String inscriaoMunicipal;
  private String telefone;
  private String emailNotaFiscal;
  private String emailCobranca;
  private LocalDate dataNascimento;
  private TipoIE tipoIe;
  private List<TipoRelacionamento> relacaoComercial;
  private List<EnderecoParceiroDto> enderecos;

  public static ParceiroDto from(Pessoa pessoa) {
    return ObjectUtils.convert(pessoa, ParceiroDto.class);
  }

  @Override
  public Pessoa toEntity() {
      var pessoa =  ObjectUtils.convert(this, Pessoa.class);
      
      pessoa.setEnderecos(null);
      if (getEnderecos() != null) {
        List<EnderecoPessoa> enderecoPessoas = new ArrayList<>();
        for (var endereco : getEnderecos()) {
          enderecoPessoas.add(endereco.toEntity());
        }
        pessoa.setEnderecos(enderecoPessoas);
      }
      pessoa.setId(null);
      if(getRelacaoComercial() != null) {
        var relacaoComercialSet = new HashSet<>(getRelacaoComercial());
        pessoa.setRelacaoComercial(relacaoComercialSet);
      }
      return pessoa;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    this.inscricaoEstadual = StringUtils.limparInscricaoEstadual(inscricaoEstadual);
  }
}
