package br.com.nuvy.api.email.utils;

import br.com.nuvy.api.email.dto.EnviarEmailPadrao;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.notification.DestinatarioEmail;
import java.util.Set;

public class EmailUtils {

  public static Boolean containsEmailUsuario(EnviarEmailPadrao dto, Usuario usuario) {
    if (dto.getCopias() == null) {
      return false;
    }
    if (dto.getCopiasOcultas() == null) {
      return false;
    }
    if (dto.getCopias().contains(usuario.getEmail())) {
      return true;
    }
    if (dto.getPara().contains(usuario.getEmail())) {
      return true;
    }
    return dto.getCopiasOcultas().contains(usuario.getEmail());
  }

  public static void validaListasEmails(DestinatarioEmail payload) {
    if (payload.getEmails().isEmpty()) {
      throw new PreconditionException("orcamento.emails.obrigatorio");
    }
    for (var email : payload.getEmails()) {
      if (payload.getCopias().contains(email)) {
        payload.getCopias().remove(email);
      }
      if (payload.getCopiasOcultas().contains(email)) {
        payload.getCopiasOcultas().remove(email);
      }
    }
    for (var email : payload.getCopias()) {
      if (payload.getCopiasOcultas().contains(email)) {
        payload.getCopiasOcultas().remove(email);
      }
    }
  }

  public static String convertDestinatariosToString(Set<String> list) {
    StringBuilder sb = new StringBuilder();
    if (list.size() > 1) {
      for (String s : list) {
        sb.append(s);
        sb.append(";");
      }
      return sb.toString();
    }
    return list.stream().findFirst().orElse("");
  }
}
