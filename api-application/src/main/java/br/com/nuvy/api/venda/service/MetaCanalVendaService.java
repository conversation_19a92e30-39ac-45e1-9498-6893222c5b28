package br.com.nuvy.api.venda.service;

import br.com.nuvy.api.venda.filter.MetaCanalVendaFilter;
import br.com.nuvy.api.venda.model.MetaCanalVenda;
import br.com.nuvy.api.venda.repository.MetaCanalVendaRepository;
import br.com.nuvy.api.venda.repository.specification.MetaCanalVendaSpecification;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.financeiro.fn7metasindicadores.model.MetaCanalVendaDto;
import br.com.nuvy.facade.financeiro.fn7metasindicadores.model.MetaDisponivel;
import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MetaCanalVendaService extends
  PageableServiceAdapterDto<MetaCanalVenda, MetaCanalVendaDto, Integer, MetaCanalVendaFilter, MetaCanalVendaRepository> {

  private final CanalVendaService canalVendaService;
  private final Badge badge;

  @Transactional
  public List<MetaCanalVendaDto> create(MetaCanalVendaDto dto) {

    var canalVenda = canalVendaService.findById(dto.getCanalVendaId());
    if (canalVenda.isEmpty()) {
      throw new PreconditionException("canal.venda.nao.encontrado");
    }
    BigDecimal parcelas = dto.getValor().divide(BigDecimal.valueOf(12), 9, RoundingMode.HALF_EVEN);
    List<MetaCanalVendaDto> metasCanalAnual = new ArrayList<>();
    for (int mes = 1; mes <= 12; mes++) {
      MetaCanalVenda metaCanal = MetaCanalVenda.builder()
        .mes(mes)
        .ano(dto.getAno())
        .valor(parcelas)
        .empresa(badge.getEmpresa())
        .canalVenda(canalVenda.get())
        .build();
      metasCanalAnual.add(MetaCanalVendaDto.from(repository.save(metaCanal)));
    }
    return metasCanalAnual;
  }

  @Override
  public MetaCanalVenda update(Integer id, MetaCanalVenda dto) {
    MetaCanalVenda metaCanal = repository.findById(id).orElseThrow(ResourceNotFoundException::new);
    metaCanal.setValor(dto.getValor());
    return repository.save(metaCanal);
  }

  public void delete(Integer id) {
    repository.findById(id).orElseThrow(ResourceNotFoundException::new);
    repository.deleteById(id);
  }

  @Override
  protected Specification<MetaCanalVenda> configureSpecification(MetaCanalVendaFilter filter) {
    return new MetaCanalVendaSpecification(filter);
  }

  public boolean exists(MetaCanalVendaFilter filter) {
    return repository.count(configureSpecification(filter)) > 0;
  }

  public MetaDisponivel getMetaCanalDisponivel(int ano, int mes) {
    Optional<BigDecimal> metaDisponivel = repository.findMetaMetaDisponivel(ano, mes);

    return MetaDisponivel.builder()
      .metaDisponivel(metaDisponivel.isPresent() ? metaDisponivel.get() : BigDecimal.ZERO).build();
  }
}
