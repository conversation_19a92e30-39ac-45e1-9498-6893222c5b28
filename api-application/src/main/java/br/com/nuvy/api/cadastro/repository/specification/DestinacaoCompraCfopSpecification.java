package br.com.nuvy.api.cadastro.repository.specification;

import br.com.nuvy.api.cadastro.filter.DestinacaoCompraCfopFilter;
import br.com.nuvy.api.cadastro.model.Cfop;
import br.com.nuvy.api.cadastro.model.Cfop_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
public class DestinacaoCompraCfopSpecification implements Specification<Cfop> {

  private final DestinacaoCompraCfopFilter filter;

  @Override
  public Predicate toPredicate(Root<Cfop> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getId(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.get(Cfop_.codigo)), "%" + e.toUpperCase() + "%"))
      .add(filter.getDescricao(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.get(Cfop_.descricao)), "%" + e.toUpperCase() + "%"))
      .add(filter.getCriterio(),
        e -> criteriaBuilder.or(
          criteriaBuilder.like(criteriaBuilder.upper(root.get(Cfop_.codigo)), "%" + e.toUpperCase() + "%"),
          criteriaBuilder.like(criteriaBuilder.upper(root.get(Cfop_.descricao)), "%" + e.toUpperCase() + "%")
        ))
      .add(filter.getTipo(),
        e -> criteriaBuilder.equal(root.get(Cfop_.tipo), e))
      .add(filter.getIdCfopsIgnorados(),
        e -> criteriaBuilder.not(root.get(Cfop_.codigo).in(e))
      )
      .and();
  }
}
