package br.com.nuvy.api.seguranca.repository;

import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.projection.UsuarioNomeUsuarioEmailProjection;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import br.com.nuvy.notificacao.dominio.usuario.UsuarioNotificacao;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UsuarioRepository extends NuvyRepository<Usuario, UUID> {

  @Query("SELECT u FROM Usuario u WHERE LOWER(u.email) = LOWER(:email)")
  Optional<Usuario> findByEmail(String email);

  @Query(value = """
    SELECT mv.id_usuario from erp.eq_movto_estoque mv
    where mv.id_aplicacao = :aplicacaoId
    and mv.id_produto = :produtoId
    order by mv.dt_movto desc
    limit 1;
    """, nativeQuery = true)
  UUID findUsuarioByProdutoId(String aplicacaoId, Integer produtoId);

  @Query("""
    SELECT
        u.id AS id,
        u.email AS email,
        u.nome AS nome,
        u.senha AS senha
    FROM
        Usuario u
    WHERE
        u.id = :id
    """)
  Optional<UsuarioNotificacao> findUsuarioNotificacaoById(UUID id);

  @Query(value = """
    SELECT
        USU.nome AS nome,
        USU.email AS email
    FROM
        ERP.sg_usuario USU
    INNER JOIN
        ERP.sg_usuario_aplicacao USP ON USU.id_usuario = USP.id_usuario
    INNER JOIN
        ERP.sg_perfil_usuario UPF ON USP.id_usuario_aplicacao = UPF.id_usuario_aplicacao
    INNER JOIN
        ERP.sg_perfil PRF ON UPF.id_perfil = PRF.id_perfil
    WHERE
        PRF.ind_sistema = TRUE AND
        USU.SITUACAO = 'ATIVO' AND
        USP.situacao = 'ATIVO' AND
        USP.id_aplicacao= :idAplicacao
    """, nativeQuery = true)
  List<UsuarioNomeUsuarioEmailProjection> findUsuarioNomeUsuarioEmailByAplicacaoId(String idAplicacao);

  @Query(value = """
    SELECT
        USU.nome AS nome,
        USU.email AS email
    FROM ERP.sg_usuario USU
    INNER JOIN ERP.sg_usuario_aplicacao USP ON USU.id_usuario = USP.id_usuario
    INNER JOIN erp.cd_empresa on cd_empresa.id_aplicacao = usp.id_aplicacao
    INNER JOIN ERP.sg_perfil_usuario UPF ON USP.id_usuario_aplicacao = UPF.id_usuario_aplicacao
    INNER JOIN ERP.sg_perfil PRF ON UPF.id_perfil = PRF.id_perfil
    WHERE PRF.ind_sistema = TRUE
      AND USU.SITUACAO = 'ATIVO'
      AND USP.situacao = 'ATIVO'
      AND cd_empresa.id_empresa = :idEmpresa
    """, nativeQuery = true)
  List<UsuarioNomeUsuarioEmailProjection> findUsuarioNomeUsuarioEmailByEmpresaId(Integer idEmpresa);
}
