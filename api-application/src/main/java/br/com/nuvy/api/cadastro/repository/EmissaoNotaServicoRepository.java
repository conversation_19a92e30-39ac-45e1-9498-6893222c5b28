package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.EmissaoNotaServico;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import org.springframework.stereotype.Repository;

@Repository
public interface EmissaoNotaServicoRepository extends NuvyRepository<EmissaoNotaServico, Long> {

  Optional<EmissaoNotaServico> findByIdEmpresa(Integer idEmpresa);

}
