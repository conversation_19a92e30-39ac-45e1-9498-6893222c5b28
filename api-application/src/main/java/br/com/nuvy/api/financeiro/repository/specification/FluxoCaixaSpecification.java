package br.com.nuvy.api.financeiro.repository.specification;

import br.com.nuvy.api.financeiro.model.BancoContaSaldoDiario;
import br.com.nuvy.api.financeiro.model.BancoContaSaldoDiario_;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.ContaBancaria_;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.facade.relatorio.rl4financeiro.model.TituloFluxoFilter;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
@EqualsAndHashCode
public class FluxoCaixaSpecification implements Specification<BancoContaSaldoDiario> {

  private final TituloFluxoFilter filter;


  @Override
  public Predicate toPredicate(Root<BancoContaSaldoDiario> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getIdsContasBancarias(),
        e -> root.get(BancoContaSaldoDiario_.contaBancaria).get(ContaBancaria_.id).in(e))
      .and();
  }
}
