package br.com.nuvy.api.venda.repository;

import br.com.nuvy.api.anuncio.CategoriaAnuncio;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;

public interface CategoriaAnuncioRepository extends NuvyRepository<CategoriaAnuncio, Integer> {

  List<CategoriaAnuncio> findByCanalVendaId(Integer id);

  List<CategoriaAnuncio> findByCategoriaErpId(Integer categoriaErpId);

  Optional<CategoriaAnuncio> findByCanalVendaIdAndCategoriaErpId(Integer canalVendaId, Integer categoriaErpId);
}
