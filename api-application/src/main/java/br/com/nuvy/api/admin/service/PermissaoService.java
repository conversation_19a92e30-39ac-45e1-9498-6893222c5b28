package br.com.nuvy.api.admin.service;

import br.com.nuvy.api.seguranca.dto.GetPermissaoDto;
import br.com.nuvy.api.seguranca.model.Funcionalidade;
import br.com.nuvy.api.seguranca.model.Modulo;
import br.com.nuvy.api.seguranca.model.Perfil;
import br.com.nuvy.api.seguranca.model.PerfilFuncionalidade;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.repository.PerfilFuncionalidadeRepository;
import br.com.nuvy.api.seguranca.service.UsuarioAplicacaoService;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PermissaoService {

  private final UsuarioAplicacaoService usuarioAplicacaoService;
  private final PerfilFuncionalidadeRepository perfilFuncionalidadeRepository;

  public GetPermissaoDto getPermissao(Usuario usuario, GetPermissaoDto dto) {
    // busca os dados de permissões do perfil do usuário do banco de dados
    UsuarioAplicacao usuarioAplicacao = usuarioAplicacaoService.findByUsuario(usuario)
      .orElseThrow(() -> new EntityNotFoundException("usuario.nao.encontrado"));
    List<PerfilFuncionalidade> permissoes = perfilFuncionalidadeRepository.findAllByPerfilIdIn(
      usuarioAplicacao.getPerfilsAplicacao().stream()
        .map(Perfil::getId)
        .toList()
    );

    // seleciona os modulos e funcionalidades permitidos para o perfil do usuário
    List<String> modulosPermitidosPerfil = permissoes.stream()
      .map(PerfilFuncionalidade::getFuncionalidade)
      .map(Funcionalidade::getModulo)
      .map(Modulo::getId)
      .distinct().toList();
    List<String> funcionalidadesPermitidasPerfil = permissoes.stream()
      .map(PerfilFuncionalidade::getFuncionalidade)
      .map(Funcionalidade::getId)
      .toList();

    // filtra os modulos e funcionalidades permitidos para o perfil do usuário
    List<String> modulosPermitidos = dto.getModulos().stream()
      .filter(modulosPermitidosPerfil::contains)
      .toList();
    List<String> funcionalidadePermitidas = dto.getFuncionalidades().stream()
      .filter(funcionalidadesPermitidasPerfil::contains)
      .toList();

    // retorna os modulos e funcionalidades permitidos para o perfil do usuário
    return new GetPermissaoDto(modulosPermitidos, funcionalidadePermitidas);
  }
}
