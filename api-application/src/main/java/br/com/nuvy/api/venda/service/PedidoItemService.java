package br.com.nuvy.api.venda.service;

import br.com.nuvy.api.venda.model.PedidoItem;
import br.com.nuvy.api.venda.repository.PedidoItemRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PedidoItemService extends
  PageableServiceAdapter<PedidoItem, UUID, NoFilter, PedidoItemRepository> {

  public Boolean findPedidoItemByProduto(Integer produtoId) {
    return repository.existsByProdutoId(produtoId);
  }

}
