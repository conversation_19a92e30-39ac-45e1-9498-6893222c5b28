package br.com.nuvy.api.cadastro.controller;

import static org.springframework.web.bind.annotation.RequestMethod.HEAD;

import br.com.nuvy.api.cadastro.dto.Imagem;
import br.com.nuvy.api.cadastro.dto.ProdutoDto;
import br.com.nuvy.api.cadastro.dto.ProdutoResumoDto;
import br.com.nuvy.api.cadastro.dto.ProdutoVariacaoOutDto;
import br.com.nuvy.api.cadastro.dto.ResumoCadastroProdutosDto;
import br.com.nuvy.api.cadastro.filter.ProdutoFilter;
import br.com.nuvy.api.cadastro.service.ProdutoService;
import br.com.nuvy.api.importacao.ProdutoImagemXlsxImportService;
import br.com.nuvy.api.importacao.ProdutoImagemZipImportService;
import br.com.nuvy.api.importacao.ProdutoImportService;
import br.com.nuvy.client.enums.TipoArquivo;
import br.com.nuvy.client.services.DownloadBuscasService;
import br.com.nuvy.common.base.controller.PageableControllerAdapter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.patch.json.Patch;
import br.com.nuvy.common.patch.json.PatchRequestBody;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "Cadastro / Produto")
@RestController
@RequestMapping(value = "/v1/cadastro/produto", headers = "empresa")
@RequiredArgsConstructor
@Slf4j
public class ProdutoController extends
  PageableControllerAdapter<ProdutoDto, Integer, ProdutoFilter, ProdutoService> {

  private final ProdutoImportService produtoImportService;
  private final ProdutoImagemXlsxImportService produtoImagemXlsxImportService;
  private final ProdutoImagemZipImportService produtoImagemZipImportService;
  private final DownloadBuscasService downloadBuscasService;
  private final DiscovererComponent discovererComponent;

  @GetMapping("/{id}")
  @Operation(summary = "Recuperar por Id", description = "Recupera o recurso conforme o id informado")
  public ProdutoDto findById(@PathVariable Integer id) {
    return service.findProdutoById(id);
  }

  @Operation(
    summary = "Retorna um resumo de quantidade",
    description = "Retorna um resumo de quantidade de produtos cadastrados, ativos e mais vendidos."
  )
  @GetMapping("/resumo")
  public ResponseEntity<ResumoCadastroProdutosDto> resumoProdutos() {
    return ResponseEntity.ok().body(service.resumoProdutos());
  }

  @Operation(
    summary = "Permite alterar as fotos de um produto",
    description = "Permite alterar as fotos de um produto já cadastrado."
  )
  @PutMapping(value = "/{id}/fotos", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public List<Imagem> updateFotos(@PathVariable Integer id,
    @RequestParam(value = "fotos", required = false) List<MultipartFile> fotos,
    @RequestParam(required = false) String remover)
    throws IOException {
    return service.updateFoto(id, fotos, remover);
  }

  //TODO: depositoEstoqueInicial e empresaEstoqueInicial sao usados na classe ProdutoImportService
  // refatorar para que seja passado via parametro para o service
  @Operation(
    summary = "Permite importar um arquivo de produtos",
    description = "Permite importar um arquivo de produtos através de um arquivo Xlsx"
  )
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public void resumoProdutos(@RequestParam("file") MultipartFile file,
    @RequestParam(value = "depositoEstoqueInicialId", required = false) Integer depositoEstoqueInicialId,
    @RequestParam(value= "empresaEstoqueInicialId", required = false) Integer empresaEstoqueInicialId) {
    produtoImportService.importProduto(file);
  }

  @Operation(
    summary = "Permite importar imagens de produtos via planilha ou ZIP",
    description = "Permite importar imagens de produtos através de um arquivo Excel (.xlsx) com URLs ou arquivo ZIP com imagens organizadas por pastas (nome da pasta = código do produto)"
  )
  @PostMapping(value = "/upload-imagens", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public ResponseEntity<String> uploadImagensProdutos(@RequestParam("file") MultipartFile file) {
    try {
      String fileName = file.getOriginalFilename();
      if (fileName == null) {
        return ResponseEntity.badRequest().body("Nome do arquivo não informado");
      }

      String fileExtension = fileName.toLowerCase();

      if (fileExtension.endsWith(".xlsx") || fileExtension.endsWith(".xls")) {
        log.info("Processando arquivo como planilha Excel: {}", fileName);
        produtoImagemXlsxImportService.importProdutoImagens(file);
        return ResponseEntity.ok("Planilha de imagens processada com sucesso. Verifique seu email para mais detalhes.");

      } else if (fileExtension.endsWith(".zip")) {
        log.info("Processando arquivo como ZIP: {}", fileName);
        produtoImagemZipImportService.importProdutoImagensZip(file);
        return ResponseEntity.ok("Arquivo ZIP de imagens processado com sucesso. Verifique os logs para detalhes.");

      } else {
        return ResponseEntity.badRequest()
          .body("Formato de arquivo não suportado. Use .xlsx (planilha) ou .zip (imagens em pastas)");
      }

    } catch (Exception e) {
      log.error("Erro ao processar arquivo de imagens de produtos", e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body("Erro ao processar o arquivo: " + e.getMessage());
    }
  }

  @Operation(
    summary = "Permite atualizar um parâmetro do produto",
    description = "Permite atualizar um ou mais parâmetros do produto"
  )
  @PatchMapping("/{id}")
  @Patch(service = ProdutoService.class)
  @ResponseStatus(HttpStatus.OK)
  public void patch(@PathVariable Integer id, @PatchRequestBody ProdutoDto produtoDto) {
    service.updateDto(id, produtoDto);
  }

  @Operation(
    summary = "Verifica se o produto existe",
    description = "Verifica se o produto existe e retorna um status 200 se existir."
  )
  @RequestMapping(method = HEAD, value = "/exists/{codigo}")
  @ResponseStatus(value = HttpStatus.OK)
  public void produtoExiste(@PathVariable String codigo) {
    if (!service.exists(codigo)) {
      throw new ResourceNotFoundException();
    }
  }

  @Operation(
    summary = "Permite realizar o download da consulta",
    description = "permite realizar o download das consultas de produtos, retornando um zip com um csv, xlsx ou pdf"
  )
  @PostMapping(value = "/consulta/download", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public ResponseEntity<InputStreamResource> downloadConsultaProduto(
    @RequestParam TipoArquivo tipoArquivo, @ParameterObject @Valid ProdutoFilter filter) {
    filter.setKit(false);
    return downloadBuscasService.downloadConsultaProduto(tipoArquivo, filter);
  }

  @Operation(
    summary = "Retorna uma lista de produtos",
    description = "Retorna uma lista de produtos paginada e com possibilidade de uso dos filtros. Retornando apenas os produtos que não são kits."
  )
  @Override
  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public List<ProdutoResumoDto> find(@ParameterObject ProdutoFilter filter,
    @ParameterObject Pageable pageable) {
    filter.setKit(false);
    Optional<Order> order = pageable.getSort().get().findFirst();
    if (order.isEmpty()) {
      pageable = ((PageRequest) pageable).withSort(Sort.by("descricao"));
    }
    Page<ProdutoResumoDto> produtos = service.find(filter, pageable).map(ProdutoResumoDto::from);
    if (!CollectionUtils.isEmpty(produtos.getContent())) {
      for (ProdutoResumoDto umProdutoResumo : produtos.getContent()) {
        if (umProdutoResumo.getVariacaoPai()) {
          var variacoes = service.listarProdutosVariacao(umProdutoResumo.getId());
          if (!CollectionUtils.isEmpty(variacoes)) {
            StringBuilder sb = new StringBuilder("(");
            sb.append(variacoes.size()).append(" Variações)");
            umProdutoResumo.setVariacaoesDescricao(sb.toString());
            umProdutoResumo.setVariacoesResumo(variacoes);
            var somaEstoqueAtual = variacoes.stream()
              .map(ProdutoVariacaoOutDto::getEstoqueAtual)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
            umProdutoResumo.setQuantidadeEstoqueAtual(somaEstoqueAtual);
          }
        }
      }
    }
    return discovererComponent.handlePaginatedResults(pageable, produtos);
  }

  @Operation(
    summary = "Permite alterar o status de uma variação",
    description = "Permite alterar o status de uma variação por id"
  )
  @PatchMapping("variacao/{id}")
  @Patch(service = ProdutoService.class)
  @ResponseStatus(HttpStatus.OK)
  public void patchVariacao(@PathVariable Integer id, @PatchRequestBody ProdutoDto produtoDto) {
    service.updateStatusVariacao(id, produtoDto);
  }

  @Operation(
    summary = "Permite atualizar o campo importado",
    description = "Permite atualizar o campo importado de um produto por id"
  )
  @PatchMapping("/atualizar-importado/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void atualizarImportado(@PathVariable Integer id) {
    service.atualizarImportado(id);
  }

}
