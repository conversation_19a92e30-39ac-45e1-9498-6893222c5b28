package br.com.nuvy.api.integration.dto;


import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;

import br.com.nuvy.api.cadastro.model.Cest;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.model.PosPdv;
import br.com.nuvy.api.cadastro.model.PosPdvProduto;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.nop.CsosnIcms;
import br.com.nuvy.api.cadastro.model.nop.CstCofins;
import br.com.nuvy.api.cadastro.model.nop.CstIpi;
import br.com.nuvy.api.cadastro.model.nop.CstPis;
import br.com.nuvy.api.cadastro.model.nop.RegraCofinsPadrao;
import br.com.nuvy.api.cadastro.model.nop.RegraIcmsItem;
import br.com.nuvy.api.cadastro.model.nop.RegraIpi;
import br.com.nuvy.api.cadastro.model.nop.RegraPis;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.utils.NumberUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Getter
@Setter
@Builder
public class PdvSysfarProdutoSqs {

  @JsonProperty("id")
  private UUID idProdutoPosPdv;
  public String aplicacao;
  public Integer empresa;
  public Integer codid;
  public String token;
  public String descricao;
  public String unidade;
  public Integer quantidadeEmbalagem;
  public Integer tipoUnidade;
  public BigDecimal comprimentoMedida;
  public BigDecimal larguraMedida;
  public BigDecimal alturaMedida;
  public String codigoTela;
  public BigDecimal valorUnitario;
  public Integer atacarejo;
  public Integer minataca;
  public String ativo;
  public String situacaotributaria;
  public BigDecimal aliquota;
  public Integer icmsDesoneracao;
  public Integer percentualReducaoBaseCalculo;
  public String ipi;
  public String codigoBarras;
  public String lerPeso;
  public String complemento;
  public String producao;
  public BigDecimal estoque;
  public String tipoItem;
  public String ncm;
  public String cstIcms;
  public String cest;
  public Integer pfcp;
  public String piss;
  public BigDecimal aliquotaCofins;
  public BigDecimal aliquotaPis;
  public String pcsll;
  public String pirrf;
  public String pinss;
  public String ctiss;
  public String cnae;
  public String tipoleiturabalanca;
  public String local;
  public String cstPis;
  public String cstCofins;
  public String cfopnfce;
  public BigDecimal precoMaxCons;
  public String codanp;
  public String descricaoanp;
  public String glp;
  public String gnn;
  public String valorpartida;
  public String tamanho;
  public String observacoes;

  private Boolean indKit = false;
  private BigDecimal qtdMinimaAtacado = BigDecimal.ZERO;
  private BigDecimal vlAtacado = BigDecimal.ZERO;

  public static PdvSysfarProdutoSqs produtoToPdvSysfarProdutoDto(ProdToPdvSysfarProdDto param) {
    PdvSysfarProdutoSqsBuilder pdvSysfarProdutoDtoBuilder = PdvSysfarProdutoSqs.builder();
    PosPdv posPdv = param.getPosPdv();
    Produto produto = param.getProduto();
    PosPdvProduto posPdvProduto = param.getPosPdvProduto();
    pdvSysfarProdutoDtoBuilder.aplicacao(posPdv.getAplicacao());
    pdvSysfarProdutoDtoBuilder.empresa(posPdv.getEmpresa().getId());
    pdvSysfarProdutoDtoBuilder.idProdutoPosPdv(posPdvProduto.getId());
    pdvSysfarProdutoDtoBuilder.token(posPdv.getToken());
    pdvSysfarProdutoDtoBuilder.codid(produto.getId());
    pdvSysfarProdutoDtoBuilder.indKit(produto.getKit());

    pdvSysfarProdutoDtoBuilder.qtdMinimaAtacado(posPdvProduto.getQtdMinimaAtacado());
    pdvSysfarProdutoDtoBuilder.vlAtacado(posPdvProduto.getVlAtacado());

    if (produto.getVariacao()){
      pdvSysfarProdutoDtoBuilder.descricao(produto.getDescricaoVariacao());
    }else {
      pdvSysfarProdutoDtoBuilder.descricao(produto.getDescricao());
    }
    pdvSysfarProdutoDtoBuilder.unidade(produto.getUnidadeMedida().getUnidade());
    pdvSysfarProdutoDtoBuilder.quantidadeEmbalagem(0);//definido Default 0
    //----Tipo de unidade (1-Normal, 2-Metro quadro, 3-Metro cúbico) se não passar default é 1
    var tipounidade = 1;
    if (produto.getUnidadeMedida().getDescricao().equals("METRO CÚBICO")) {
      tipounidade = 3;
      pdvSysfarProdutoDtoBuilder.tipoUnidade(tipounidade);
    } else if (produto.getUnidadeMedida().getDescricao().equals("METRO QUADRADO")) {
      tipounidade = 2;
      pdvSysfarProdutoDtoBuilder.tipoUnidade(tipounidade);
    } else {
      pdvSysfarProdutoDtoBuilder.tipoUnidade(tipounidade);
    }
    if (tipounidade > 1) {
      pdvSysfarProdutoDtoBuilder.comprimentoMedida(produto.getProfundidade());
      pdvSysfarProdutoDtoBuilder.larguraMedida(produto.getLargura());
      pdvSysfarProdutoDtoBuilder.alturaMedida(produto.getAltura());
    }
    pdvSysfarProdutoDtoBuilder.codigoTela(produto.getCodigo());
    if (NumberUtils.isNotEmptyAndPositive(posPdvProduto.getPrecoVenda())) {
      pdvSysfarProdutoDtoBuilder.valorUnitario(posPdvProduto.getPrecoVenda());
    } else {
      pdvSysfarProdutoDtoBuilder.valorUnitario(produto.getPrecoVenda());
    }

    pdvSysfarProdutoDtoBuilder.atacarejo(0);//definido Default 0
    pdvSysfarProdutoDtoBuilder.minataca(0);//definido Default 0!!!!!!!!!!!!!!!!!!!!

    pdvSysfarProdutoDtoBuilder.ativo(posPdvProduto.getSituacao().equals(Situacao.INATIVO) ? "N" : "S");//definido Default S

    // fazer verficacao conforme a liliane mandar
    pdvSysfarProdutoDtoBuilder.situacaotributaria("N");//definido Default !!!!!

    pdvSysfarProdutoDtoBuilder.aliquota(BigDecimal.ZERO);//definido Default 0
    pdvSysfarProdutoDtoBuilder.icmsDesoneracao(null);//definido Default null
    pdvSysfarProdutoDtoBuilder.percentualReducaoBaseCalculo(0);//definido Default 0
    pdvSysfarProdutoDtoBuilder.ipi(get(param.getRegraIpi(), RegraIpi::getCstIpi,
      CstIpi::getId));//definido Default 0
    pdvSysfarProdutoDtoBuilder.codigoBarras(produto.getCodigoEan());//definido Default 0
    pdvSysfarProdutoDtoBuilder.lerPeso("N");//definido Default N
    pdvSysfarProdutoDtoBuilder.complemento("N");
    pdvSysfarProdutoDtoBuilder.producao("N");//definido Default N
    // verifica se o saldo é menor ou igual a zero
    pdvSysfarProdutoDtoBuilder.estoque(param.getSaldo());//definido Default 0
    pdvSysfarProdutoDtoBuilder.tipoItem("00");//definido Default 0

    pdvSysfarProdutoDtoBuilder.ncm(get(produto.getNcm(), Ncm::getId));
    pdvSysfarProdutoDtoBuilder.cstIcms(get(param.getRegraIcmsItem(), RegraIcmsItem::getCsosnConsumidorFinalNc,
      CsosnIcms::getId));//pegar da nop do produto
    pdvSysfarProdutoDtoBuilder.cest(get(produto.getCest(), Cest::getId));//

    pdvSysfarProdutoDtoBuilder.cstCofins(get(param.getRegraPis(), RegraPis::getCstPis,
      CstPis::getId));

    pdvSysfarProdutoDtoBuilder.cstPis(get(param.getRegraCofins(), RegraCofinsPadrao::getCstCofins,
      CstCofins::getId));

    pdvSysfarProdutoDtoBuilder.pfcp(0);

    pdvSysfarProdutoDtoBuilder.aliquotaPis(get(param.getRegraPis(), RegraPis::getAliquota,
      NumberUtils::getPercentualDecimalizado));
    pdvSysfarProdutoDtoBuilder.aliquotaCofins(get(param.getRegraCofins(), RegraCofinsPadrao::getAliquota,
      NumberUtils::getPercentualDecimalizado));

    pdvSysfarProdutoDtoBuilder.piss(null);
    pdvSysfarProdutoDtoBuilder.pcsll(null);
    pdvSysfarProdutoDtoBuilder.pirrf(null);
    pdvSysfarProdutoDtoBuilder.pinss(null);
    pdvSysfarProdutoDtoBuilder.ctiss(null);
    pdvSysfarProdutoDtoBuilder.cnae(null);
    pdvSysfarProdutoDtoBuilder.tipoleiturabalanca(null);

    pdvSysfarProdutoDtoBuilder.local(param.getDeposito().getNome());
    pdvSysfarProdutoDtoBuilder.cfopnfce("00");
    pdvSysfarProdutoDtoBuilder.precoMaxCons(BigDecimal.ZERO);
    pdvSysfarProdutoDtoBuilder.codanp(null);
    pdvSysfarProdutoDtoBuilder.descricaoanp(null);
    pdvSysfarProdutoDtoBuilder.glp(null);
    pdvSysfarProdutoDtoBuilder.gnn(null);
    pdvSysfarProdutoDtoBuilder.valorpartida(null);
    pdvSysfarProdutoDtoBuilder.tamanho(null);
    pdvSysfarProdutoDtoBuilder.observacoes(null);

    return pdvSysfarProdutoDtoBuilder.build();
  }
}
