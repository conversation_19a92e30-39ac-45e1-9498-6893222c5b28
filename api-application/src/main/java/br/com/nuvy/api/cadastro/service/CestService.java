package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.dto.CestDto;
import br.com.nuvy.api.cadastro.filter.CestFilter;
import br.com.nuvy.api.cadastro.model.Cest;
import br.com.nuvy.api.cadastro.repository.CestRepository;
import br.com.nuvy.api.cadastro.repository.specification.CestCriterioSpecification;
import br.com.nuvy.api.cadastro.repository.specification.CestSpecification;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CestService extends PageableServiceAdapterDto<Cest, CestDto, String, CestFilter, CestRepository> {

  @Override
  protected Specification<Cest> configureSpecification(CestFilter filter) {
    return CestSpecification.from(filter);
  }

  public Page<Cest> find(String filter, Pageable pageable) {
    var specification = new CestCriterioSpecification(filter);
    return repository.findAll(specification, pageable);
  }
}
