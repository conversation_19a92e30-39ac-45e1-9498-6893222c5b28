package br.com.nuvy.api.venda.service;

import br.com.nuvy.api.venda.model.PedidoHistoricoEmail;
import br.com.nuvy.api.venda.repository.PedidoHistoricoEmailRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.config.AmazonS3Service;
import br.com.nuvy.config.datasource.DataSource;
import br.com.nuvy.config.datasource.DataSourceType;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class PedidoHistoricoEmailService extends
  PageableServiceAdapter<PedidoHistoricoEmail, <PERSON>UI<PERSON>, <PERSON><PERSON><PERSON>er, PedidoHistoricoEmailRepository> {

  private final AmazonS3Service amazonS3Service;

  @DataSource(DataSourceType.READ)
  public Page<PedidoHistoricoEmail> findPedidoHistoricoEmail(UUID id, Pageable pageable) {
    var pedidoHistoricoEmail = repository.findAllByPedidoId(id, pageable);
    pedidoHistoricoEmail.forEach(pedidoHistoricoEmailOutDto -> {
      if (!CollectionUtils.isEmpty(pedidoHistoricoEmailOutDto.getArquivos())) {
        pedidoHistoricoEmailOutDto.getArquivos().forEach(pedidoHistoricoEmailArquivoDto -> {
          if (Objects.nonNull(pedidoHistoricoEmailArquivoDto.getArquivo())) {
            pedidoHistoricoEmailArquivoDto.setArquivo(
              amazonS3Service.generateSignedUrl(pedidoHistoricoEmailArquivoDto.getArquivo()));
          }
        });
      }
    });
    return pedidoHistoricoEmail;
  }

  public void deleteAllByPedidoId(UUID pedidoId) {
    repository.deleteAllByPedidoId(pedidoId);
  }
}
