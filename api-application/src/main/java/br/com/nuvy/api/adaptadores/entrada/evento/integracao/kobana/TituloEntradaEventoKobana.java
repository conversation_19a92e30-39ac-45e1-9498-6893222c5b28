package br.com.nuvy.api.adaptadores.entrada.evento.integracao.kobana;

import br.com.nuvy.api.financeiro.event.titulo.TituloBoletoGerado;
import br.com.nuvy.client.services.BoletoService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
@ConditionalOnProperty(
  name = "aws.sqs.queue.nuvy-enviar-email-boleto.enabled",
  havingValue = "false",
  matchIfMissing = true
)
class TituloEntradaEventoKobana {

  private final BoletoService boletoService;

  TituloEntradaEventoKobana(BoletoService boletoService) {
    this.boletoService = boletoService;
  }

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(TituloBoletoGerado evento) {
    boletoService.enviaEmailBoletos(evento.getIdTitulo());
  }
}
