package br.com.nuvy.api.adaptadores.saida.sqs.integracao.nfe;

import br.com.nuvy.api.cadastro.model.CertificadoDigital;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.repository.CertificadoDigitalRepository;
import br.com.nuvy.api.cadastro.repository.EmpresaRepository;
import br.com.nuvy.api.notafiscal.model.NotaFiscalSerie;
import br.com.nuvy.api.notafiscal.service.NotaFiscalSerieService;
import br.com.nuvy.multitenent.TenantContext;
import br.com.nuvy.integracao.nfe.dominio.evento.EnvioCertificadoEmpresaRecebidoNfe;
import br.com.nuvy.integracao.nfe.dominio.evento.EnvioEmpresaRecebidoNfe;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.MessageAttributeValue;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(
  name = "aws.sqs.queue.nfe-empresa-entrada.enabled",
  havingValue = "true"
)
class NfeSaidaSqs {

  private static final Logger log = LoggerFactory.getLogger(NfeSaidaSqs.class);

  private final ObjectMapper objectMapper;
  private final AmazonSQS amazonSqs;
  private final EmpresaRepository empresaRepository;
  private final CertificadoDigitalRepository certificadoDigitalRepository;
  private final NotaFiscalSerieService notaFiscalSerieService;

  @Value("${aws.sqs.queue.nfe-empresa-entrada.url}")
  private String sqsQueueUrl;

  NfeSaidaSqs(
    ObjectMapper objectMapper,
    AmazonSQS amazonSqs,
    EmpresaRepository empresaRepository,
    CertificadoDigitalRepository certificadoDigitalRepository,
    NotaFiscalSerieService notaFiscalSerieService
  ) {
    this.objectMapper = objectMapper;
    this.amazonSqs = amazonSqs;
    this.empresaRepository = empresaRepository;
    this.certificadoDigitalRepository = certificadoDigitalRepository;
    this.notaFiscalSerieService = notaFiscalSerieService;
    log.info("{} habilitado", NfeSaidaSqs.class.getSimpleName());
  }

  @EventListener
  void processaEvento(EnvioEmpresaRecebidoNfe evento) throws JsonProcessingException {
    log.info("Processando evento EnvioEmpresaRecebidoNfe para empresa ID: {}", evento.getIdEmpresa());
    log.debug("Detalhes do evento: ID={}, IdAplicacao={}, IdEmpresa={}, IdUsuario={}, OcorridoAs={}", 
              evento.getId(), evento.getIdAplicacao(), evento.getIdEmpresa(), 
              evento.getIdUsuario(), evento.getOcorridoAs());

    try {
      TenantContext.setTenant(evento.getIdAplicacao().toString());
      log.debug("Tenant definido como: {}", evento.getIdAplicacao().toString());

      Optional<Empresa> empresaOpt = empresaRepository.findById(evento.getIdEmpresa());
      if (empresaOpt.isEmpty()) {
        log.error("Empresa não encontrada: {}", evento.getIdEmpresa());
        return;
      }
      log.debug("Empresa encontrada com sucesso: {}", evento.getIdEmpresa());

      Empresa empresa = empresaOpt.get();

      Optional<CertificadoDigital> certificadoOpt = certificadoDigitalRepository
        .findByEmpresasId(empresa.getId());

      if (certificadoOpt.isEmpty()) {
        log.info("Não existe certificado digital para a empresa {}. Ignorando envio para SQS.", empresa.getId());
        return;
      }
      log.debug("Certificado digital encontrado para empresa: {}", empresa.getId());

      CertificadoDigital certificadoDigital = certificadoOpt.get();
      log.debug("Certificado ID: {}, Situação: {}", certificadoDigital.getId(), certificadoDigital.getSituacao());

      enviarDadosParaSqs(evento, empresa, certificadoDigital);
    } catch (Exception e) {
      log.error("Erro ao processar evento EnvioEmpresaRecebidoNfe: {}", e.getMessage(), e);
    }
  }

  @EventListener
  void processaEvento(EnvioCertificadoEmpresaRecebidoNfe evento) throws JsonProcessingException {
    log.info("Processando evento EnvioCertificadoEmpresaRecebidoNfe para empresa ID: {} e certificado ID: {}",
      evento.getIdEmpresa(), evento.getIdCertificado());

    TenantContext.setTenant(evento.getIdAplicacao().toString());

    Optional<Empresa> empresaOpt = empresaRepository.findById(evento.getIdEmpresa());
    if (empresaOpt.isEmpty()) {
      log.error("Empresa não encontrada: {}", evento.getIdEmpresa());
      return;
    }

    Empresa empresa = empresaOpt.get();

    Optional<CertificadoDigital> certificadoOpt = certificadoDigitalRepository
      .findById(evento.getIdCertificado());

    if (certificadoOpt.isEmpty()) {
      log.error("Certificado digital não encontrado: {}", evento.getIdCertificado());
      return;
    }

    CertificadoDigital certificadoDigital = certificadoOpt.get();

    enviarDadosParaSqs(evento, empresa, certificadoDigital);
  }

  private void enviarDadosParaSqs(Object evento, Empresa empresa, CertificadoDigital certificadoDigital)
    throws JsonProcessingException {

    log.debug("Iniciando envio de dados para SQS para empresa ID: {}", empresa.getId());
    
    try {
      NotaFiscalSerie notaFiscalSerie = null;
      List<NotaFiscalSerie> notaFiscalSerieList = notaFiscalSerieService.findAllByEmpresaId(empresa.getId());
      if (!notaFiscalSerieList.isEmpty()) {
        notaFiscalSerie = notaFiscalSerieList.getFirst();
        log.debug("Série de nota fiscal encontrada: {}", notaFiscalSerie.getId());
      } else {
        log.debug("Nenhuma série de nota fiscal encontrada para a empresa ID: {}", empresa.getId());
      }

      Map<String, Object> payload = new HashMap<>();
      payload.put("tipo", "SalvarEmpresa");
      payload.put("idExterno", empresa.getId().toString());
      payload.put("documento", empresa.getCpfCnpj());
      payload.put("nome", empresa.getNome());
      payload.put("uf", empresa.getUf());
      payload.put("status", "ATIVA");
      payload.put("ambiente", "PRODUCAO"); //definido como padrao
      payload.put("coletaNfe", empresa.getColetaSefazNfe());
      payload.put("caminhoCertificado", certificadoDigital.getCaminho());
      payload.put("senhaCertificado", certificadoDigital.getSenha());
      payload.put("caminhoLogo", empresa.getLogotipo());

      // Adiciona campos adicionais da empresa
      payload.put("ie", empresa.getInscricaoEstadual());
      payload.put("razaoSocial", empresa.getNome());
      payload.put("nomeFantasia", empresa.getNomeFantasia());
      payload.put("logradouro", empresa.getEndereco());
      payload.put("numero", empresa.getNumero());
      payload.put("bairro", empresa.getBairro());
      payload.put("codigoMunicipio", empresa.getCodigoCidade());
      payload.put("nomeMunicipio", empresa.getCidade());
      payload.put("cep", empresa.getCep());
      payload.put("telefone", empresa.getTelefone());
      payload.put("email", empresa.getEmail());

      // Adiciona os campos de MDF-e se existirem
      if (notaFiscalSerie != null) {
        payload.put("numeracaoMdfe", notaFiscalSerie.getMdfeNumero());
        payload.put("serieMdfe", notaFiscalSerie.getMdfeSerie());
      }

      String message = objectMapper.writeValueAsString(payload);
      log.debug("Payload JSON serializado com sucesso");

      Map<String, MessageAttributeValue> attributes;

      if (evento instanceof EnvioEmpresaRecebidoNfe e) {
        log.debug("Preparando atributos para evento EnvioEmpresaRecebidoNfe");
        attributes = Map.of(
          "tipo", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue("SalvarEmpresa"),
          "id", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getId().toString()),
          "idAplicacao", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdAplicacao().toString()),
          "idEmpresa", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdEmpresa().toString()),
          "idUsuario", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdUsuario().toString()),
          "entidade", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(Empresa.class.getSimpleName()),
          "idEntidade", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdEmpresa().toString()),
          "dataHora", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getOcorridoAs().toString())
        );
      } else if (evento instanceof EnvioCertificadoEmpresaRecebidoNfe e) {
        log.debug("Preparando atributos para evento EnvioCertificadoEmpresaRecebidoNfe");
        attributes = Map.of(
          "tipo", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue("SalvarEmpresa"),
          "id", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getId().toString()),
          "idAplicacao", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdAplicacao().toString()),
          "idEmpresa", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdEmpresa().toString()),
          "idCertificado", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdCertificado().toString()),
          "idUsuario", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdUsuario().toString()),
          "entidade", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(CertificadoDigital.class.getSimpleName()),
          "idEntidade", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getIdCertificado().toString()),
          "dataHora", new MessageAttributeValue()
            .withDataType("String")
            .withStringValue(e.getOcorridoAs().toString())
        );
      } else {
        log.warn("Tipo de evento desconhecido: {}", evento.getClass().getName());
        return;
      }

      log.debug("URL da fila SQS: {}", sqsQueueUrl);
      log.debug("Enviando evento {} para a fila nfe-empresa-entrada", "SalvarEmpresa");

      SendMessageRequest request = new SendMessageRequest()
        .withQueueUrl(sqsQueueUrl)
        .withMessageBody(message)
        .withMessageAttributes(attributes);

      SendMessageResult resultado = amazonSqs.sendMessage(request);
      String messageId = resultado.getMessageId();

      log.info("Mensagem enviada para a fila com sucesso. MessageID: {}", messageId);
    } catch (Exception e) {
      log.error("Erro ao enviar dados para SQS: {}", e.getMessage(), e);
      throw e; // Re-throw para que o chamador possa tratar
    }
  }
}