package br.com.nuvy.api.adaptadores.entrada.evento.integracao.plug4market;

import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnviaProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.evento.ProdutoRecebidoPlug4market;
import br.com.nuvy.multitenent.TenantContext;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
  name = "aws.sqs.queue.plug4market.enabled",
  havingValue = "false",
  matchIfMissing = true
)
class Plug4marketEntradaEvento {

  private static final Logger log = LoggerFactory.getLogger(Plug4marketEntradaEvento.class);

  private final ProcessadorProdutoPlug4market processadorProduto;

  @Async
  @EventListener
  void processaEvento(ProdutoRecebidoPlug4market evento) {
    try {
      TenantContext.setTenant(evento.getIdAplicacao());
      BadgeContext.generateBadgeContext(
        evento.getIdAplicacao(),
        evento.getIdEmpresa(),
        evento.getIdUsuario()
      );
      processadorProduto.processaComando(new EnviaProdutoPlug4market(
        UUID.randomUUID(), evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
        evento.getIdProduto(), evento.getOcorridoAs()
      ));
    } catch (Exception ex) {
      log.error("Erro ao processar comando {}!", EnviaProdutoPlug4market.class.getSimpleName(), ex);
    }
  }
}
