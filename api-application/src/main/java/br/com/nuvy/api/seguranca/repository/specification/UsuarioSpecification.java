package br.com.nuvy.api.seguranca.repository.specification;

import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.filter.UsuarioFilter;
import br.com.nuvy.api.seguranca.model.Usuario_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
@EqualsAndHashCode
public class UsuarioSpecification implements Specification<Usuario> {

  private final UsuarioFilter filter;

  @Override
  public Predicate toPredicate(Root<Usuario> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getNome(),
        e -> criteriaBuilder.equal(root.get(Usuario_.nome), e))
      .add(filter.getEmail(),
        e -> criteriaBuilder.equal(root.get(Usuario_.email), e))
      .add(filter.getSituacao(),
        e -> criteriaBuilder.equal(root.get(Usuario_.situacao), e))
      .and();
  }
}
