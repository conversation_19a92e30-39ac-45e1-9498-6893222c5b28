package br.com.nuvy.api.compras.repository;

import br.com.nuvy.api.compras.model.DestinacaoCstIcms;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DestinacaoIcmsCstRepository extends NuvyRepository<DestinacaoCstIcms, Integer> {

  @Query(nativeQuery = true, value =
    "Select * from cc_dest_icms_cst c where c.id_dest = :destinacaoCompraId "
      + "and c.cod_cst_icms_origem = :cstIcmsOrigemCodigo "
      + "and c.cod_csosn_icms_entrada is not null")
  Optional<DestinacaoCstIcms> findDestinacaoForEntradaItem(Integer destinacaoCompraId,
    String cstIcmsOrigemCodigo);

}
