package br.com.nuvy.api.anuncio.repository.specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;

import br.com.nuvy.api.anuncio.Catalogo;
import br.com.nuvy.api.anuncio.Catalogo_;
import br.com.nuvy.api.anuncio.filter.CatalogoFilter;
import br.com.nuvy.api.enums.Marketplace;
import br.com.nuvy.api.venda.model.CanalOrigem_;
import br.com.nuvy.api.venda.model.CanalVenda_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "of")
public class CatalogoSpecification implements Specification<Catalogo> {

  private final CatalogoFilter filter;

  @Override
  public Predicate toPredicate(
    Root<Catalogo> catalogo, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    var marketplaces = Marketplace.allUsaCatalogo().map(Marketplace::getCanalOrigemId).toList();

    var canalVenda = catalogo.join(Catalogo_.canalVendas, JoinType.LEFT);
    var origem = canalVenda.join(CanalVenda_.origem, JoinType.LEFT);
    var predicate =  PredicateBuilder.create(builder)
      .add(marketplaces,
        e -> builder.or(origem.get(CanalOrigem_.id).isNull(), origem.get(CanalOrigem_.id).in(e)))
      .add(filter.getNome(), e -> iLike(builder, catalogo.get(Catalogo_.nome), e))
      .add(filter.getMarketplace(), e -> iLike(builder, origem.get(CanalOrigem_.nome), e))
      .add(filter.getIdEmpresa(), e -> builder.equal(catalogo.get(Catalogo_.empresa).get("id"), e));
    if (ObjectUtils.isNotEmpty(filter.getSituacao())){
      predicate = predicate.add(filter.getSituacao(),
        e -> builder.equal(catalogo.get(Catalogo_.situacao), e));
    }
    return predicate.and();
  }
}
