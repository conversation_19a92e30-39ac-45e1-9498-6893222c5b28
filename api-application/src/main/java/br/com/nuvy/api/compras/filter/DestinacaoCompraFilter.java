package br.com.nuvy.api.compras.filter;

import br.com.nuvy.common.base.filter.Filter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springdoc.core.annotations.ParameterObject;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ParameterObject
public class DestinacaoCompraFilter implements Filter {

  private Integer id;
  private String nome;
  private String tipo;
  private String geraContasPagar;
  private String geraMovimentacaoEstoque;
  private String situacao;
  private String criterioSituacao;
  private String empresasNomeFantasia;
  private String criterio;
}
