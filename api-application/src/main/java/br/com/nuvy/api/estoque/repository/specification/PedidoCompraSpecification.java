package br.com.nuvy.api.estoque.repository.specification;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.estoque.filter.PedidoCompraFilter;
import br.com.nuvy.api.pedidocompra.model.PedidoCompra;
import br.com.nuvy.api.pedidocompra.model.PedidoCompra_;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompra;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompra_;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
@Slf4j
public class PedidoCompraSpecification implements Specification<PedidoCompra> {

  private final transient PedidoCompraFilter filter;
  private final Empresa empresa;

  transient Join<SolicitacaoCompra, Usuario> joinUsuario;

  @Override
  public Predicate toPredicate(Root<PedidoCompra> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);

    var predicate = predicateBuilder

      .add(filter.getNumeroPedido(),
        e -> criteriaBuilder.equal(root.get(PedidoCompra_.numeroPedido), e))
      .add(filter.getEmpresa(),
        e -> {
          Join<PedidoCompra, Empresa> join = root.join(PedidoCompra_.empresa,
            JoinType.LEFT);
          return criteriaBuilder.equal(join.get(Empresa_.id), e);
        });

    if (filter.getEmpresa() != null) {
      predicate = predicateBuilder.add(filter.getEmpresa(),
        e -> root.get(PedidoCompra_.empresa).get(Empresa_.id).in(e));
    } else {
      predicate = predicateBuilder.add(empresa.getId(),
        e -> criteriaBuilder.equal(root.get(PedidoCompra_.empresa).get(Empresa_.id), e));
    }

    return predicate.and();
  }

  public Join<SolicitacaoCompra, Usuario> getJoinUsuario(Root<SolicitacaoCompra> root) {
    if (joinUsuario == null) {
      return root.join(SolicitacaoCompra_.solicitante, JoinType.LEFT);
    }
    return joinUsuario;
  }

}
