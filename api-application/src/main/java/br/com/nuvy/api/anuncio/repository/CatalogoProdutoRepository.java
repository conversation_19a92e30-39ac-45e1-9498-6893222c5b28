package br.com.nuvy.api.anuncio.repository;

import br.com.nuvy.api.anuncio.CatalogoProduto;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Repository;

@Repository
public interface CatalogoProdutoRepository extends NuvyRepository<CatalogoProduto, UUID> {

  List<CatalogoProduto> findCatalogoProdutosByProdutoId(Integer produtoId);

  Optional<CatalogoProduto> findCatalogoProdutoByProdutoIdAndCatalogoId(Integer produtoId, Integer catalogoId);

}
