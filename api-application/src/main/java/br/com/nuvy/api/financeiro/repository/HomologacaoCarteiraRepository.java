package br.com.nuvy.api.financeiro.repository;

import br.com.nuvy.api.financeiro.model.HomologacaoCarteira;
import br.com.nuvy.common.base.repository.NuvyRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface HomologacaoCarteiraRepository extends NuvyRepository<HomologacaoCarteira, Integer> {

  Optional<HomologacaoCarteira> findByContaBancariaId(Integer contaBancariaId);


}
