package br.com.nuvy.api.cadastro.repository.specification;

import br.com.nuvy.api.cadastro.model.Cest;
import br.com.nuvy.api.cadastro.model.Cest_;
import br.com.nuvy.common.query.PredicateBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;

@RequiredArgsConstructor
public class CestCriterioSpecification implements Specification<Cest> {

  private final transient String criterio;

  @Override
  public Predicate toPredicate(Root<Cest> root, CriteriaQuery<?> query,
                               CriteriaBuilder criteriaBuilder) {
    query.distinct(true);
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
            .add(criterio,
                    e -> criteriaBuilder.like(criteriaBuilder.upper(root.get(Cest_.id)), criterio.toUpperCase() + "%"))
            .add(criterio,
                    e -> iLike(criteriaBuilder, root.get(Cest_.descricao), criterio))
            .or();
  }
}
