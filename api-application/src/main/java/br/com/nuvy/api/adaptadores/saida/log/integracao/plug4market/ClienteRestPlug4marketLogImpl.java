package br.com.nuvy.api.adaptadores.saida.log.integracao.plug4market;

import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonCategoriaPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonProdutoPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonTokenPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorCategoriaPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorProdutoPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorTokenPlug4market;
import br.com.nuvy.config.plug4market.Plug4marketConfig;
import br.com.nuvy.integracao.plug4market.aplicacao.porta.saida.rest.ClienteRestPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.CategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.ProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.RetornoTokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.TokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.ErroComunicacaoPlug4market;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(
  name = "nuvy.service.plug4market.enabled",
  havingValue = "false",
  matchIfMissing = true
)
@RequiredArgsConstructor
class ClienteRestPlug4marketLogImpl implements ClienteRestPlug4market {

  private static final Logger log = LoggerFactory.getLogger(ClienteRestPlug4marketLogImpl.class);

  private static final ObjectMapper jsonMapper = Plug4marketConfig.plug4marketJsonMapper();

  private final MapeadorTokenPlug4market mapeadorAutenticacao;
  private final MapeadorProdutoPlug4market mapeadorProduto;
  private final MapeadorCategoriaPlug4market mapeadorCategoria;

  @Override
  public ObjectMapper getJsonMapper() {
    return null;
  }

  @Override
  public RetornoTokenPlug4market postAtualizacaoToken(TokenPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonTokenPlug4market json = mapeadorAutenticacao.converte(entidade);
    try {
      log.debug(
        "Plug4market POST atualização token: {}",
        jsonMapper.writerWithDefaultPrettyPrinter()
          .writeValueAsString(json)
      );
      return null;
    } catch (Exception ex) {
      log.error("Erro ao atualizar o token no Plug4market!", ex);
      throw new ErroComunicacaoPlug4market("Erro ao atualizar o token no Plug4market!");
    }
  }

  @Override
  public void postProduto(String token, ProdutoPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonProdutoPlug4market json = mapeadorProduto.converte(entidade);
    try {
      log.debug(
        "Plug4market POST produto: {}",
        jsonMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json)
      );
    } catch (Exception ex) {
      log.error("Erro ao enviar produto para o Plug4market!", ex);
      throw new ErroComunicacaoPlug4market("Erro ao enviar produto para o Plug4market!");
    }
  }

  @Override
  public void putProduto(String token, ProdutoPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonProdutoPlug4market json = mapeadorProduto.converte(entidade);
    try {
      log.debug(
        "Plug4market PUT produto {}: {}",
        entidade.getSku(),
        jsonMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json)
      );
    } catch (Exception ex) {
      log.error("Erro ao atualizar produto no Plug4market!", ex);
      throw new ErroComunicacaoPlug4market("Erro ao atualizar produto no Plug4market!");
    }
  }
}
