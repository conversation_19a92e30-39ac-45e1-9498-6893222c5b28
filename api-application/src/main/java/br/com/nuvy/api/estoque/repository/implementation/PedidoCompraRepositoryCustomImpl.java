package br.com.nuvy.api.estoque.repository.implementation;

import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.estoque.repository.PedidoCompraRepositoryCustom;
import br.com.nuvy.api.estoque.repository.SolicitacaoCompraRepositoryCustom;
import br.com.nuvy.api.estoque.repository.specification.PedidoCompraSpecification;
import br.com.nuvy.api.estoque.repository.specification.SolicitacaoCompraSpecification;
import br.com.nuvy.api.pedidocompra.model.PedidoCompra;
import br.com.nuvy.api.pedidocompra.model.PedidoCompra_;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompra;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompra_;
import br.com.nuvy.api.seguranca.model.Usuario_;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.PedidoCompraListagemDto;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.SolicitacaoCompraListagemDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class PedidoCompraRepositoryCustomImpl implements
  PedidoCompraRepositoryCustom {

  private final EntityManager entityManager;

  @Override
  public Page<PedidoCompraListagemDto> findPedidosCompraBuscaLista(
    PedidoCompraSpecification specification, Pageable pageable) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();

    CriteriaQuery<PedidoCompraListagemDto> query = cb.createQuery(
      PedidoCompraListagemDto.class);

    Root<PedidoCompra> root = query.from(PedidoCompra.class);
    Long count = getCountLista(specification, cb);
    if (count == 0) {
      return Page.empty();
    }

    Join<PedidoCompra, Pessoa> joinFornecedor = root.join(PedidoCompra_.fornecedor, JoinType.LEFT);

    query.select(cb.construct(
      PedidoCompraListagemDto.class,
      root.get(PedidoCompra_.id).alias("id"),
      root.get(PedidoCompra_.uuid).alias("uuid"),
      root.get(PedidoCompra_.empresa).get(Empresa_.id).alias("empresaId"),
      root.get(PedidoCompra_.empresa).get(Empresa_.nome).alias("empresaNome"),
      root.get(PedidoCompra_.situacao).alias("situacao"),
      root.get(PedidoCompra_.numeroPedido).alias("numeroPedido"),
      joinFornecedor.get(Pessoa_.id).alias("fornecedorId"),
      joinFornecedor.get(Pessoa_.nome).alias("fornecedorNome"),
      root.get(PedidoCompra_.dataSolicitacao).alias("dataSolicitacao"),
      root.get(PedidoCompra_.previsaoEntrega).alias("previsaoEntrega")
    ));

    Predicate predicate = specification
      .toPredicate(root, query, cb);

    if (predicate != null) {
      query.where(predicate);
    }

    if (pageable.getSort().isSorted()) {
      var lista = query.getSelection().getCompoundSelectionItems();
      List<Order> orders = new ArrayList<>();
      pageable.getSort().forEach(order -> {
        String property = order.getProperty();
        Expression<?> expression;
        try {
          expression = root.get(property);
        } catch (Exception e) {
          expression = (Expression<?>) lista.stream()
            .filter(item -> item.getAlias().equals(property)).findFirst()
            .orElseThrow(
              () -> new RuntimeException("Campo de ordenação não encontrado: " + property));
        }
        if (order.isDescending()) {
          orders.add(cb.desc(expression));
        } else {
          orders.add(cb.asc(expression));
        }
      });
      query.orderBy(orders);
    }

    TypedQuery<PedidoCompraListagemDto> typedQuery = entityManager.createQuery(query);
    typedQuery.setFirstResult((int) pageable.getOffset());
    typedQuery.setMaxResults(pageable.getPageSize());

    List<PedidoCompraListagemDto> resultados = typedQuery.getResultList();

    return new PageImpl<>(resultados, pageable, count);
  }

  private Long getCountLista(PedidoCompraSpecification specification,
    CriteriaBuilder criteriaBuilder) {
    CriteriaQuery<Long> countQuery = criteriaBuilder.createQuery(Long.class);
    Root<PedidoCompra> countRoot = countQuery.from(PedidoCompra.class);
    countQuery.select(criteriaBuilder.count(countRoot));

    Predicate countPredicate = specification
      .toPredicate(countRoot, countQuery, criteriaBuilder);

    if (countPredicate != null) {
      countQuery.where(countPredicate);
    }
    var count = entityManager.createQuery(countQuery).getSingleResult();
    return count;
  }

}
