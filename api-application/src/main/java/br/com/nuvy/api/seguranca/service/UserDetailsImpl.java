package br.com.nuvy.api.seguranca.service;

import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import java.util.Collection;
import java.util.HashSet;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

@Getter @Setter
@AllArgsConstructor
public class UserDetailsImpl implements UserDetails {

  private final Usuario usuario;
  private final Collection<SimpleGrantedAuthority> authorities = new HashSet<>();

  public static UserDetailsImpl of(Usuario usuario, UsuarioAplicacao usuarioAplicacao) {
    var result = new UserDetailsImpl(usuario);
    if (usuarioAplicacao == null) {
      return result;
    }
    usuarioAplicacao.getPerfilsAplicacao().stream()
      .flatMap(p -> p.getPermissoes().stream())
      .map(f -> new SimpleGrantedAuthority("ROLE_" + f.getFuncionalidade().getId()))
      .collect(Collectors.toCollection(() -> result.authorities));

    return result;
  }

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    return authorities;
  }

  @Override
  public String getPassword() {
    return usuario.getSenha();
  }

  @Override
  public String getUsername() {
    return usuario.getEmail();
  }

  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  @Override
  public boolean isAccountNonLocked() {
    return true;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  @Override
  public boolean isEnabled() {
    return true;
  }
}
