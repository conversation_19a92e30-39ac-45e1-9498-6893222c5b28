package br.com.nuvy.api.sqs.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BoletoRecebidoDto {

  private UUID id_aplicacao;
  private Integer id_empresa;
  private UUID id_usuario;
  private String id;
  private Integer id_titulo;
  private Integer id_conta_bancaria;
  private String barcode;
  private String our_number;
  private String processed_our_number_raw;
  private String document_number;
  private Formats formats;
  private String url;
  private LocalDate expire_at;
  private BigDecimal valor;
  private LocalDate dataVencimento;

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class Formats {
    private String defaultFormat;
    private String png;
    private String pdf;
    private String boleto_hibrido;
    private String boleto_pix;
    private String barcode;
    private String envelope;
    private String letter;
    private String line;
    private String recibo;
  }

  public BoletoRecebidoDto(
    String id, Integer idTitulo, String nossoNumero,
    String codigoBarras, String linhaDigitavel, String urlPdf,
    BigDecimal valor, LocalDate dataVencimento
  ) {
    this(
      null, null, null, id, idTitulo, null, codigoBarras, nossoNumero, nossoNumero,
      null, new Formats(
        null, null, urlPdf, null, null, codigoBarras, null, null, linhaDigitavel, null
      ), null, null, valor, dataVencimento
    );
  }
}
