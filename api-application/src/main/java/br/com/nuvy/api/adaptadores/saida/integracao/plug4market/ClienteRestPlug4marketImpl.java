package br.com.nuvy.api.adaptadores.saida.integracao.plug4market;

import static java.util.Objects.requireNonNull;

import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonCategoriaPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonTokenPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonRetornoTokenPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorCategoriaPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorTokenPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador.MapeadorProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.porta.saida.rest.ClienteRestPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.CategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.ProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.TokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.RetornoTokenPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.ErroComunicacaoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.excecao.EntidadeNaoEncontradaPlug4market;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

@Component
@ConditionalOnProperty(
  name = "nuvy.service.plug4market.enabled",
  havingValue = "true"
)
class ClienteRestPlug4marketImpl implements ClienteRestPlug4market {

  private static final Logger log = LoggerFactory.getLogger(ClienteRestPlug4marketImpl.class);

  private final RestTemplate restTemplate;
  private final MapeadorTokenPlug4market mapeadorAutenticacao;
  private final MapeadorProdutoPlug4market mapeadorProduto;
  private final MapeadorCategoriaPlug4market mapeadorCategoria;

  public ClienteRestPlug4marketImpl(
    @Qualifier("plug4marketRestTemplate") RestTemplate restTemplate,
    MapeadorTokenPlug4market mapeadorAutenticacao,
    MapeadorProdutoPlug4market mapeadorProduto,
    MapeadorCategoriaPlug4market mapeadorCategoria
  ) {
    this.restTemplate = restTemplate;
    this.mapeadorAutenticacao = mapeadorAutenticacao;
    this.mapeadorProduto = mapeadorProduto;
    this.mapeadorCategoria = mapeadorCategoria;
  }

  @Override
  @SuppressWarnings("OptionalGetWithoutIsPresent")
  public ObjectMapper getJsonMapper() {
    return restTemplate.getMessageConverters().stream()
      .filter(MappingJackson2HttpMessageConverter.class::isInstance)
      .map(MappingJackson2HttpMessageConverter.class::cast)
      .map(MappingJackson2HttpMessageConverter::getObjectMapper)
      .findFirst().get();
  }

  private String writeValueAsString(Object value) {
    try {
      return getJsonMapper().writeValueAsString(value);
    } catch (JsonProcessingException ex) {
      throw new RuntimeException(ex);
    }
  }

  private void trataExcecao(
    String metodo, Object json, RestClientResponseException ex
  ) {
    log.warn(
      "Plug4market {} - Erro: {}, Json: {}",
      metodo, ex.getResponseBodyAsString(), writeValueAsString(json)
    );
  }

  @Override
  public RetornoTokenPlug4market postAtualizacaoToken(TokenPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonTokenPlug4market json = mapeadorAutenticacao.converte(entidade);
    try {
      ResponseEntity<JsonRetornoTokenPlug4market> response = restTemplate.exchange(
        "/auth/refresh", HttpMethod.POST,
        new HttpEntity<>(json), JsonRetornoTokenPlug4market.class
      );
      return requireNonNull(response.getBody(), "Plug4market POST atualização de token. Resposta sem corpo");
    } catch (
      HttpClientErrorException.BadRequest
      | HttpClientErrorException.UnprocessableEntity
      | HttpClientErrorException.Unauthorized ex
    ) {
      trataExcecao("POST atualização de token", json, ex);
      return null;
    } catch (HttpClientErrorException | HttpServerErrorException ex) {
      trataExcecao("POST atualização de token", json, ex);
      throw new ErroComunicacaoPlug4market(ex.getMessage());
    }
  }

  @Override
  public void postProduto(String token, ProdutoPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonProdutoPlug4market json = mapeadorProduto.converte(entidade);
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.set(HttpHeaders.AUTHORIZATION, "Bearer %s".formatted(token));

      restTemplate.exchange(
        "/products", HttpMethod.POST,
        new HttpEntity<>(json, headers), Void.class
      );
    } catch (
      HttpClientErrorException.BadRequest
      | HttpClientErrorException.UnprocessableEntity
      | HttpClientErrorException.Unauthorized ex
    ) {
      trataExcecao("POST produto", json, ex);
    } catch (HttpClientErrorException | HttpServerErrorException ex) {
      trataExcecao("POST produto", json, ex);
      throw new ErroComunicacaoPlug4market(ex.getMessage());
    }
  }

  @Override
  public void putProduto(String token, ProdutoPlug4market entidade)
    throws ErroComunicacaoPlug4market, EntidadeNaoEncontradaPlug4market
  {
    JsonProdutoPlug4market json = mapeadorProduto.converte(entidade);
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.set(HttpHeaders.AUTHORIZATION, "Bearer %s".formatted(token));

      restTemplate.exchange(
        "/products/" + entidade.getSku(), HttpMethod.PUT,
        new HttpEntity<>(json, headers), Void.class
      );
    } catch (HttpClientErrorException.NotFound ex) {
      trataExcecao("PUT produto", json, ex);
      throw new EntidadeNaoEncontradaPlug4market(
        "Produto não encontrado no Plug4market: " + entidade.getSku());
    } catch (
      HttpClientErrorException.BadRequest
      | HttpClientErrorException.UnprocessableEntity
      | HttpClientErrorException.Unauthorized ex
    ) {
      trataExcecao("PUT produto", json, ex);
    } catch (HttpClientErrorException | HttpServerErrorException ex) {
      trataExcecao("PUT produto", json, ex);
      throw new ErroComunicacaoPlug4market(ex.getMessage());
    }
  }

  @Override
  public void postCategoria(String token, CategoriaPlug4market entidade)
    throws ErroComunicacaoPlug4market
  {
    JsonCategoriaPlug4market json = mapeadorCategoria.converte(entidade);
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.set(HttpHeaders.AUTHORIZATION, "Bearer %s".formatted(token));

      restTemplate.exchange(
        "/custom-categories", HttpMethod.POST,
        new HttpEntity<>(json, headers), Void.class
      );
    } catch (
      HttpClientErrorException.BadRequest
      | HttpClientErrorException.UnprocessableEntity
      | HttpClientErrorException.Unauthorized ex
    ) {
      trataExcecao("POST categoria", json, ex);
    } catch (HttpClientErrorException | HttpServerErrorException ex) {
      trataExcecao("POST categoria", json, ex);
      throw new ErroComunicacaoPlug4market(ex.getMessage());
    }
  }

  @Override
  public void putCategoria(String token, CategoriaPlug4market entidade)
    throws ErroComunicacaoPlug4market, EntidadeNaoEncontradaPlug4market
  {
    JsonCategoriaPlug4market json = mapeadorCategoria.converte(entidade);
    try {
      HttpHeaders headers = new HttpHeaders();
      headers.set(HttpHeaders.AUTHORIZATION, "Bearer %s".formatted(token));

      restTemplate.exchange(
        "/custom-categories/" + entidade.getAlternativeId(), HttpMethod.PUT,
        new HttpEntity<>(json, headers), Void.class
      );
    } catch (HttpClientErrorException.NotFound ex) {
      trataExcecao("PUT categoria", json, ex);
      throw new EntidadeNaoEncontradaPlug4market(
        "Categoria não encontrada no Plug4market: " + entidade.getAlternativeId());
    } catch (
      HttpClientErrorException.BadRequest
      | HttpClientErrorException.UnprocessableEntity
      | HttpClientErrorException.Unauthorized ex
    ) {
      trataExcecao("PUT categoria", json, ex);
    } catch (HttpClientErrorException | HttpServerErrorException ex) {
      trataExcecao("PUT categoria", json, ex);
      throw new ErroComunicacaoPlug4market(ex.getMessage());
    }
  }
}
