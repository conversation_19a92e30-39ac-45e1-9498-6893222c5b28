package br.com.nuvy.api.seguranca.service;

import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;

import br.com.nuvy.api.admin.BackClienteUsuario;
import br.com.nuvy.api.enums.BackSituacaoUsuario;
import br.com.nuvy.api.enums.SituacaoUsuario;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.repository.BackClienteUsuarioRepository;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.credential.Badge;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BackClienteUsuarioService {

  private final BackClienteUsuarioRepository clienteUsuarioRepository;
  private final Badge badge;

  public void createUser(List<Integer> perfil, Usuario usuarioErp, String idEmpresa) {
    var clienteUsuario = clienteUsuarioRepository.findByclienteIdAndEmailIgnoreCase(idEmpresa,
      usuarioErp.getEmail());

    if (clienteUsuario != null) {
      log.error("Usuario ja cadastrado na base do backoffice!");
    } else {
      BackClienteUsuario backClienteUsuario = BackClienteUsuario.builder()
        .id(usuarioErp.getId().toString())
        .clienteId(idEmpresa)
        .email(usuarioErp.getEmail())
        .nome(usuarioErp.getNome())
        .situacao(mapSituationRelationship(usuarioErp.getSituacao()))
        .perfilId(perfil.get(0).toString())
        .administrador(false)
        .recebeNf(false)
        .createdAt(LocalDateTime.now())
        .build();
      clienteUsuarioRepository.save(backClienteUsuario);
    }
  }

  public void updateUser(UsuarioAplicacao usuarioAplicacao, String perfil) {
    var clienteUsuario = clienteUsuarioRepository.findByclienteIdAndEmailIgnoreCase(badge.getTenantId(),
      usuarioAplicacao.getUsuario().getEmail());
    if (clienteUsuario == null) {
      log.error("Usuario nao encontrado na base do backoffice!");
      throw new ResourceNotFoundException();
    }
    clienteUsuario.setEmail(usuarioAplicacao.getUsuario().getEmail());
    clienteUsuario.setNome(usuarioAplicacao.getUsuario().getNome());
    clienteUsuario.setSituacao(mapSituationRelationship(usuarioAplicacao.getSituacao()));
    clienteUsuario.setTelefone(getOrElse(usuarioAplicacao.getPessoa().getTelefone(), ""));
    clienteUsuario.setPerfilId(perfil);
    clienteUsuario.setSetorId("1");//getOrElse(usuarioAplicacao.getSetor().getId().toString(), ""));
    clienteUsuario.setUpdatedAt(LocalDateTime.now());
    clienteUsuarioRepository.save(clienteUsuario);
  }

  public void updateMeusDados(UsuarioAplicacao usuarioAplicacao) {
    var clienteUsuario = clienteUsuarioRepository.findByclienteIdAndEmailIgnoreCase(badge.getTenantId(),
      usuarioAplicacao.getUsuario().getEmail());
    if (clienteUsuario != null) {
      clienteUsuario.setEmail(usuarioAplicacao.getUsuario().getEmail());
      clienteUsuario.setNome(usuarioAplicacao.getUsuario().getNome());
      clienteUsuario.setTelefone(getOrElse(usuarioAplicacao.getPessoa().getTelefone(), ""));
      clienteUsuario.setSetorId("1");//getOrElse(usuarioAplicacao.getSetor().getId().toString(), ""));
      clienteUsuario.setSituacao(mapSituationRelationship(usuarioAplicacao.getSituacao()));
      clienteUsuarioRepository.save(clienteUsuario);
    } else {
      log.error("Usuario nao encontrado na base do backoffice!");
      throw new ResourceNotFoundException();
    }
  }

  public void deleteUser(UsuarioAplicacao usuarioAplicacao) {
    var usuarioBackoffice = clienteUsuarioRepository.findByclienteIdAndEmailIgnoreCase(badge.getTenantId(),
      usuarioAplicacao.getUsuario().getEmail());
    if (usuarioBackoffice == null) {
      log.error("Usuario nao encontrado na base do backoffice!");
      throw new ResourceNotFoundException();
    }
    clienteUsuarioRepository.deleteById(usuarioBackoffice.getId());
  }

  private BackSituacaoUsuario mapSituationRelationship(SituacaoUsuario situacao) {
      return switch (situacao) {
          case ATIVO -> BackSituacaoUsuario.ATIVO;
          case PENDENTE -> BackSituacaoUsuario.PENDENTE;
          default -> BackSituacaoUsuario.SUSPENSO;
      };
  }
}
