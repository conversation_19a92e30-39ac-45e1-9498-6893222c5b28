package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.CondicaoPagamento;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CondicaoPagamentoRepository extends NuvyRepository<CondicaoPagamento, Integer> {

  @Query("SELECT cp FROM CondicaoPagamento cp WHERE cp.aplicacao = :aplicacao order by cp.ordem")
  List<CondicaoPagamento> findCondicaoPagamentoByAplicacao(String aplicacao);

  @Query("""
    SELECT cp
    FROM CondicaoPagamento cp
    WHERE cp.quantidadeParcelas = 0
    ORDER BY cp.ordem ASC
    LIMIT 1
    """)
  Optional<CondicaoPagamento> getUmaParcelaAVista();
}
