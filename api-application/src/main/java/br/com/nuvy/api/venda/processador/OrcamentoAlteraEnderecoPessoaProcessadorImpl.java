package br.com.nuvy.api.venda.processador;

import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.api.cadastro.repository.PessoaRepository;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoRepository;
import br.com.nuvy.api.venda.comando.OrcamentoPessoaAlteraEndereco;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

import static br.com.nuvy.api.enums.SituacaoOrdemServico.APROVADO;
import static br.com.nuvy.api.enums.SituacaoOrdemServico.NF_REJEITADA;
import static br.com.nuvy.api.enums.SituacaoOrdemServico.ORCAMENTO;
import static br.com.nuvy.api.enums.SituacaoOrdemServico.RECORRENCIA;

@Component
@RequiredArgsConstructor
public class OrcamentoAlteraEnderecoPessoaProcessadorImpl implements OrcamentoAlteraEnderecoPessoaProcessador {
  private final PessoaRepository pessoaRepository;
  private final OrdemServicoRepository ordemServicoRepository;
  private final OrdemServicoNotaFiscalRepository ordemServicoNotaFiscalRepository;


  @Override
  public void processaComando(OrcamentoPessoaAlteraEndereco orcamentoAlteraEndereco) {
    var pessoa = pessoaRepository.findById(orcamentoAlteraEndereco.getIdPessoa())
      .orElseThrow(() -> new ResourceNotFoundException("pessoa.nao.encontrada"));
    var enderecoPrincipal = pessoa.getEnderecos().stream()
      .filter(endereco -> TipoEndereco.FISCAL.equals(endereco.getTipo()))
      .findFirst()
      .orElseThrow(() -> new PreconditionException("cliente.com.endereco.vazio"));
    var ordemServicoList = ordemServicoRepository.findAllByClienteAndSituacaoIn(pessoa, List.of(ORCAMENTO,APROVADO, RECORRENCIA, NF_REJEITADA));
    var ids = new ArrayList<Integer>();
    for (OrdemServico ordemServico : ordemServicoList) {
      updateOrdemServico(ordemServico, enderecoPrincipal);
      ids.add(ordemServico.getId().intValue());
    }
    var ordemServicoNotaFiscalList = ordemServicoNotaFiscalRepository.findAllByOrdemServico_ClienteAndSituacaoIn(pessoa, List.of("NOVA", "PENDENTE", "NF_REJEITADA"));
    for (var ordemServicoNotaFiscal : ordemServicoNotaFiscalList) {
        updateOrdemServicoNotaFiscal(ordemServicoNotaFiscal, enderecoPrincipal);
        if (!ids.contains(ordemServicoNotaFiscal.getOrdemServico().getId().intValue())) {
          updateOrdemServico(ordemServicoNotaFiscal.getOrdemServico(), enderecoPrincipal);
        }
      }
    }

  private void updateOrdemServico(OrdemServico ordemServico, EnderecoPessoa enderecoPrincipal){
    ordemServico.setCepEnderecoFiscal(enderecoPrincipal.getCep());
    ordemServico.setEnderecoFiscal(enderecoPrincipal.getEndereco());
    ordemServico.setNumeroEnderecoFiscal(enderecoPrincipal.getNumero());
    ordemServico.setComplementoEnderecoFiscal(enderecoPrincipal.getComplemento());
    ordemServico.setBairroEnderecoFiscal(enderecoPrincipal.getBairro());
    ordemServico.setCodigoCidadeEnderecoFiscal(enderecoPrincipal.getCodigoCidade());
    ordemServico.setCidadeEnderecoFiscal(enderecoPrincipal.getCidade());
    ordemServico.setUfEnderecoFiscal(enderecoPrincipal.getUf());
    ordemServicoRepository.save(ordemServico);
  }

  private void updateOrdemServicoNotaFiscal(OrdemServicoNotaFiscal ordemServicoNotaFiscal, EnderecoPessoa enderecoPrincipal){
    ordemServicoNotaFiscal.setCep(enderecoPrincipal.getCep());
    ordemServicoNotaFiscal.setLogradouro(enderecoPrincipal.getEndereco());
    ordemServicoNotaFiscal.setNumero(enderecoPrincipal.getNumero());
    ordemServicoNotaFiscal.setComplemento(enderecoPrincipal.getComplemento());
    ordemServicoNotaFiscal.setBairro(enderecoPrincipal.getBairro());
    ordemServicoNotaFiscal.setCodigoCidade(enderecoPrincipal.getCodigoCidade());
    ordemServicoNotaFiscal.setCidade(enderecoPrincipal.getCidade());
    ordemServicoNotaFiscal.setEstado(enderecoPrincipal.getUf());
    ordemServicoNotaFiscalRepository.save(ordemServicoNotaFiscal);
  }
}
