package br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador;

import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonProdutoPlug4market;
import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonProdutoPlug4market.JsonCategoriaErp;
import br.com.nuvy.integracao.plug4market.dominio.entidade.ProdutoPlug4market;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface MapeadorProdutoPlug4market {

  @Mapping(target = "categoriaErp", source = "categoriaErpId")
  JsonProdutoPlug4market converte(ProdutoPlug4market entidade);

  default JsonProdutoPlug4market.JsonCategoriaErp converteCategoriaErp(String categoriaErpId) {
    if (categoriaErpId == null) {
      return null;
    }
    JsonCategoriaErp jsonCategoriaErp = new JsonCategoriaErp();
    jsonCategoriaErp.id = categoriaErpId;
    return jsonCategoriaErp;
  }
}
