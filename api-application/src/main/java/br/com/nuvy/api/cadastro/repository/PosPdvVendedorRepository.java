package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.PosPdvVendedor;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import java.util.UUID;

public interface PosPdvVendedorRepository extends NuvyRepository<PosPdvVendedor, UUID> {

  Optional<PosPdvVendedor> findByPessoaIdAndPosPdvId(Integer pessoaId, UUID posPdvId);
  Optional<PosPdvVendedor> findByIdExternoAndPosPdvId(String idExterno, UUID posPdvId);
}
