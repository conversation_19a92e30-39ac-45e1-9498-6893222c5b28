package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.api.enums.TipoTitulo.PAGAR;
import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;

import br.com.nuvy.api.cadastro.service.FormaPagamentoService;
import br.com.nuvy.api.enums.SituacaoMovimentacaoBancaria;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.SituacaoTituloBaixa;
import br.com.nuvy.api.enums.TipoAcao;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.enums.TipoTransacao;
import br.com.nuvy.api.financeiro.dto.ContaBancariaDto;
import br.com.nuvy.api.financeiro.dto.FormaPagamentoDto;
import br.com.nuvy.api.financeiro.dto.TituloBaixaCalculoDto;
import br.com.nuvy.api.financeiro.dto.TituloBaixaResumoDto;
import br.com.nuvy.api.financeiro.dto.TransacaoDto;
import br.com.nuvy.api.financeiro.mapper.TituloModelMapper;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancaria;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBaixa;
import br.com.nuvy.api.financeiro.model.TituloHistorico;
import br.com.nuvy.api.financeiro.model.Transacao;
import br.com.nuvy.api.financeiro.repository.TituloBaixaRepository;
import br.com.nuvy.api.financeiro.repository.TituloBoletoRepository;
import br.com.nuvy.api.portalcontador.service.FechamentoService;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.repository.UsuarioRepository;
import br.com.nuvy.api.sqs.dto.RegistroBoletoPagamentoRecebidoBodyDto;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.TituloBaixaDto;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.TituloBaixaDtoOut;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TituloBaixaService extends
  PageableServiceAdapterDto<TituloBaixa, TituloBaixaDto, Integer, NoFilter, TituloBaixaRepository> {

  private final UsuarioRepository usuarioRepository;

  private final TituloService tituloService;
  private final FormaPagamentoService formaPagamentoService;
  private final ContaBancariaService contaBancariaService;
  private final TransacaoService transacaoService;
  private final MovimentacaoBancariaService movimentacaoBancariaService;
  private final TituloHistoricoService tituloHistoricoService;
  private final FechamentoService fechamentoService;
  private final TituloBoletoRepository tituloBoletoRepository;
  private final TituloModelMapper modelMapper;

  public TituloBaixaDtoOut findTitulosBaixaByTituloId(Integer tituloId, Titulo titulo) {
    List<TituloBaixa> baixas = repository.findByTituloId(tituloId);
    TituloBaixaCalculoDto calculo = calcularBaixas(baixas, titulo.getValorLiquido());
    return TituloBaixaDtoOut.builder()
      .baixas(baixas.stream().map(TituloBaixaResumoDto::from).toList())
      .valorOriginal(titulo.getValorTotal())
      .valorLiquido(titulo.getValorLiquido())
      .totalDesconto(calculo.getTotalDesconto())
      .totalJurosMulta(calculo.getTotalJurosMulta())
      .totalAbatimento(calculo.getTotalAbatimento())
      .valorJaPago(calculo.getValorJaPago())
      .valorRestante(calculo.getValorRestante())
      .build();
  }

  public TituloBaixaDto createBaixa(
    Integer tituloId, TipoTitulo tipoTitulo, Boolean isImportacaoPlanilha,
    TituloBaixaDto payload
  ) {
    Titulo titulo = tituloService.findById(tituloId)
      .filter((Titulo t) -> t.getTipo().equals(tipoTitulo))
      .orElseThrow(ResourceNotFoundException::new);
    Hibernate.initialize(titulo.getTituloBaixas());
    Optional.ofNullable(titulo.getSituacao())
      .filter(
        (SituacaoTitulo s) -> s.equals(SituacaoTitulo.PAGO)
          || s.equals(SituacaoTitulo.RECEBIDO))
      .ifPresent((SituacaoTitulo s) -> {
        throw new PreconditionException("titulo.pago.ou.recebido", s.name().toLowerCase());
      });

    FormaPagamento formaPagamento = formaPagamentoService.required(payload.getFormaPagamentoId());
    ContaBancaria contaBancaria = contaBancariaService.required(payload.getContaBancariaId());

    payload.setTitulo(modelMapper.map(titulo));
    payload.setFormaPagamentoDto(FormaPagamentoDto.from(formaPagamento));
    payload.setContaBancariaDto(ContaBancariaDto.from(contaBancaria));

    TituloBaixa baixa = payload.toEntity();
    Transacao transacao = transacaoService.create(TipoTransacao.BAIXA).toEntity();

    baixa.setTransacao(transacao);
    baixa.setTitulo(titulo);
    baixa.setEmpresa(titulo.getEmpresa());
    baixa.setContaBancaria(contaBancaria);
    baixa.setSituacao(
      titulo.getSituacao().equals(SituacaoTitulo.CONCILIADO) ? SituacaoTituloBaixa.CONCILIADO
        : SituacaoTituloBaixa.NAO_CONCILIADO);

    TituloBaixa tituloBaixa = super.create(baixa);

    titulo.adicionaBaixa(tituloBaixa);
    tituloService.saveAndPublishEvent(titulo);

    MovimentacaoBancaria movimentacaoBancaria = createMovimentacaoBancaria(payload,
      transacao, tipoTitulo, contaBancaria);
    contaBancariaService.atualizaSaldoBanco(payload.getContaBancariaDto().getId(),
      movimentacaoBancaria.getValorMovimento(), false,
      PAGAR.equals(titulo.getTipo()), baixa.getDataBaixa(), isImportacaoPlanilha);

    tituloService.createHistorico(baixa.getTitulo(), TipoAcao.BAIXA, LocalDateTime.now());

    return TituloBaixaDto.from(tituloBaixa);
  }

  public void deleteBaixa(Integer tituloBaixaId) {
    TituloBaixa tituloBaixa = required(tituloBaixaId, "baixa.titulo.nao.encontrado");

    Titulo titulo = tituloBaixa.getTitulo();
    Hibernate.initialize(titulo.getTituloBaixas());
    ContaBancaria contaBancaria = tituloBaixa.getContaBancaria();

    if (Objects.nonNull(tituloBaixa.getTransacao())) {
      BigDecimal valorMovimentoBancario = gerarEstornoBaixa(tituloBaixa,
        tituloBaixa.getTitulo().getTipo(), contaBancaria);
      contaBancariaService.atualizaSaldoBanco(contaBancaria.getId(), valorMovimentoBancario,
        false, false, tituloBaixa.getDataBaixa(), false);
    }

    titulo.removeBaixa(tituloBaixaId);
    tituloService.saveAndPublishEvent(titulo);

    repository.delete(tituloBaixa);
  }

  @Transactional
  public void updateTituloBaixa(Integer id, TituloBaixaDto payload) {
    TituloBaixa tituloBaixa = required(id);
    Titulo titulo = tituloBaixa.getTitulo();
    if (Objects.nonNull(tituloBaixa.getTransacao())) {
      updateMovimentacaoBancaria(payload, tituloBaixa, tituloBaixa.getTitulo().getTipo());
    }

    TituloBaixa baixa = payload.toEntity();
    baixa.setSituacao(tituloBaixa.getSituacao());
    baixa.setTransacao(tituloBaixa.getTransacao());
    baixa.setEmpresa(titulo.getEmpresa());
    baixa.setTitulo(titulo);
    baixa.setContaBancaria(tituloBaixa.getContaBancaria());
    super.update(id, baixa);

    titulo.alteraBaixa(baixa);
    tituloService.saveAndPublishEvent(titulo);

    tituloService.createHistorico(titulo, TipoAcao.EDICAO, LocalDateTime.now());
  }

  private MovimentacaoBancaria createMovimentacaoBancaria(TituloBaixaDto payload,
    Transacao transacao, TipoTitulo tipoTitulo, ContaBancaria contaBancaria) {

    BigDecimal valorMovimento;
    if (tipoTitulo.equals(PAGAR)) {
      valorMovimento = payload.getValorCalculado().negate();
    } else {
      valorMovimento = payload.getValorCalculado();
    }

    MovimentacaoBancaria movimentacaoBancariaDto = MovimentacaoBancaria.builder()
      .dataMovimento(payload.getDataBaixa().atStartOfDay())
      .valorMovimento(valorMovimento)
      .contaBancaria(contaBancaria)
      .transacao(transacao)
      .formaPagamento(payload.getFormaPagamentoDto().toEntity())
      .build();

    movimentacaoBancariaService.create(movimentacaoBancariaDto);
    return movimentacaoBancariaDto;
  }

  private void updateMovimentacaoBancaria(TituloBaixaDto payload, TituloBaixa tituloBaixaOld,
    TipoTitulo tipoTitulo) {
    Boolean deletarMovimentacao = false;
    BigDecimal valorMovimentoAnterior;
    BigDecimal valorMovimentoAtual;
    if (tipoTitulo.equals(PAGAR)) {
      valorMovimentoAnterior = tituloBaixaOld.getValorBaixa().negate();
      valorMovimentoAtual = payload.getValorBaixa().negate();
    } else {
      valorMovimentoAnterior = tituloBaixaOld.getValorBaixa();
      valorMovimentoAtual = payload.getValorBaixa();
    }

    List<MovimentacaoBancaria> movimentacaoBancariaList;

    movimentacaoBancariaList = movimentacaoBancariaService.findByTituloBaixaAndTransacao(
      valorMovimentoAnterior, tituloBaixaOld.getDataBaixa().atStartOfDay(),
      tituloBaixaOld.getContaBancaria(), tituloBaixaOld.getTransacao(),
      tituloBaixaOld.getFormaPagamento());

    BigDecimal diferencaValor = valorMovimentoAtual.subtract(valorMovimentoAnterior);
    movimentacaoBancariaList.stream().findFirst().ifPresent(
      movimentacaoBancaria -> {
        movimentacaoBancaria.setValorMovimento(valorMovimentoAtual);
        movimentacaoBancaria.setDataMovimento(payload.getDataBaixa().atStartOfDay());
        movimentacaoBancaria.setContaBancaria(
          ContaBancaria.of(payload.getContaBancariaId()));
        movimentacaoBancaria.setFormaPagamento(
          FormaPagamento.of(payload.getFormaPagamentoId()));
        movimentacaoBancariaService.update(movimentacaoBancaria.getId(), movimentacaoBancaria);
        contaBancariaService.atualizaSaldoBanco(payload.getContaBancariaId(),
          diferencaValor, deletarMovimentacao, tipoTitulo.equals(PAGAR), movimentacaoBancaria.getDataMovimento().toLocalDate(), false);
      }
    );
  }

  private BigDecimal gerarEstornoBaixa(TituloBaixa tituloBaixa, TipoTitulo tipoTitulo,
    ContaBancaria contaBancaria) {

    BigDecimal valorMovimento;
    TipoTransacao tipoTransacao;

    if (tipoTitulo.equals(PAGAR)) {
      valorMovimento = tituloBaixa.getValorCalculado();
      tipoTransacao = TipoTransacao.DEVOLUCAO_COMPRA;
    } else {
      valorMovimento = tituloBaixa.getValorCalculado().negate();
      tipoTransacao = TipoTransacao.DEVOLUCAO_VENDA;
    }

    TransacaoDto transacao = transacaoService.create(tipoTransacao);

    MovimentacaoBancaria movimentacaoBancariaDto = MovimentacaoBancaria.builder()
      .dataMovimento(LocalDateTime.now())
      .valorMovimento(valorMovimento)
      .contaBancaria(contaBancaria)
      .transacao(transacao.toEntity())
      .formaPagamento(tituloBaixa.getFormaPagamento())
      .build();

    tituloService.createHistorico(tituloBaixa.getTitulo(), TipoAcao.ESTORNO, LocalDateTime.now());
    return movimentacaoBancariaService.create(movimentacaoBancariaDto).getValorMovimento();

  }

  public TituloBaixaDtoOut findBaixas(Integer tituloId) {
    Titulo titulo = tituloService.required(tituloId);
    return findTitulosBaixaByTituloId(tituloId, titulo);
  }

  public List<TituloBaixa> findByTituloId(Integer tituloId) {
    return repository.findByTituloId(tituloId);
  }

  public static TituloBaixaCalculoDto calcularBaixas(
    List<TituloBaixa> baixas, BigDecimal valorLiquido
  ) {
    BigDecimal totalDesconto = baixas.parallelStream()
      .map((TituloBaixa baixa) -> getOrElse(baixa.getValorDesconto(), BigDecimal.ZERO))
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal totalJurosMulta = baixas.parallelStream()
      .map((TituloBaixa baixa) -> getOrElse(baixa.getValorJuros(), BigDecimal.ZERO).add(
        getOrElse(baixa.getValorMulta(), BigDecimal.ZERO)))
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal totalAbatimento = baixas.parallelStream()
      .map((TituloBaixa baixa) -> getOrElse(baixa.getValorAbatimento(), BigDecimal.ZERO))
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal totalJaPago = baixas.parallelStream()
      .filter((TituloBaixa baixa) -> Objects.nonNull(baixa.getValorBaixa()))
      .map((TituloBaixa baixa) -> baixa.getValorBaixa()
        .add(getOrElse(baixa.getValorJuros(), BigDecimal.ZERO))
        .add(getOrElse(baixa.getValorMulta(), BigDecimal.ZERO))
        .subtract(getOrElse(baixa.getValorDesconto(), BigDecimal.ZERO))
        .subtract(getOrElse(baixa.getValorAbatimento(), BigDecimal.ZERO)))
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal valorRestante = valorLiquido.add(totalJurosMulta).subtract(totalJaPago)
      .subtract(totalDesconto).subtract(totalAbatimento);

    return TituloBaixaCalculoDto.builder()
      .totalDesconto(totalDesconto)
      .totalJurosMulta(totalJurosMulta)
      .totalAbatimento(totalAbatimento)
      .valorJaPago(totalJaPago)
      .valorRestante(valorRestante)
      .build();
  }

  public void retornarSituacao(Integer tituloId) {
    Titulo titulo = tituloService.required(tituloId);
    switch (titulo.getSituacao()) {
      case PAGO, PAGO_PARCIAL, RECEBIDO, RECEBIDO_PARCIAL, AGENDADO_CNAB -> {
        titulo.setSituacao(SituacaoTitulo.ABERTO);
        titulo.getTituloBaixas()
          .forEach(tituloBaixa -> deleteBaixa(tituloBaixa.getId()));
      }
      case CONCILIADO -> {
        titulo.setSituacao(
          titulo.getTipo().equals(PAGAR) ? SituacaoTitulo.PAGO : SituacaoTitulo.RECEBIDO);
        titulo.getTituloBaixas()
          .forEach(tituloBaixa -> tituloBaixa.setSituacao(SituacaoTituloBaixa.NAO_CONCILIADO));
        titulo.getTituloBaixas().forEach(baixa -> {
          MovimentacaoBancaria movimentacao = movimentacaoBancariaService.findByTransacaoId(
            baixa.getTransacao().getId());
          if (movimentacao != null) {
            movimentacao.setSituacao(SituacaoMovimentacaoBancaria.NAO_CONCILIADO);
          }
        });
        criaHistoricoTitulo(titulo, TipoAcao.CONCILIACAO_CANCELADA);
      }
      default -> throw new PreconditionException("situacao.titulo.impedido.alterar");
    }
  }

  public void retornarSituacaoPorIdBaixa(Integer tituloId, Integer IdBaixa) {
    Titulo titulo = tituloService.required(tituloId);
    switch (titulo.getSituacao()) {
      case PAGO, PAGO_PARCIAL, RECEBIDO, RECEBIDO_PARCIAL, AGENDADO_CNAB -> {
        for (TituloBaixa tituloBaixa : titulo.getTituloBaixas()) {
          if (tituloBaixa.getId().equals(IdBaixa)) {
            if (tituloBaixa.getSituacao().equals(SituacaoTituloBaixa.CONCILIADO)) {
              alteraSituacaoBaixaConciliada(tituloBaixa);
            } else {
              deleteBaixa(tituloBaixa.getId());
            }
            break;
          }
        }
      }
      case CONCILIADO -> {
        titulo.setSituacao(titulo.getTipo().equals(PAGAR)
          ? SituacaoTitulo.PAGO : SituacaoTitulo.RECEBIDO);
        titulo.getTituloBaixas().forEach((TituloBaixa baixa) -> {
          if (baixa.getId().equals(IdBaixa)) {
            alteraSituacaoBaixaConciliada(baixa);
          }
        });
        criaHistoricoTitulo(titulo, TipoAcao.CONCILIACAO_CANCELADA);
        tituloService.saveAndPublishEvent(titulo);
      }
      default -> throw new PreconditionException("situacao.titulo.impedido.alterar");
    }
  }

  public void conciliacaoBaixa(MovimentacaoBancaria movimentacaoBancaria) {
    TituloBaixa tituloBaixa = repository.findByTransacaoIdParaConciliarDesconciliar(
      movimentacaoBancaria.getTransacao().getId()).orElseThrow(ResourceNotFoundException::new);
    Titulo titulo = tituloBaixa.getTitulo();

    tituloBaixa.setSituacao(SituacaoTituloBaixa.CONCILIADO);
    repository.save(tituloBaixa);

    movimentacaoBancariaService.updateSituacao(
      movimentacaoBancaria.getId(), SituacaoMovimentacaoBancaria.CONCILIADO);

    setSituacaoTituloAoConciliarDesconciliar(
      titulo.getId(), titulo.getTipo(), titulo.getSituacao(), false);
  }

  public void desconciliacaoBaixa(MovimentacaoBancaria movimentacaoBancaria) {
    TituloBaixa tituloBaixa = repository.findByTransacaoIdParaConciliarDesconciliar(
      movimentacaoBancaria.getTransacao().getId()).orElseThrow(ResourceNotFoundException::new);
    Titulo titulo = tituloBaixa.getTitulo();

    tituloBaixa.setSituacao(SituacaoTituloBaixa.NAO_CONCILIADO);
    repository.save(tituloBaixa);

    movimentacaoBancariaService.updateSituacao(
      movimentacaoBancaria.getId(), SituacaoMovimentacaoBancaria.NAO_CONCILIADO);

    setSituacaoTituloAoConciliarDesconciliar(
      titulo.getId(), titulo.getTipo(), titulo.getSituacao(), true);
  }

  public void conciliacaoBaixaPorUploadDeExtrato(TituloBaixa tituloBaixa) {
    Titulo titulo = tituloBaixa.getTitulo();

    tituloBaixa.setSituacao(SituacaoTituloBaixa.CONCILIADO);
    repository.save(tituloBaixa);

    MovimentacaoBancaria movimento = movimentacaoBancariaService.findByTransacaoId(
      tituloBaixa.getTransacao().getId());
    movimento.setSituacao(SituacaoMovimentacaoBancaria.CONCILIADO);

    setSituacaoTituloAoConciliarDesconciliar(
      titulo.getId(), titulo.getTipo(), titulo.getSituacao(), false);
  }

  public void conciliacaoBaixaPorUploadDeExtrato(
    TituloBaixa tituloBaixa, String fitId, String memo
  ) {
    Titulo titulo = tituloBaixa.getTitulo();

    tituloBaixa.setSituacao(SituacaoTituloBaixa.CONCILIADO);
    tituloBaixa.setFitId(fitId);
    tituloBaixa.setMemo(memo);
    repository.save(tituloBaixa);

    MovimentacaoBancaria movimento = movimentacaoBancariaService.findByTransacaoId(
      tituloBaixa.getTransacao().getId());
    movimento.setSituacao(SituacaoMovimentacaoBancaria.CONCILIADO);

    setSituacaoTituloAoConciliarDesconciliar(
      titulo.getId(), titulo.getTipo(), titulo.getSituacao(), false);
  }

  private void setSituacaoTituloAoConciliarDesconciliar(
    Integer tituloId, TipoTitulo tituloTipo, SituacaoTitulo situacaoAtual, Boolean isDesconciliacao
  ) {
    boolean todasConciliadas = repository.allBaixasConciliadas(tituloId);

    SituacaoTitulo situacaoNova;
    if (
      todasConciliadas && !isDesconciliacao && (
        SituacaoTitulo.PAGO.equals(situacaoAtual) || SituacaoTitulo.RECEBIDO.equals(situacaoAtual)
      )
    ) {
      situacaoNova = SituacaoTitulo.CONCILIADO;
    } else if (
      !todasConciliadas && isDesconciliacao &&
        SituacaoTitulo.CONCILIADO.equals(situacaoAtual)
    ) {
      situacaoNova = tituloTipo == PAGAR
          ? SituacaoTitulo.PAGO : SituacaoTitulo.RECEBIDO;
    } else {
      return;
    }

    tituloService.updateSituacao(tituloId, situacaoNova);
  }

  @Transactional
  public void gerarBaixaBoletoPago(
    RegistroBoletoPagamentoRecebidoBodyDto registroBoletoPagamentoRecebidoDto) {
    log.debug("Gerando baixa de boleto para o título {}", registroBoletoPagamentoRecebidoDto.getObject().getId());

    var tituloBoletoOptional = tituloBoletoRepository.findByIdServicoBoleto(
      registroBoletoPagamentoRecebidoDto.getObject().getId().toString());

    if (tituloBoletoOptional.isEmpty()) {
      log.error("Título não encontrado para o id do serviço do boleto: {}", registroBoletoPagamentoRecebidoDto.getObject().getId());
      return;
    }

    var objectDto = registroBoletoPagamentoRecebidoDto.getObject();

    BigDecimal amount = objectDto.getAmount();
    BigDecimal paidAmount = objectDto.getPaid_amount();
    BigDecimal fineAmount = BigDecimal.ZERO;
    BigDecimal interestAmount = BigDecimal.ZERO;

    BigDecimal difference = paidAmount.subtract(amount);

    if (difference.compareTo(BigDecimal.ZERO) > 0) {
      if (objectDto.getFine_type() == 0) {
        fineAmount = difference;
        log.debug("Multa calculada (fine_type=0): {}", fineAmount);
      }
      else if (objectDto.getInterest_type() == 0) {
        interestAmount = difference;
        log.debug("Juros calculados (interest_type=0): {}", interestAmount);
      }
      else {
        if (objectDto.getFine_type() == 1) {
          fineAmount = amount.multiply(
            objectDto.getFine_percentage().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN));
        } else if (objectDto.getFine_type() == 2) {
          fineAmount = objectDto.getFine_value();
        }
        log.debug("Multa calculada: {}", fineAmount);

        if (objectDto.getInterest_type() == 1) {
          long diasAtraso = ChronoUnit.DAYS.between(objectDto.getExpire_at(), objectDto.getPaid_at());
          if (diasAtraso > 0) {
            interestAmount = amount.multiply(
                objectDto.getInterest_percentage().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN))
              .multiply(BigDecimal.valueOf(diasAtraso));
          }
        } else if (objectDto.getInterest_type() == 2) {
          long diasAtraso = ChronoUnit.DAYS.between(objectDto.getExpire_at(), objectDto.getPaid_at());
          if (diasAtraso > 0) {
            interestAmount = objectDto.getInterest_value().multiply(BigDecimal.valueOf(diasAtraso));
          }
        }
        log.debug("Juros calculados: {}", interestAmount);
      }
    } else {
      log.debug("O valor pago é menor ou igual ao valor do boleto. Nenhuma multa ou juros aplicados.");
    }

    createBaixa(tituloBoletoOptional.get().getTitulo().getId(), tituloBoletoOptional.get().getTitulo().getTipo(),
      Boolean.FALSE,
      TituloBaixaDto.builder()
        .titulo(modelMapper.map(tituloBoletoOptional.get().getTitulo()))
        .dataBaixa(objectDto.getPaid_at())
        .valorBaixa(tituloBoletoOptional.get().getTitulo().getValorLiquido())
        .valorJuros(interestAmount)
        .valorMulta(fineAmount)
        .valorDesconto(BigDecimal.ZERO)
        .contaBancariaId(tituloBoletoOptional.get().getTitulo().getContaBancaria().getId())
        .formaPagamentoId(tituloBoletoOptional.get().getTitulo().getFormaPagamento().getId())
        .build());
  }

  @Override
  protected TituloBaixa beforeCreate(TituloBaixa entity) {
    fechamentoService.validarPeriodoBloqueado(BadgeContext.getEmpresa().getId(), entity.getDataBaixa());
    return super.beforeCreate(entity);
  }

  @Override
  protected TituloBaixa beforeUpdate(TituloBaixa oldEntity, TituloBaixa newEntity) {
    fechamentoService.validarPeriodoBloqueado(BadgeContext.getEmpresa().getId(), oldEntity.getDataBaixa(),
      newEntity.getDataBaixa());
    return super.beforeUpdate(oldEntity, newEntity);
  }

  private void alteraSituacaoBaixaConciliada(TituloBaixa tituloBaixa) {
    tituloBaixa.setSituacao(SituacaoTituloBaixa.NAO_CONCILIADO);
    MovimentacaoBancaria movimentacao = movimentacaoBancariaService.findByTransacaoId(
      tituloBaixa.getTransacao().getId());
    if (movimentacao != null) {
      movimentacao.setSituacao(SituacaoMovimentacaoBancaria.NAO_CONCILIADO);
    }
  }

  private void criaHistoricoTitulo(Titulo titulo, TipoAcao tipoAcao) {
    Usuario usuario = get(BadgeContext.getUsuario(), Usuario::getId,
      usuarioRepository::getReferenceById);
    tituloHistoricoService.create(
      TituloHistorico.builder()
        .dataHistorico(LocalDateTime.now())
        .acao(tipoAcao)
        .titulo(titulo)
        .usuario(usuario)
        .nomeUsuario(usuario.getNome())
        .build());
  }
}
