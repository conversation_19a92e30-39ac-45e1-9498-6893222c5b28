package br.com.nuvy.api.venda.event.listener;

import static br.com.nuvy.api.enums.OrigemNotificacao.NOTA_FISCAL_DEVOLUCAO_EMITIDA;
import static br.com.nuvy.api.enums.SituacaoPedido.NF_DEVOLUCAO_EMITIDA;

import br.com.nuvy.api.admin.service.NotificacaoService;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoNotificacao;
import br.com.nuvy.api.estoque.service.MovimentoEstoquePorPedidoService;
import br.com.nuvy.api.financeiro.service.TituloPorPedidoService;
import br.com.nuvy.api.notificacao.dto.PayloadPedido;
import br.com.nuvy.api.notificacao.model.Notificacao;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoHistorico;
import br.com.nuvy.api.venda.repository.PedidoHistoricoRepository;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.events.venda.AlteracaoSituacaoPedidoEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PedidoNfDevolucaoEmitidaListener implements
  ApplicationListener<AlteracaoSituacaoPedidoEvent> {

  private final PedidoRepository pedidoRepository;
  private final NotificacaoService notificacaoService;
  private final TituloPorPedidoService tituloPorPedidoService;
  private final MovimentoEstoquePorPedidoService movimentoEstoqueService;
  private final PedidoHistoricoRepository pedidoHistoricoRepository;

  @Override
  public void onApplicationEvent(AlteracaoSituacaoPedidoEvent event) {
    if (event.getSituacaoNova() != NF_DEVOLUCAO_EMITIDA) {
      return;
    }
    processaPedido(event);
  }

  private void processaPedido(AlteracaoSituacaoPedidoEvent event) {
    Pedido pedido = pedidoRepository.findById(event.getPedidoId()).orElseThrow(() ->
      new RuntimeException("Pedido não encontrado para alterar situacao!" + event.getPedidoId()));
    criarContaAPagar(pedido);
    criarEntradaEstoque(pedido);
    alterarPedido(pedido, event);
    criaHistorico(pedido, event);
    criaNotificacao(pedido);
  }

  private void alterarPedido(Pedido pedido, AlteracaoSituacaoPedidoEvent event) {
    pedidoRepository.alterarSituacao(pedido.getPedidoPai().getId(), SituacaoPedido.NF_DEVOLVIDA);
    pedido.setSituacao(NF_DEVOLUCAO_EMITIDA);
    pedido.setXmlNotaFiscal(event.getXmlNf());
    pedido.setChaveNotaFiscal(event.getChaveNf());
    pedido.setDataNotaFiscalEmitida(LocalDateTime.now());
    pedidoRepository.save(pedido);
  }

  private void criarEntradaEstoque(Pedido pedido) {
    log.info("gerando entrada de estoque para itens de pedido devolução");
    movimentoEstoqueService.movimentaEstoque(pedido);
  }

  private void criarContaAPagar(Pedido pedido) {
    log.info("gerando conta a pagar de pedido devolução");
    tituloPorPedidoService.criarContaPagarParaNotaFiscalAutorizada(pedido);
  }

  private void criaHistorico(Pedido pedido, AlteracaoSituacaoPedidoEvent event) {
    try {
      log.info("Montando histórico de nota emitida: {}", pedido.getNumeroNotaFiscal());
      var historico = new PedidoHistorico();
      historico.setDataHistorico(LocalDateTime.now());
      historico.setSituacao(NF_DEVOLUCAO_EMITIDA);
      historico.setPedido(pedido);
      historico.setUsuario(Usuario.of(UUID.fromString(pedido.getUpdatedBy())));
      historico.setDetalhe("Nota fiscal de devolução emitida!");
      historico.setDescricao(
        "Nota fiscal de devolução " + pedido.getNumeroNotaFiscal() + " emitida!");
      historico.setCaminhoXml(event.getXmlNf());
      pedidoHistoricoRepository.save(historico);
      log.info(
        "Histórico de nota emitida criado com sucesso para a nota: {}",
        pedido.getNumeroNotaFiscal()
      );
    } catch (Exception ex) {
      log.error(
        "Erro ao criar histórico de nota emitida! Pedido: {}",
        pedido.getId(), ex
      );
    }
  }

  private void criaNotificacao(Pedido pedido) {
    try {
      Notificacao notificacao = new Notificacao();
      notificacao.setAplicacao(pedido.getAplicacao());
      notificacao.setEmpresa(pedido.getEmpresa());
      notificacao.setUsuario(pedido.getUsuario());
      notificacao.setTitulo(NOTA_FISCAL_DEVOLUCAO_EMITIDA.getDescricao());
      notificacao.setMensagem(
        "Nota fiscal de devolução" + pedido.getNumeroNotaFiscal() + " emitida!");
      notificacao.setDetalhe(
        "Nota fiscal de devolução" + pedido.getNumeroNotaFiscal() + " emitida!");
      notificacao.setTipo(TipoNotificacao.USUARIO);
      notificacao.setDataEnvio(LocalDateTime.now());
      notificacao.setOrigem(NOTA_FISCAL_DEVOLUCAO_EMITIDA);

      // Monta a notificação para o front
      var payloadPedidoNotificacao = PayloadPedido.builder()
        .titulo(notificacao.getTitulo())
        .mensagem(notificacao.getMensagem())
        .pedidoId(pedido.getId())
        .numeroNota(pedido.getNumeroNotaFiscal().toString())
        .build();

      notificacaoService.createNotification(notificacao, payloadPedidoNotificacao);
    } catch (Exception e) {
      log.error(
        "Erro ao criar notificação para o frontend! Notificação de nota fiscal emitida. Pedido: {}",
        pedido.getId(), e);
    }
  }
}
