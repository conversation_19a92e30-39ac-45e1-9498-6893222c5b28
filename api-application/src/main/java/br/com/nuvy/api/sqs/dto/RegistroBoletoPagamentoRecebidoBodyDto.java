package br.com.nuvy.api.sqs.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class RegistroBoletoPagamentoRecebidoBodyDto {
  private ObjectDto object;
  private String event_code;

  @Data
  public static class ObjectDto {
    private Integer id;
    private LocalDate expire_at;
    private LocalDate paid_at;
    private String description;
    private String status;
    private List<BoletoRecebidoKobanaWebhookDto> bank_billets;
    private BigDecimal paid_amount;
    private BigDecimal amount;
    private Integer interest_type;
    private BigDecimal interest_percentage;
    private BigDecimal interest_value;
    private Integer fine_type;
    private BigDecimal fine_percentage;
    private BigDecimal fine_value;
    private Integer discount_type;
    private BigDecimal discount_percentage;
    private BigDecimal discount_value;

  }

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class BoletoRecebidoKobanaWebhookDto {
    private Integer id;
    private String barcode;
    private String our_number;
    private String processed_our_number_raw;
    private String document_number;
    private BoletoRecebidoFormatsKobanaWebhookDto formats;
    private String url;
    private LocalDate expire_at;
  }

  @Data
  @Builder
  @AllArgsConstructor
  @NoArgsConstructor
  public static class BoletoRecebidoFormatsKobanaWebhookDto {
    private String defaultFormat;
    private String png;
    private String pdf;
    private String boleto_hibrido;
    private String boleto_pix;
    private String barcode;
    private String envelope;
    private String letter;
    private String line;
    private String recibo;
    private String carne;
  }
}
