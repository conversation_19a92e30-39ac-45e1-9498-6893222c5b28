package br.com.nuvy.api.estoque.filter;

import br.com.nuvy.api.enums.SituacaoNotaEntrada;
import br.com.nuvy.api.enums.TipoNotaFiscal;
import br.com.nuvy.common.base.filter.Filter;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class NotaFiscalEntradaFilter implements Filter {

  @NotNull
  @Hidden
  Integer empresaId;
  String fornecedorNome;
  String numeroNota;
  String situacao;
  String valorTotal;
  LocalDate dataEmissao;
  LocalDate dataRecebimento;
  LocalDate dataEmissaoInicio;
  LocalDate dataEmissaoFim;
  LocalDate dataRecebimentoInicio;
  LocalDate dataRecebimentoFim;
  transient List<SituacaoNotaEntrada> situacoes;
  @Hidden
  TipoNotaFiscal tipoNotaFiscal;
  transient List<TipoNotaFiscal> tiposNotas;
  @Hidden
  transient List<TipoNotaFiscal> ignorarTiposNotas;
  @Hidden
  transient List<SituacaoNotaEntrada> ignorarSituacoes;
  String criterio;
  @Hidden
  String cnpjEmpresa;
}
