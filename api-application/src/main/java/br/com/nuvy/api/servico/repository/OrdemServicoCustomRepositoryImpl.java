package br.com.nuvy.api.servico.repository;

import br.com.nuvy.api.cadastro.filter.servico.OrdemServicoApiExternaFilter;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa_;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.cadastro.model.servico.OrdemServicoApiExterna;
import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.api.financeiro.model.CentroCusto_;
import br.com.nuvy.api.financeiro.model.ContaBancaria_;
import br.com.nuvy.api.financeiro.model.FormaPagamento_;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.Titulo_;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoCentroCusto_;
import br.com.nuvy.api.venda.model.OrdemServicoItem;
import br.com.nuvy.api.venda.model.OrdemServicoItem_;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal_;
import br.com.nuvy.api.venda.model.OrdemServicoParcela;
import br.com.nuvy.api.venda.model.OrdemServicoParcela_;
import br.com.nuvy.api.venda.model.OrdemServico_;
import br.com.nuvy.api.venda.model.Servico_;
import br.com.nuvy.api.venda.repository.specification.RecorrenciaRelatorioSpecification;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.facade.relatorio.rl5servicos.model.RecorrenciaRelatorioDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.ListJoin;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
public class OrdemServicoCustomRepositoryImpl implements OrdemServicoCustomRepository {

  private final EntityManager em;

  @Override
  public List<RecorrenciaRelatorioDto> findAllRecorrencias(
    RecorrenciaRelatorioSpecification specification) {
    CriteriaBuilder cb = em.getCriteriaBuilder();
    CriteriaQuery<RecorrenciaRelatorioDto> query = cb.createQuery(RecorrenciaRelatorioDto.class);
    Root<OrdemServico> root = query.from(OrdemServico.class);

    var clienteJoin = root.join(OrdemServico_.cliente, JoinType.LEFT);
    var vendedorJoin = root.join(OrdemServico_.vendedor, JoinType.LEFT);
    var tecnicoJoin = root.join(OrdemServico_.tecnico, JoinType.LEFT);
    var enderecoJoin = clienteJoin.join(Pessoa_.enderecos, JoinType.LEFT);
    var itemJoin = root.join(OrdemServico_.itens, JoinType.LEFT);
    var servicosJoin = itemJoin.join(OrdemServicoItem_.servico, JoinType.LEFT);
    var ordemServicoCentroCustoJoin = root.join(OrdemServico_.centroCustos, JoinType.LEFT);
    var centroCustoJoin = ordemServicoCentroCustoJoin.join(OrdemServicoCentroCusto_.centroCusto,
      JoinType.LEFT);
    var formaPagamentoJoin = root.join(OrdemServico_.formaRecebimento, JoinType.LEFT);
    var contaBancariaJoin = root.join(OrdemServico_.contaBancaria, JoinType.LEFT);
    var precoUnitarioPorRateioExpression = cb.quot(
      servicosJoin.get(Servico_.valor), // Valor do serviço
      ordemServicoCentroCustoJoin.get(
        OrdemServicoCentroCusto_.percentualRateio)); // Percentual de rateio do centro de custo

    Expression<BigDecimal> iss = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.issRetido)),
        itemJoin.get(OrdemServicoItem_.iss))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);
    Expression<BigDecimal> inss = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.inssRetido)),
        itemJoin.get(OrdemServicoItem_.inss))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);
    Expression<BigDecimal> cofins = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.cofinsRetido)),
        itemJoin.get(OrdemServicoItem_.cofins))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);
    Expression<BigDecimal> csll = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.csllRetido)),
        itemJoin.get(OrdemServicoItem_.csll))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);
    Expression<BigDecimal> ir = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.irRetido)), itemJoin.get(OrdemServicoItem_.ir))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);
    Expression<BigDecimal> pis = cb.coalesce(cb.selectCase()
      .when(cb.isTrue(itemJoin.get(OrdemServicoItem_.pisRetido)),
        itemJoin.get(OrdemServicoItem_.pis))
      .otherwise(cb.literal(BigDecimal.ZERO)), cb.literal(BigDecimal.ZERO)).as(BigDecimal.class);

    var impostosRetidos = cb.sum(iss, inss);
    impostosRetidos = cb.sum(impostosRetidos, cofins);
    impostosRetidos = cb.sum(impostosRetidos, csll);
    impostosRetidos = cb.sum(impostosRetidos, ir);
    impostosRetidos = cb.sum(impostosRetidos, pis);

    query.select(cb.construct(RecorrenciaRelatorioDto.class,
      root.get(OrdemServico_.id).alias("id"),
      root.get(OrdemServico_.codigo).alias("numeroOrdem"),
      // Alias para o campo codigo da ordem de serviço (OrdemServico.codigo
      clienteJoin.get(Pessoa_.nome).alias("nomeCliente"),
      clienteJoin.get(Pessoa_.nomeInterno).alias("nomeInternoCliente"),
      clienteJoin.get(Pessoa_.nomeFantasia).alias("nomeFantasiaCliente"),
      clienteJoin.get(Pessoa_.cpfCnpj).alias("cpfCnpj"),
      vendedorJoin.get(Pessoa_.nome).alias("nomeVendedor"),
      tecnicoJoin.get(Pessoa_.nome).alias("nomeTecnico"),
      enderecoJoin.get(EnderecoPessoa_.uf).alias("estado"),
      enderecoJoin.get(EnderecoPessoa_.cidade).alias("cidade"),
      enderecoJoin.get(EnderecoPessoa_.endereco).alias("endereco"),
      root.get(OrdemServico_.consideracoes).alias("consideracoes"),
      root.get(OrdemServico_.observacoes).alias("observacoes"),
      root.get(OrdemServico_.observacoesNf).alias("observacoesNf"),
      impostosRetidos.alias("TotalImpostoRetido"),
      root.get(OrdemServico_.vigenciaInicial).alias("vigenciaInicial"),
      root.get(OrdemServico_.vigenciaFinal).alias("vigenciaFinal"),
      root.get(OrdemServico_.diaFaturamento).alias("diaFaturamento"),
      itemJoin.get(OrdemServicoItem_.servico).get(Servico_.id).alias("idServico"),
      itemJoin.get(OrdemServicoItem_.quantidade).alias("quantidadeServico"),
      itemJoin.get(OrdemServicoItem_.valorDesconto).alias("totalDesconto"),
      servicosJoin.get(Servico_.descricao).alias("descricaoServico"),
      servicosJoin.get(Servico_.descricaoCompleta).alias("descricaoCompletaServico"),
      servicosJoin.get(Servico_.codigo).alias("codigoServico"),
      servicosJoin.get(Servico_.cnae).alias("cnae"),
      servicosJoin.get(Servico_.idLc116).alias("lc116"),
      servicosJoin.get(Servico_.iss).alias("iss"),
      servicosJoin.get(Servico_.inss).alias("inss"),
      servicosJoin.get(Servico_.cofins).alias("cofins"),
      servicosJoin.get(Servico_.csll).alias("csll"),
      servicosJoin.get(Servico_.ir).alias("ir"),
      servicosJoin.get(Servico_.idNbs).alias("nbs"),
      servicosJoin.get(Servico_.pis).alias("pis"),
      servicosJoin.get(Servico_.unidade).alias("unidade"),
      servicosJoin.get(Servico_.observacao).alias("observacoesInternas"),
      servicosJoin.get(Servico_.valor).alias("precoUnitario"),
      formaPagamentoJoin.get(FormaPagamento_.nome).alias("formaRecebimento"),
      contaBancariaJoin.get(ContaBancaria_.numConta).alias("contaCorrente"),
      centroCustoJoin.get(CentroCusto_.nome).alias("centroCusto"),
      precoUnitarioPorRateioExpression.alias("valorCentroCusto")
    ));

    Predicate predicate = specification.toPredicate(root, query, cb);
    if (predicate != null) {
      query.where(predicate);
    }

    TypedQuery<RecorrenciaRelatorioDto> typedQuery = em.createQuery(query);

    return typedQuery.getResultList();
  }

  @Override
  @Transactional(readOnly = true)
  public Page<OrdemServicoApiExterna> buscaApiExterna(OrdemServicoApiExternaFilter filter,
    Pageable pageable) {
    if (pageable.getSort().isUnsorted()) {
      pageable = PageRequest.of(
        pageable.getPageNumber(), pageable.getPageSize(),
        Direction.ASC, "id"
      );
    }

    CriteriaBuilder builder = em.getCriteriaBuilder();

    CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
    Root<OrdemServico> countOrdemServico = countQuery.from(OrdemServico.class);
    CamposBuscaApiExterna camposCount = getCamposBuscaApiExterna(countOrdemServico, countQuery,
      builder);
    countQuery.select(builder.count(countOrdemServico));
    countQuery.where(toPredicate(filter, camposCount, countOrdemServico, countQuery, builder));

    Long count = em.createQuery(countQuery).getSingleResult();

    if (count == 0) {
      return Page.empty(pageable);
    }

    CriteriaQuery<OrdemServicoApiExterna> query = builder.createQuery(OrdemServicoApiExterna.class);
    Root<OrdemServico> ordemServico = query.from(OrdemServico.class);
    CamposBuscaApiExterna campos = getCamposBuscaApiExterna(ordemServico, query, builder);

    query.select(builder.construct(
      OrdemServicoApiExterna.class,
      campos.ordemServicoId().alias("ordemServicoId"),
      campos.numeroRps().alias("numeroRps"),
      campos.dataFaturamentoRps().alias("dataFaturamentoRps"),
      campos.codigo().alias("codigo"),
      campos.dataServico().alias("dataServico"),
      campos.situacao().alias("situacao"),
      campos.clienteId().alias("clienteId"),
      campos.observacao().alias("observacao"),
      campos.valor().alias("valor"),
      campos.servicos().alias("servicos"),
      campos.tipoPagamento().alias("tipoPagamento"),
      campos.parcelas().alias("parcelas"),
      campos.ordemServicoNotaFiscal().alias("ordemServicoNotaFiscal")
    ));
    query.where(toPredicate(filter, campos, ordemServico, query, builder));
    query.orderBy(toOrderBy(pageable, campos, builder));

    TypedQuery<OrdemServicoApiExterna> typedQuery = em.createQuery(query);
    typedQuery.setFirstResult((int) pageable.getOffset());
    typedQuery.setMaxResults(pageable.getPageSize());

    List<OrdemServicoApiExterna> list = typedQuery.getResultList();
    return new PageImpl<>(list, pageable, count);
  }

  @Transactional(readOnly = true)
  public OrdemServicoApiExterna buscaPorIdApiExterna(UUID id) {
    CriteriaBuilder builder = em.getCriteriaBuilder();

    CriteriaQuery<OrdemServicoApiExterna> query = builder.createQuery(OrdemServicoApiExterna.class);
    Root<OrdemServico> ordemServico = query.from(OrdemServico.class);
    CamposBuscaApiExterna campos = getCamposBuscaApiExterna(ordemServico, query, builder);

    query.select(builder.construct(
      OrdemServicoApiExterna.class,
      campos.ordemServicoId().alias("ordemServicoId"),
      campos.numeroRps().alias("numeroRps"),
      campos.dataFaturamentoRps().alias("dataFaturamentoRps"),
      campos.codigo().alias("codigo"),
      campos.dataServico().alias("dataServico"),
      campos.situacao().alias("situacao"),
      campos.clienteId().alias("clienteId"),
      campos.observacao().alias("observacao"),
      campos.valor().alias("valor"),
      campos.servicos().alias("servicos"),
      campos.tipoPagamento().alias("tipoPagamento"),
      campos.parcelas().alias("parcelas"),
      campos.ordemServicoNotaFiscal().alias("ordemServicoNotaFiscal")
    ));
    query.where(builder.equal(campos.ordemServicoId(), id));

    TypedQuery<OrdemServicoApiExterna> typedQuery = em.createQuery(query);

    try {
      return typedQuery.getSingleResult();
    } catch (jakarta.persistence.NoResultException e) {
      throw new ResourceNotFoundException("Ordem de serviço com ID " + id + " não encontrada");
    }
  }

  record CamposBuscaApiExterna(
    Expression<UUID> ordemServicoId,
    Expression<BigInteger> numeroRps,
    Expression<LocalDateTime> dataFaturamentoRps,
    Expression<BigInteger> codigo,
    Expression<LocalDateTime> dataServico,
    Expression<SituacaoOrdemServico> situacao,
    Expression<UUID> clienteId,
    Expression<String> observacao,
    Expression<BigDecimal> valor,
    Expression<String[]> servicos,
    Expression<String> tipoPagamento,
    Expression<String[]> parcelas,
    Expression<String[]> ordemServicoNotaFiscal
  ) {

  }

  private static CamposBuscaApiExterna getCamposBuscaApiExterna(
    Root<OrdemServico> ordemservico, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Expression<UUID> ordemServicoId = ordemservico.get(OrdemServico_.uuid);
    Expression<BigInteger> numeroRps = ordemservico.get(OrdemServico_.numeroRps);
    Expression<LocalDateTime> dataFaturamentoRps = ordemservico.get(OrdemServico_.dataFaturamentoRps);
    Expression<BigInteger> codigo = ordemservico.get(OrdemServico_.codigo);
    Expression<LocalDateTime> dataCriacao = ordemservico.get(OrdemServico_.dataCriacao);
    Expression<SituacaoOrdemServico> situacao = ordemservico.get(OrdemServico_.situacao);
    Expression<UUID> clienteId = ordemservico.get(OrdemServico_.cliente).get(Pessoa_.uuid);
    Expression<String> observacoesNf = ordemservico.get(OrdemServico_.observacoesNf);
    Expression<BigDecimal> valorNf = ordemservico.get(OrdemServico_.valorNf);
    Expression<String[]> servicos = subQueryServicos(ordemservico, query, builder);
    Expression<String> formaPagamento = ordemservico.get(OrdemServico_.formaRecebimento)
      .get(FormaPagamento_.nome);
    Expression<String[]> parcelas = subQueryParcelas(ordemservico, query, builder);
    Expression<String[]> ordemServicoNotaFiscal = subQueryOrdemServicoNotaFiscal(ordemservico,
      query, builder);

    return new CamposBuscaApiExterna(
      ordemServicoId, numeroRps, dataFaturamentoRps, codigo,
      dataCriacao, situacao, clienteId, observacoesNf, valorNf, servicos,
      formaPagamento, parcelas, ordemServicoNotaFiscal
    );
  }

  private static Subquery<String[]> subQueryServicos(
    Root<OrdemServico> ordemServico, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<String[]> subQuery = query.subquery(String[].class);
    Root<OrdemServicoItem> ordemServicoItem = subQuery.from(OrdemServicoItem.class);
    return subQuery
      .select(builder.function(
        "array_agg", String[].class,
        builder.function(
          "json_build_object", String.class,
          builder.literal("descricao"),
          ordemServicoItem.get(OrdemServicoItem_.servico).get(Servico_.descricao),
          builder.literal("quantidade"), ordemServicoItem.get(OrdemServicoItem_.quantidade),
          builder.literal("servicoId"),
          ordemServicoItem.get(OrdemServicoItem_.servico).get(Servico_.uuid),
          builder.literal("valor"), ordemServicoItem.get(OrdemServicoItem_.valorTotalItem)
        )
      ))
      .where(builder.equal(
        ordemServicoItem.join(OrdemServicoItem_.ordemServico).get(OrdemServico_.id),
        ordemServico.get(OrdemServico_.id)
      ));
  }

  private static Subquery<String[]> subQueryParcelas(
    Root<OrdemServico> ordemServico, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<String[]> subQuery = query.subquery(String[].class);
    Root<OrdemServicoParcela> ordemServicoParcela = subQuery.from(OrdemServicoParcela.class);

    ListJoin<OrdemServicoParcela, Titulo> joinTitulo = ordemServicoParcela.join(
      OrdemServicoParcela_.titulos, JoinType.LEFT);

    return subQuery
      .select(builder.function(
        "array_agg", String[].class,
        builder.function(
          "json_build_object", String.class,
          builder.literal("numero"), ordemServicoParcela.get(OrdemServicoParcela_.numero),
          builder.literal("valor"), ordemServicoParcela.get(OrdemServicoParcela_.valor),
          builder.literal("data"), ordemServicoParcela.get(OrdemServicoParcela_.vencimento),
          builder.literal("situacao"), joinTitulo.get(Titulo_.situacao)
        )
      ))
      .where(builder.equal(
        ordemServicoParcela.join(OrdemServicoParcela_.ordemServico).get(OrdemServico_.id),
        ordemServico.get(OrdemServico_.id)
      ));
  }

  private static Subquery<String[]> subQueryOrdemServicoNotaFiscal(
    Root<OrdemServico> ordemServico, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<String[]> subQuery = query.subquery(String[].class);
    Root<OrdemServicoNotaFiscal> ordemServicoNotaFiscal = subQuery.from(
      OrdemServicoNotaFiscal.class);

    return subQuery
      .select(builder.function(
        "array_agg", String[].class,
        builder.function(
          "json_build_object", String.class,
          builder.literal("numero"), ordemServicoNotaFiscal.get(OrdemServicoNotaFiscal_.numeroNfse),
          builder.literal("caminhoXml"),
          ordemServicoNotaFiscal.get(OrdemServicoNotaFiscal_.caminhoXml),
          builder.literal("caminhoPdf"),
          ordemServicoNotaFiscal.get(OrdemServicoNotaFiscal_.caminhoPdf)
        )
      ))
      .where(builder.equal(
        ordemServicoNotaFiscal.join(OrdemServicoNotaFiscal_.ordemServico).get(OrdemServico_.id),
        ordemServico.get(OrdemServico_.id)
      ));
  }

  private List<Order> toOrderBy(
    Pageable pageable, CamposBuscaApiExterna campos, CriteriaBuilder builder
  ) {
    var disponiveis = List.of("situacao");
    return pageable.getSort().stream()
      .filter(o -> disponiveis.contains(o.getProperty()))
      .map(o -> switch (o.getProperty()) {
        case "situacao" -> o.getDirection().isDescending()
          ? builder.desc(campos.situacao())
          : builder.asc(campos.situacao());
        default -> throw new IllegalStateException("Unexpected value: " + o.getProperty());
      }).toList();
  }

  private static Predicate toPredicate(
    OrdemServicoApiExternaFilter filter, CamposBuscaApiExterna campos,
    Root<OrdemServico> ordemServico, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    return PredicateBuilder.create(builder)
      .add(filter.dataCriacaoOrdemServicoInicio(),
        v -> builder.between(campos.dataServico(), filter.dataCriacaoOrdemServicoInicio(),
          filter.dataCriacaoOrdemServicoTermino()))
      .add(filter.situacao(), v -> builder.equal(campos.situacao(), v))
      .and();
  }
}
