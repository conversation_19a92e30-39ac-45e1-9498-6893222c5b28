package br.com.nuvy.api.adaptadores.saida.jpa.titulo;

import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.notificacao.aplicacao.porta.saida.persistencia.RepositorioTituloNotificacao;
import br.com.nuvy.notificacao.dominio.titulo.TituloNotificacao;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
class RepositorioTituloReguaCobrancaImpl implements RepositorioTituloNotificacao {

  private final TituloRepository titulos;

  RepositorioTituloReguaCobrancaImpl(TituloRepository titulos) {
    this.titulos = titulos;
  }

  @Override
  public Optional<TituloNotificacao> buscaPorId(Integer id) {
    return titulos.findTituloNotificacaoById(id);
  }
}
