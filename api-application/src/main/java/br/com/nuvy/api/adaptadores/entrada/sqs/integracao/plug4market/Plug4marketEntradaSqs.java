package br.com.nuvy.api.adaptadores.entrada.sqs.integracao.plug4market;

import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnviaCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnviaProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorProdutoPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.evento.CategoriaRecebidaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.evento.ProdutoRecebidoPlug4market;
import br.com.nuvy.multitenent.TenantContext;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(
  name = "aws.sqs.queue.plug4market.enabled",
  havingValue = "true"
)
class Plug4marketEntradaSqs {

  private static final Logger log = LoggerFactory.getLogger(Plug4marketEntradaSqs.class);

  private final ObjectMapper objectMapper;

  private final ProcessadorProdutoPlug4market processadorProduto;
  private final ProcessadorCategoriaPlug4market processadorCategoria;

  Plug4marketEntradaSqs(
    ObjectMapper objectMapper,
    ProcessadorProdutoPlug4market processadorProduto,
    ProcessadorCategoriaPlug4market processadorCategoria
  ) {
    this.objectMapper = objectMapper;

    this.processadorProduto = processadorProduto;
    this.processadorCategoria = processadorCategoria;

    log.info("{} habilitado", Plug4marketEntradaSqs.class.getSimpleName());
  }

  @JmsListener(
    destination = "${aws.sqs.queue.plug4market.name}",
    containerFactory = "jmsListenerContainerFactoryConcorrente"
  )
  public void processaMensagem(
    SQSTextMessage message
  ) throws JMSException, JsonProcessingException {
    log.debug("mensagem recebida da fila plug4market: {}", message.getText());

    TenantContext.setTenant(message.getStringProperty("idAplicacao"));
    BadgeContext.generateBadgeContext(
      message.getStringProperty("idAplicacao"),
      message.getStringProperty("idEmpresa"),
      message.getStringProperty("idUsuario")
    );

    switch (message.getStringProperty("tipo")) {
      case "ProdutoRecebidoPlug4market" -> {
        ProdutoRecebidoPlug4market evento = objectMapper.readValue(
          message.getText(), ProdutoRecebidoPlug4market.class
        );
        processadorProduto.processaComando(new EnviaProdutoPlug4market(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdProduto(), evento.getOcorridoAs()
        ));
      }
      case "CategoriaRecebidaPlug4market" -> {
        CategoriaRecebidaPlug4market evento = objectMapper.readValue(
          message.getText(), CategoriaRecebidaPlug4market.class
        );
        processadorCategoria.processaComando(new EnviaCategoriaPlug4market(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdCategoria(), evento.getOcorridoAs()
        ));
      }
      default -> throw new UnsupportedOperationException(
        "Tipo de evento não identificado na fila plug4market! Tipo: %s"
          .formatted(message.getStringProperty("tipo"))
      );
    }
  }
}
