package br.com.nuvy.api.notafiscal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NfeCCeDto {

  private String id;
  private String versao;
  private String cOrgao;
  private String cUF;
  private String tpAmb;
  private String chNFe;
  private LocalDateTime dhEvento;
  private String nSeqEvento;
  private String verEvento;
  private String xCorrecao;
  private String xMotivo;
  private Integer cStat;
  private String status;
  private String nProt;
  private String emailDest;
}
