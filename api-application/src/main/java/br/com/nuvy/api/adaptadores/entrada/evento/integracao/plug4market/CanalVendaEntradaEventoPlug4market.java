package br.com.nuvy.api.adaptadores.entrada.evento.integracao.plug4market;

import br.com.nuvy.api.cadastro.event.canalvenda.CanalVendaAlterado;
import br.com.nuvy.api.cadastro.event.canalvenda.CanalVendaCriado;
import br.com.nuvy.api.cadastro.event.canalvenda.CanalVendaSincronizacaoSolicitada;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.SincronizaCategoriasPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.SincronizaProdutosPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorProdutoPlug4market;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
@RequiredArgsConstructor
class CanalVendaEntradaEventoPlug4market {

  private final ProcessadorProdutoPlug4market processadorProduto;
  private final ProcessadorCategoriaPlug4market processadorCategoria;

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(CanalVendaSincronizacaoSolicitada evento) {
    // Sincronizar categorias primeiro
    processadorCategoria.processaComando(new SincronizaCategoriasPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCanalVenda(),
      evento.getOcorridoAs()
    ));

    // Depois sincronizar produtos
    processadorProduto.processaComando(new SincronizaProdutosPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCanalVenda(),
      evento.getOcorridoAs()
    ));
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(CanalVendaCriado evento) {
    // Sincronizar categorias primeiro
    processadorCategoria.processaComando(new SincronizaCategoriasPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCanalVenda(),
      evento.getOcorridoAs()
    ));

    // Depois sincronizar produtos
    processadorProduto.processaComando(new SincronizaProdutosPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCanalVenda(),
      evento.getOcorridoAs()
    ));
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(CanalVendaAlterado evento) {
    if (evento.isAlterouTabelaPreco()) {
      processadorProduto.processaComando(new SincronizaProdutosPlug4market(
        UUID.randomUUID(),
        evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
        evento.getIdCanalVenda(),
        evento.getOcorridoAs()
      ));
    }
  }
}
