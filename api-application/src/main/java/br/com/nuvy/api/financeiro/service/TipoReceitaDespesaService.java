package br.com.nuvy.api.financeiro.service;

import br.com.nuvy.api.financeiro.dto.TipoReceitaDespesaDto;
import br.com.nuvy.api.financeiro.model.TipoReceitaDespesa;
import br.com.nuvy.api.financeiro.repository.TipoReceitaDespesaRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import org.springframework.stereotype.Service;

@Service
public class TipoReceitaDespesaService extends
        PageableServiceAdapterDto<TipoReceitaDespesa, TipoReceitaDespesaDto, Integer, NoFilter, TipoReceitaDespesaRepository> {
}
