package br.com.nuvy.api.financeiro.repository.specification;

import br.com.nuvy.api.financeiro.filter.BancoFilter;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.financeiro.model.Banco_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;

@RequiredArgsConstructor(staticName = "from")
@EqualsAndHashCode
public class BancoSpecification implements Specification<Banco> {

  private final BancoFilter filter;

  @Override
  public Predicate toPredicate(Root<Banco> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getCodigo(),
        e -> criteriaBuilder.equal(root.get(Banco_.codigo), e))
      .add(filter.getNome(),
        e -> iLike(criteriaBuilder, root.get(Banco_.nome), e))
      .add(filter.getCodigoIspb(),
        e -> criteriaBuilder.equal(root.get(Banco_.codigoIspb), e))
      .and();
  }
}
