package br.com.nuvy.api.financeiro.controller;

import br.com.nuvy.api.financeiro.dto.ContaBancariaDto;
import br.com.nuvy.api.financeiro.dto.ContaBancariaPutDto;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoDto;
import br.com.nuvy.api.financeiro.filter.ContaBancariaFilter;
import br.com.nuvy.api.financeiro.service.ContaBancariaService;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.common.patch.json.Patch;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Financeiro / Conta Bancaria")
@RestController
@RequestMapping("/v1/financeiro/conta-bancaria")
@RequiredArgsConstructor
public class ContaBancariaController {

  private final ContaBancariaService service;
  private final DiscovererComponent discoverer;

  @PatchMapping("/{id}")
  @Patch(service = ContaBancariaService.class)
  @ResponseStatus(HttpStatus.OK)
  public void patch(@PathVariable Integer id, @RequestBody ContaBancariaDto payload) {
    var result = service.updateSituacao(id, payload);
      discoverer.handleUpdatedResource(result);
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public List<ContaBancariaDto> find(@ParameterObject @Valid ContaBancariaFilter filter,
    @ParameterObject Pageable pageable) {
    return discoverer.handlePaginatedResults(pageable, service.findContasBancarias(filter, pageable));
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ContaBancariaDto findById(@PathVariable Integer id) {
    return service.findContaBancariaById(id);
  }

  @GetMapping("/list/{empresaId}")
  @ResponseStatus(HttpStatus.OK)
  public List<ContaBancariaResumoDto> findAllContaCorrente(@PathVariable Integer empresaId) {
    return service.findAllContaCorrenteWithSaldo(empresaId);
  }

  @PostMapping(headers = "empresa")
  @ResponseStatus(HttpStatus.CREATED)
  public void create(@RequestBody @Valid ContaBancariaDto payload) {
    var result = service.create(payload.toEntity());
    discoverer.handleCreatedResource(result.getId());
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void update(@PathVariable Integer id, @RequestBody @Valid ContaBancariaPutDto payload) {
    var result = service.update(id, payload.toEntity());
    discoverer.handleUpdatedResource(result.getId());
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void delete(@PathVariable Integer id) {
    service.delete(id);
  }
}
