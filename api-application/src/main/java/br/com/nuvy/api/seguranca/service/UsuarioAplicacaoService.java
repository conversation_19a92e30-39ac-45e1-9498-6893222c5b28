package br.com.nuvy.api.seguranca.service;

import static br.com.nuvy.common.exception.Preconditions.newPreconditionException;

import br.com.nuvy.api.admin.Aplicacao;
import br.com.nuvy.api.admin.event.aplicacao.AplicacaoUsuarioAdicionado;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Setor;
import br.com.nuvy.api.cadastro.repository.PessoaRepository;
import br.com.nuvy.api.cadastro.repository.SetorRepository;
import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.enums.BucketsS3;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.enums.SituacaoRecebedorNfe;
import br.com.nuvy.api.enums.SituacaoUsuario;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.api.seguranca.event.usuario.UsuarioCriado;
import br.com.nuvy.api.seguranca.filter.UsuarioAplicacaoFilter;
import br.com.nuvy.api.seguranca.model.Perfil;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.repository.UsuarioAplicacaoRepository;
import br.com.nuvy.api.seguranca.repository.specification.UsuarioAplicacaoSpecification;
import br.com.nuvy.api.seguranca.repository.specification.UsuarioCriterioSpecification;
import br.com.nuvy.client.services.IntercomService;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceConflictException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuarioDto;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsuarioAplicacaoService extends
  PageableServiceAdapter<UsuarioAplicacao, Integer, UsuarioAplicacaoFilter, UsuarioAplicacaoRepository> {

  private final UsuarioService usuarioService;
  private final PerfilService perfilService;
  private final PessoaRepository pessoaRepository;
  private final SetorRepository setorRepository;
  private final FileService fileService;
  private final IntercomService intercomService;
  private final BackClienteUsuarioService backClienteUsuarioService;
  private final ApplicationEventPublisher eventPublisher;

  @Override
  protected Specification<UsuarioAplicacao> configureSpecification(UsuarioAplicacaoFilter filter) {
    return UsuarioAplicacaoSpecification.from(filter);
  }

  public Page<UsuarioAplicacao> find(String criterio, Pageable pageable, Situacao perfilSituacao,
    TipoRelacionamento tipoRelacionamento, SituacaoUsuario situacaoUsuario) {

    var usuarioCriterioSpecification = new UsuarioCriterioSpecification(criterio, perfilSituacao,
      tipoRelacionamento, situacaoUsuario);
    return repository.findAll(usuarioCriterioSpecification, pageable);
  }

  public Optional<UsuarioAplicacao> findFirstByAplicacaoId(String aplicacaoId) {
    return repository.findFirstByAplicacaoId(aplicacaoId);
  }

  public Optional<UsuarioAplicacao> findFirstByAplicacaoId(UUID aplicacaoId) {
    return findFirstByAplicacaoId(aplicacaoId.toString());
  }

  public Optional<UsuarioAplicacao> findFirstByAplicacaoId(Aplicacao aplicacao) {
    return findFirstByAplicacaoId(aplicacao.getUuid());
  }

  public UsuarioAplicacao requiredFirstByAplicacaoId(String aplicacaoId) {
    return repository.findFirstByAplicacaoId(aplicacaoId)
      .orElseThrow(newPreconditionException(
        "Nenhum usuário encontrado para a aplicação: {0}", aplicacaoId
      ));
  }

  public UsuarioAplicacao requiredFirstByAplicacaoId(UUID aplicacaoId) {
    return requiredFirstByAplicacaoId(aplicacaoId.toString());
  }

  public UsuarioAplicacao requiredFirstByAplicacaoId(Aplicacao aplicacao) {
    return requiredFirstByAplicacaoId(aplicacao.getUuid());
  }

  public Optional<UsuarioAplicacao> findByUsuarioId(UUID usuarioId) {
    return repository.findByUsuarioId(usuarioId);
  }

  @Override
  protected UsuarioAplicacao beforeDelete(UsuarioAplicacao entity) {
    if (Objects.nonNull(entity.getUsuario().getDataUltimoAcesso())) {
      throw new PreconditionException("impossivel.excluir.usuario.ja.acessou");
    }
    return entity;
  }

  @Transactional
  public UsuarioAplicacao create(UsuarioDto dto, String idEmpresa) {
    if (dto.getPerfilIds().size() > 1) {
      throw new PreconditionException("Mais de 1 perfil selecionado para o usuário!");
    }
    Aplicacao aplicacao = BadgeContext.getAplicacao();
    Empresa empresa = BadgeContext.getEmpresa();
    Usuario usuarioCriou = BadgeContext.getUsuario();
    Pair<Usuario, String> result = usuarioService.save(dto);
    Usuario usuario = result.getFirst();
    backClienteUsuarioService.createUser(dto.getPerfilIds(), usuario, idEmpresa);
    UsuarioAplicacao usuarioAplicacao = repository.findByUsuario(usuario)
      .orElseGet(() -> {
        UsuarioAplicacao newUsuarioAplicacao = UsuarioAplicacao.builder()
          .situacao(SituacaoUsuario.PENDENTE)
          .situacaoRecebedorNfe(
            Objects.nonNull(dto.getSituacaoRecebedorNfe())
              ? dto.getSituacaoRecebedorNfe()
              : SituacaoRecebedorNfe.INATIVO
          )
          .build();

        Pessoa pessoa = pessoaRepository.save(Pessoa.builder()
            .email(dto.getEmail())
            .nome(dto.getNome())
            .tipo(TipoPessoa.FISICA)
            .situacao(SituacaoParceiro.PENDENTE)
            .relacaoComercial(Set.of(TipoRelacionamento.COLABORADOR))
            .build());

        if (Objects.nonNull(dto.getSetorId())) {
          Setor setor = setorRepository.findById(dto.getSetorId())
            .orElseThrow(ResourceNotFoundException::new);
          newUsuarioAplicacao.setSetor(setor);
        }

        newUsuarioAplicacao.setPessoa(pessoa);
        newUsuarioAplicacao.setPessoa(pessoa);
        newUsuarioAplicacao.setUsuario(usuario);
        newUsuarioAplicacao.setTermoUso(false);

        if (!CollectionUtils.isEmpty(dto.getPerfilIds())) {
          List<Perfil> perfils = findAllPerfil(dto.getPerfilIds());
          if (!CollectionUtils.isEmpty(perfils)) {
            newUsuarioAplicacao.setPerfilsAplicacao(perfils);
          }
        }
        return repository.save(newUsuarioAplicacao);
      });
    Pessoa pessoa = usuarioAplicacao.getPessoa();
    if (usuario.getSituacao().equals(SituacaoUsuario.PENDENTE)) {
      String senha = result.getSecond();
      eventPublisher.publishEvent(new UsuarioCriado(
        UUID.randomUUID(),
        aplicacao.getUuid(), empresa.getId(), usuarioCriou.getId(),
        usuario.getId(), pessoa.getId(), senha,
        LocalDateTime.now()
      ));
    } else {
      eventPublisher.publishEvent(new AplicacaoUsuarioAdicionado(
        UUID.randomUUID(),
        aplicacao.getUuid(), empresa.getId(), usuarioCriou.getId(),
        usuario.getId(), pessoa.getId(),
        LocalDateTime.now()
      ));
    }
    return usuarioAplicacao;
  }

  public Optional<UsuarioAplicacao> findByUsuario(Usuario usuario) {
    return this.repository.findByUsuario(usuario);
  }

  @Transactional
  public void deletarUsuarioAplicacao(Integer id) {
    var usuarioAplicacao = repository.findById(id)
      .orElseThrow(ResourceNotFoundException::new);
    if (!usuarioAplicacao.getSituacao().equals(SituacaoUsuario.PENDENTE)) {
      throw new ResourceConflictException("usuario.ja.ativo");
    }
    backClienteUsuarioService.deleteUser(usuarioAplicacao);
    this.repository.delete(usuarioAplicacao);
  }

  public List<Perfil> findAllPerfil(List<Integer> perfilIds) {
    return this.perfilService.findAllPerfil(perfilIds);
  }

  public Optional<UsuarioAplicacao> findByRamalCallFaceAndAplicacao(Integer ramalCallFace,
    String aplicacao) {
    return repository.findFirstByRamalCallFaceAndAplicacao(ramalCallFace, aplicacao);
  }

//  public void updateSituacaoUsuarioAplicacaoPorIdApp(String idAplicacao, String situacao) {
//    repository.updateSituacaoUsuarioAplicacaoPorIdApp(idAplicacao, situacao);
//  }

  public Optional<UsuarioAplicacao> findByUsuarioFotoFormatada(Usuario usuario) {
    var usuarioAplicacao = this.repository.findByUsuario(usuario);
    usuarioAplicacao.ifPresent(
      u -> u.getPessoa().setFoto(fileService.getUrl(BucketsS3.ERP, u.getPessoa().getFoto(), true)));
    return usuarioAplicacao;
  }

  public Integer findRamalCallfaceByUsuarioId(UUID usuarioId) {
    return repository.findRamalCallfaceByUsuarioId(usuarioId);
  }

  @Override
  protected UsuarioAplicacao afterUpdate(UsuarioAplicacao entity) {
    if(!entity.getSincronizadoIntercom()){
      intercomService.sincronizaUsuarioIntercom(entity);
    }
    return super.afterUpdate(entity);
  }
}
