package br.com.nuvy.api.financeiro.repository.specification;

import br.com.nuvy.api.financeiro.filter.ExtratoConciliacaoFilter;
import br.com.nuvy.api.financeiro.model.ExtratoConciliacaoView;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
@EqualsAndHashCode
public class ExtratoConciliacaoSpecification implements Specification<ExtratoConciliacaoView> {

  private final ExtratoConciliacaoFilter filter;

  @Override
  public Predicate toPredicate(Root<ExtratoConciliacaoView> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {


    if (query != null) {
      query.distinct(true);
    }

    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getBancoNome(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get("bancoNome")), "%" + e.toUpperCase() + "%"))
      .add(filter.getContaBancariaId(),
        e -> criteriaBuilder.equal(root.get("contaBancariaId"), e))
      .add(filter.getDataBaixaInicial(),
        e -> criteriaBuilder.greaterThanOrEqualTo(root.get("dataBaixa"), e))
      .add(filter.getDataBaixaFinal(),
        e -> criteriaBuilder.lessThanOrEqualTo(root.get("dataBaixa"), e))
      .add(filter.getParceiroNome(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get("parceiroNome")), "%" + e.toUpperCase() + "%"))
      .add(filter.getValorBaixa(),
        e -> criteriaBuilder.like(root.get("valorBaixa").as(String.class), "%" + e + "%"))
      .add(filter.getDataBaixa(),
        e -> criteriaBuilder.equal(root.get("dataBaixa"), e))
      .add(filter.getDataVencimento(),
        e -> criteriaBuilder.equal(root.get("vencimento"), e))
      .add(filter.getStatus(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get("status").as(String.class)),
          "%" + e.toUpperCase() + "%"))
      .add(filter.getTipo(), e -> root.get("tipo").in(e))
      .add(filter.getEmpresaId(), e -> criteriaBuilder.equal(root.get("empresaId"), e))
      .and();
  }
}
