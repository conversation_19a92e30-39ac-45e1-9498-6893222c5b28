package br.com.nuvy.api.integration.service;

import br.com.nuvy.api.admin.repository.AplicacaoRepository;
import br.com.nuvy.api.admin.service.AplicacaoService;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.service.PessoaService;
import br.com.nuvy.api.integration.filter.CallfaceChamadaHistoricoFilter;
import br.com.nuvy.api.integration.repository.CallfaceRepository;
import br.com.nuvy.api.integration.specfication.CallfaceChamadaHistoricoSpecification;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.service.UsuarioAplicacaoService;
import br.com.nuvy.callface.callface.model.CallfaceChamadaHistorico;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.AmazonS3Service;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuarioAplicacaoResumoDto;
import br.com.nuvy.facade.historico.ht1callfacechamada.model.CallfaceChamadaHistoricoResumoDto;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CallfaceService extends
  PageableServiceAdapter<CallfaceChamadaHistorico, UUID, CallfaceChamadaHistoricoFilter, CallfaceRepository> {

  private final CallfaceRepository callfaceRepository;
  private final Badge badge;
  private final AplicacaoService aplicacaoService;
  private final UsuarioAplicacaoService usuarioAplicacaoService;
  private final PessoaService pessoaService;
  private final AplicacaoRepository aplicacaoRepository;
  private final ServicoNotificacao sqsService;
  private final AmazonS3Service amazonS3Service;

  public Long getCallFaceOrganizationId() {
    return repository.getCallFaceOrganizationId();
  }

  public Long getCallFaceRamal() {
    return repository.getCallFaceRamal();
  }

  @Override
  protected CallfaceChamadaHistoricoSpecification configureSpecification(
    CallfaceChamadaHistoricoFilter filter) {
    return CallfaceChamadaHistoricoSpecification.from(filter);
  }

  public Page<CallfaceChamadaHistoricoResumoDto> findCallfaceChamadaHistorico(
    CallfaceChamadaHistoricoFilter filter, Pageable pageable) {
    if (pageable.getSort().isUnsorted()) {
      pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
        Sort.by(Direction.DESC, "dataChamada")
      );
    }

    var aplicacao = aplicacaoService.findById(badge.getTenantId())
      .orElseThrow(ResourceNotFoundException::new);

    if (!aplicacao.getCallface()) {
      throw new PreconditionException("tenant.callface.nao.contratado");
    }

    filter.setOrganizacaoId(aplicacao.getOrganizacaoCallFace());

    UsuarioAplicacao usuario = null;
    if (Objects.nonNull(filter.getUsuarioId())) {
      usuario = usuarioAplicacaoService.findById(filter.getUsuarioId())
        .orElseThrow(ResourceNotFoundException::new);
      filter.setRamal(usuario.getRamalCallFace());
    }

    Pessoa parceiro = null;
    if (Objects.nonNull(filter.getParceiroId())) {
      parceiro = pessoaService.findById(filter.getParceiroId())
        .orElseThrow(ResourceNotFoundException::new);
      filter.setTelefoneDestino(parceiro.getTelefone());
    }

    var callfaceHistoricoPage = callfaceRepository.findAll(configureSpecification(filter),
      pageable);
    var callfacesList = new ArrayList<CallfaceChamadaHistoricoResumoDto>();

    DateTimeFormatter format = DateTimeFormatter.ofPattern("HH:mm:ss");
    for (var callfaceChamadaHistorico : callfaceHistoricoPage.stream().toList()) {
      var callfaceDtoBuilder = CallfaceChamadaHistoricoResumoDto.builder()
        .id(callfaceChamadaHistorico.getId())
        .dataChamada(callfaceChamadaHistorico.getDataChamada())
        .horaInicio(callfaceChamadaHistorico.getDataChamada().format(format))
        .telefoneParceiro(callfaceChamadaHistorico.getTelefoneDestino())
        .duracaoChamada(montaDuracaoChamada(callfaceChamadaHistorico.getDuracaoChamada()));

      LocalDateTime dataChamadaFinal = callfaceChamadaHistorico.getDataChamada()
        .plusSeconds(Long.parseLong(callfaceChamadaHistorico.getDuracaoChamada()));
      callfaceDtoBuilder.horaFim(dataChamadaFinal.format(format));

      if (Objects.nonNull(parceiro)) {
        callfaceDtoBuilder.nomeParceiro(parceiro.getNome());
      } else {
        Optional<Pessoa> parceiroOpt = pessoaService.findByTelefone(
          callfaceChamadaHistorico.getTelefoneDestino());
        parceiroOpt.ifPresent(pessoa -> callfaceDtoBuilder.nomeParceiro(pessoa.getNome()));
      }

      if (Objects.nonNull(usuario)) {
        callfaceDtoBuilder.usuario(UsuarioAplicacaoResumoDto.from(usuario));
      } else {
        Optional<UsuarioAplicacao> usuarioOpt = usuarioAplicacaoService.findByRamalCallFaceAndAplicacao(
          callfaceChamadaHistorico.getUsuarioId(), aplicacao.getId());
        usuarioOpt.ifPresent(
          usuarioAplicacao -> callfaceDtoBuilder.usuario(
            UsuarioAplicacaoResumoDto.from(usuarioAplicacao)));
      }
      callfaceDtoBuilder.urlGravacao(montaUrlGravacao(callfaceChamadaHistorico.getUrlGravacao()));
      callfacesList.add(callfaceDtoBuilder.build());
    }
    if (callfaceHistoricoPage.getTotalElements() < pageable.getPageSize()
      && pageable.getPageNumber() != 0) {
      return new PageImpl<>(new ArrayList<>(), pageable, callfacesList.size());
    }
    return new PageImpl<>(callfacesList, pageable, callfaceHistoricoPage.getTotalElements());
  }

  private String montaUrlGravacao(String urlGravacao) {
    // Defina uma data de expiração. Aqui usamos um período de 1 hora.
    /*Instant expiration = Instant.now().plus(Duration.ofHours(1));

    GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest("nuvy-callface", "2023/06/07/7000/5905.mp3");
    generatePresignedUrlRequest.setExpiration(Date.from(expiration));
    //https://nuvy-callface.s3.amazonaws.com/2023/06/07/7000/5905.mp3
    URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
    return url.toString();*/
    return amazonS3Service.generateUrl(urlGravacao);
  }

  private String montaDuracaoChamada(String duracaoChamada) {
    Duration total = Duration.ofSeconds(Long.parseLong(duracaoChamada));
    int horas = total.toHoursPart();
    int minutos = total.toMinutesPart();
    int segundos = total.toSecondsPart();
    if (segundos < 60 && minutos == 0 && horas == 0) {
      return duracaoChamada + " seg";
    } else {
      if (minutos > 0 && horas == 0) {
        return minutos + "min:" + segundos + "seg";
      } else {
        return String.format("%02d:%02d:%02d",
          horas,
          minutos,
          segundos);
      }
    }
  }

  public void ativarCallface() {
    var aplicacao = aplicacaoService.findById(badge.getTenantId()).orElseThrow(
      ResourceNotFoundException::new);

    if (Objects.nonNull(aplicacao.getOrganizacaoCallFace()) && aplicacao.getCallface()) {
      throw new PreconditionException("aplicacao.callface.ja.ativa");
    } else if (Objects.nonNull(aplicacao.getOrganizacaoCallFace()) && !aplicacao.getCallface()) {
      aplicacao.setCallface(true);
    } else {
      aplicacao.setOrganizacaoCallFace(callfaceRepository.getCallFaceOrganizationId().intValue());
      aplicacao.setCallface(true);
    }

    aplicacaoRepository.save(aplicacao);

    sqsService.enfilerarQueueCallfaceRegistro(aplicacao);

  }

  public void desativarCallface() {
    var aplicacao = aplicacaoService.findById(badge.getTenantId()).orElseThrow(
      ResourceNotFoundException::new);

    aplicacao.setCallface(false);

    aplicacaoRepository.save(aplicacao);

    sqsService.enfilerarQueueCallfaceRegistro(aplicacao);

  }

  public void ativarRamalUsuario(Integer usuarioAplicacaoId) {
    var usuarioAplicacao = usuarioAplicacaoService.findById(usuarioAplicacaoId).orElseThrow(ResourceNotFoundException::new);

    if (Objects.isNull(usuarioAplicacao.getRamalCallFace())) {
      usuarioAplicacao.setRamalCallFace(getProximoRamalDisponivel());
    }

    usuarioAplicacao.setCallface(true);
    usuarioAplicacao.setDataVencimento(null);
    usuarioAplicacaoService.update(usuarioAplicacaoId, usuarioAplicacao);
  }

  private Integer getProximoRamalDisponivel() {
    var ramal = repository.getCallFaceRamal().intValue();

    var usuarioAplicacao = usuarioAplicacaoService.findByRamalCallFaceAndAplicacao(ramal, badge.getTenantId());

    if (usuarioAplicacao.isEmpty()) {
      return ramal;
    } else {
      return getProximoRamalDisponivel();
    }
  }

  public void desativarRamalUsuario(Integer usuarioAplicacaoId) {
    var usuarioAplicacao = usuarioAplicacaoService.findById(usuarioAplicacaoId).orElseThrow(ResourceNotFoundException::new);

    var dataAtual = LocalDate.now().plusMonths(1);
    usuarioAplicacao.setDataVencimento(LocalDate.of(dataAtual.getYear(), dataAtual.getMonth(), 1));
    usuarioAplicacaoService.update(usuarioAplicacaoId, usuarioAplicacao);
  }

  public void cancelarDesativacaoRamalUsuario(Integer usuarioAplicacaoId) {
    var usuarioAplicacao = usuarioAplicacaoService.findById(usuarioAplicacaoId).orElseThrow(ResourceNotFoundException::new);

    usuarioAplicacao.setDataVencimento(null);
    usuarioAplicacaoService.update(usuarioAplicacaoId, usuarioAplicacao);
  }

}
