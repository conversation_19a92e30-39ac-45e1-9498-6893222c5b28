package br.com.nuvy.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.StreamSupport;

/**
 * Componente que loga todas as configurações relacionadas ao HikariCP
 * para ajudar a diagnosticar problemas de carregamento de configuração.
 */
@Component
@Slf4j
public class ConfigurationLogger implements ApplicationListener<ApplicationPreparedEvent> {

    @Override
    public void onApplicationEvent(ApplicationPreparedEvent event) {
        ConfigurableEnvironment environment = event.getApplicationContext().getEnvironment();
        
        log.info("======= CONFIGURAÇÕES DO HIKARI =======");
        
        // Exibe os perfis ativos
        log.info("Perfis ativos: {}", Arrays.toString(environment.getActiveProfiles()));
        
        // Procura e exibe todas as propriedades relacionadas ao HikariCP
        Map<String, Object> hikariProps = findHikariProperties(environment);
        hikariProps.forEach((key, value) -> log.info("{} = {}", key, value));
        
        // Exibe as propriedades mais importantes
        log.info("spring.datasource.hikari.maximum-pool-size = {}", 
                environment.getProperty("spring.datasource.hikari.maximum-pool-size"));
        log.info("spring.datasource.hikari.minimum-idle = {}", 
                environment.getProperty("spring.datasource.hikari.minimum-idle"));
        log.info("spring.datasource.hikari.connection-timeout = {}", 
                environment.getProperty("spring.datasource.hikari.connection-timeout"));
        
        // Listar todas as fontes de propriedades em ordem de prioridade
        log.info("======= FONTES DE PROPRIEDADES =======");
        StreamSupport.stream(environment.getPropertySources().spliterator(), false)
                .forEach(source -> log.info("{}: {}", source.getName(), source.getClass().getSimpleName()));
        
        log.info("======================================");
    }
    
    private Map<String, Object> findHikariProperties(ConfigurableEnvironment environment) {
        Map<String, Object> hikariProps = new LinkedHashMap<>();
        
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            if (propertySource instanceof EnumerablePropertySource) {
                EnumerablePropertySource<?> enumSource = (EnumerablePropertySource<?>) propertySource;
                for (String propertyName : enumSource.getPropertyNames()) {
                    if (propertyName.contains("hikari")) {
                        hikariProps.put(propertyName, environment.getProperty(propertyName));
                    }
                }
            }
        }
        
        return hikariProps;
    }
}
