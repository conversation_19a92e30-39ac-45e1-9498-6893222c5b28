package br.com.nuvy.api.venda.repository;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoPedido;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PedidoRepository extends NuvyRepository<Pedido, UUID> {

  @Modifying
  @Query("UPDATE Pedido pd SET pd.situacao = :situacaoPedido WHERE pd.id = :id")
  void alterarSituacao(UUID id, SituacaoPedido situacaoPedido);

  @Modifying
  @Query("UPDATE Pedido pd SET pd.xmlNotaFiscalInutilizacao = :xmlNotaFiscalInutilizacao WHERE pd.id = :id")
  void alterarCaminhoXmlInutilizacao(UUID id, String xmlNotaFiscalInutilizacao);

  @Query("SELECT pd.situacao FROM Pedido pd WHERE pd.id = :id")
  SituacaoPedido findSituacaoById(UUID id);

  @Query(value = """
    SELECT SUM(total_valor) AS total_geral
        FROM (
         -- Primeiro select: total de vl_nota_fiscal da tabela erp.vd_pedido
         SELECT COALESCE(SUM(pd.vl_nota_fiscal), 0) AS total_valor
         FROM erp.vd_pedido pd
         WHERE pd.id_empresa = :empresaId
           AND pd.situacao IN (
                               'FATURADO',
                               'NF_EMITIDA',
                               'CONCLUIDO',
                               'ENVIADO',
                               'ENTREGUE',
                               'PEDIDO_AUTORIZADO'
             )
           AND pd.dt_pedido BETWEEN :dataInicial AND :dataFinal
           AND pd.excluido = false

         UNION ALL

         -- Segundo select: total de valor_total da tabela erp.cd_pos_pdv_venda
         SELECT COALESCE(SUM(pd.valor_total), 0) AS total_valor
         FROM erp.cd_pos_pdv_venda pd
         WHERE pd.id_empresa = :empresaId
           AND pd.situacao = 'ATIVO'
           AND pd.ambiente = 'PRODUCAO'
           AND pd.excluido = false
           AND pd.data_venda BETWEEN :dataInicial AND :dataFinal

         UNION ALL

         -- Terceiro select: total de valor_nf da tabela "erp-nfs".ordem_servico (apenas com o histórico mais recente)
         SELECT COALESCE(SUM(os.valor_nf), 0) AS total_valor
         FROM "erp-nfs".ordem_servico os
                  INNER JOIN (
             SELECT DISTINCT ON (id_ordem_servico) *
             FROM "erp-nfs".ordem_servico_historico
             WHERE situacao = 'FATURADO'
               AND data_historico BETWEEN :dataInicial AND :dataFinal
             ORDER BY id_ordem_servico, data_historico DESC
         ) AS osh ON os.id = osh.id_ordem_servico
         WHERE os.id_empresa = :empresaId
           AND os.situacao = 'FATURADO'
     )
      AS somas
    """, nativeQuery = true)
  Optional<BigDecimal> sumTotalPedidosPorEmpresa(
    Integer empresaId, LocalDateTime dataInicial, LocalDateTime dataFinal
  );

  @Query(value = """
    SELECT SUM(total_valor) AS total_geral
        FROM (
         -- Primeiro select: total de vl_nota_fiscal da tabela erp.vd_pedido
         SELECT COALESCE(SUM(pd.vl_nota_fiscal), 0) AS total_valor
         FROM erp.vd_pedido pd
         WHERE pd.id_empresa = :empresaId
           AND pd.situacao IN (
                               'FATURADO',
                               'NF_EMITIDA',
                               'CONCLUIDO',
                               'ENVIADO',
                               'ENTREGUE',
                               'PEDIDO_AUTORIZADO'
             )
           AND pd.dt_pedido BETWEEN :dataInicial AND :dataFinal
           AND pd.excluido = false

         UNION ALL

        SELECT COALESCE(SUM(pd.vl_nota_fiscal), 0) AS total_valor
        FROM erp.vd_pedido pd
        WHERE pd.id_canal_venda = :canalVendaId
        AND pd.situacao IN ('FATURADO', 'NF_EMITIDA')
        AND pd.dt_pedido BETWEEN :dataInicial AND :dataFinal
        AND pd.excluido = false

        UNION ALL
      -- Terceiro select: total de valor_nf da tabela "erp-nfs".ordem_servico (apenas com o histórico mais recente)
         SELECT COALESCE(SUM(os.valor_nf), 0) AS total_valor
         FROM "erp-nfs".ordem_servico os
                  INNER JOIN (
             SELECT DISTINCT ON (id_ordem_servico) *
             FROM "erp-nfs".ordem_servico_historico
             WHERE situacao = 'FATURADO'
               AND data_historico BETWEEN :dataInicial AND :dataFinal
             ORDER BY id_ordem_servico, data_historico DESC
         ) AS osh ON os.id = osh.id_ordem_servico
         WHERE os.id_empresa = :empresaId
           AND os.situacao = 'FATURADO'

  )
  AS somas
  """, nativeQuery = true)
  Optional<BigDecimal> sumTotalPedidosPorCanalVenda(
    Integer canalVendaId, LocalDateTime dataInicial, LocalDateTime dataFinal
  );

  @Query(value = """
    SELECT SUM(total_valor) AS total_geral
    FROM (
       -- Primeiro select: total de vl_nota_fiscal da tabela erp.vd_pedido
        SELECT COALESCE(SUM(pd.vl_nota_fiscal), 0) AS total_valor
    FROM erp.vd_pedido pd
    WHERE pd.id_vendedor = :vendedorId
    AND pd.situacao IN (
                             'FATURADO',
                               'NF_EMITIDA',
                               'CONCLUIDO',
                               'ENVIADO',
                               'ENTREGUE',
                               'PEDIDO_AUTORIZADO'
    )
    AND pd.dt_pedido BETWEEN :dataInicial AND :dataFinal
    AND pd.excluido = false

    UNION ALL

       -- Segundo select: total de valor_total da tabela erp.cd_pos_pdv_venda
    SELECT COALESCE(SUM(pd.valor_total), 0) AS total_valor
    FROM erp.cd_pos_pdv_venda pd
    WHERE pd.id_vendedor = :vendedorId
    AND pd.situacao = 'ATIVO'
    AND pd.ambiente = 'PRODUCAO'
    AND pd.excluido = false
    AND pd.data_venda BETWEEN :dataInicial AND :dataFinal

    UNION ALL

       -- Terceiro select: total de valor_nf da tabela "erp-nfs".ordem_servico (apenas com o histórico mais recente)
    SELECT COALESCE(SUM(os.valor_nf), 0) AS total_valor
    FROM "erp-nfs".ordem_servico os
    INNER JOIN (
      SELECT DISTINCT ON (id_ordem_servico) *
    FROM "erp-nfs".ordem_servico_historico
    WHERE situacao = 'FATURADO'
    AND data_historico BETWEEN :dataInicial AND :dataFinal
    ORDER BY id_ordem_servico, data_historico DESC
       ) AS osh ON os.id = osh.id_ordem_servico
    WHERE os.id_vendedor = :vendedorId
    AND os.situacao = 'FATURADO'
      ) AS somas
    """, nativeQuery = true)
  Optional<BigDecimal> sumTotalPedidosPorVendedor(
    Integer vendedorId, LocalDateTime dataInicial, LocalDateTime dataFinal
  );

  @Query(value = """
    SELECT COALESCE(MAX(CAST(codigo_tela AS INTEGER)), 0)
    FROM vd_pedido
    WHERE id_aplicacao = :aplicacaoId
    AND id_empresa = :empresaId
    AND tp_pedido = 'NORMAL'
    """, nativeQuery = true)
  Integer getUltimoCodigoTelaPedidoSaidaNormal(String aplicacaoId, Integer empresaId);

  @Query(value = """
    SELECT COALESCE(MAX(CAST(codigo_tela AS INTEGER)), 0)
    FROM vd_pedido
    WHERE id_aplicacao = :aplicacaoId
    AND id_empresa = :empresaId
    AND tp_pedido = 'ENTRADA_NORMAL'
    """, nativeQuery = true)
  Integer getUltimoCodigoTelaPedidoEntradaNormal(String aplicacaoId, Integer empresaId);

  @Query(value = """
    select count(p)
    from Pedido p
    where p.pedidoPai.id = :pedidoId
    and p.tipoPedido = 'DEVOLUCAO'
    """)
  Long countPedidosDevolucao(UUID pedidoId);

  @Query(value = """
    select count(p)
    from Pedido p
    where p.pedidoPai.id = :pedidoId
    and p.tipoPedido = 'RETORNO'
    """)
  Long countPedidosRetorno(UUID pedidoId);

  @Query("""
    select case when count(pd)> 0 then true else false end
    from Pedido pd
    where pd.cliente.id = :clienteId
    and pd.dataPedido >= :data
    and not pd.excluido
    """)
  boolean existsPedidoByClienteIdAndData(Integer clienteId, LocalDateTime data);

  Optional<Pedido> findPedidoByCodigoPedidoIgnoreCase(String codigoPedido);

  Optional<Pedido> findFirstBySerieNotaFiscalAndNumeroNotaFiscalAndEmpresa(
    String serieNotaFiscal, Integer numeroNotaFiscal, Empresa empresa
  );

  @Query("""
    SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END
    FROM Pedido p
    WHERE p.serieNotaFiscal = :serieNotaFiscal
    AND p.numeroNotaFiscal BETWEEN :numeroInicial AND :numeroFinal
    AND NOT p.excluido
    """)
  boolean existsBySerieNotaFiscalAndNumeroNotaFiscalBetween(
    String serieNotaFiscal, Integer numeroInicial, Integer numeroFinal
  );

  @Query("""
    SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END
    FROM Pedido p
    WHERE p.serieNotaFiscal = :serieNotaFiscal
    AND p.numeroNotaFiscal BETWEEN :numeroInicial AND :numeroFinal
    AND p.id <> :id
    AND NOT p.excluido
    """)
  boolean existsByFaixaNumerosAndSerie(
    String serieNotaFiscal, Integer numeroInicial, Integer numeroFinal, UUID id
  );

  @Query("""
    SELECT p
    FROM Pedido p
    WHERE p.tipoPedido IN :tipoPedido
    AND p.pedidoPai.id = :pedidoId
    AND NOT p.excluido
    """)
  List<Pedido> findPedidoByTipoPedidoAndPedidoPaiId(List<TipoPedido> tipoPedido, UUID pedidoId);

  @Query("""
    SELECT COUNT(pd)
    FROM Pedido pd
    WHERE pd.pedidoPai = :pedidoPai
    AND pd.situacao = 'NF_DEVOLUCAO_EMITIDA'
    AND NOT pd.excluido
    """)
  Integer countDevolucoesEmitidasByPedidoPai(Pedido pedidoPai);

  boolean existsByIdAndOrderIdIsNotNull(UUID pedidoId);

  @Query("""
    SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END
    FROM Pedido p
    WHERE p.empresa.id = :empresaId
    AND (:situacaoPedido IS NULL OR p.situacao = :situacaoPedido)
    AND (:orderNotNull IS NULL OR p.orderId IS NOT NULL)
    AND NOT p.excluido
    """)
  boolean existsByEmpresaIdAndSituacaoAndOrderIdIsNotNull(
    Integer empresaId, SituacaoPedido situacaoPedido, Boolean orderNotNull
  );

  @Query("""
    SELECT p
    FROM Pedido p
    WHERE p.integrationOrderid = :integrationOrderId
    AND NOT p.excluido
    """)
  Optional<Pedido> findByIntegrationOrderid(String integrationOrderId);

  @Query("""
    SELECT CASE WHEN COUNT(p) > 0 THEN true ELSE false END
    FROM Pedido p
    WHERE p.nop = :nop
    AND NOT p.excluido
    """)
  boolean existsByNop(Nop nop);

  @Modifying(flushAutomatically = true, clearAutomatically = true)
  @Query(value= """
    UPDATE erp.vd_pedido
    SET situacao = :situacaoPedido,
        tp_comprovante = :tipoComprovante
    WHERE id_pedido = :id
    and id_aplicacao = :idAplicacao
    """, nativeQuery = true)
  void alterarSituacaoETipoComprovante(
    UUID id, String situacaoPedido, String tipoComprovante, String idAplicacao
  );

  @Query("""
    SELECT pd
    FROM Pedido pd
    WHERE pd.cliente = :cliente
    AND pd.situacao IN :situacoes
    AND NOT pd.excluido
    """)
  List<Pedido> findAllByClienteAndSituacaoIn(Pessoa cliente, List<SituacaoPedido> situacoes);

  @Modifying
  @Query(value = """
    UPDATE vd_pedido
       SET excluido = true
     WHERE id_pedido = :id
    """,
    nativeQuery = true
  )
  void deleteById(UUID id);

  @Modifying(clearAutomatically = true, flushAutomatically = true)
  @Query(value = """
    UPDATE vd_pedido
    SET id_empresa = :novaEmpresaId,
        id_nop = :novaNopId,
        id_banco_conta = :novaContaBancariaId,
        codigo_tela = :novoCodigo,
        descricao_nop = :descricaoNop,
        id_conta = :planoContaId,
        updated_at = now(),
        updated_by = :updatedBy
    WHERE id_pedido = :pedidoId
    """, nativeQuery = true)
  void moverPedidoParaEmpresa(
    UUID pedidoId,
    Integer novaEmpresaId,
    Integer novaNopId,
    Integer novaContaBancariaId,
    String novoCodigo,
    String descricaoNop,
    Integer planoContaId,
    String updatedBy
  );

  @Modifying(clearAutomatically = true, flushAutomatically = true)
  @Query(value = """
    UPDATE vd_pedido_item
    SET id_nop = :novaNopId,
        id_conta_receita = :planoContaId
    WHERE id_pedido = :pedidoId
    """, nativeQuery = true)
  void atualizarItensPedidoMover(UUID pedidoId, Integer novaNopId, Integer planoContaId);

  @Modifying(clearAutomatically = true, flushAutomatically = true)
  @Query(value = """
    UPDATE vd_pedido_recebimento
    SET id_banco_conta = :novaContaBancariaId
    WHERE id_pedido = :pedidoId
    """, nativeQuery = true)
  void atualizarRecebimentosPedidoMover(UUID pedidoId, Integer novaContaBancariaId);

  @Modifying
  @Query(value = "UPDATE vd_pedido SET codigo_tela = :novoCodigo WHERE id_pedido = :idPedido", nativeQuery = true)
  void atualizarCodigoTela(UUID idPedido,  String novoCodigo);

  @Modifying
  @Query(value = "UPDATE erp.vd_pedido SET id_empresa = :empresaId, codigo_tela = :codigoTela, " +
    "updated_by = :updatedBy, updated_at = CURRENT_TIMESTAMP WHERE id_pedido = :pedidoId", nativeQuery = true)
  void atualizarEmpresaECodigo(UUID pedidoId, Integer empresaId, String codigoTela, String updatedBy);

  @Modifying
  @Query(value = "UPDATE erp.vd_pedido SET id_nop = :nopId, descricao_nop = :nopDescricao, " +
    "id_banco_conta = :contaBancariaId WHERE id_pedido = :pedidoId", nativeQuery = true)
  void atualizarNopEContaBancaria(UUID pedidoId, Integer nopId, String nopDescricao, Integer contaBancariaId);
}
