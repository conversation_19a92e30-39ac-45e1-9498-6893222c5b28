package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.dto.PosPdvProdutoDto;
import br.com.nuvy.api.cadastro.model.PosPdvProduto;
import br.com.nuvy.api.cadastro.model.PosPdvProduto_;
import br.com.nuvy.api.cadastro.model.PosPdv_;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.Produto_;
import br.com.nuvy.api.cadastro.repository.specification.PosPdvProdutoSpecification;
import br.com.nuvy.api.enums.Situacao;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
class PosPdvProdutoCustomRepositoryImpl implements PosPdvProdutoCustomRepository {

  private final EntityManager entityManager;

  public PosPdvProdutoCustomRepositoryImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @Override
  public Page<PosPdvProdutoDto> findProdutosPosPdv(
    UUID idPosPdv, PosPdvProdutoSpecification spec, Pageable pageable
  ) {
    CriteriaBuilder builder = entityManager.getCriteriaBuilder();

    // Abordagem com join
    CriteriaQuery<PosPdvProdutoDto> criteriaQuery = builder.createQuery(PosPdvProdutoDto.class);
    Root<PosPdvProduto> posPdvProdutoRoot = criteriaQuery.from(PosPdvProduto.class);
    jakarta.persistence.criteria.Join<PosPdvProduto, Produto> produtoJoin =
      posPdvProdutoRoot.join(PosPdvProduto_.produto);

    // Lista de predicados
    List<Predicate> predicates = new ArrayList<>();

    // Filtro por PosPdv
    predicates.add(builder.equal(
      posPdvProdutoRoot.get(PosPdvProduto_.posPdv).get(PosPdv_.id), idPosPdv
    ));

    // Filtro de variação
    predicates.add(builder.or(
      // adiciona produtos comuns
      builder.and(builder.isFalse(produtoJoin.get(Produto_.variacaoPai)),
        builder.isFalse(produtoJoin.get(Produto_.variacao))),
      // adiciona produtos que são variações de um produto pai
      builder.and(builder.isFalse(produtoJoin.get(Produto_.variacaoPai)),
        builder.isTrue(produtoJoin.get(Produto_.variacao)))
    ));

    // Filtro de situação
    predicates.add(builder.equal(produtoJoin.get(Produto_.situacao), Situacao.ATIVO));

    // Aplicar especificação adicional
    if (spec != null) {
      Predicate specPredicate = spec.toPredicate(posPdvProdutoRoot, criteriaQuery, builder);
      if (specPredicate != null) {
        predicates.add(specPredicate);
      }
    }

    // Construir o SELECT com os campos necessários
    criteriaQuery.select(builder.construct(
      PosPdvProdutoDto.class,
      produtoJoin.get(Produto_.id).alias("id"),
      produtoJoin.get(Produto_.codigo).alias("codigo"),
      produtoJoin.get(Produto_.descricao).alias("descricao"),
      produtoJoin.get(Produto_.precoVenda).alias("precoVenda"),
      posPdvProdutoRoot.get(PosPdvProduto_.precoVenda).alias("precoVendaPosPdv"),
      posPdvProdutoRoot.get(PosPdvProduto_.qtdMinimaAtacado).alias("quantidadeAtacado"),
      posPdvProdutoRoot.get(PosPdvProduto_.vlAtacado).alias("precoVendaAtacado"),
      posPdvProdutoRoot.get(PosPdvProduto_.limiteDesconto).alias("limiteDesconto")
    ));

    // Aplicar todos os predicados combinados com AND
    criteriaQuery.where(builder.and(predicates.toArray(new Predicate[0])));

    // Aplicar ordenação
    List<Order> orders = createOrderBy(builder, produtoJoin, posPdvProdutoRoot, pageable);

    if (!orders.isEmpty()) {
      criteriaQuery.orderBy(orders);
    }

    TypedQuery<PosPdvProdutoDto> query = entityManager.createQuery(criteriaQuery);
    query.setFirstResult((int) pageable.getOffset());
    query.setMaxResults(pageable.getPageSize());

    List<PosPdvProdutoDto> resultList = query.getResultList();
    long total = getTotalCount(idPosPdv, builder, spec);

    return new PageImpl<>(resultList, pageable, total);
  }

  private long getTotalCount(UUID idPosPdv, CriteriaBuilder builder, PosPdvProdutoSpecification spec) {
    CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
    Root<PosPdvProduto> posPdvProdutoRoot = countQuery.from(PosPdvProduto.class);
    jakarta.persistence.criteria.Join<PosPdvProduto, Produto> produtoJoin =
      posPdvProdutoRoot.join(PosPdvProduto_.produto);

    List<Predicate> predicates = new ArrayList<>();

    // Filtro por PosPdv
    predicates.add(builder.equal(
      posPdvProdutoRoot.get(PosPdvProduto_.posPdv).get(PosPdv_.id), idPosPdv
    ));

    // Filtro de variação
    predicates.add(builder.or(
      // adiciona produtos comuns
      builder.and(builder.isFalse(produtoJoin.get(Produto_.variacaoPai)),
        builder.isFalse(produtoJoin.get(Produto_.variacao))),
      // adiciona produtos que são variações de um produto pai
      builder.and(builder.isFalse(produtoJoin.get(Produto_.variacaoPai)),
        builder.isTrue(produtoJoin.get(Produto_.variacao)))
    ));

    // Filtro de situação
    predicates.add(builder.equal(produtoJoin.get(Produto_.situacao), Situacao.ATIVO));

    // Aplicar especificação adicional
    if (spec != null) {
      Predicate specPredicate = spec.toPredicate(posPdvProdutoRoot, countQuery, builder);
      if (specPredicate != null) {
        predicates.add(specPredicate);
      }
    }

    countQuery.select(builder.count(posPdvProdutoRoot));
    countQuery.where(builder.and(predicates.toArray(new Predicate[0])));

    return entityManager.createQuery(countQuery).getSingleResult();
  }

  private List<Order> createOrderBy(
    CriteriaBuilder builder,
    jakarta.persistence.criteria.Join<PosPdvProduto, Produto> produtoJoin,
    Root<PosPdvProduto> posPdvProdutoRoot,
    Pageable pageable) {

    List<Order> orders = new ArrayList<>();

    pageable.getSort().forEach(order -> {
      switch (order.getProperty().toLowerCase()) {
        case "id":
          if (order.isAscending()) {
            orders.add(builder.asc(produtoJoin.get(Produto_.id)));
          } else {
            orders.add(builder.desc(produtoJoin.get(Produto_.id)));
          }
          break;

        case "codigo":
          if (order.isAscending()) {
            orders.add(builder.asc(produtoJoin.get(Produto_.codigo)));
          } else {
            orders.add(builder.desc(produtoJoin.get(Produto_.codigo)));
          }
          break;

        case "descricao":
          if (order.isAscending()) {
            orders.add(builder.asc(produtoJoin.get(Produto_.descricao)));
          } else {
            orders.add(builder.desc(produtoJoin.get(Produto_.descricao)));
          }
          break;

        case "precovenda":
          if (order.isAscending()) {
            orders.add(builder.asc(produtoJoin.get(Produto_.precoVenda)));
          } else {
            orders.add(builder.desc(produtoJoin.get(Produto_.precoVenda)));
          }
          break;

        case "precovendapospdv":
          if (order.isAscending()) {
            orders.add(builder.asc(builder.coalesce(posPdvProdutoRoot.get(PosPdvProduto_.precoVenda), 0)));
          } else {
            orders.add(builder.desc(builder.coalesce(posPdvProdutoRoot.get(PosPdvProduto_.precoVenda), 0)));
          }
          break;

        case "limitedesconto":
          if (order.isAscending()) {
            orders.add(builder.asc(builder.coalesce(posPdvProdutoRoot.get(PosPdvProduto_.limiteDesconto), 0)));
          } else {
            orders.add(builder.desc(builder.coalesce(posPdvProdutoRoot.get(PosPdvProduto_.limiteDesconto), 0)));
          }
          break;

        default:
          // Ordenação padrão por ID
          orders.add(builder.asc(produtoJoin.get(Produto_.id)));
          break;
      }
    });

    if (orders.isEmpty()) {
      orders.add(builder.asc(produtoJoin.get(Produto_.id)));
    }

    return orders;
  }
}