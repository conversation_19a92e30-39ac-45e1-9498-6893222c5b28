package br.com.nuvy.api.venda.repository.specification;

import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.venda.filter.ComissaoExcecaoFilter;
import br.com.nuvy.api.venda.model.Comissao;
import br.com.nuvy.api.venda.model.ComissaoExcecao;
import br.com.nuvy.api.venda.model.ComissaoExcecao_;
import br.com.nuvy.api.venda.model.Comissao_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
public class ComissaoExcecaoSpecification implements Specification<ComissaoExcecao> {

  private final transient ComissaoExcecaoFilter filter;

  @Override
  public Predicate toPredicate(Root<ComissaoExcecao> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    query.distinct(true);
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getComissaoId(),
        e -> {
          Join<ComissaoExcecao, Comissao> comissao = root.join(ComissaoExcecao_.comissao);
          return criteriaBuilder.equal(comissao.get(Comissao_.id), e);
        })
      .add(filter.getName(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.join(ComissaoExcecao_.vendedor).get(
          Pessoa_.nome)), "%" + e.toUpperCase() + "%"))
      .add(filter.getEmail(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.join(ComissaoExcecao_.vendedor).get(
          Pessoa_.emailCobranca)), "%" + e.toUpperCase() + "%"))
      .add(filter.getPercentualComissao(),
        e -> criteriaBuilder.equal(root.get(ComissaoExcecao_.percentualComissao), e))
      .add(filter.getEmpresaId(),
        e -> criteriaBuilder.equal(root.join(ComissaoExcecao_.comissao).get(Comissao_.empresa).get(
          Empresa_.id), e)
        )
      .and();
  }
}
