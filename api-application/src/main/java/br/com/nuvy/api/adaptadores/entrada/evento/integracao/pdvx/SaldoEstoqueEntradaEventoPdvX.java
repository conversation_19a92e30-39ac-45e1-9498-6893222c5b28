package br.com.nuvy.api.adaptadores.entrada.evento.integracao.pdvx;

import br.com.nuvy.api.estoque.event.saldo.SaldoEstoqueAtualizado;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnfileiraEstoquePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.processador.ProcessadorProdutoPdvX;
import java.util.UUID;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
class SaldoEstoqueEntradaEventoPdvX {

  private final ProcessadorProdutoPdvX processadorProduto;

  SaldoEstoqueEntradaEventoPdvX(ProcessadorProdutoPdvX processadorProduto) {
    this.processadorProduto = processadorProduto;
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(SaldoEstoqueAtualizado evento) {
    processadorProduto.processaComando(new EnfileiraEstoquePdvX(
      UUID.randomUUID(), evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getProdutoId(), evento.getOcorridoAs()
    ));
  }
}
