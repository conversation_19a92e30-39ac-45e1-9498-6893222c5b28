package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.config.BadgeContext.getAplicacao;
import static br.com.nuvy.config.BadgeContext.getEmpresa;
import static br.com.nuvy.config.BadgeContext.getUsuario;
import static java.util.Comparator.comparing;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.enums.Especie;
import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.TipoAcao;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.financeiro.event.titulo.TituloInserido;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloCentroCustos;
import br.com.nuvy.api.financeiro.model.TituloDespesa;
import br.com.nuvy.api.financeiro.model.TituloHistorico;
import br.com.nuvy.api.financeiro.repository.TituloBaixaRepository;
import br.com.nuvy.api.financeiro.repository.TituloCentroCustosRepository;
import br.com.nuvy.api.financeiro.repository.TituloDespesaRepository;
import br.com.nuvy.api.financeiro.repository.TituloHistoricoRepository;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.integration.service.ServicoNotificacao;
import br.com.nuvy.api.servico.repository.OrdemServicoCentroCustoRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoParcelaRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoRepository;
import br.com.nuvy.api.sqs.dto.NotaFiscalServicoCancelada;
import br.com.nuvy.api.sqs.dto.OrdemServicoSqs;
import br.com.nuvy.api.sqs.dto.OrdemServicoSqs.TipoComprovante;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoCentroCusto;
import br.com.nuvy.api.venda.model.OrdemServicoItem;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.api.venda.model.OrdemServicoParcela;
import br.com.nuvy.client.services.BoletoService;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.utils.MathUtils;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.common.utils.StringUtils;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.EnviarEmailFaturadoSqs;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Classe responsável por realizar contas a receber de nota fiscal de servico/recibo.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TituloPorNFSService {

  private final OrdemServicoRepository repository;
  private final TituloRepository tituloRepository;
  private final TituloDespesaRepository despesaRepository;
  private final TituloBaixaRepository tituloBaixaRepository;
  private final TituloCentroCustosRepository centroCustoRepository;
  private final TituloHistoricoRepository tituloHistoricoRepository;
  private final OrdemServicoParcelaRepository recebimentoRepository;
  private final OrdemServicoNotaFiscalRepository ordemServicoNotaFiscalRepository;
  private final OrdemServicoCentroCustoRepository ordemServicoCentroCustoRepository;
  private final BoletoService boletoService;
  private final ServicoNotificacao servicoNotificacaoSqs;
  private final ApplicationEventPublisher eventPublisher;

  public boolean permiteCancelarOrcamento(Long ordemServicoId){
    OrdemServico ordemServico = repository.required(ordemServicoId);
    List<Titulo> titulos = tituloRepository.findAllByOrdemServico(ordemServico);
    for (Titulo umTitulo : titulos) {
      if(tituloBaixaRepository.existsByTitulo(umTitulo)){
        return false;
      }
    }
    return true;
  }

  @Transactional
  public void gerar(OrdemServicoSqs evento){
    log.debug("INÍCIO - Gerando recebimento para OS ID: {}, Tipo: {}, Situação: {}",
              evento.getIdOrdemService(), evento.getTipoComprovante(), evento.getSituacao());

    SituacaoOrdemServico situacao = evento.getSituacao(); // é passado a situação da nota nao da ordem
    OrdemServico ordemServico = repository.required(evento.getIdOrdemService());
    Hibernate.initialize(ordemServico.getUsuario());
    BadgeContext.setUsuario(ordemServico.getUsuario());

    log.debug("OS recuperada: ID: {}, Cliente: {}, Valor Total: {}",
              ordemServico.getId(),
              ordemServico.getCliente() != null ? ordemServico.getCliente().getNome() : "N/A",
              ordemServico.getValorNf());

    if(evento.getTipoComprovante().equals(TipoComprovante.recibo)){
      log.debug("Processando comprovante do tipo RECIBO para OS ID: {}", ordemServico.getId());

      if(situacao.equals(SituacaoOrdemServico.FATURADO)){
        log.debug("OS ID: {} com situação FATURADO - Iniciando inserção de recebimento por recibo", ordemServico.getId());
        inserirRecebimentoPorRecibo(ordemServico);
        log.debug("OS ID: {} - Recebimento por recibo inserido com sucesso", ordemServico.getId());

        log.debug("OS ID: {} - Enfileirando envio de email de faturamento", ordemServico.getId());
        enfileirarEnvioDeEmailFaturamento(ordemServico);
        log.debug("OS ID: {} - Email de faturamento enfileirado com sucesso", ordemServico.getId());
      }else if(situacao.equals(SituacaoOrdemServico.CANCELADO)){
        log.debug("OS ID: {} com situação CANCELADO - Iniciando exclusão de recebimentos", ordemServico.getId());
        excluiRecebimento(ordemServico);
        log.debug("OS ID: {} - Recebimentos excluídos com sucesso", ordemServico.getId());

        log.debug("OS ID: {} - Iniciando cancelamento de boletos", ordemServico.getId());
        processarCancelamentoDeBoletos(ordemServico);
        log.debug("OS ID: {} - Boletos cancelados com sucesso", ordemServico.getId());
      } else {
        log.warn("OS ID: {} - Situação não identificada para processamento: {}", ordemServico.getId(), situacao);
      }
    }else if(evento.getTipoComprovante().equals(TipoComprovante.nota_fiscal)){
      log.debug("Processando comprovante do tipo NOTA_FISCAL para OS ID: {}", ordemServico.getId());

      if(situacao.equals(SituacaoOrdemServico.NF_EMITIDA)){
        log.debug("OS ID: {} com situação NF_EMITIDA - Iniciando inserção de recebimento por NFS", ordemServico.getId());
        inserirRecebimentoPorNfs(ordemServico);
        log.debug("OS ID: {} - Recebimento por NFS inserido com sucesso", ordemServico.getId());

        log.debug("OS ID: {} - Enfileirando envio de email de faturamento", ordemServico.getId());
        enfileirarEnvioDeEmailFaturamento(ordemServico);
        log.debug("OS ID: {} - Email de faturamento enfileirado com sucesso", ordemServico.getId());
      } else if (situacao.equals(SituacaoOrdemServico.CANCELADO)
        || situacao.equals(SituacaoOrdemServico.NF_CANCELADA)) {
        log.debug("OS ID: {} com situação {} - Iniciando exclusão de recebimentos",
                 ordemServico.getId(), situacao);
        excluiRecebimento(ordemServico);
        log.debug("OS ID: {} - Recebimentos excluídos com sucesso", ordemServico.getId());

        log.debug("OS ID: {} - Iniciando cancelamento de boletos", ordemServico.getId());
        processarCancelamentoDeBoletos(ordemServico);
        log.debug("OS ID: {} - Boletos cancelados com sucesso", ordemServico.getId());
      } else {
        log.warn("OS ID: {} - Situação não identificada para processamento: {}", ordemServico.getId(), situacao);
      }
    }else{
      log.warn("OS ID: {} - Tipo de comprovante não identificado: {}", ordemServico.getId(), evento.getTipoComprovante());
    }

    log.debug("FIM - Processamento de recebimento para OS ID: {} concluído", ordemServico.getId());
  }

  @Transactional
  public void cancelar(NotaFiscalServicoCancelada evento) {
    log.debug("INÍCIO - Gerando cancelamento para NFS ID: {}", evento.getId());

    var nf = ordemServicoNotaFiscalRepository.findByIdCancelar(evento.getId())
      .orElseThrow(() -> {
        log.error("NFS não encontrada para cancelar! ID: {}", evento.getId());
        return new ResourceNotFoundException(
          "NFS não encontrada para cancelar! id=%d".formatted(evento.getId())
        );
      });

    log.debug("NFS encontrada: Número: {}, Série: {}", nf.getNumeroNfse(), nf.getSerie());

    if (nf.getOrdemServico() != null) {
      OrdemServico ordemServico = nf.getOrdemServico();
      log.debug("OS encontrada: ID: {}, Cliente: {}",
                ordemServico.getId(),
                ordemServico.getCliente() != null ? ordemServico.getCliente().getNome() : "N/A");

      log.debug("OS ID: {} - Iniciando exclusão de recebimentos", ordemServico.getId());
      excluiRecebimento(ordemServico);
      log.debug("OS ID: {} - Recebimentos excluídos com sucesso", ordemServico.getId());

      log.debug("OS ID: {} - Iniciando cancelamento de boletos", ordemServico.getId());
      processarCancelamentoDeBoletos(ordemServico);
      log.debug("OS ID: {} - Boletos cancelados com sucesso", ordemServico.getId());
    } else {
      log.error("OS não encontrada ao cancelar NFS ID: {}", evento.getId());
    }

    log.debug("FIM - Cancelamento para NFS ID: {} concluído", evento.getId());
  }

  private void enfileirarEnvioDeEmailFaturamento(OrdemServico ordemServico){
    log.debug("INÍCIO - Enfileirando envio de email de faturamento para OS ID: {}", ordemServico.getId());

    if (deveEnviarEmail(ordemServico)) {
      log.debug("OS ID: {} - Critérios para envio de email atendidos", ordemServico.getId());

      var emailOsFaturada = EnviarEmailFaturadoSqs.builder()
        .aplicacaoId(ordemServico.getAplicacao())
        .empresaId(ordemServico.getEmpresa().getId())
        .usuarioId(ordemServico.getUsuario().getId())
        .ordemServicoId(ordemServico.getId())
        .build();

      log.debug("OS ID: {} - Enviando para fila de emails: Empresa ID: {}, Usuário ID: {}",
               ordemServico.getId(),
               ordemServico.getEmpresa().getId(),
               ordemServico.getUsuario().getId());

      servicoNotificacaoSqs.enfileirarQueueEnvioEmailOSFaturada(emailOsFaturada);
      log.debug("OS ID: {} - Email enfileirado com sucesso", ordemServico.getId());
    } else {
      log.debug("OS ID: {} - Critérios para envio de email NÃO atendidos, email não será enviado", ordemServico.getId());
    }

    log.debug("FIM - Processo de enfileiramento de email concluído para OS ID: {}", ordemServico.getId());
  }

  private void inserirRecebimentoPorRecibo(OrdemServico ordemServico) {
    log.debug("INÍCIO - Inserindo recebimento por recibo para OS ID: {}", ordemServico.getId());
    var recebimentos = recebimentoRepository
      .findAllByOrdemServico(ordemServico)
      .stream().sorted(comparing(OrdemServicoParcela::getNumero))
      .toList();
    log.debug("OS ID: {} - Encontradas {} parcelas de recebimento", ordemServico.getId(), recebimentos.size());

    var totalParcelas = recebimentos.size();

    BigDecimal valorTotalServicos = ordemServico.getItens().stream()
      .map(OrdemServicoItem::getValorTotalItem)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Valor total dos serviços: {}", ordemServico.getId(), valorTotalServicos);

    List<OrdemServicoItem> itens = ordemServico.getItens();
    log.debug("OS ID: {} - Total de itens: {}", ordemServico.getId(), itens.size());
    log.debug("OS ID: {} - Calculando valores de impostos retidos", ordemServico.getId());

    BigDecimal totalPisRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getPisRetido()))
      .map(OrdemServicoItem::getPis)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total PIS retido: {}", ordemServico.getId(), totalPisRetido);

    BigDecimal totalIssRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getIssRetido()))
      .map(OrdemServicoItem::getIss)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total ISS retido: {}", ordemServico.getId(), totalIssRetido);

    BigDecimal totalIrRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getIrRetido()))
      .map(OrdemServicoItem::getIr)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total IR retido: {}", ordemServico.getId(), totalIrRetido);

    BigDecimal totalCsllRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getCsllRetido()))
      .map(OrdemServicoItem::getCsll)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total CSLL retido: {}", ordemServico.getId(), totalCsllRetido);

    BigDecimal totalCofinsRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getCofinsRetido()))
      .map(OrdemServicoItem::getCofins)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total COFINS retido: {}", ordemServico.getId(), totalCofinsRetido);

    BigDecimal totalInssRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getInssRetido()))
      .map(OrdemServicoItem::getInss)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total INSS retido: {}", ordemServico.getId(), totalInssRetido);

    BigDecimal totalImpostosRetidos = totalPisRetido
      .add(totalIssRetido)
      .add(totalIrRetido)
      .add(totalCsllRetido)
      .add(totalCofinsRetido)
      .add(totalInssRetido);
    log.debug("OS ID: {} - Total de impostos retidos: {}", ordemServico.getId(), totalImpostosRetidos);

    BigDecimal totalRecebimentos = recebimentos.stream()
      .map(OrdemServicoParcela::getValor)
      .reduce(BigDecimal.ZERO, BigDecimal::add);
    log.debug("OS ID: {} - Total de recebimentos: {}", ordemServico.getId(), totalRecebimentos);

    log.debug("OS ID: {} - Calculando proporções das parcelas", ordemServico.getId());
    List<BigDecimal> proporcoesParcelas = new ArrayList<>();
    for (OrdemServicoParcela parcela : recebimentos) {
      BigDecimal proporcao = parcela.getValor().divide(totalRecebimentos, 10, RoundingMode.HALF_UP);
      proporcoesParcelas.add(proporcao);
      log.debug("OS ID: {} - Parcela {}: Valor: {}, Proporção: {}",
                ordemServico.getId(),
                parcela.getNumero(),
                parcela.getValor(),
                proporcao);
    }

    List<BigDecimal> rateiosPisRetido = new ArrayList<>();
    List<BigDecimal> rateiosIssRetido = new ArrayList<>();
    List<BigDecimal> rateiosIrRetido = new ArrayList<>();
    List<BigDecimal> rateiosCsllRetido = new ArrayList<>();
    List<BigDecimal> rateiosCofinsRetido = new ArrayList<>();
    List<BigDecimal> rateiosInssRetido = new ArrayList<>();

    log.debug("OS ID: {} - Calculando rateios de impostos retidos por parcela", ordemServico.getId());
    int parcelaIndex = 0;
    for (BigDecimal proporcao : proporcoesParcelas) {
      BigDecimal rateioPis = totalPisRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);
      BigDecimal rateioIss = totalIssRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);
      BigDecimal rateioIr = totalIrRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);
      BigDecimal rateioCsll = totalCsllRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);
      BigDecimal rateioCofins = totalCofinsRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);
      BigDecimal rateioInss = totalInssRetido.multiply(proporcao).setScale(2, RoundingMode.HALF_UP);

      rateiosPisRetido.add(rateioPis);
      rateiosIssRetido.add(rateioIss);
      rateiosIrRetido.add(rateioIr);
      rateiosCsllRetido.add(rateioCsll);
      rateiosCofinsRetido.add(rateioCofins);
      rateiosInssRetido.add(rateioInss);

      log.debug("OS ID: {} - Parcela {}: PIS: {}, ISS: {}, IR: {}, CSLL: {}, COFINS: {}, INSS: {}",
                ordemServico.getId(),
                parcelaIndex + 1,
                rateioPis, rateioIss, rateioIr, rateioCsll, rateioCofins, rateioInss);
      parcelaIndex++;
    }

    log.debug("OS ID: {} - Iniciando criação de {} títulos", ordemServico.getId(), totalParcelas);
    List<Titulo> titulos = new ArrayList<>();
    for (int i = 0; i < totalParcelas; i++) {
      log.debug("OS ID: {} - Criando título para parcela {}/{}", ordemServico.getId(), i+1, totalParcelas);
      Titulo titulo = new Titulo();
      titulo.setOrdemServico(ordemServico);
      titulo.setTipo(TipoTitulo.RECEBER);
      titulo.setNumeroNotaFiscal(ordemServico.getNumeroRps().toString());

      BigDecimal valorImpostosParcelados = rateiosPisRetido.get(i)
        .add(rateiosIssRetido.get(i))
        .add(rateiosIrRetido.get(i))
        .add(rateiosCsllRetido.get(i))
        .add(rateiosCofinsRetido.get(i))
        .add(rateiosInssRetido.get(i));

      BigDecimal valorLiquido = recebimentos.get(i).getValor();
      BigDecimal valorBruto = valorLiquido.add(valorImpostosParcelados);
      log.debug("OS ID: {} - Título parcela {}: Valor líquido: {}, Impostos: {}, Valor bruto: {}",
                ordemServico.getId(), i+1, valorLiquido, valorImpostosParcelados, valorBruto);

      titulo.setValorLiquido(valorLiquido);
      titulo.setValorTotal(valorBruto);

      titulo.setContaBancaria(recebimentos.get(i).getContaCorrente());
      titulo.setDataEmissao(LocalDateTime.now());
      titulo.setDataVencimento(recebimentos.get(i).getVencimento().toLocalDate());
      titulo.setFormaPagamento(recebimentos.get(i).getFormaRecebimento());
      titulo.setEspecie(Especie.CR);
      titulo.setSituacao(SituacaoTitulo.ABERTO);
      titulo.setNumeroParcela(recebimentos.get(i).getNumero());
      titulo.setFornecedor(ordemServico.getCliente());
      titulo.setEmpresa(ordemServico.getEmpresa());

      log.debug("OS ID: {} - Título parcela {}: Configurando impostos retidos", ordemServico.getId(), i+1);

      boolean pisRetido = rateiosPisRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setPisRetido(pisRetido);
      titulo.setValorPis(rateiosPisRetido.get(i));

      boolean issRetido = rateiosIssRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setIssRetido(issRetido);
      titulo.setValorIss(rateiosIssRetido.get(i));

      boolean irRetido = rateiosIrRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setIrRetido(irRetido);
      titulo.setValorIr(rateiosIrRetido.get(i));

      boolean csllRetido = rateiosCsllRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setCsllRetido(csllRetido);
      titulo.setValorCsll(rateiosCsllRetido.get(i));

      boolean cofinsRetido = rateiosCofinsRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setCofinsRetido(cofinsRetido);
      titulo.setValorCofins(rateiosCofinsRetido.get(i));

      boolean inssRetido = rateiosInssRetido.get(i).compareTo(BigDecimal.ZERO) > 0;
      titulo.setInssRetido(inssRetido);
      titulo.setValorInss(rateiosInssRetido.get(i));

      log.debug("OS ID: {} - Título parcela {}: PIS retido: {} ({}), ISS retido: {} ({}), IR retido: {} ({})",
                ordemServico.getId(), i+1,
                pisRetido, rateiosPisRetido.get(i),
                issRetido, rateiosIssRetido.get(i),
                irRetido, rateiosIrRetido.get(i));
      log.debug("OS ID: {} - Título parcela {}: CSLL retido: {} ({}), COFINS retido: {} ({}), INSS retido: {} ({})",
                ordemServico.getId(), i+1,
                csllRetido, rateiosCsllRetido.get(i),
                cofinsRetido, rateiosCofinsRetido.get(i),
                inssRetido, rateiosInssRetido.get(i));

      titulo.setTituloNFE(true);

      if(!titulos.isEmpty()){
        titulo.setTituloReferencia(Titulo.of(titulos.get(0).getId()));
      }

      log.debug("OS ID: {} - Salvando título da parcela {}", ordemServico.getId(), i+1);
      tituloRepository.save(titulo);
      log.debug("OS ID: {} - Título da parcela {} salvo com ID: {}", ordemServico.getId(), i+1, titulo.getId());

      log.debug("OS ID: {} - Publicando evento TituloInserido para título ID: {}", ordemServico.getId(), titulo.getId());
      eventPublisher.publishEvent(new TituloInserido(
        UUID.randomUUID(), getAplicacao().getUuid(), getEmpresa().getId(), getUsuario().getId(),
        titulo.getId(), LocalDateTime.now()
      ));
      log.debug("OS ID: {} - Evento TituloInserido publicado com sucesso", ordemServico.getId());

      List<TituloDespesa> tituloDespesas = new ArrayList<>();
      for (OrdemServicoItem item : itens) {
        TituloDespesa despesa = new TituloDespesa();
        despesa.setEmpresa(ordemServico.getEmpresa());
        despesa.setPlanoConta(item.getContaReceita());
        despesa.setPercentual(BigDecimal.ZERO);
        despesa.setTitulo(titulo);
        tituloDespesas.add(despesa);
      }
      if (!tituloDespesas.isEmpty()) {
        int size = tituloDespesas.size();
        BigDecimal percentual = BigDecimal.ONE.divide(new BigDecimal(size), 2, RoundingMode.DOWN);
        BigDecimal ultimoPercentual = BigDecimal.ONE.subtract(percentual.multiply(new BigDecimal(size - 1)));

        for (int j = 0; j < size; j++) {
          if (j == size - 1) {
            tituloDespesas.get(j).setPercentual(ultimoPercentual);
          } else {
            tituloDespesas.get(j).setPercentual(percentual);
          }
        }

        despesaRepository.saveAll(tituloDespesas);
      }

      List<TituloCentroCustos> tituloCentroCustos = new ArrayList<TituloCentroCustos>();
      List<OrdemServicoCentroCusto> ordemServicoCentroCustos = ordemServicoCentroCustoRepository
        .findAllByOrdemServico(ordemServico);

      for (OrdemServicoCentroCusto umaOrdemServicoCentroCusto : ordemServicoCentroCustos) {
        TituloCentroCustos centroCustoRateio = new TituloCentroCustos();
        centroCustoRateio.setTitulo(titulo);
        centroCustoRateio.setEmpresa(titulo.getEmpresa());
        centroCustoRateio.setCentroCusto(umaOrdemServicoCentroCusto.getCentroCusto());
        centroCustoRateio.setPercentual(umaOrdemServicoCentroCusto.getPercentualRateio());
        log.debug("Centro de Custos {}", umaOrdemServicoCentroCusto.getCentroCusto().getNome());
        log.debug("Inserido centro de custo com percentual {}", umaOrdemServicoCentroCusto.getPercentualRateio());
        tituloCentroCustos.add(centroCustoRateio);
      }

      if(!tituloCentroCustos.isEmpty()){
        centroCustoRepository.saveAll(tituloCentroCustos);
      }

      TituloHistorico historico = new TituloHistorico();
      historico.setDataHistorico(LocalDateTime.now());
      historico.setAcao(TipoAcao.CONTA_CRIADA_NFS);
      historico.setTitulo(titulo);
      historico.setDescricao("Conta Criada NFS " + titulo.getNumeroNotaFiscal());
      historico.setAplicacao(titulo.getAplicacao());
      historico.setUsuario(ordemServico.getUsuario());
      historico.setNomeUsuario(ordemServico.getUsuario().getNome());
      tituloHistoricoRepository.save(historico);
      titulos.add(titulo);
      log.debug("OS ID: {} - Título da parcela {} adicionado à lista de títulos", ordemServico.getId(), i+1);
    }

    log.debug("OS ID: {} - Processando {} títulos criados", ordemServico.getId(), titulos.size());
    processarTitulos(titulos);
    log.debug("FIM - Recebimento por recibo inserido com sucesso para OS ID: {}", ordemServico.getId());
  }

  private void inserirRecebimentoPorNfs(OrdemServico ordemServico) {
    log.debug("INÍCIO - Inserindo recebimento por NFS para OS ID: {}", ordemServico.getId());

    List<OrdemServicoParcela> recebimentos = recebimentoRepository
      .findAllByOrdemServico(ordemServico)
      .stream().sorted(comparing(OrdemServicoParcela::getNumero))
      .toList();
    log.debug("OS ID: {} - Encontradas {} parcelas de recebimento", ordemServico.getId(), recebimentos.size());

    int totalParcelas = recebimentos.size();
    OrdemServicoNotaFiscal notaFiscal = ordemServicoNotaFiscalRepository
      .findFirstByOrdemServico(ordemServico);
    log.debug("OS ID: {} - Nota Fiscal encontrada: Número: {}, Série: {}",
             ordemServico.getId(),
             notaFiscal != null ? notaFiscal.getNumeroNfse() : "N/A",
             notaFiscal != null ? notaFiscal.getSerie() : "N/A");

    List<OrdemServicoItem> itens = ordemServico.getItens().stream()
      .filter(item -> item.getGerarContaReceber())  // Filtra os itens onde gerarContaReceber é true
      .collect(Collectors.toList());
    log.debug("OS ID: {} - {} itens selecionados para gerar contas a receber", ordemServico.getId(), itens.size());

    List<BigDecimal> rateioPisRetido = calcularRateioPisRetido(itens, totalParcelas);
    List<BigDecimal> rateioIssRetido = calcularRateioIssRetido(itens, totalParcelas);
    List<BigDecimal> rateioIrRetido = calcularRateioIrRetido(itens, totalParcelas);
    List<BigDecimal> rateioCsllRetido = calcularRateioCsllRetido(itens, totalParcelas);
    List<BigDecimal> rateioCofinsRetido = calcularRateioCofinsRetido(itens, totalParcelas);
    List<BigDecimal> rateioInssRetido = calcularRateioInssRetido(itens, totalParcelas);

    List<Titulo> titulos = new ArrayList<>();
    for (int i = 0; i < totalParcelas; i++) {
      Titulo titulo = new Titulo();
      titulo.setOrdemServico(ordemServico);
      titulo.setTipo(TipoTitulo.RECEBER);
      titulo.setNumeroNotaFiscal(notaFiscal.getNumeroNfse());
      titulo.setSerie(notaFiscal.getSerie());

      titulo.setValorLiquido(recebimentos.get(i).getValor());
      titulo.setValorTotal(recebimentos.get(i).getValorBruto());

      titulo.setContaBancaria(recebimentos.get(i).getContaCorrente());
      titulo.setDataEmissao(LocalDateTime.now());
      titulo.setDataVencimento(recebimentos.get(i).getVencimento().toLocalDate());
      titulo.setFormaPagamento(recebimentos.get(i).getFormaRecebimento());
      titulo.setEspecie(Especie.CR);
      titulo.setSituacao(SituacaoTitulo.ABERTO);

      titulo.setNumeroParcela(recebimentos.get(i).getNumero());
      titulo.setFornecedor(ordemServico.getCliente());
      titulo.setEmpresa(ordemServico.getEmpresa());

      titulo.setPisRetido(rateioPisRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorPis(rateioPisRetido.get(i));
      titulo.setIssRetido(rateioIssRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorIss(rateioIssRetido.get(i));
      titulo.setIrRetido(rateioIrRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorIr(rateioIrRetido.get(i));
      titulo.setCsllRetido(rateioCsllRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorCsll(rateioCsllRetido.get(i));
      titulo.setCofinsRetido(rateioCofinsRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorCofins(rateioCofinsRetido.get(i));
      titulo.setInssRetido(rateioInssRetido.get(i).compareTo(BigDecimal.ZERO) > 0);
      titulo.setValorInss(rateioInssRetido.get(i));

      titulo.setTituloNFE(true);

      if(!titulos.isEmpty()){
        titulo.setTituloReferencia(Titulo.of(titulos.get(0).getId()));
      }

      tituloRepository.save(titulo);

      eventPublisher.publishEvent(new TituloInserido(
        UUID.randomUUID(), getAplicacao().getUuid(), getEmpresa().getId(), getUsuario().getId(),
        titulo.getId(), LocalDateTime.now()
      ));

      List<TituloDespesa> tituloDespesas = new ArrayList<>();
      for (OrdemServicoItem item : itens) {
        TituloDespesa despesa = new TituloDespesa();
        despesa.setEmpresa(ordemServico.getEmpresa());
        despesa.setPlanoConta(item.getContaReceita());
        despesa.setPercentual(BigDecimal.ZERO);
        despesa.setTitulo(titulo);
        tituloDespesas.add(despesa);
      }
      if (!tituloDespesas.isEmpty()) {
        int size = tituloDespesas.size();
        BigDecimal percentual = BigDecimal.ONE.divide(new BigDecimal(size), 2, RoundingMode.DOWN);
        BigDecimal ultimoPercentual = BigDecimal.ONE.subtract(percentual.multiply(new BigDecimal(size - 1)));

        for (int j = 0; j < size; j++) {
          if (j == size - 1) {
            tituloDespesas.get(j).setPercentual(ultimoPercentual);
          } else {
            tituloDespesas.get(j).setPercentual(percentual);
          }
        }

        despesaRepository.saveAll(tituloDespesas);
      }

      List<TituloCentroCustos> tituloCentroCustos = new ArrayList<TituloCentroCustos>();
      List<OrdemServicoCentroCusto> ordemServicoCentroCustos = ordemServicoCentroCustoRepository
        .findAllByOrdemServico(ordemServico);

      for (OrdemServicoCentroCusto umaOrdemServicoCentroCusto : ordemServicoCentroCustos) {

        TituloCentroCustos centroCustoRateio = new TituloCentroCustos();
        centroCustoRateio.setTitulo(titulo);
        centroCustoRateio.setEmpresa(titulo.getEmpresa());
        centroCustoRateio.setCentroCusto(umaOrdemServicoCentroCusto.getCentroCusto());
        centroCustoRateio.setPercentual(umaOrdemServicoCentroCusto.getPercentualRateio());

        log.debug("Centro de Custos {}", umaOrdemServicoCentroCusto.getCentroCusto().getNome());
        log.debug("Inserido centro de custo com percentual {}", umaOrdemServicoCentroCusto.getPercentualRateio());

        tituloCentroCustos.add(centroCustoRateio);
      }

      if(!tituloCentroCustos.isEmpty()){
        centroCustoRepository.saveAll(tituloCentroCustos);
      }

      TituloHistorico historico = new TituloHistorico();
      historico.setDataHistorico(LocalDateTime.now());
      historico.setAcao(TipoAcao.CONTA_CRIADA_NFS);
      historico.setTitulo(titulo);
      historico.setDescricao("Conta Criada NFS " + titulo.getNumeroNotaFiscal());
      historico.setAplicacao(titulo.getAplicacao());
      historico.setUsuario(ordemServico.getUsuario());
      historico.setNomeUsuario(ordemServico.getUsuario().getNome());
      tituloHistoricoRepository.save(historico);

      titulos.add(titulo);
      log.debug("OS ID: {} - Título da parcela {} adicionado à lista de títulos", ordemServico.getId(), i+1);
      BadgeContext.setUsuario(ordemServico.getUsuario());
    }

    log.debug("OS ID: {} - Processando {} títulos criados", ordemServico.getId(), titulos.size());
    processarTitulos(titulos);
    log.debug("FIM - Recebimento por NFS inserido com sucesso para OS ID: {}", ordemServico.getId());
  }

  private void excluiRecebimento(OrdemServico ordemServico) {
    log.debug("INÍCIO - Excluindo recebimentos para OS ID: {}", ordemServico.getId());

    List<Titulo> titulos = tituloRepository.findAllByOrdemServicoAndTituloReferenciaIsNull(
      ordemServico
    );
    log.debug("OS ID: {} - Encontrados {} títulos para exclusão", ordemServico.getId(), titulos.size());

    int count = 0;
    for (Titulo titulo : titulos) {
      count++;
      log.debug("OS ID: {} - Excluindo título {}/{}: ID: {}, Valor: {}",
                ordemServico.getId(), count, titulos.size(), titulo.getId(), titulo.getValorTotal());

      log.debug("OS ID: {} - Excluindo recorrências do título ID: {}", ordemServico.getId(), titulo.getId());
      excluirRecorrencias(titulo);

      log.debug("OS ID: {} - Realizando exclusão lógica do título ID: {}", ordemServico.getId(), titulo.getId());
      tituloRepository.softDeleteTituloById(titulo.getId());
      log.debug("OS ID: {} - Título ID: {} excluído com sucesso", ordemServico.getId(), titulo.getId());
    }

    log.debug("FIM - Recebimentos excluídos com sucesso para OS ID: {}", ordemServico.getId());
  }

  private void excluirRecorrencias(Titulo titulo) {
    log.debug("INÍCIO - Excluindo recorrências do título ID: {}", titulo.getId());

    List<Titulo> recorrencias = tituloRepository.findAllByTituloReferenciaIdAndIndTituloExcluidoIsFalse(titulo.getId());
    log.debug("Título ID: {} - Encontradas {} recorrências para exclusão", titulo.getId(), recorrencias.size());

    int count = 0;
    for (Titulo recorrencia : recorrencias) {
      if (recorrencia.getTituloReferencia() != null) {
        count++;
        log.debug("Título ID: {} - Excluindo recorrência {}/{}: ID: {}, Valor: {}",
                  titulo.getId(), count, recorrencias.size(), recorrencia.getId(), recorrencia.getValorTotal());

        tituloRepository.softDeleteTituloById(recorrencia.getId());
        log.debug("Título ID: {} - Recorrência ID: {} excluída com sucesso", titulo.getId(), recorrencia.getId());
      }
    }

    log.debug("FIM - Recorrências do título ID: {} excluídas com sucesso", titulo.getId());
  }

  private void processarTitulos(List<Titulo> titulos) {
    log.debug("INÍCIO - Processando títulos para geração de boletos");

    if (titulos == null || titulos.isEmpty()) {
      log.debug("Nenhum título para processar");
      return;
    }

    log.debug("Processando {} títulos para geração de boletos", titulos.size());

    int count = 0;
    int elegiveisCount = 0;

    for (Titulo titulo : titulos) {
      count++;
      if (TipoTitulo.RECEBER.equals(titulo.getTipo()) && Objects.nonNull(titulo.getFormaPagamento())) {
        elegiveisCount++;
        log.debug("Gerando boleto para título {}/{}: ID: {}, Valor: {}",
                  count, titulos.size(), titulo.getId(), titulo.getValorTotal());

        boletoService.geraHomologacaoBoletos(titulo.getId());
        log.debug("Boleto gerado com sucesso para título ID: {}", titulo.getId());
      } else {
        log.debug("Título {}/{}: ID: {} não elegível para geração de boleto (Tipo: {}, Forma Pagamento: {})",
                  count, titulos.size(), titulo.getId(),
                  titulo.getTipo(),
                  titulo.getFormaPagamento() != null ? titulo.getFormaPagamento().getNome() : "null");
      }
    }

    log.debug("FIM - Processamento de títulos concluído: {}/{} títulos elegíveis para boletos",
              elegiveisCount, titulos.size());
  }

  private boolean deveEnviarEmail(OrdemServico os) {
    log.debug("Verificando se deve enviar email para OS ID: {}", os.getId());

    String envioNfseCliente = get(os.getCliente(), Pessoa::getEmailNotaFiscal, String::trim);
    String envioCobrancaCliente = get(os.getCliente(), Pessoa::getEmailCobranca, String::trim);
    log.debug("OS ID: {} - Emails do cliente: NF: {}, Cobrança: {}",
              os.getId(),
              StringUtils.isNotBlank(envioNfseCliente) ? envioNfseCliente : "<vazio>",
              StringUtils.isNotBlank(envioCobrancaCliente) ? envioCobrancaCliente : "<vazio>");

    String envioNfse = getOrElse(os.getEmailEnvioNfse(), String::trim, envioNfseCliente);
    String envioCobranca = getOrElse(os.getEmailEnvioCobranca(), String::trim, envioCobrancaCliente);
    log.debug("OS ID: {} - Emails para envio: NF: {}, Cobrança: {}",
              os.getId(),
              StringUtils.isNotBlank(envioNfse) ? envioNfse : "<vazio>",
              StringUtils.isNotBlank(envioCobranca) ? envioCobranca : "<vazio>");

    boolean deveEnviar = StringUtils.isNotBlank(envioNfse) || StringUtils.isNotBlank(envioCobranca);
    log.debug("OS ID: {} - Deve enviar email: {}", os.getId(), deveEnviar);

    return deveEnviar;
  }

  private void processarCancelamentoDeBoletos(OrdemServico ordemServico) {
    log.debug("INÍCIO - Processando cancelamento de boletos para OS ID: {}", ordemServico.getId());

    BadgeContext.setUsuario(ordemServico.getUsuario());
    log.debug("OS ID: {} - Usuário definido no contexto: {}",
              ordemServico.getId(),
              ordemServico.getUsuario() != null ? ordemServico.getUsuario().getNome() : "N/A");

    log.debug("OS ID: {} - Buscando títulos com boletos para cancelamento", ordemServico.getId());
    List<Integer> boletosIds = tituloRepository.findAllByOrdemServicoAndTituloReferenciaIsNull(ordemServico)
      .stream()
      .filter(titulo -> titulo.getTituloBoletos() != null && !titulo.getTituloBoletos().isEmpty())
      .map(Titulo::getId)
      .peek(id -> log.debug("OS ID: {} - Título ID: {} possui boletos para cancelamento", ordemServico.getId(), id))
      .toList();

    if (boletosIds.isEmpty()) {
      log.debug("OS ID: {} - Nenhum boleto encontrado para cancelamento", ordemServico.getId());
    } else {
      log.debug("OS ID: {} - Iniciando cancelamento de {} boletos", ordemServico.getId(), boletosIds.size());

      int count = 0;
      for (Integer boletoId : boletosIds) {
        count++;
        log.debug("OS ID: {} - Cancelando boleto {}/{}: Título ID: {}",
                  ordemServico.getId(), count, boletosIds.size(), boletoId);

        boletoService.gerarCancelamento(boletoId);
        log.debug("OS ID: {} - Boleto do título ID: {} cancelado com sucesso", ordemServico.getId(), boletoId);
      }
    }

    log.debug("FIM - Cancelamento de boletos concluído para OS ID: {}", ordemServico.getId());
  }

  private List<BigDecimal> calcularRateioPisRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalPisRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getPisRetido()))
      .map(OrdemServicoItem::getPis)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalPisRetido, qtdRecebimento);
  }

  private List<BigDecimal> calcularRateioIssRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalIssRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getIssRetido()))
      .map(OrdemServicoItem::getIss)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalIssRetido, qtdRecebimento);
  }

  private List<BigDecimal> calcularRateioIrRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalIrRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getIrRetido()))
      .map(OrdemServicoItem::getIr)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalIrRetido, qtdRecebimento);
  }

  private List<BigDecimal> calcularRateioCsllRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalCsllRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getCsllRetido()))
      .map(OrdemServicoItem::getCsll)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalCsllRetido, qtdRecebimento);
  }

  private List<BigDecimal> calcularRateioCofinsRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalCofinsRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getCofinsRetido()))
      .map(OrdemServicoItem::getCofins)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalCofinsRetido, qtdRecebimento);
  }

  private List<BigDecimal> calcularRateioInssRetido(List<OrdemServicoItem> itens, Integer qtdRecebimento){
    BigDecimal totalInssRetido = itens.stream()
      .filter(item -> Boolean.TRUE.equals(item.getInssRetido()))
      .map(OrdemServicoItem::getInss)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return MathUtils.rateia(totalInssRetido, qtdRecebimento);
  }

}
