package br.com.nuvy.api.util;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

public class ConverteMultiPartFileArrayUtil implements MultipartFile {

    private final String name;
    private final String originalFilename;
    private final String contentType;
    private final byte[] content;

    public ConverteMultiPartFileArrayUtil(@NonNull String name, @Nullable String originalFilename,
      @Nullable String contentType, @NonNull byte[] content) {
      this.name = name;
      this.originalFilename = originalFilename;
      this.contentType = contentType;
      this.content = content;
    }

    @Override
    @NonNull
    public String getName() {
      return name;
    }

    @Override
    @Nullable
    public String getOriginalFilename() {
      return originalFilename;
    }

    @Override
    @Nullable
    public String getContentType() {
      return contentType;
    }

    @Override
    public boolean isEmpty() {
      return content.length == 0;
    }

    @Override
    public long getSize() {
      return content.length;
    }

    @Override
    @NonNull
    public byte[] getBytes() throws IOException {
      return content;
    }

    @Override
    @NonNull
    public InputStream getInputStream() throws IOException {
      return new ByteArrayInputStream(content);
    }

  @Override
  public void transferTo(@NonNull File dest) throws IOException, IllegalStateException {
      Objects.requireNonNull(dest, "O arquivo de destino não pode ser nulo");
      try (FileOutputStream fos = new FileOutputStream(dest)) {
        fos.write(content);
      }
    }
  }
