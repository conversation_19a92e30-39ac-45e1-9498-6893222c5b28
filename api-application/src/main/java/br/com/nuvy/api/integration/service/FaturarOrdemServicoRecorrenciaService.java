package br.com.nuvy.api.integration.service;

import br.com.nuvy.api.cadastro.model.EmissaoNotaServico;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.repository.EmissaoNotaServicoRepository;
import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.api.integration.dto.OrdemServicoRecorrenciaDto;
import br.com.nuvy.api.integration.mapper.OrdemServicoRecorrenciaMapper;
import br.com.nuvy.api.notafiscal.dto.NotaFiscalDeServicoDto;
import br.com.nuvy.api.notafiscal.dto.ReciboNfsDto;
import br.com.nuvy.api.servico.repository.OrdemServicoItemRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalItemRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoRepository;
import br.com.nuvy.api.servico.service.OrdemServicoHistoricoService;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoItem;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscalItem;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class FaturarOrdemServicoRecorrenciaService {

  private final OrdemServicoNotaFiscalRepository ordemServicoNotaFiscalRepository;
  private final OrdemServicoRepository ordemServicoRepository;
  private final OrdemServicoItemRepository ordemServicoItemRepository;
  private final OrdemServicoNotaFiscalItemRepository ordemServicoNotaFiscalItemRepository;
  private final OrdemServicoHistoricoService ordemServicoHistoricoService;
  private final EmissaoNotaServicoRepository emissaoNotaServicoRepository;
  private final OrdemServicoRecorrenciaMapper ordemServicoRecorrenciaMapper;
  private final ServicoNotificacao servicoNotificacaoSqs;

  @Transactional
  public void faturarOrdemServicoRecorrencia(OrdemServicoRecorrenciaDto ordemServicoRecorrenciaDto) {
    // Se não tiver origem é uma nota pai que esta sendo faturada
    if (ordemServicoRecorrenciaDto.getIdOrdemServicoOrigem() != null) {
      faturarRecorrenciaFilha(ordemServicoRecorrenciaDto);
    } else {
      faturarRecorrenciaPai(ordemServicoRecorrenciaDto);
    }
  }

  @Transactional
  public void faturarRecorrenciaPai(OrdemServicoRecorrenciaDto ordemServicoRecorrenciaDto) {
    log.info("Ordem de Serviço Recorrencia de Origem. Apenas gerando o faturamento para a OS: {}", ordemServicoRecorrenciaDto.getId());
    OrdemServico ordemServicoRecorrencia = ordemServicoRepository.findById(ordemServicoRecorrenciaDto.getId())
      .orElseThrow(() -> new RuntimeException(
        "Ordem de Serviço Recorrencia não encontrada! Id: " + ordemServicoRecorrenciaDto.getId())
      );

    ordemServicoRecorrencia.setSituacao(SituacaoOrdemServico.FATURADO);
    ordemServicoRepository.saveAndFlush(ordemServicoRecorrencia);

    if (ordemServicoRecorrencia.getTipoComprovante().equals("nota_fiscal")) {
      EmissaoNotaServico configEmissaoNotaServico = emissaoNotaServicoRepository.findByIdEmpresa(
          ordemServicoRecorrencia.getEmpresa().getId())
        .orElseThrow(() -> new RuntimeException(
          "Configuração de emissão de Nota de Serviço não encontrada para a empresa: "
            + ordemServicoRecorrencia.getEmpresa().getId())
        );

      OrdemServicoNotaFiscal ordemServicoOrigemNotaFiscalRecorrencia = mapperAndSaveOrdemServicoOrigemNotaFiscal(
        ordemServicoRecorrencia
      );
      ordemServicoRecorrencia.setIdNotaFiscal(ordemServicoOrigemNotaFiscalRecorrencia.getId());

      List<OrdemServicoItem> ordemServicoItems = ordemServicoItemRepository.findAllByOrdemServico(
        ordemServicoRecorrencia
      );

      if (!ordemServicoItems.isEmpty()) {
        mapperAndSaveOrdemServicoOrigemNotaFiscalItens(
          ordemServicoOrigemNotaFiscalRecorrencia, ordemServicoItems, configEmissaoNotaServico
        );
      }
      mapperAndSendEmissaoNotaFiscalServico(
        ordemServicoOrigemNotaFiscalRecorrencia, ordemServicoRecorrencia
      );
    }
    if (ordemServicoRecorrencia.getTipoComprovante().equals("recibo")) {
      mapperAndSendEmissaoReciboServico(ordemServicoRecorrencia);
    }
    ordemServicoRepository.saveAndFlush(ordemServicoRecorrencia);

    log.debug("Criando histórico para a Ordem de Serviço: {}", ordemServicoRecorrencia.getId());
    ordemServicoHistoricoService.createHistorico(ordemServicoRecorrencia);
  }

  @Transactional
  public void faturarRecorrenciaFilha(OrdemServicoRecorrenciaDto ordemServicoRecorrenciaDto) {
    OrdemServico ordemServicoPai = ordemServicoRepository.findById(ordemServicoRecorrenciaDto.getIdOrdemServicoOrigem())
      .orElseThrow(() -> new RuntimeException(
        "Ordem de Serviço Origem não encontrada! Id: " + ordemServicoRecorrenciaDto.getIdOrdemServicoOrigem())
      );

    OrdemServico ordemServicoRecorrencia = ordemServicoRepository.findById(ordemServicoRecorrenciaDto.getId())
      .orElseThrow(() -> new RuntimeException(
        "Ordem de Serviço Recorrencia não encontrada! Id: " + ordemServicoRecorrenciaDto.getId())
      );

    // Verificar se os emails estão vazios e preenchê-los com informações do cliente se necessário
    verificarEPreencherEmailsDoCliente(ordemServicoRecorrencia);

    ordemServicoRecorrencia.setSituacao(SituacaoOrdemServico.FATURADO);
    ordemServicoRepository.saveAndFlush(ordemServicoRecorrencia);

    if (ordemServicoPai.getTipoComprovante().equals("nota_fiscal")) {
      log.info("Ordem de Serviço Pai possui Nota Fiscal, emitindo Nota!");
      OrdemServicoNotaFiscal ordemServicoNotaFiscalPai = ordemServicoNotaFiscalRepository.findFirstByOrdemServicoId(
        ordemServicoRecorrenciaDto.getIdOrdemServicoOrigem()
      ).orElseThrow(() -> new RuntimeException(
        "Nota Fiscal Pai não encontrada! Id Ordem de Serviço Origem: " + ordemServicoRecorrenciaDto.getIdOrdemServicoOrigem()
      ));

      OrdemServicoNotaFiscal ordemServicoNotaFiscalRecorrencia = mapperAndSaveOrdemServicoNotaFiscal(
        ordemServicoRecorrencia, ordemServicoNotaFiscalPai
      );
      ordemServicoRecorrencia.setIdNotaFiscal(ordemServicoNotaFiscalRecorrencia.getId());

      List<OrdemServicoItem> ordemServicoItems =
        ordemServicoItemRepository.findAllByOrdemServico(ordemServicoRecorrencia);

      List<OrdemServicoNotaFiscalItem> ordemServicoNotaFiscalItems =
        ordemServicoNotaFiscalItemRepository.findAllByOrdemServicoNotaFiscal(ordemServicoNotaFiscalPai);

      if (!ordemServicoNotaFiscalItems.isEmpty()) {
        log.info("Ordem de Serviço Nota Fiscal Itens encontrados: {}", ordemServicoNotaFiscalItems.size());
        mapperAndSaveOrdemServicoNotaFiscalItens(ordemServicoItems, ordemServicoNotaFiscalItems, ordemServicoNotaFiscalRecorrencia);
      }
      mapperAndSendEmissaoNotaFiscalServico(ordemServicoNotaFiscalRecorrencia, ordemServicoRecorrencia);
    }

    if (ordemServicoPai.getTipoComprovante().equals("recibo")) {
      log.info("Ordem de Serviço Pai não possui Nota Fiscal, emitindo Recibo!");
      mapperAndSendEmissaoReciboServico(ordemServicoRecorrencia);
    }

    ordemServicoRepository.save(ordemServicoRecorrencia);

    log.debug("Criando histórico para a Ordem de Serviço: {}", ordemServicoRecorrencia.getId());
    ordemServicoHistoricoService.createHistorico(ordemServicoRecorrencia);
  }

  private void mapperAndSendEmissaoNotaFiscalServico(OrdemServicoNotaFiscal ordemServicoNotaFiscal,
    OrdemServico ordemServicoRecorrencia) {
    NotaFiscalDeServicoDto notaFiscalDeServicoDto = ordemServicoRecorrenciaMapper.mapperNotaDeServicoDto(ordemServicoNotaFiscal,
      ordemServicoRecorrencia);
    servicoNotificacaoSqs.enfileirarQueueEmissaoNotaFiscal(notaFiscalDeServicoDto);
  }

  private void mapperAndSendEmissaoReciboServico(OrdemServico ordemServicoRecorrencia) {
    ReciboNfsDto reciboDeServicoDto = ordemServicoRecorrenciaMapper.mapperReciboDeServicoDto(ordemServicoRecorrencia);
    servicoNotificacaoSqs.enfileirarQueueEmissaoRecibo(reciboDeServicoDto);
  }

  private OrdemServicoNotaFiscal mapperAndSaveOrdemServicoNotaFiscal(
    OrdemServico ordemServicoRecorrencia,
    OrdemServicoNotaFiscal ordemServicoNotaFiscalPai
  ) {
    OrdemServicoNotaFiscal ordemServicoNotaFiscal = ordemServicoRecorrenciaMapper
      .mapperOrdemServicoNotaFiscal(ordemServicoRecorrencia, ordemServicoNotaFiscalPai);
    return ordemServicoNotaFiscalRepository.save(ordemServicoNotaFiscal);
  }

  private OrdemServicoNotaFiscal mapperAndSaveOrdemServicoOrigemNotaFiscal(OrdemServico ordemServicoRecorrencia) {
    OrdemServicoNotaFiscal ordemServicoNotaFiscal = ordemServicoRecorrenciaMapper
      .mapperOrdemServicoOrigemNotaFiscal(ordemServicoRecorrencia);
    return ordemServicoNotaFiscalRepository.save(ordemServicoNotaFiscal);
  }

  private void mapperAndSaveOrdemServicoOrigemNotaFiscalItens(
    OrdemServicoNotaFiscal notaFiscalOrigem, List<OrdemServicoItem> ordemServicoItems, EmissaoNotaServico emissaoNotaServico) {
    List<OrdemServicoNotaFiscalItem> ordemServicoNotaFiscalItems = ordemServicoRecorrenciaMapper
      .mapperOrdemServicoOrigemNotaFiscalItens(ordemServicoItems, emissaoNotaServico, notaFiscalOrigem);
    ordemServicoNotaFiscalItemRepository.saveAll(ordemServicoNotaFiscalItems);
  }

  private void mapperAndSaveOrdemServicoNotaFiscalItens(
    List<OrdemServicoItem> ordemServicoItems,
    List<OrdemServicoNotaFiscalItem> ordemServicoNotaFiscalItems,
    OrdemServicoNotaFiscal ordemServicoNotaFiscal
  ) {
    for (OrdemServicoNotaFiscalItem ordemServicoNotaFiscalItem : ordemServicoNotaFiscalItems) {
      List<OrdemServicoItem> ordemServicoItemsServico = ordemServicoItems.stream()
        .filter(i -> i.getServico().getId()
          .equals(ordemServicoNotaFiscalItem.getIdOrdemServicoItem().getServico().getId()))
        .toList();
      if (ordemServicoItemsServico.size() != 1) {
        throw new RuntimeException(
          "Não foi possível identificar o item da ordem de servico pelo item da nfs. Servico: %d"
            .formatted(ordemServicoNotaFiscalItem.getIdOrdemServicoItem().getServico().getId())
        );
      }
      OrdemServicoNotaFiscalItem ordemServicoNotaFiscalItemMapped = ordemServicoRecorrenciaMapper
        .mapperOrdemServicoNotaFiscalItem(ordemServicoItemsServico.get(0), ordemServicoNotaFiscalItem, ordemServicoNotaFiscal);
      ordemServicoNotaFiscalItemRepository.save(ordemServicoNotaFiscalItemMapped);
    }
  }

  /**
   * Verifica se os campos de email da ordem de serviço estão vazios e, caso estejam,
   * preenche-os com os emails do cliente.
   *
   * @param ordemServico A ordem de serviço a ser verificada e atualizada
   */
  private void verificarEPreencherEmailsDoCliente(OrdemServico ordemServico) {
    if (ordemServico == null || ordemServico.getCliente() == null) {
      log.warn("Ordem de serviço ou cliente não encontrado para verificar emails");
      return;
    }

    Pessoa cliente = ordemServico.getCliente();
    boolean houveMudancas = false;

    // Verificar se o email de cobrança está vazio
    if (ordemServico.getEmailEnvioCobranca() == null || ordemServico.getEmailEnvioCobranca().trim().isEmpty()) {
      log.info("Email de cobrança vazio para a OS: {}", ordemServico.getId());
      // Verificar se existe email de cobrança no cliente
      if (cliente.getEmailCobranca() != null && !cliente.getEmailCobranca().trim().isEmpty()) {
        ordemServico.setEmailEnvioCobranca(cliente.getEmailCobranca());
        log.info("Preenchendo email de cobrança da OS com email de cobrança do cliente: {}", cliente.getEmailCobranca());
        houveMudancas = true;
      } else if (cliente.getEmailNotaFiscal() != null && !cliente.getEmailNotaFiscal().trim().isEmpty()) {
        ordemServico.setEmailEnvioCobranca(cliente.getEmailNotaFiscal());
        log.info("Preenchendo email de cobrança da OS com email de nota fiscal do cliente: {}", cliente.getEmailNotaFiscal());
        houveMudancas = true;
      } else if (cliente.getEmail() != null && !cliente.getEmail().trim().isEmpty()) {
        ordemServico.setEmailEnvioCobranca(cliente.getEmail());
        log.info("Preenchendo email de cobrança da OS com email principal do cliente: {}", cliente.getEmail());
        houveMudancas = true;
      } else {
        log.warn("Nenhum email encontrado no cliente para preencher o email de cobrança da OS: {}", ordemServico.getId());
      }
    }

    // Verificar se o email de NFSe está vazio
    if (ordemServico.getEmailEnvioNfse() == null || ordemServico.getEmailEnvioNfse().trim().isEmpty()) {
      log.info("Email de NFSe vazio para a OS: {}", ordemServico.getId());
      // Verificar se existe email de nota fiscal no cliente
      if (cliente.getEmailNotaFiscal() != null && !cliente.getEmailNotaFiscal().trim().isEmpty()) {
        ordemServico.setEmailEnvioNfse(cliente.getEmailNotaFiscal());
        log.info("Preenchendo email de NFSe da OS com email de nota fiscal do cliente: {}", cliente.getEmailNotaFiscal());
        houveMudancas = true;
      } else if (cliente.getEmail() != null && !cliente.getEmail().trim().isEmpty()) {
        ordemServico.setEmailEnvioNfse(cliente.getEmail());
        log.info("Preenchendo email de NFSe da OS com email principal do cliente: {}", cliente.getEmail());
        houveMudancas = true;
      } else if (cliente.getEmailCobranca() != null && !cliente.getEmailCobranca().trim().isEmpty()) {
        ordemServico.setEmailEnvioNfse(cliente.getEmailCobranca());
        log.info("Preenchendo email de NFSe da OS com email de cobrança do cliente: {}", cliente.getEmailCobranca());
        houveMudancas = true;
      } else {
        log.warn("Nenhum email encontrado no cliente para preencher o email de NFSe da OS: {}", ordemServico.getId());
      }
    }

    if (houveMudancas) {
      log.info("Atualizando ordem de serviço com emails do cliente. ID: {}", ordemServico.getId());
      ordemServicoRepository.save(ordemServico);
    }
  }
}
