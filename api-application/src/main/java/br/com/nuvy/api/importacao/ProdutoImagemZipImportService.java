package br.com.nuvy.api.importacao;

import static br.com.nuvy.config.BadgeContext.getAplicacao;
import static br.com.nuvy.config.BadgeContext.getBadge;
import static br.com.nuvy.config.BadgeContext.getEmpresa;
import static br.com.nuvy.config.BadgeContext.getUsuario;

import br.com.nuvy.api.admin.service.NotificacaoService;
import br.com.nuvy.api.cadastro.dto.DimensoesImagemDto;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.ImportacaoArquivo;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.ProdutoImagem;
import br.com.nuvy.api.cadastro.repository.ProdutoImagemRepository;
import br.com.nuvy.api.cadastro.repository.ProdutoRepository;
import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.cadastro.service.ImportacaoArquivoService;
import br.com.nuvy.api.enums.OrigemNotificacao;
import br.com.nuvy.api.enums.TipoArquivoImportacao;
import br.com.nuvy.api.enums.TipoNotificacao;
import br.com.nuvy.api.notificacao.dto.PayloadPadrao;
import br.com.nuvy.api.notificacao.model.Notificacao;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.service.UsuarioService;
import br.com.nuvy.api.util.ConverteMultiPartFileArrayUtil;
import br.com.nuvy.common.constants.FileTag;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.message.TranslatorCompomnent;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.events.ArquivoXlsxErrorEvent;
import br.com.nuvy.events.SuccessImportEmailEvent;
import br.com.nuvy.notification.Destinatario;
import br.com.nuvy.validation.ArquivoXlsxError;
import br.com.nuvy.validation.ErrorDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProdutoImagemZipImportService {

  private final ProdutoRepository produtoRepository;
  private final FileService fileService;
  private final ProdutoImagemRepository produtoImagemRepository;
  private final Badge badge;
  private final ApplicationEventPublisher eventPublisher;
  private final UsuarioService usuarioService;
  private final ImportacaoArquivoService importacaoArquivoService;
  private final NotificacaoService notificacaoService;
  private final TranslatorCompomnent translator;
  private final TransactionTemplate transactionManager;

  private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
    ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp"
  );

  private int salvos = 0;
  private int atualizados = 0;

  public void importProdutoImagensZip(MultipartFile file) {
    log.info("Iniciando importação de imagens via ZIP: {}", file.getOriginalFilename());

    try {
      String localFile = fileService.write(file, getBadge().getTenantId(),
        getEmpresa().getId().toString(), "planilhas", file.getOriginalFilename());

      ImportacaoArquivo arquivo = ImportacaoArquivo.builder()
        .tipo(TipoArquivoImportacao.IMAGENS)
        .url(localFile)
        .build();

      if (importacaoArquivoService.findArquivosByUrl(arquivo.getUrl()).isEmpty()) {
        importacaoArquivoService.create(arquivo);
      } else {
        importacaoArquivoService.findArquivosByUrl(arquivo.getUrl()).ifPresent(e ->
          importacaoArquivoService.update(e.getId(), arquivo));
      }

      List<ArquivoXlsxError> listErros = processarZipFile(file);

      enviarNotificacoes(listErros);

    } catch (Exception e) {
      log.error("Erro durante importação do ZIP", e);
      throw new PreconditionException("Erro ao processar arquivo ZIP: " + e.getMessage());
    } finally {
      this.salvos = 0;
      this.atualizados = 0;
    }
  }

  private List<ArquivoXlsxError> processarZipFile(MultipartFile zipFile) throws IOException {
    List<ArquivoXlsxError> listErros = new ArrayList<>();
    List<String> produtosProcessados = new ArrayList<>();
    int posicao = 1;

    try (ZipInputStream zipInputStream = new ZipInputStream(zipFile.getInputStream())) {
      ZipEntry entry;

      while ((entry = zipInputStream.getNextEntry()) != null) {
        if (entry.isDirectory()) {
          continue;
        }

        String entryName = entry.getName();
        List<ErrorDetail> errosImagem = new ArrayList<>();

        try {
          if (!isImageFile(entryName)) {
            log.debug("Arquivo ignorado (não é imagem): {}", entryName);
            continue;
          }

          String codigoProduto = extrairCodigoProduto(entryName);
          if (codigoProduto == null || codigoProduto.trim().isEmpty()) {
            errosImagem.add(ErrorDetail.builder()
              .mensagem("Não foi possível extrair código do produto do caminho")
              .nomeCampo("Estrutura do ZIP")
              .valorInformado(entryName)
              .build());
            addErroToList(listErros, posicao, errosImagem);
            posicao++;
            continue;
          }

          Optional<Produto> produtoOpt = produtoRepository.findByCodigo(codigoProduto.trim());
          if (produtoOpt.isEmpty()) {
            errosImagem.add(ErrorDetail.builder()
              .mensagem("Produto não encontrado com o código informado")
              .nomeCampo("Código do Produto")
              .valorInformado(codigoProduto)
              .build());
            addErroToList(listErros, posicao, errosImagem);
            posicao++;
            continue;
          }

          transactionManager.executeWithoutResult(status -> {
            try {
              if (!produtosProcessados.contains(codigoProduto)) {
                deletarImagensAnteriores(produtoOpt.get());
                produtosProcessados.add(codigoProduto);
                log.info("Imagens antigas removidas para produto: {}", codigoProduto);
              }

              byte[] imageBytes = readZipEntryBytes(zipInputStream);
              if (imageBytes.length == 0) {
                throw new RuntimeException("Arquivo de imagem vazio");
              }

              processarImagemIndividual(produtoOpt.get(), imageBytes, entryName, codigoProduto);
              atualizados++;

            } catch (Exception e) {
              throw new RuntimeException("Erro ao processar imagem: " + e.getMessage(), e);
            }
          });

        } catch (Exception e) {
          log.error("Erro ao processar entrada do ZIP: {}", entryName, e);
          errosImagem.add(ErrorDetail.builder()
            .mensagem("Erro ao processar arquivo: " + e.getMessage())
            .nomeCampo("Processamento")
            .valorInformado(entryName)
            .build());
        }

        if (!errosImagem.isEmpty()) {
          addErroToList(listErros, posicao, errosImagem);
        }

        posicao++;
      }
    }

    log.info("Processamento do ZIP concluído - Produtos atualizados: {}, Erros: {}",
      atualizados, listErros.size());

    return listErros;
  }

  private void addErroToList(List<ArquivoXlsxError> listErros, int posicao, List<ErrorDetail> errosImagem) {
    if (!errosImagem.isEmpty()) {
      listErros.add(ArquivoXlsxError.builder()
        .posicao(posicao)
        .detalhes(errosImagem)
        .build());
    }
  }

  private void enviarNotificacoes(List<ArquivoXlsxError> listErros) {
    try {
      Usuario usuario = usuarioService.findById(getUsuario().getId())
        .orElseThrow(() -> new PreconditionException("usuario.nao.encontrado"));

      HashMap<String, String> data = new HashMap<>();
      data.put("nome_usuario", usuario.getNome());
      data.put("qtde_reg_novos", String.valueOf(salvos));
      data.put("qtde_reg_atualizados", String.valueOf(atualizados));
      data.put("tipo_planilha", "Imagens de Produtos (ZIP)");

      var destinatario = Destinatario.builder()
        .email(usuario.getEmail())
        .nome(usuario.getNome())
        .build();

      if (listErros.isEmpty()) {
        eventPublisher.publishEvent(new SuccessImportEmailEvent(this, destinatario, data));
      } else {
        byte[] documento = criarArquivoErros(listErros);
        eventPublisher.publishEvent(new ArquivoXlsxErrorEvent(this, destinatario, documento, data));
      }

      notificarFrontEnd();

    } catch (Exception e) {
      log.error("Erro ao enviar notificações", e);
    }
  }

  private byte[] criarArquivoErros(List<ArquivoXlsxError> listErros) {
    ByteArrayOutputStream br = new ByteArrayOutputStream();
    try {
      Writer writer = new OutputStreamWriter(br, StandardCharsets.ISO_8859_1);
      for (ArquivoXlsxError error : listErros) {
        String posicao = "Posicao: " + error.getPosicao().toString() + "\n";
        writer.write(posicao);
        String linha = "\r\n";
        writer.write(linha);
        for (ErrorDetail detail : error.getDetalhes()) {
          String campo = "Campo: " + detail.getNomeCampo() + "\n";
          writer.write(campo);
          String detalhe = "Detalhe: " + detail.getMensagem() + "\n";
          writer.write(detalhe);
          String valor = "Valor informado: " + detail.getValorInformado() + "\n";
          writer.write(valor);
          writer.write(linha);
        }
      }
      writer.close();
    } catch (IOException e) {
      throw new PreconditionException("erro.ao.criar.arquivo.erro");
    }
    return br.toByteArray();
  }

  private void notificarFrontEnd() {
    Notificacao notificacao = new Notificacao();
    notificacao.setAplicacao(getAplicacao().getId());
    notificacao.setEmpresa(Empresa.of(getEmpresa().getId()));
    notificacao.setUsuario(Usuario.of(getUsuario().getId()));
    notificacao.setTitulo("Arquivo ZIP de Imagens de Produtos importado com sucesso!");
    notificacao.setMensagem("Verifique seu email com os detalhes.");
    notificacao.setTipo(TipoNotificacao.USUARIO);
    notificacao.setDataEnvio(LocalDateTime.now());
    notificacao.setOrigem(OrigemNotificacao.PLANILHA_PRODUTOS);

    var payload = PayloadPadrao.builder()
      .titulo(notificacao.getTitulo())
      .mensagem(notificacao.getMensagem())
      .build();

    notificacaoService.createNotification(notificacao, payload);
  }

  private boolean isImageFile(String fileName) {
    String lowerCaseFileName = fileName.toLowerCase();
    return SUPPORTED_IMAGE_EXTENSIONS.stream()
      .anyMatch(lowerCaseFileName::endsWith);
  }

  private String extrairCodigoProduto(String entryPath) {
    String[] pathParts = entryPath.split("/");

    for (String part : pathParts) {
      if (!part.trim().isEmpty() && !part.contains(".")) {
        return part.trim();
      }
    }

    return null;
  }

  private byte[] readZipEntryBytes(ZipInputStream zipInputStream) throws IOException {
    try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
      byte[] data = new byte[1024];
      int nRead;

      while ((nRead = zipInputStream.read(data, 0, data.length)) != -1) {
        buffer.write(data, 0, nRead);
      }

      return buffer.toByteArray();
    }
  }

  private void processarImagemIndividual(Produto produto, byte[] imageBytes, String fileName, String codigoProduto) throws IOException {
    log.debug("Processando imagem: {} para produto: {}", fileName, codigoProduto);

    BufferedImage originalImage;
    try (ByteArrayInputStream is = new ByteArrayInputStream(imageBytes)) {
      originalImage = ImageIO.read(is);
      if (originalImage == null) {
        throw new IOException("Arquivo não é uma imagem válida: " + fileName);
      }
    }

    String originalFileName = fileName.substring(fileName.lastIndexOf("/") + 1);
    MultipartFile processedFile = processarImagemBytes(originalImage, originalFileName);

    DimensoesImagemDto dimensoes = getImageInfo(processedFile);

    String filePath = fileService.writeImage(
      processedFile,
      badge.getTenantId(),
      badge.getEmpresa().getId().toString(),
      FileTag.PRODUTO
    );

    ProdutoImagem produtoImagem = ProdutoImagem.builder()
      .produto(produto)
      .url(filePath)
      .altura(dimensoes.altura())
      .largura(dimensoes.largura())
      .tamanho(dimensoes.tamanho())
      .build();

    produtoImagemRepository.save(produtoImagem);

    log.debug("Imagem salva com sucesso: {} -> {}", fileName, filePath);
  }

  private MultipartFile processarImagemBytes(BufferedImage originalImage, String fileName) throws IOException {
    BufferedImage resizedImage = resizeIfNeeded(originalImage);
    byte[] processedBytes = convertToJpgWithQuality(resizedImage);
    String uniqueFileName = java.util.UUID.randomUUID() + ".jpg";

    return new ConverteMultiPartFileArrayUtil(
      "file", uniqueFileName, "image/jpeg", processedBytes
    );
  }

  private BufferedImage resizeIfNeeded(BufferedImage original) {
    int originalWidth = original.getWidth();
    int originalHeight = original.getHeight();
    int maxDimension = 1200;

    if (originalWidth <= maxDimension && originalHeight <= maxDimension) {
      return original;
    }

    int newWidth, newHeight;
    if (originalWidth > originalHeight) {
      newWidth = maxDimension;
      newHeight = (int) (originalHeight * ((double) maxDimension / originalWidth));
    } else {
      newHeight = maxDimension;
      newWidth = (int) (originalWidth * ((double) maxDimension / originalHeight));
    }

    BufferedImage resized = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
    resized.createGraphics()
      .drawImage(original.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);

    return resized;
  }

  private byte[] convertToJpgWithQuality(BufferedImage image) throws IOException {
    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpg");
      if (!writers.hasNext()) {
        throw new IOException("Nenhum escritor JPEG disponível");
      }

      ImageWriter writer = writers.next();
      ImageWriteParam param = writer.getDefaultWriteParam();
      param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
      param.setCompressionQuality(0.95f);

      try (ImageOutputStream ios = ImageIO.createImageOutputStream(outputStream)) {
        writer.setOutput(ios);
        writer.write(null, new IIOImage(image, null, null), param);
      } finally {
        writer.dispose();
      }

      return outputStream.toByteArray();
    }
  }

  public DimensoesImagemDto getImageInfo(MultipartFile file) throws IOException {
    BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
    if (bufferedImage == null) {
      throw new IOException("Imagem não suportada");
    }

    byte[] tamanho = file.getBytes();
    int largura = bufferedImage.getWidth();
    int altura = bufferedImage.getHeight();

    return new DimensoesImagemDto(largura, altura, tamanho.length);
  }

  private void deletarImagensAnteriores(Produto produto) {
    List<ProdutoImagem> imagensExistentes = produtoImagemRepository.findAllByProdutoId(produto.getId());

    for (ProdutoImagem imagem : imagensExistentes) {
      try {
        fileService.delete(imagem.getUrl());
        log.debug("Imagem antiga deletada do S3: {}", imagem.getUrl());
      } catch (Exception e) {
        log.error("Erro ao deletar imagem antiga do S3: {}", imagem.getUrl(), e);
      }

      produtoImagemRepository.delete(imagem);
    }

    log.debug("Deletadas {} imagens antigas do produto código: {}", imagensExistentes.size(), produto.getCodigo());
  }
}