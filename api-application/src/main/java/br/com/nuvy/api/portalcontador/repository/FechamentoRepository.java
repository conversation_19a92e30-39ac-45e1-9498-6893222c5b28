package br.com.nuvy.api.portalcontador.repository;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.TipoFechamento;
import br.com.nuvy.api.portalcontador.model.Fechamento;
import br.com.nuvy.common.base.repository.NuvyRepository;
import feign.Param;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface FechamentoRepository extends NuvyRepository<Fechamento, UUID> {

  Optional<Fechamento> findFirstByEmpresaAndPeriodoMesAnoAndTipoFechamento(
    Empresa empresa, YearMonth periodoMesAno, TipoFechamento tipoFechamento
  );

  @Query(value = """
    SELECT
       STRING_AGG(
             CASE WHEN pd.situacao = 'NF_EMITIDA' THEN pd.xml_nota_fiscal END, ';') AS xmlNotasEmitidas,
       STRING_AGG(
             CASE WHEN pd.situacao = 'NF_CANCELADA' THEN pd.xml_nota_fiscal END, ';') AS xmlNotasCanceladas,
       STRING_AGG(
             CASE WHEN pd.situacao = 'NF_INUTILIZADA' THEN pd.xml_nota_fiscal END, ';') AS xmlNotasInutilizadas
    FROM
       vd_pedido pd
    WHERE
           pd.situacao IN ('NF_EMITIDA', 'NF_CANCELADA', 'NF_INUTILIZADA')
     AND DATE_TRUNC('MONTH', pd.dt_nfe_emitida) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
     AND DATE_TRUNC('YEAR', pd.dt_nfe_emitida) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
     AND pd.id_aplicacao = :idAplicacao
     AND pd.id_empresa = :idEmpresa
     AND pd.tp_pedido = 'NORMAL'
     AND pd.tp_ambiente = 'PRODUCAO'
    GROUP BY
       pd.id_empresa,
       pd.id_aplicacao,
       date_trunc('YEAR', pd.dt_nfe_emitida),
       date_trunc('MONTH', pd.dt_nfe_emitida)
      LIMIT 1
    """, nativeQuery = true)
  List<Object[]> findNotasVendaFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param
  LocalDate dataFechamento);

  @Query(value = """
    SELECT
        STRING_AGG(
                CASE WHEN nf.caminho_xml is not null and nf.caminho_xml != '' THEN nf.caminho_xml END, ';') AS xml_notas
       FROM
        eq_nf_entrada nf
    WHERE
       DATE_TRUNC('MONTH', nf.dt_nf) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
      AND DATE_TRUNC('YEAR', nf.dt_nf) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
      AND nf.id_aplicacao = :idAplicacao
      AND nf.id_empresa = :idEmpresa
      AND nf.ambiente = 'PRODUCAO'
    GROUP BY
        nf.id_empresa,
        nf.id_aplicacao,
        date_trunc('YEAR', nf.dt_nf),
        date_trunc('MONTH', nf.dt_nf)
    LIMIT 1
    """, nativeQuery = true)
  List<Object[]> findNotasCompraFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param
  LocalDate dataFechamento);

  @Query(value = """
    SELECT
        STRING_AGG(
            CASE WHEN osnf.caminho_xml is not null and osnf.caminho_xml != '' THEN osnf.caminho_xml END, ';') AS xml_notas,
        STRING_AGG(
            CAST(CASE WHEN os.id is not null THEN os.id END AS VARCHAR), ';') AS os_ids,
        STRING_AGG(
            CASE WHEN osnf.caminho_pdf is not null and osnf.caminho_pdf != '' THEN osnf.caminho_pdf END, ';') AS pdf_notas
       FROM
        "erp-nfs".ordem_servico_nota_fiscal osnf
       INNER JOIN "erp-nfs".ordem_servico os ON osnf.id_ordem_servico = os.id
    WHERE
      osnf.situacao = 'NF_EMITIDA'
      AND DATE_TRUNC('MONTH', os.data_criacao) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
      AND DATE_TRUNC('YEAR', os.data_criacao) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
      AND os.tipo_comprovante = 'nota_fiscal'
      AND os.id_aplicacao = :idAplicacao
      AND os.id_empresa = :idEmpresa
    GROUP BY
        os.id_empresa,
        os.id_aplicacao,
        date_trunc('YEAR', os.data_criacao),
        date_trunc('MONTH', os.data_criacao)
    LIMIT 1
    """, nativeQuery = true)
  List<Object[]> findNotasServicoFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param
  LocalDate dataFechamento);

  @Query(value = """
        SELECT *
          FROM "erp-parceiros".pc_fechamento f
          WHERE EXTRACT(MONTH from f.periodo_mes_ano) = :mes
            AND EXTRACT(YEAR from f.periodo_mes_ano) = :ano
            AND f.id_empresa = :empresaId
    """, nativeQuery = true)
  Optional<Fechamento> findFechamentoByPeriodoMesAnoAndEmpresaId(Integer mes, Integer ano,
    Integer empresaId);

  @Query(value = """
    SELECT *
    FROM "erp-nfs".ordem_servico os
    WHERE os.situacao = 'FATURADO'
      AND DATE_TRUNC('MONTH', os.data_criacao) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
    AND DATE_TRUNC('YEAR', os.data_criacao) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
    AND os.tipo_comprovante = 'recibo'
    AND os.id_aplicacao = :idAplicacao
    AND os.id_empresa = :idEmpresa
    """, nativeQuery = true)
  List<Object[]> findRecibosFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param
  LocalDate dataFechamento);

  @Query(value = """
    SELECT
      id_pedido
    FROM
       vd_pedido pd
    WHERE
           pd.situacao IN ('PEDIDO_AUTORIZADO')
     AND DATE_TRUNC('MONTH', pd.dt_faturamento) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
     AND DATE_TRUNC('YEAR', pd.dt_faturamento) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
     AND pd.id_aplicacao = :idAplicacao
     AND pd.id_empresa = :idEmpresa
     AND pd.tp_pedido = 'NORMAL'
    """, nativeQuery = true)
  List<UUID> findRecibosVendaFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param
  LocalDate dataFechamento);

  @Query( value = """
    SELECT
           CAST(cpp.descricao as varchar) as descricao,
           CAST(ppv.url_nfce as varchar) as urlXml
      FROM erp.cd_pos_pdv_venda ppv
      LEFT JOIN erp.cd_pos_pdv cpp ON ppv.id_pospdv = cpp.id
     WHERE ppv.situacao = 'ATIVO'
       AND ppv.url_nfce IS NOT NULL
       AND DATE_TRUNC('MONTH', ppv.data_venda) = DATE_TRUNC('MONTH', CAST(:dataFechamento as DATE))
       AND DATE_TRUNC('YEAR', ppv.data_venda) = DATE_TRUNC('YEAR', CAST(:dataFechamento as DATE))
       AND ppv.id_aplicacao = :idAplicacao
       AND ppv.id_empresa = :idEmpresa
  GROUP BY
           ppv.id_aplicacao,
           ppv.id_empresa,
           cpp.id,
           ppv.id,
           date_trunc('YEAR', ppv.data_venda),
           date_trunc('MONTH', ppv.data_venda);
  """, nativeQuery = true)
  List<Object[]> findVendasPosPdvFechamento(@Param String idAplicacao, @Param Integer idEmpresa, @Param LocalDate dataFechamento);

  @Modifying
  @Query(value = """
    UPDATE "erp-parceiros".pc_fechamento
    SET status_fechamento = 'PROCESSADO'
    WHERE status_fechamento = 'EM_PROCESSAMENTO'
      AND updated_at < (CURRENT_TIMESTAMP - INTERVAL '2 hours')
  """, nativeQuery = true
  )
  void liberaFechamentoTravados();
}
