package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.outrasintegracoes.ConfiguracaoOutrasIntegracoes;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface OutrasIntegracoesEmpresaRepository extends
  NuvyRepository<ConfiguracaoOutrasIntegracoes, Long> {

  @Query("""
     SELECT coi FROM ConfiguracaoOutrasIntegracoes coi
    INNER JOIN FETCH coi.contaBancaria
    INNER JOIN FETCH coi.integracao
               WHERE coi.empresa.id = :empresaId
                 AND coi.aplicacao = :aplicacao
                 AND coi.integracao.tipo = 'TNI'
    """)
  Optional<ConfiguracaoOutrasIntegracoes> findConfiguracaoIntegracaoEmpresa(Integer empresaId, String aplicacao);

}
