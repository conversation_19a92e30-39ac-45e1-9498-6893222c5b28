package br.com.nuvy.api.cadastro.importacaonfe.models;

import java.util.Objects;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class eICMS {

  private eICMS ICMS00;
  private eICMS ICMS10;
  private eICMS ICMS20;
  private eICMS ICMS30;
  private eICMS ICMS40;
  private eICMS ICMS51;
  private eICMS ICMS60;
  private eICMS ICMS70;
  private eICMS ICMS90;
  private eICMS IcmsSt;

  private eICMS ICMSSN101;
  private eICMS ICMSSN102;
  private eICMS ICMSSN201;
  private eICMS ICMSSN202;
  private eICMS ICMSSN500;
  private eICMS ICMSSN900;

  private String orig;
  private String CST;
  private String modBC;
  private String pRedBC;
  private String vBC;
  private String pICMS;

  private String vICMS;

  private String motDesICMS;
  private String vICMSDeson;
  private String CSOSN;

  private String modBCST;
  private String pRedBCST;
  private String pMVAST;
  private String vBCST;
  private String pICMSST;
  private String vICMSST;

  private String pDif;
  private String vICMSDif;

  private String vBCFCP;

  private String pFCP;

  private String vFCP;

  private String vBCFCPST;
  private String pFCPST;
  private String vFCPST;
  private String pCredSN;
  private String vCredICMSSN;

  public eICMS getIcms() {
    IcmsSt = getICMS00();
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS10())) {
      IcmsSt = getICMS10();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS20())) {
      IcmsSt = getICMS20();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS30())) {
      IcmsSt = getICMS30();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS40())) {
      IcmsSt = getICMS40();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS51())) {
      IcmsSt = getICMS51();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS60())) {
      IcmsSt = getICMS60();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS70())) {
      IcmsSt = getICMS70();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMS90())) {
      IcmsSt = getICMS90();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN101())) {
      IcmsSt = getICMSSN101();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN102())) {
      IcmsSt = getICMSSN102();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN201())) {
      IcmsSt = getICMSSN201();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN202())) {
      IcmsSt = getICMSSN202();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN500())) {
      IcmsSt = getICMSSN500();
    }
    if (Objects.isNull(IcmsSt) && Objects.nonNull(getICMSSN900())) {
      IcmsSt = getICMSSN900();
    }
    if (Objects.isNull(IcmsSt)) {
      return new eICMS();
    }
    return IcmsSt;
  }

  public String getVICMS() {
    if (Objects.isNull(vICMS) || vICMS.isEmpty()) {
      return "0.00";
    }
    return vICMS;
  }

  public String getVBCFCP() {
    if (Objects.isNull(vBCFCP) || vBCFCP.isEmpty() || vBCFCP.equals("0.00")) {
      return null;
    }
    return vBCFCP;
  }

  public String getVFCP() {
    if (Objects.isNull(vFCP) || vFCP.isEmpty() || vFCP.equals("0.00")) {
      return null;
    }
    return vFCP;
  }

  public String getPFCP() {
    if (Objects.isNull(pFCP) || pFCP.isEmpty() || pFCP.equals("0.00")) {
      return null;
    }
    return pFCP;
  }

  public String getVBCFCPST() {
    if (Objects.isNull(vBCFCPST) || vBCFCPST.isEmpty() || vBCFCPST.equals("0.00")) {
      return null;
    }
    return vBCFCPST;
  }

  public String getVFCPST() {
    if (Objects.isNull(vFCPST) || vFCPST.isEmpty() || vFCPST.equals("0.00")) {
      return null;
    }
    return vFCPST;
  }

  public String getPFCPST() {
    if (Objects.isNull(pFCPST) || pFCPST.isEmpty() || pFCPST.equals("0.00")) {
      return null;
    }
    return pFCPST;
  }

}
