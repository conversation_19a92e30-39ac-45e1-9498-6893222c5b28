package br.com.nuvy.api.venda.repository.specification;

import br.com.nuvy.api.venda.filter.TabelaPrecoFilter;
import br.com.nuvy.api.venda.model.TabelaPreco;
import br.com.nuvy.api.venda.model.TabelaPreco_;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
public class TabelaPrecoSpecification implements Specification<TabelaPreco> {

  private final transient TabelaPrecoFilter filter;


  @Override
  public Predicate toPredicate(Root<TabelaPreco> root, CriteriaQuery<?> query,
    CriteriaBuilder cb) {
    query.distinct(true);
    List<Predicate> predicates = new ArrayList<>();

    if (filter.getSituacao() != null) {
      predicates.add(cb.equal(root.get(TabelaPreco_.situacao), filter.getSituacao()));
    }

    if (filter.getCriterioSituacao() != null) {
      predicates.add(cb.like(cb.upper(root.get(TabelaPreco_.situacao).as(String.class)),
        "%" + filter.getCriterioSituacao().toUpperCase() + "%"));
    }

    if (filter.getNomeTabela() != null) {
      predicates.add(cb.like(cb.upper(root.get(TabelaPreco_.nome).as(String.class)),
        "%" + filter.getNomeTabela().toUpperCase() + "%"));
    }

    if (filter.getBaseCalculo() != null) {
      predicates.add(cb.like(cb.upper(root.get(TabelaPreco_.baseCalculo).as(String.class)),
        "%" + filter.getBaseCalculo().toUpperCase() + "%"));
    }

    if (filter.getDescontoAcrescimo() != null) {
      BigDecimal percentualPreco = filter.getDescontoAcrescimo()
        .divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP);
      predicates.add(cb.equal(root.get(TabelaPreco_.percentualPreco), percentualPreco));
    }

    if (filter.getValidade() != null) {
      predicates.add(cb.equal(root.get(TabelaPreco_.dataFim), filter.getValidade()));
    }

    if (filter.getIsValido() != null && filter.getIsValido()) {
      LocalDate now = LocalDate.now();
      Predicate isValidPredicate = cb.or(
        cb.and(
          cb.lessThanOrEqualTo(root.get(TabelaPreco_.dataInicio), now),
          cb.or(
            cb.greaterThanOrEqualTo(root.get(TabelaPreco_.dataFim), now),
            cb.isNull(root.get(TabelaPreco_.dataFim))
          )
        ),
        cb.and(
          cb.isNull(root.get(TabelaPreco_.dataInicio)),
          cb.isNull(root.get(TabelaPreco_.dataFim))
        )
      );
      predicates.add(isValidPredicate);
    }

    return cb.and(predicates.toArray(new Predicate[0]));
  }
}
