package br.com.nuvy.api.sqs.servico;

import static br.com.nuvy.api.enums.PosPdvIntegradores.SYSFAR;

import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.integration.dto.PosPdvTokenDto;
import br.com.nuvy.api.integration.factory.PosPdvIntegradorFactory;
import br.com.nuvy.api.integration.interfaces.PosPdvIntegrador;
import br.com.nuvy.api.seguranca.service.UsuarioAplicacaoService;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(
    name = {"aws.sqs.enabled", "aws.sqs.queue.nuvy-pdv-empresa-ativada.enabled"},
    havingValue = "true"
)
@RequiredArgsConstructor
public class PdvEmpresaAtivadaListener extends BaseSqsConsumer {

  private final ObjectMapper objectMapper;
  private final EmpresaService empresaService;
  private final UsuarioAplicacaoService usuarioAplicacaoService;
  private final PosPdvIntegradorFactory posPdvIntegradorFactory;

  private void setTenantContext(Integer empresaId) {
    BaseSqsConsumer.setTenantContext(empresaService, usuarioAplicacaoService, empresaId);
  }

  @JmsListener(destination = "${aws.sqs.queue.nuvy-pdv-empresa-ativada.name}")
  public void pdvEmpresaAtivada(Message message) {
    try {
      String payload = ((SQSTextMessage) message).getText();
      log.debug("Payload recebido: {}", payload);
      PosPdvTokenDto empresaSqsDto = objectMapper.readValue(payload, PosPdvTokenDto.class);
      int idEmpresa = Integer.parseInt(empresaSqsDto.getIdEmpresa());
      setTenantContext(idEmpresa);

      var integradorService = posPdvIntegradorFactory.getPosPdvIntegrador(SYSFAR);
      integradorService.salvaToken(empresaSqsDto);
    } catch (Exception ex) {
      throw new RuntimeException(ex.getMessage(), ex);
    }
  }
}