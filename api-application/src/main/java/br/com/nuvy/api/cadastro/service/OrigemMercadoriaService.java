package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.dto.OrigemMercadoriaDto;
import br.com.nuvy.api.cadastro.filter.OrigemMercadoriaFilter;
import br.com.nuvy.api.cadastro.filter.ProdutoFilter;
import br.com.nuvy.api.cadastro.model.OrigemMercadoria;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.repository.OrigemMercadoriaRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OrigemMercadoriaService extends
  PageableServiceAdapterDto<OrigemMercadoria, OrigemMercadoriaDto, Integer, NoFilter, OrigemMercadoriaRepository> {

  private final ProdutoService produtoService;

  @Override
  public Page<?> findDto(NoFilter filter, Pageable pageable) {
    throw new UnsupportedOperationException();
  }

  public List<OrigemMercadoria> findAll(OrigemMercadoriaFilter filter) {
    if (Objects.nonNull(filter.getNcmId())) {
      List<Produto> produtos = produtoService.find(
        ProdutoFilter.builder().ncmId(filter.getNcmId()).build());
      List<OrigemMercadoria> origens = new ArrayList<>();
      for (Produto produto : produtos) {
        if (Objects.nonNull(produto.getOrigemMercadoria())) {
          origens.add(produto.getOrigemMercadoria());
        }
      }
      return origens.stream().sorted(Comparator.comparing(OrigemMercadoria::getCodigo))
        .toList();
    } else {
      return repository.findAll().stream().sorted(Comparator.comparing(OrigemMercadoria::getCodigo))
        .toList();
    }
  }

  public Optional<OrigemMercadoriaDto> findByCodigo(String codigo) {
    return repository.findByCodigo(codigo).map(OrigemMercadoriaDto::from);
  }
}
