package br.com.nuvy.api.venda.service;

import br.com.nuvy.api.anuncio.CategoriaAnuncio;
import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.api.venda.repository.CategoriaAnuncioRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.common.exception.PreconditionException;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CategoriaAnuncioService extends
  PageableServiceAdapter<CategoriaAnuncio, Integer, NoFilter, CategoriaAnuncioRepository> {

  @Override
  protected CategoriaAnuncio beforeCreate(CategoriaAnuncio entity) {
    var categoria = findByCanalVendaAndCategoriaErpId(entity.getCanalVenda(), entity.getCategoriaErp().getId());
    if (categoria.isPresent()) {
      throw new PreconditionException("vinculo.categoria.ja.cadastrado");
    }
    return super.beforeCreate(entity);
  }

  public List<CategoriaAnuncio> findByCanalVenda(Integer id) {
    return repository.findByCanalVendaId(id);
  }

  public Optional<CategoriaAnuncio> findByCategoriaErpId(Integer categoriaErpId) {
    return repository.findByCategoriaErpId(categoriaErpId).stream().findFirst();
  }

  public Optional<CategoriaAnuncio> findByCanalVendaAndCategoriaErpId(CanalVenda canalVenda, Integer categoriaErpId) {
    return repository.findByCanalVendaIdAndCategoriaErpId(canalVenda.getId(), categoriaErpId);
  }
}
