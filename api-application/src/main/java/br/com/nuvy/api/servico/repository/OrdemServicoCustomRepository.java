package br.com.nuvy.api.servico.repository;

import br.com.nuvy.api.cadastro.filter.servico.OrdemServicoApiExternaFilter;
import br.com.nuvy.api.cadastro.model.servico.OrdemServicoApiExterna;
import br.com.nuvy.api.venda.repository.specification.RecorrenciaRelatorioSpecification;
import br.com.nuvy.facade.relatorio.rl5servicos.model.RecorrenciaRelatorioDto;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OrdemServicoCustomRepository {

  List<RecorrenciaRelatorioDto> findAllRecorrencias(
    RecorrenciaRelatorioSpecification specification);

  Page<OrdemServicoApiExterna> buscaApiExterna(
    OrdemServicoApiExternaFilter filter, Pageable pageable
  );

  OrdemServicoApiExterna buscaPorIdApiExterna(
    UUID id
  );

}
