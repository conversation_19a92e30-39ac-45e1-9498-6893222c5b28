package br.com.nuvy.api.adaptadores.entrada.evento.integracao.plug4market;

import br.com.nuvy.api.cadastro.event.categoria.CategoriaAlterada;
import br.com.nuvy.api.cadastro.event.categoria.CategoriaInserida;
import br.com.nuvy.integracao.plug4market.aplicacao.comando.EnfileiraCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.aplicacao.processador.ProcessadorCategoriaPlug4market;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.UUID;

@Component
@RequiredArgsConstructor
class CategoriaEntradaEventoPlug4market {

  private final ProcessadorCategoriaPlug4market processadorCategoria;

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(CategoriaInserida evento) {
    processadorCategoria.processaComando(new EnfileiraCategoriaPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCategoria(), evento.getOcorridoAs()
    ));
  }

  @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
  void processaEvento(CategoriaAlterada evento) {
    processadorCategoria.processaComando(new EnfileiraCategoriaPlug4market(
      UUID.randomUUID(),
      evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
      evento.getIdCategoria(), evento.getOcorridoAs()
    ));
  }
}
