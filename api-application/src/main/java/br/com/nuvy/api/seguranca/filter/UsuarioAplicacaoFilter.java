package br.com.nuvy.api.seguranca.filter;

import br.com.nuvy.common.base.filter.Filter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springdoc.core.annotations.ParameterObject;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ParameterObject
public class UsuarioAplicacaoFilter implements Filter {

  Integer id;
  String nome;
  String email;
  String situacao;
  String perfilNome;
  Integer perfilId;
  Boolean callface;
  Integer ramalCallFace;
  Boolean ativo;

}
