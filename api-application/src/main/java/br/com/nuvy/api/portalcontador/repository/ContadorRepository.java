package br.com.nuvy.api.portalcontador.repository;

import br.com.nuvy.api.portalcontador.model.Contador;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import java.util.UUID;
import org.springframework.stereotype.Repository;

@Repository
public interface ContadorRepository extends NuvyRepository<Contador, UUID> {

  Optional<Contador> findContadorByAplicacaoAndEmpresaId(String aplicacaoId, Integer empresaId);

}
