package br.com.nuvy.api.adaptadores.saida.sqs.integracao.pdvx;

import br.com.nuvy.api.cadastro.filter.SaldoEstoqueProdutosKitFilter.Produto;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.PosPdvVenda;
import br.com.nuvy.integracao.pdvx.dominio.evento.ClientePdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.EstoquePdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.ProdutoPdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.VendaPdvXRecebida;
import br.com.nuvy.integracao.pdvx.dominio.evento.VendedorPdvXRecebido;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.MessageAttributeValue;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
@ConditionalOnProperty(
  name = "aws.sqs.queue.pdvx.enabled",
  havingValue = "true"
)
class PdvXSaidaSqs {

  private static final Logger log = LoggerFactory.getLogger(PdvXSaidaSqs.class);

  private final ObjectMapper objectMapper;
  private final AmazonSQS amazonSqs;

  @Value("${aws.sqs.queue.pdvx.url}")
  private String sqsQueueUrl;

  PdvXSaidaSqs(ObjectMapper objectMapper, AmazonSQS amazonSqs) {
    this.objectMapper = objectMapper;
    this.amazonSqs = amazonSqs;
    log.info("{} habilitado", PdvXSaidaSqs.class.getSimpleName());
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(ProdutoPdvXRecebido evento) throws JsonProcessingException {
    String message = objectMapper.writeValueAsString(evento);
    Map<String, MessageAttributeValue> attributes = Map.of(
      "tipo", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getClass().getSimpleName()),
      "id", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getId().toString()),
      "idAplicacao", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdAplicacao().toString()),
      "idEmpresa", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdEmpresa().toString()),
      "idUsuario", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdUsuario().toString()),
      "entidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(Produto.class.getSimpleName()),
      "idEntidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdProduto().toString()),
      "dataHora", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getOcorridoAs().toString())
    );

    log.debug(
      "Enviando evento {} para a fila pdvx: {}",
      evento.getClass().getSimpleName(), message
    );

    SendMessageRequest request = new SendMessageRequest()
      .withQueueUrl(sqsQueueUrl)
      .withMessageBody(message)
      .withMessageAttributes(attributes);

    SendMessageResult resultado = amazonSqs.sendMessage(request);
    String messageId = resultado.getMessageId();

    log.debug("id da mensagem enviada para a fila: {}", messageId);
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(ClientePdvXRecebido evento) throws JsonProcessingException {
    String message = objectMapper.writeValueAsString(evento);
    Map<String, MessageAttributeValue> attributes = Map.of(
      "tipo", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getClass().getSimpleName()),
      "id", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getId().toString()),
      "idAplicacao", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdAplicacao().toString()),
      "idEmpresa", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdEmpresa().toString()),
      "idUsuario", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdUsuario().toString()),
      "entidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(Pessoa.class.getSimpleName()),
      "idEntidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdCliente().toString()),
      "dataHora", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getOcorridoAs().toString())
    );

    log.debug(
      "Enviando evento {} para a fila pdvx: {}",
      evento.getClass().getSimpleName(), message
    );

    SendMessageRequest request = new SendMessageRequest()
      .withQueueUrl(sqsQueueUrl)
      .withMessageBody(message)
      .withMessageAttributes(attributes);

    SendMessageResult resultado = amazonSqs.sendMessage(request);
    String messageId = resultado.getMessageId();

    log.debug("id da mensagem enviada para a fila: {}", messageId);
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(VendedorPdvXRecebido evento) throws JsonProcessingException {
    String message = objectMapper.writeValueAsString(evento);
    Map<String, MessageAttributeValue> attributes = Map.of(
      "tipo", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getClass().getSimpleName()),
      "id", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getId().toString()),
      "idAplicacao", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdAplicacao().toString()),
      "idEmpresa", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdEmpresa().toString()),
      "idUsuario", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdUsuario().toString()),
      "entidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(Pessoa.class.getSimpleName()),
      "idEntidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdVendedor().toString()),
      "dataHora", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getOcorridoAs().toString())
    );

    log.debug(
      "Enviando evento {} para a fila pdvx: {}",
      evento.getClass().getSimpleName(), message
    );

    SendMessageRequest request = new SendMessageRequest()
      .withQueueUrl(sqsQueueUrl)
      .withMessageBody(message)
      .withMessageAttributes(attributes);

    SendMessageResult resultado = amazonSqs.sendMessage(request);
    String messageId = resultado.getMessageId();

    log.debug("id da mensagem enviada para a fila: {}", messageId);
  }

  @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
  void processaEvento(EstoquePdvXRecebido evento) throws JsonProcessingException {
    String message = objectMapper.writeValueAsString(evento);
    Map<String, MessageAttributeValue> attributes = Map.of(
      "tipo", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getClass().getSimpleName()),
      "id", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getId().toString()),
      "idAplicacao", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdAplicacao().toString()),
      "idEmpresa", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdEmpresa().toString()),
      "idUsuario", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdUsuario().toString()),
      "entidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(Produto.class.getSimpleName()),
      "idEntidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdProduto().toString()),
      "dataHora", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getOcorridoAs().toString())
    );

    log.debug(
      "Enviando evento {} para a fila pdvx: {}",
      evento.getClass().getSimpleName(), message
    );

    SendMessageRequest request = new SendMessageRequest()
      .withQueueUrl(sqsQueueUrl)
      .withMessageBody(message)
      .withMessageAttributes(attributes);

    SendMessageResult resultado = amazonSqs.sendMessage(request);
    String messageId = resultado.getMessageId();

    log.debug("id da mensagem enviada para a fila: {}", messageId);
  }

  @EventListener
  void processaEvento(VendaPdvXRecebida evento) throws JsonProcessingException {
    String message = objectMapper.writeValueAsString(evento);
    Map<String, MessageAttributeValue> attributes = Map.of(
      "tipo", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getClass().getSimpleName()),
      "id", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getId().toString()),
      "idAplicacao", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdAplicacao().toString()),
      "idEmpresa", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdEmpresa().toString()),
      "idUsuario", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdUsuario().toString()),
      "entidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(PosPdvVenda.class.getSimpleName()),
      "idEntidade", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getIdVenda()),
      "dataHora", new MessageAttributeValue()
        .withDataType("String")
        .withStringValue(evento.getOcorridoAs().toString())
    );

    log.debug(
      "Enviando evento {} para a fila pdvx: {}",
      evento.getClass().getSimpleName(), message
    );

    SendMessageRequest request = new SendMessageRequest()
      .withQueueUrl(sqsQueueUrl)
      .withMessageBody(message)
      .withMessageAttributes(attributes);

    SendMessageResult resultado = amazonSqs.sendMessage(request);
    String messageId = resultado.getMessageId();

    log.debug("id da mensagem enviada para a fila: {}", messageId);
  }
}
