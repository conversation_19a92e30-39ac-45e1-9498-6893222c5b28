package br.com.nuvy.api.financeiro.repository.implementation;

import static br.com.nuvy.common.query.CriteriaUtils.stringAgg;
import static br.com.nuvy.config.BadgeContext.getAplicacao;
import static br.com.nuvy.config.BadgeContext.getUsuario;
import static java.math.BigDecimal.ZERO;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.enums.SituacaoBoleto;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.SituacaoTituloBaixa;
import br.com.nuvy.api.enums.TipoComprovante;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.financeiro.dto.TituloBuscaDto;
import br.com.nuvy.api.financeiro.filter.TituloFilter;
import br.com.nuvy.api.financeiro.filter.emum.KanbanTitulos;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.financeiro.model.Banco_;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.ContaBancaria_;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.FormaPagamento_;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.api.financeiro.model.PlanoConta_;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBaixa;
import br.com.nuvy.api.financeiro.model.TituloBaixa_;
import br.com.nuvy.api.financeiro.model.TituloBoleto;
import br.com.nuvy.api.financeiro.model.TituloBoleto_;
import br.com.nuvy.api.financeiro.model.TituloDespesa;
import br.com.nuvy.api.financeiro.model.TituloDespesa_;
import br.com.nuvy.api.financeiro.model.Titulo_;
import br.com.nuvy.api.financeiro.model.titulo.ViewTituloBaixa;
import br.com.nuvy.api.financeiro.model.titulo.ViewTituloBaixa_;
import br.com.nuvy.api.financeiro.repository.TituloRepositoryCustom;
import br.com.nuvy.api.financeiro.repository.specification.TituloSpecification;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao_;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServico_;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.Pedido_;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.common.utils.CollectionUtils;
import br.com.nuvy.common.utils.NumberUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.From;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.ListJoin;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Selection;
import jakarta.persistence.criteria.Subquery;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class TituloRepositoryCustomImpl implements TituloRepositoryCustom {

  private final EntityManager entityManager;

  @Override
  public Page<TituloBuscaDto> findTitulosBuscaKanban(
    TituloSpecification specification, Pageable pageable
  ) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();

    Long count = getCountKaban(specification, cb);
    if (count == 0) {
      return Page.empty();
    }

    CriteriaQuery<TituloBuscaDto> query = cb.createQuery(TituloBuscaDto.class);

    Root<ViewTituloBaixa> root = query.from(ViewTituloBaixa.class);
    Join<ViewTituloBaixa, Titulo> tituloJoin = root.join(ViewTituloBaixa_.titulo, JoinType.INNER);
    Join<ViewTituloBaixa, TituloBaixa> baixasJoin = root.join(ViewTituloBaixa_.tituloBaixa, JoinType.LEFT);
    Join<Titulo, ContaBancaria> contaBancariaJoin = tituloJoin.join(Titulo_.contaBancaria, JoinType.LEFT);
    Join<ContaBancaria, Banco> bancoJoin = contaBancariaJoin.join(ContaBancaria_.banco, JoinType.LEFT);
    Join<Titulo, Pessoa> fornecedorJoin = tituloJoin.join(Titulo_.fornecedor, JoinType.LEFT);
    Join<Titulo, Empresa> empresaJoin = tituloJoin.join(Titulo_.empresa, JoinType.LEFT);
    Join<Titulo, FormaPagamento> formaPagamentoJoin = tituloJoin.join(Titulo_.formaPagamento, JoinType.LEFT);

    Expression<BigDecimal> zero = cb.literal(ZERO);
    Expression<Integer> idBaixa = cb.coalesce(baixasJoin.get(TituloBaixa_.id), cb.literal(0));
    Expression<BigDecimal> valorBaixa = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryValorBaixa(query, tituloJoin, cb), zero))
      .otherwise(cb.coalesce(baixasJoin.get(TituloBaixa_.valorBaixa), zero));
    Expression<BigDecimal> acrescimos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryJurosMultas(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorJuros), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorMulta), zero)));
    Expression<BigDecimal> descontos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryDescontoAbatimentos(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorDesconto), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorAbatimento), zero)));
    Expression<LocalDate> dataRecebimento = baixasJoin.get(TituloBaixa_.dataBaixa);
    Expression<LocalDate> dataConciliacao = baixasJoin.get(TituloBaixa_.dataConciliacao)
      .as(LocalDate.class);
    Expression<BigDecimal> valorPago = cb.diff(cb.sum(valorBaixa, acrescimos), descontos);
    Expression<BigDecimal> valorRestante = cb.diff(
      cb.diff(cb.sum(tituloJoin.get(Titulo_.valorLiquido), acrescimos), descontos),
      valorPago);
    Expression<BigDecimal> valorTela = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin), valorRestante)
      .otherwise(valorPago);
    Expression<SituacaoTitulo> situacao = cb.<SituacaoTitulo>selectCase()
      .when(cb.and(
        cb.equal(baixasJoin.get(TituloBaixa_.situacao), SituacaoTituloBaixa.NAO_CONCILIADO),
        cb.equal(tituloJoin.get(Titulo_.tipo), TipoTitulo.RECEBER)
      ), SituacaoTitulo.RECEBIDO)
      .when(cb.and(
        cb.equal(baixasJoin.get(TituloBaixa_.situacao), SituacaoTituloBaixa.NAO_CONCILIADO),
        cb.equal(tituloJoin.get(Titulo_.tipo), TipoTitulo.PAGAR)
      ), SituacaoTitulo.PAGO)
      .when(cb.equal(baixasJoin.get(TituloBaixa_.situacao), SituacaoTituloBaixa.CONCILIADO),
        SituacaoTitulo.CONCILIADO)
      .otherwise(tituloJoin.get(Titulo_.situacao));

    query.select(cb.construct(
      TituloBuscaDto.class,
      tituloJoin.get(Titulo_.id).alias("id"),
      idBaixa.alias("idBaixa"),
      situacao.alias("situacao"),
      bancoJoin.get(Banco_.id).as(String.class).alias("idContaBancariaBanco"),
      bancoJoin.get(Banco_.codigo).alias("codigoContaBancariaBanco"),
      bancoJoin.get(Banco_.nome).alias("nomeContaBancariaBanco"),
      tituloJoin.get(Titulo_.dataVencimento).alias("dataVencimento"),
      tituloJoin.get(Titulo_.valorTotal).alias("valorTotalTitulo"),
      valorPago.alias("valorJaPago"),
      valorRestante.alias("valorRestante"),
      valorTela.alias("totalValorCard"),
      tituloJoin.get(Titulo_.valorLiquido).alias("valorLiquido"),
      fornecedorJoin.get(Pessoa_.telefone).alias("telefoneFornecedor"),
      fornecedorJoin.get(Pessoa_.nome).alias("nomeFornecedor"),
      fornecedorJoin.get(Pessoa_.nomeInterno).alias("nomeInternoFornecedor"),
      cb.literal(getAplicacao().getCallface()).alias("callfaceAtivado"),
      cb.literal(getAplicacao().getOrganizacaoCallFace()).alias("organizacaoCallFace"),
      getRamalCallface(cb, query).alias("ramalCallFace"),
      cb.literal(null).alias("idTipoReceitaDespesa"),
      cb.literal(null).alias("nomeTipoReceitaDespesa"),
      tituloJoin.get(Titulo_.numeroNotaFiscal).alias("numeroNotaFiscal"),
      contaBancariaJoin.get(ContaBancaria_.id).alias("idContaBancaria"),
      contaBancariaJoin.get(ContaBancaria_.nome).alias("nomeContaBancaria"),
      contaBancariaJoin.get(ContaBancaria_.carteiraHomologada).alias("carteiraHomologadaContaBancaria"),
      empresaJoin.get(Empresa_.id).alias("idEmpresa"),
      empresaJoin.get(Empresa_.nome).alias("nomeEmpresa"),
      formaPagamentoJoin.get(FormaPagamento_.id).alias("idFormaPagamento"),
      formaPagamentoJoin.get(FormaPagamento_.nome).alias("nomeFormaPagamento"),
      getIsGeradoOrdemServico(cb, tituloJoin).alias("isGeradoOrdemServico"),
      getIsGeradoPedido(cb, tituloJoin).alias("isGeradoPedido"),
      getPlanoContaNomesKanban(cb, query, tituloJoin).alias("categoriasReceitaDespesa"),
      subqueryIsBoletoGerado(cb, query, tituloJoin).alias("isBoletoGerado"),
      subqueryIsCarneGerado(cb, query, tituloJoin).alias("isCarneGerado"),
      isRecorrenciaKaban(cb, query, tituloJoin).alias("isRecorrencia"),
      subqueryIsPedidoRecibo(cb, query, tituloJoin).alias("isRecibo"),
      dataRecebimento.alias("dataRecebimento"),
      dataConciliacao.alias("dataConciliacao")
    ));

    specification
      .setJoinTituloBaixa(baixasJoin)
      .setJoinContaBancaria(contaBancariaJoin)
      .setJoinBanco(bancoJoin)
      .setJoinFornecedor(fornecedorJoin);
    query.where(
      toPredicate(
        specification, cb, query, root, tituloJoin, baixasJoin, valorRestante, valorTela));

    // Aplica a ordenação
    List<Order> orders = new ArrayList<>();
    Map<String, Expression<?>> propertyMapping = new HashMap<>();
    propertyMapping.put("situacao", situacao);
    propertyMapping.put("status", situacao);
    propertyMapping.put("nomeFornecedor", fornecedorJoin.get(Pessoa_.nome));
    propertyMapping.put("nomePlanoConta", getPlanoContaNomesKanban(cb, query, tituloJoin));
    propertyMapping.put("nomeContaBancariaBanco", bancoJoin.get(Banco_.nome));
    propertyMapping.put("codigoContaBancariaBanco", bancoJoin.get(Banco_.codigo));
    propertyMapping.put("valorTotalTitulo", valorTela);
    propertyMapping.put("dataVencimento", tituloJoin.get(Titulo_.dataVencimento));
    propertyMapping.put("dataRecebimento", tituloJoin.get(Titulo_.dataRecebimento));
    propertyMapping.put("numeroNotaFiscal", tituloJoin.get(Titulo_.numeroNotaFiscal));
    if (pageable.getSort().isSorted()) {
      for (Sort.Order order : pageable.getSort()) {
        String property = order.getProperty();
        Expression<?> expression = propertyMapping.get(property);
        if (expression != null) {
          orders.add(order.isAscending() ? cb.asc(expression) : cb.desc(expression));
        }
      }
    }
    query.orderBy(orders);

    TypedQuery<TituloBuscaDto> typedQuery = entityManager.createQuery(query);
    typedQuery.setFirstResult((int) pageable.getOffset());
    typedQuery.setMaxResults(pageable.getPageSize());

    return new PageImpl<>(typedQuery.getResultList(), pageable, count);
  }

  @Override
  public BigDecimal sumValorTitulosBusca(TituloSpecification specification) {
    CriteriaBuilder cb = entityManager.getCriteriaBuilder();

    CriteriaQuery<BigDecimal> query = cb.createQuery(BigDecimal.class);

    Root<ViewTituloBaixa> root = query.from(ViewTituloBaixa.class);
    Join<ViewTituloBaixa, Titulo> tituloJoin = root.join(ViewTituloBaixa_.titulo, JoinType.INNER);
    Join<ViewTituloBaixa, TituloBaixa> baixasJoin = root.join(ViewTituloBaixa_.tituloBaixa, JoinType.LEFT);

    Expression<BigDecimal> zero = cb.literal(ZERO);
    Expression<BigDecimal> valorBaixa = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryValorBaixa(query, tituloJoin, cb), zero))
      .otherwise(cb.coalesce(baixasJoin.get(TituloBaixa_.valorBaixa), zero));
    Expression<BigDecimal> acrescimos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryJurosMultas(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorJuros), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorMulta), zero)));
    Expression<BigDecimal> descontos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryDescontoAbatimentos(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorDesconto), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorAbatimento), zero)));
    Expression<BigDecimal> valorPago = cb.diff(cb.sum(valorBaixa, acrescimos), descontos);
    Expression<BigDecimal> valorRestante = cb.diff(
      cb.diff(cb.sum(tituloJoin.get(Titulo_.valorLiquido), acrescimos), descontos),
      valorPago);
    Expression<BigDecimal> valorTela = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin), valorRestante)
      .otherwise(valorPago);

    query.select(
      cb.coalesce(cb.sum(valorTela), zero));

    specification
      .setJoinTituloBaixa(baixasJoin);
    query.where(
      toPredicate(
        specification, cb, query, root, tituloJoin, baixasJoin, valorRestante, valorTela));

    return entityManager.createQuery(query).getSingleResult();
  }

  private Predicate toPredicate(
    TituloSpecification specification, CriteriaBuilder cb, CriteriaQuery<?> query,
    Root<ViewTituloBaixa> root, Join<ViewTituloBaixa, Titulo> tituloJoin,
    Join<ViewTituloBaixa, TituloBaixa> baixasJoin,
    Expression<BigDecimal> valorRestante, Expression<BigDecimal> valorTela
  ) {
    Empresa empresa = specification.getEmpresa();
    TituloFilter filter = specification.getTituloFilter();
    if (CollectionUtils.isEmpty(filter.getEmpresasId()) && empresa != null) {
      filter.setEmpresasId(List.of(empresa.getId()));
    }
    return PredicateBuilder.create(cb)
      .add(filter.getEmpresasId(),
        (List<Integer> e) -> root.get(ViewTituloBaixa_.empresa).get(Empresa_.id).in(e))
      .add(filter.getKanbanTitulos(),
        (KanbanTitulos posicaoKanban) ->
          List.of(KanbanTitulos.A_RECEBER, KanbanTitulos.A_PAGAR).contains(posicaoKanban)
            ? cb.isNull(baixasJoin) : cb.isNotNull(baixasJoin))
      .add(filter.getSoTitulosParcialmenteBaixadosEBaixas(),
        (Boolean e) -> cb.or(
          cb.isNotNull(baixasJoin),
          cb.gt(valorRestante, ZERO)
        ))
      .add(filter.getDataVencimentoInicial(),
        (LocalDate e) -> cb.greaterThanOrEqualTo(tituloJoin.get(Titulo_.dataVencimento), e))
      .add(filter.getDataVencimentoFinal(),
        (LocalDate e) -> cb.lessThanOrEqualTo(tituloJoin.get(Titulo_.dataVencimento), e))
      .add(filter.getDataRecebimentoInicial(),
        (LocalDate e) -> cb.and(
          cb.isNotNull(baixasJoin),
          cb.greaterThanOrEqualTo(
            cb.function("date", LocalDate.class, baixasJoin.get(TituloBaixa_.dataBaixa)),
            e
          )
        ))
      .add(filter.getDataRecebimentoFinal(),
        (LocalDate e) -> cb.and(
          cb.isNotNull(baixasJoin),
          cb.lessThanOrEqualTo(
            cb.function("date", LocalDate.class, baixasJoin.get(TituloBaixa_.dataBaixa)),
            e
          )
        ))
      .add(filter.getDataPagamentoInicial(),
        (LocalDate e) -> cb.and(
          cb.isNotNull(baixasJoin),
          cb.greaterThanOrEqualTo(
            cb.function("date", LocalDate.class, baixasJoin.get(TituloBaixa_.dataBaixa)),
            e
          )
        ))
      .add(filter.getDataPagamentoFinal(),
        (LocalDate e) -> cb.and(
          cb.isNotNull(baixasJoin),
          cb.lessThanOrEqualTo(
            cb.function("date", LocalDate.class, baixasJoin.get(TituloBaixa_.dataBaixa)),
            e
          )
        ))
      .add(NumberUtils.parseStringToBigDecimal(filter.getValorTotalTitulo()),
        (BigDecimal e) -> cb.equal(valorTela, e))
      .add(specification.toPredicate(tituloJoin, query, cb))
      .and();
  }

  private Selection<Boolean> subqueryIsBoletoGerado(
    CriteriaBuilder cb, CriteriaQuery<TituloBuscaDto> query, From<?, Titulo> root
  ) {
    Subquery<Integer> subquery = query.subquery(Integer.class);
    Root<TituloBoleto> subqueryRoot = subquery.from(TituloBoleto.class);
    return cb.exists(
      subquery.select(cb.literal(1))
        .where(
          cb.equal(subqueryRoot.get(TituloBoleto_.titulo), root),
          cb.equal(subqueryRoot.get(TituloBoleto_.situacao), SituacaoBoleto.RECEBIDO)
        )
    );
  }

  private Selection<Boolean> subqueryIsCarneGerado(CriteriaBuilder cb,
    CriteriaQuery<TituloBuscaDto> query, From<?, Titulo> root
  ) {
    Subquery<Integer> subquery = query.subquery(Integer.class);
    Root<TituloBoleto> subqueryRoot = subquery.from(TituloBoleto.class);
    return cb.exists(
      subquery.select(cb.literal(1))
        .where(
          cb.equal(subqueryRoot.get(TituloBoleto_.titulo), root),
          cb.equal(subqueryRoot.get(TituloBoleto_.situacao), SituacaoBoleto.RECEBIDO),
          cb.isNotNull(subqueryRoot.get(TituloBoleto_.boletoCarne))
        )
    );
  }

  private static Expression<Object> getIsGeradoOrdemServico(
    CriteriaBuilder cb, From<?, Titulo> root
  ) {
    return cb.selectCase()
      .when(cb.isNotNull(root.get(Titulo_.ordemServico)), cb.literal(true))
      .otherwise(cb.literal(false));
  }

  private static Expression<Object> getIsGeradoPedido(
    CriteriaBuilder cb, From<?, Titulo> root
  ) {
    return cb.selectCase()
      .when(cb.isNotNull(root.get(Titulo_.pedido)), cb.literal(true))
      .otherwise(cb.literal(false));
  }

  private static Expression<Boolean> subqueryIsPedidoRecibo(
    CriteriaBuilder cb, CriteriaQuery<TituloBuscaDto> query, From<?, Titulo> titulo
  ) {
    // Subquery 1: Conta os pedidos com tipo de comprovante 'RECIBO'
    Subquery<Long> subqueryPedidos = query.subquery(Long.class);
    Root<Pedido> pedido = subqueryPedidos.from(Pedido.class);
    subqueryPedidos.select(cb.count(pedido));
    subqueryPedidos.where(
      cb.equal(pedido.get(Pedido_.id), titulo.get(Titulo_.pedido).get(Pedido_.id)),
      cb.equal(pedido.get(Pedido_.tipoComprovante), TipoComprovante.RECIBO)
    );

    // Subquery 2: Conta as ordens de serviço com tipo de comprovante 'RECIBO'
    Subquery<Long> subqueryOrdemServico = query.subquery(Long.class);
    Root<OrdemServico> ordemServico = subqueryOrdemServico.from(OrdemServico.class);
    subqueryOrdemServico.select(cb.count(ordemServico));
    subqueryOrdemServico.where(
      cb.equal(ordemServico.get(OrdemServico_.id), titulo.get(Titulo_.ordemServico).get(OrdemServico_.id)),
      cb.equal(ordemServico.get(OrdemServico_.tipoComprovante), "recibo")
    );

    // Soma dos counts das duas subqueries
    Expression<Long> totalCount = cb.sum(subqueryPedidos.getSelection(), subqueryOrdemServico.getSelection());

    // Subquery que verifica se a soma é maior que 0
    Subquery<Boolean> subqueryIsRecibo = query.subquery(Boolean.class);
    subqueryIsRecibo.select(cb.greaterThan(totalCount, 0L));
    return subqueryIsRecibo;
  }

  private static Expression<Integer> getRamalCallface(CriteriaBuilder cb,
    CriteriaQuery<TituloBuscaDto> query) {
    Subquery<Integer> ramalCallFace = query.subquery(Integer.class);
    Root<UsuarioAplicacao> ramalCallFaceRoot = ramalCallFace.from(UsuarioAplicacao.class);
    ramalCallFace.select(ramalCallFaceRoot.get(UsuarioAplicacao_.ramalCallFace)).where(
      cb.equal(ramalCallFaceRoot.get(UsuarioAplicacao_.usuario), getUsuario()));
    return ramalCallFace;
  }

  public static Subquery<String> getPlanoContaNomesKanban(
    CriteriaBuilder cb, CriteriaQuery<?> query, From<?, Titulo> root
  ) {
    Subquery<String> planoContaNameSubquery = query.subquery(String.class);
    Root<Titulo> subqueryRoot = planoContaNameSubquery.from(Titulo.class);
    ListJoin<Titulo, TituloDespesa> tituloDespesaListJoin = subqueryRoot.join(Titulo_.despesas,
      JoinType.LEFT);
    Join<TituloDespesa, PlanoConta> planoContaJoin = tituloDespesaListJoin.join(
      TituloDespesa_.planoConta, JoinType.LEFT);
    planoContaNameSubquery.select(
      stringAgg(cb, planoContaJoin.get(PlanoConta_.nome), ", "))
      .where(cb.equal(subqueryRoot, root));
    return planoContaNameSubquery;
  }

  private Long getCountKaban(TituloSpecification specification, CriteriaBuilder cb) {
    CriteriaQuery<Long> query = cb.createQuery(Long.class);
    Root<ViewTituloBaixa> root = query.from(ViewTituloBaixa.class);
    Join<ViewTituloBaixa, Titulo> tituloJoin = root.join(ViewTituloBaixa_.titulo, JoinType.INNER);
    Join<ViewTituloBaixa, TituloBaixa> baixasJoin = root.join(ViewTituloBaixa_.tituloBaixa, JoinType.LEFT);

    Expression<BigDecimal> zero = cb.literal(ZERO);
    Expression<BigDecimal> valorBaixa = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryValorBaixa(query, tituloJoin, cb), zero))
      .otherwise(cb.coalesce(baixasJoin.get(TituloBaixa_.valorBaixa), zero));
    Expression<BigDecimal> acrescimos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryJurosMultas(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorJuros), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorMulta), zero)));
    Expression<BigDecimal> descontos = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin),
        cb.coalesce(getSubqueryDescontoAbatimentos(query, tituloJoin, cb), zero))
      .otherwise(cb.sum(
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorDesconto), zero),
        cb.coalesce(baixasJoin.get(TituloBaixa_.valorAbatimento), zero)));
    Expression<BigDecimal> valorPago = cb.diff(cb.sum(valorBaixa, acrescimos), descontos);
    Expression<BigDecimal> valorRestante = cb.diff(
      cb.diff(cb.sum(tituloJoin.get(Titulo_.valorLiquido), acrescimos), descontos),
      valorPago);
    Expression<BigDecimal> valorTela = cb.<BigDecimal>selectCase()
      .when(cb.isNull(baixasJoin), valorRestante)
      .otherwise(valorPago);

    query.select(cb.count(root))
      .where(
        toPredicate(
          specification, cb, query, root, tituloJoin, baixasJoin, valorRestante, valorTela));

    return entityManager.createQuery(query).getSingleResult();
  }

  private Subquery<BigDecimal> getSubqueryValorBaixa(
    CriteriaQuery<?> query, From<?, Titulo> root, CriteriaBuilder cb
  ) {
    Subquery<BigDecimal> subQuery = query.subquery(BigDecimal.class);
    Root<TituloBaixa> subRoot = subQuery.from(TituloBaixa.class);
    subQuery.select(cb.sum(subRoot.get(TituloBaixa_.valorBaixa)));
    subQuery.where(cb.equal(subRoot.get(TituloBaixa_.titulo), root));
    return subQuery;
  }

  private Subquery<BigDecimal> getSubqueryJurosMultas(
    CriteriaQuery<?> query, From<?, Titulo> root, CriteriaBuilder cb
  ) {
    Subquery<BigDecimal> subQuery = query.subquery(BigDecimal.class);
    Root<TituloBaixa> subRoot = subQuery.from(TituloBaixa.class);
    subQuery.select(cb.sum(
      cb.coalesce(cb.sum(subRoot.get(TituloBaixa_.valorJuros)), cb.literal(ZERO)),
      cb.coalesce(cb.sum(subRoot.get(TituloBaixa_.valorMulta)), cb.literal(ZERO))
    ));
    subQuery.where(cb.equal(subRoot.get(TituloBaixa_.titulo), root));
    return subQuery;
  }

  private Subquery<BigDecimal> getSubqueryDescontoAbatimentos(
    CriteriaQuery<?> query, From<?, Titulo> root, CriteriaBuilder cb
  ) {
    Subquery<BigDecimal> subQuery = query.subquery(BigDecimal.class);
    Root<TituloBaixa> subRoot = subQuery.from(TituloBaixa.class);
    subQuery.select(cb.sum(
      cb.coalesce(cb.sum(subRoot.get(TituloBaixa_.valorDesconto)), cb.literal(ZERO)),
      cb.coalesce(cb.sum(subRoot.get(TituloBaixa_.valorAbatimento)), cb.literal(ZERO))
    ));
    subQuery.where(cb.equal(subRoot.get(TituloBaixa_.titulo), root));
    return subQuery;
  }

  private Predicate isRecorrenciaKaban(
    CriteriaBuilder cb, CriteriaQuery<?> query, From<?, Titulo> root
  ) {
    Subquery<Integer> subquery = query.subquery(Integer.class);
    Root<Titulo> subqueryRoot = subquery.from(Titulo.class);
    subquery.select(cb.literal(1))
      .where(cb.equal(subqueryRoot.get(Titulo_.tituloReferencia), root));

    Predicate isReferenciado = cb.exists(subquery);

    Join<Titulo, Titulo> joinTituloReferencia = root.join(Titulo_.tituloReferencia, JoinType.LEFT);
    Predicate isReferencia = cb.isNotNull(joinTituloReferencia.get(Titulo_.id));

    return cb.or(isReferenciado, isReferencia);
  }
}
