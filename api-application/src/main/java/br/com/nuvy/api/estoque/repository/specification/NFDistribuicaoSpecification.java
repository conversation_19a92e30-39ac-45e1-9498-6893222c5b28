package br.com.nuvy.api.estoque.repository.specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;
import br.com.nuvy.api.estoque.filter.NFDistribuicaoFilter;
import br.com.nuvy.api.estoque.model.NFDistribuicao;
import br.com.nuvy.api.estoque.model.NFDistribuicao_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "of")
public class NFDistribuicaoSpecification implements Specification<NFDistribuicao> {

  private final NFDistribuicaoFilter filter;

  @Override
  public Predicate toPredicate(Root<NFDistribuicao> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {

    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getNumeroNota(), e -> criteriaBuilder.equal(root.get(NFDistribuicao_.numeroNota), e))
      .add(filter.getChave(), e -> iLike(criteriaBuilder, root.get(NFDistribuicao_.chave), e))
      .add(filter.getDataEmissao(), e -> criteriaBuilder.equal(root.get(NFDistribuicao_.dataEmissao), e))
      .add(filter.getCnpj(), e -> iLike(criteriaBuilder, root.get(NFDistribuicao_.cnpj), e))
      .add(filter.getEmitente(), e -> iLike(criteriaBuilder, root.get(NFDistribuicao_.emitente), e))
      .add(filter.getValorTotal(), e -> criteriaBuilder.equal(root.get(NFDistribuicao_.valorTotal), e))
      .add(filter.getSituacao(), e -> criteriaBuilder.equal(root.get(NFDistribuicao_.situacao), e))
      .and();
  }
}