package br.com.nuvy.api.cadastro.repository.specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;

import br.com.nuvy.api.cadastro.filter.EmpresaFilter;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.cadastro.model.RegimeTributario_;
import br.com.nuvy.api.cadastro.model.Segmento;
import br.com.nuvy.api.cadastro.model.Segmento_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
public class EmpresaSpecification implements Specification<Empresa> {

  private final EmpresaFilter filter;

  @Override
  public Predicate toPredicate(Root<Empresa> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getId(),
        e -> criteriaBuilder.equal(root.get(Empresa_.id), e))
      .add(filter.getCpfCnpj(),
        e -> criteriaBuilder.like(root.get(Empresa_.cpfCnpj), "%" + e + "%"))
      .add(filter.getNome(),
        e -> iLike(criteriaBuilder, root.get(Empresa_.nome), e))
      .add(filter.getCidade(),
        e -> iLike(criteriaBuilder, root.get(Empresa_.cidade), e))
      .add(filter.getUf(),
        e -> criteriaBuilder.equal(criteriaBuilder.lower(root.get(Empresa_.uf)), e.toLowerCase()))
      .add(filter.getIdSegmento(),
        e -> criteriaBuilder.equal(root.get(Empresa_.segmento).get(Segmento_.id), e))
      .add(filter.getNomeSegmento(),
        e -> {
          Fetch<Empresa, Segmento> segmentofetch = root.fetch(Empresa_.segmento, JoinType.LEFT);
          Join<Empresa, Segmento> segmento = (Join<Empresa, Segmento>) segmentofetch;
          return iLike(criteriaBuilder, segmento.get(Segmento_.nome), e);
        })
      .add(filter.getIdRegimeTributario(),
        e -> criteriaBuilder.equal(root.get(Empresa_.regimeTributario).get(RegimeTributario_.id),
          e))
      .add(filter.getSiglaEstado(),
        e -> criteriaBuilder.equal(root.get(Empresa_.uf), e))
      .add(filter.getSituacao(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get(Empresa_.situacao).as(String.class)),
          "%" + e.toUpperCase() + "%"))
      .add(filter.getIdsRetorno(), e -> root.get(Empresa_.id).in(e))
      .and();
  }
}
