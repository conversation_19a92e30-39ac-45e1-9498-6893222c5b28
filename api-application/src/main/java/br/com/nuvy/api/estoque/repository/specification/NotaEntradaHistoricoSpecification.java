package br.com.nuvy.api.estoque.repository.specification;

import static br.com.nuvy.common.utils.StringUtils.getIlikeStringUpper;

import br.com.nuvy.api.estoque.filter.NotaEntradaHistoricoFilter;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaHistorico;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaHistorico_;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada_;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.model.Usuario_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
public class NotaEntradaHistoricoSpecification implements
  Specification<NotaFiscalEntradaHistorico> {

  private final NotaEntradaHistoricoFilter filter;
  @NotNull
  private final UUID notaFiscalEntradaId;

  @Override
  public Predicate toPredicate(Root<NotaFiscalEntradaHistorico> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {

    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(notaFiscalEntradaId, e -> {
        Join<NotaFiscalEntradaHistorico, NotaFiscalEntrada> join = root.join(
          NotaFiscalEntradaHistorico_.notaFiscalEntrada, JoinType.LEFT);
        return criteriaBuilder.equal(join.get(NotaFiscalEntrada_.id), notaFiscalEntradaId);
      })
      .add(filter.getDataHistorico(), e -> criteriaBuilder.equal(
        root.get(NotaFiscalEntradaHistorico_.dataHistorico).as(LocalDate.class), e))
      .add(filter.getAcao(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(
          root.get(NotaFiscalEntradaHistorico_.acao).as(String.class)), getIlikeStringUpper(e)))
      .add(filter.getUsuarioNome(), e -> {
        Join<NotaFiscalEntradaHistorico, Usuario> join = root.join(
          NotaFiscalEntradaHistorico_.usuario, JoinType.LEFT);
        return criteriaBuilder.like(criteriaBuilder.upper(join.get(Usuario_.nome)),
          getIlikeStringUpper(e));
      })
      .add(filter.getDescricao(), e -> criteriaBuilder.like(
        criteriaBuilder.upper(root.get(NotaFiscalEntradaHistorico_.descricao)),
        getIlikeStringUpper(e)))
      .and();
  }
}
