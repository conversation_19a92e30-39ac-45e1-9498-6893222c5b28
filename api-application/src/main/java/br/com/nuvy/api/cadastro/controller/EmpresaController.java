package br.com.nuvy.api.cadastro.controller;

import br.com.nuvy.api.cadastro.dto.ContatoEmpresaDto;
import br.com.nuvy.api.cadastro.dto.ContatoEmpresaResumoDto;
import br.com.nuvy.api.cadastro.dto.EmpresaDto;
import br.com.nuvy.api.cadastro.dto.Imagem;
import br.com.nuvy.api.cadastro.filter.EmpresaFilter;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.FaixaFaturamentoService;
import br.com.nuvy.client.enums.TipoArquivo;
import br.com.nuvy.client.services.DownloadBuscasService;
import br.com.nuvy.common.base.controller.PageableControllerAdapter;
import br.com.nuvy.common.exception.OperacaoNaoSuportadaException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.patch.json.Patch;
import br.com.nuvy.common.patch.json.PatchRequestBody;
import br.com.nuvy.facade.cadastro.cd1minhaempresa.model.CalculoAliquotaIcmsDto;
import br.com.nuvy.facade.cadastro.cd1minhaempresa.model.CalculoAliquotaIcmsResumoDto;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "Cadastro / Empresa")
@RestController
@RequestMapping(value = "/v1/cadastro/empresa")
@RequiredArgsConstructor
public class EmpresaController extends
  PageableControllerAdapter<EmpresaDto, Integer, EmpresaFilter, EmpresaService> {

  private final FaixaFaturamentoService faixaFaturamentoService;
  private final DownloadBuscasService downloadBuscasService;

  @Override
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void create(EmpresaDto payload) {
    throw new OperacaoNaoSuportadaException("operacao.nao.suportada");
  }

  @Override
  @Operation(
    summary = "Permite editar uma empresa",
    description = "Permite editar uma empresa, passando o id e os parâmetros a serem atualizados"
  )
  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void update(@PathVariable Integer id, @RequestBody @Valid EmpresaDto payload) {
    var result = service.update(id, payload.toEntity()).getId();
    super.fireResourceUpdatedEvent(result);
  }

  @Operation(
    summary = "Permite atualizar parâmetros de uma empresa",
    description = "Permite atualizar parâmetros de uma empresa, passando o id e os parâmetros a serem atualizados"
  )
  @PatchMapping("/{id}")
  @Patch(service = EmpresaService.class)
  @ResponseStatus(HttpStatus.OK)
  public void patch(@PathVariable Integer id, @PatchRequestBody EmpresaDto empresa) {
    service.updateDto(id, empresa);
  }

  @Operation(
    summary = "Permite realizar o download da consulta",
    description = "permite realizar o download das consultas de empresas, podendo retornar um zip com um csv, xlsx ou pdf"
  )
  @PostMapping(value = "/consulta/download", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public ResponseEntity<InputStreamResource> downloadConsultaEmpresa(
    @RequestParam TipoArquivo tipoArquivo, @ParameterObject @Valid EmpresaFilter filter) {
    return downloadBuscasService.downloadConsultaEmpresa(tipoArquivo, filter);
  }

  @Operation(
    summary = "Permite atualizar o logotipo de uma empresa",
    description = "Permite atualizar o logotipo de uma empresa, passando o id e o arquivo do logotipo"
  )
  @PutMapping(value = "/{id}/logo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ResponseStatus(HttpStatus.OK)
  public Imagem updateLogotipo(@PathVariable Integer id,
    @RequestPart("logotipo") MultipartFile logotipo) {
    return service.updateLogotipo(id, logotipo);
  }

  @Operation(
    summary = "Permite listar os contatos de uma empresa",
    description = "Permite listar os contatos de uma empresa, passando o id da empresa"
  )
  @GetMapping("/{id}/contato")
  public List<ContatoEmpresaResumoDto> findAllContatos(@PathVariable Integer id) {
    return service.findAllContatos(id);
  }

  @Operation(
    summary = "Permite buscar um contato de uma empresa",
    description = "Permite buscar um contato de uma empresa, passando o id da empresa e o id do contato"
  )
  @GetMapping("/{empresaId}/contato/{id}")
  public ContatoEmpresaDto findContatoById(@PathVariable Integer empresaId,
    @PathVariable Integer id) {
    return service.findContatoById(empresaId, id).orElseThrow(ResourceNotFoundException::new);
  }

  @Operation(
    summary = "Permite criar um contato para uma empresa",
    description = "Permite criar um contato para uma empresa, passando o id da empresa e os dados do contato"
  )
  @PostMapping("/{empresaId}/contato")
  @ResponseStatus(HttpStatus.CREATED)
  public void createContato(@PathVariable Integer empresaId, @RequestBody ContatoEmpresaDto dto) {
    Integer contatoId = service.createContato(empresaId, dto);
    fireResourceCreatedEvent(contatoId);
  }

  @Operation(
    summary = "Permite atualizar um contato de uma empresa",
    description = "Permite atualizar um contato de uma empresa, passando o id da empresa, o id e os dados do contato"
  )
  @PutMapping("/{empresaId}/contato/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateContato(@PathVariable Integer empresaId, @PathVariable Integer id,
    @RequestBody ContatoEmpresaDto dto) {
    service.updateContato(empresaId, id, dto);
  }

  @Operation(
    summary = "Permite deletar um contato de uma empresa",
    description = "Permite deletar um contato de uma empresa, passando o id da empresa e o id do contato"
  )
  @DeleteMapping("/{empresaId}/contato/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteContato(@PathVariable Integer empresaId, @PathVariable Integer id) {
    service.deleteContato(empresaId, id);
  }

  @Operation(
    summary = "Permite calcular a alíquota ICMS de uma empresa",
    description = "Permite calcular a alíquota ICMS de uma empresa, passando o id da empresa"
  )
  @PostMapping(value = "/aliquota-icms")
  @ResponseStatus(HttpStatus.OK)
  public CalculoAliquotaIcmsResumoDto calculateAliquotaIcms(
    @RequestBody CalculoAliquotaIcmsDto dto) {
    return faixaFaturamentoService.calculaAliquotaIcms(dto);
  }

  @Override
  @Operation(
    summary = "Permite deletar uma empresa",
    description = "Permite deletar uma empresa, passando o id da empresa"
  )
  @Hidden
  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void delete(@PathVariable Integer id) {
    service.delete(id);
  }

}
