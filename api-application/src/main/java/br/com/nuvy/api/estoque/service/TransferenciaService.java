package br.com.nuvy.api.estoque.service;

import br.com.nuvy.api.estoque.model.Transferencia;
import br.com.nuvy.api.estoque.repository.TransferenciaRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import java.util.Optional;
import org.springframework.stereotype.Service;

@Service
public class TransferenciaService extends
  PageableServiceAdapter<Transferencia, Integer, NoFilter, TransferenciaRepository> {

  public Optional<Transferencia> findByMovimentoEstoqueOrigemId(Integer id) {
    return repository.findByMovimentoEstoqueOrigemId(id);
  }

  public Optional<Transferencia> findByMovimentoEstoqueDestinoId(Integer id) {
    return repository.findByMovimentoEstoqueDestinoId(id);
  }

}
