package br.com.nuvy.api.cadastro.repository;

import static br.com.nuvy.api.notafiscal.dto.SituacaoPedido.NF_EMITIDA;
import static br.com.nuvy.common.query.CriteriaUtils.iLike;
import static br.com.nuvy.common.utils.StringUtils.removePunctuation;

import br.com.nuvy.api.cadastro.filter.PessoaFilter;
import br.com.nuvy.api.cadastro.filter.pessoa.PessoaApiExternaFilter;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa_;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.PessoaRelacaoComercial;
import br.com.nuvy.api.cadastro.model.PessoaRelacaoComercial_;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.cadastro.model.pessoa.PessoaApiExterna;
import br.com.nuvy.api.cadastro.service.PessoaService;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.financeiro.model.Banco_;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.Pedido_;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.common.utils.StringUtils;
import br.com.nuvy.facade.cadastro.cd3parceiros.model.PessoaDto;
import br.com.nuvy.facade.cadastro.cd3parceiros.model.PessoaResumoDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
class PessoaCustomRepositoryImpl implements PessoaCustomRepository {

  private final EntityManager entityManager;

  PessoaCustomRepositoryImpl(EntityManager entityManager) {
    this.entityManager = entityManager;
  }

  @Override
  public Page<PessoaResumoDto> busca(PessoaFilter filter, Pageable pageable) {
    if (pageable.getSort().isUnsorted()) {
      pageable = PageRequest.of(
        pageable.getPageNumber(), pageable.getPageSize(),
        Direction.ASC, "nome"
      );
    }

    CriteriaBuilder builder = entityManager.getCriteriaBuilder();

    CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
    Root<Pessoa> countPessoa = countQuery.from(Pessoa.class);
    Join<Pessoa, Pessoa> vendedorCount = countPessoa.join(Pessoa_.vendedor, JoinType.LEFT);
    Join<Pessoa, EnderecoPessoa> enderecoCount = countPessoa.join(Pessoa_.enderecos, JoinType.LEFT);
    enderecoCount.on(builder.equal(enderecoCount.get(EnderecoPessoa_.tipo), TipoEndereco.FISCAL));
    Join<Pessoa, Banco> bancoCount = countPessoa.join(Pessoa_.banco, JoinType.LEFT);
    CamposBusca camposCount = getCamposBusca(
      countPessoa, vendedorCount, enderecoCount, bancoCount, countQuery, builder
    );
    countQuery.select(builder.count(countPessoa));
    countQuery.where(toPredicate(filter, camposCount, countPessoa, countQuery, builder));

    Long count = entityManager.createQuery(countQuery).getSingleResult();

    if (count == 0) {
      return Page.empty(pageable);
    }

    CriteriaQuery<PessoaResumoDto> query = builder.createQuery(PessoaResumoDto.class);
    Root<Pessoa> pessoa = query.from(Pessoa.class);
    Join<Pessoa, Pessoa> vendedor = pessoa.join(Pessoa_.vendedor, JoinType.LEFT);
    Join<Pessoa, EnderecoPessoa> endereco = pessoa.join(Pessoa_.enderecos, JoinType.LEFT);
    endereco.on(builder.equal(endereco.get(EnderecoPessoa_.tipo), TipoEndereco.FISCAL));
    Join<Pessoa, Banco> banco = pessoa.join(Pessoa_.banco, JoinType.LEFT);
    CamposBusca campos = getCamposBusca(
      pessoa, vendedor, endereco, banco, query, builder
    );

    query.select(builder.construct(
      PessoaResumoDto.class,
      campos.id().alias("id"),
      campos.tipo().alias("tipo"),
      campos.cpfCnpj().alias("cpfCnpj"),
      campos.situacao().alias("situacao"),
      campos.nomeFantasia().alias("nomeFantasia"),
      campos.nome().alias("nome"),
      campos.nomeInterno().alias("nomeInterno"),
      campos.cidade().alias("cidade"),
      campos.codigoCidade().alias("codigoCidade"),
      campos.ufNome().alias("ufNome"),
      campos.uf().alias("uf"),
      campos.telefone().alias("telefone"),
      campos.vendedorId().alias("vendedorId"),
      campos.vendedorNomeFantasia().alias("vendedorNomeFantasia"),
      campos.relacaoComercial().alias("relacaoComercial"),
      campos.nomeTitularConta().alias("nomeTitularConta"),
      campos.cpfTitularConta().alias("cpfTitularConta"),
      campos.chavePixConta().alias("chavePixConta"),
      campos.bancoId().alias("bancoId"),
      campos.bancoCodigo().alias("bancoCodigo"),
      campos.bancoNome().alias("bancoNome"),
      campos.agenciaConta().alias("agenciaConta"),
      campos.numeroConta().alias("numeroConta"),
      builder.literal(false).alias("callfaceAtivado"),
      builder.literal(null).alias("organizacaoCallFace"),
      builder.literal(null).alias("ramalCallFace"),
      campos.email().alias("email"),
      campos.emailCobranca().alias("emailCobranca"),
      campos.emailNotaFiscal().alias("emailNotaFiscal"),
      campos.existeEnderecoCadastrado().alias("existeEnderecoCadastrado")
    ));
    query.where(toPredicate(filter, campos, pessoa, query, builder));
    query.orderBy(toOrderBy(pageable, campos, builder));

    TypedQuery<PessoaResumoDto> typedQuery = entityManager.createQuery(query);
    typedQuery.setFirstResult((int) pageable.getOffset());
    typedQuery.setMaxResults(pageable.getPageSize());

    List<PessoaResumoDto> list = typedQuery.getResultList();
    return new PageImpl<>(list, pageable, count);
  }

  record CamposBusca(
    Expression<Integer> id,
    Expression<TipoPessoa> tipo,
    Expression<String> cpfCnpj,
    Expression<SituacaoParceiro> situacao,
    Expression<String> nome,
    Expression<String> nomeFantasia,
    Expression<String> nomeInterno,
    Expression<String> cidade,
    Expression<String> codigoCidade,
    Expression<String> uf,
    Expression<String> ufNome,
    Expression<String> telefone,
    Expression<Integer> vendedorId,
    Expression<String> vendedorNomeFantasia,
    Expression<String[]> relacaoComercial,
    Expression<String> nomeTitularConta,
    Expression<String> cpfTitularConta,
    Expression<String> chavePixConta,
    Expression<Integer> bancoId,
    Expression<String> bancoCodigo,
    Expression<String> bancoNome,
    Expression<String> agenciaConta,
    Expression<String> numeroConta,
    Expression<String> email,
    Expression<String> emailCobranca,
    Expression<String> emailNotaFiscal,
    Expression<Boolean> existeEnderecoCadastrado
  ) {}

  private static Subquery<Boolean> subQueryExisteEndereco(
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<Boolean> subQuery = query.subquery(Boolean.class);
    Root<EnderecoPessoa> pessoaEndereco = subQuery.from(EnderecoPessoa.class);
    return subQuery
      .select(builder.greaterThan(builder.count(pessoaEndereco.get(EnderecoPessoa_.id)), 0L))
      .where(builder.equal(pessoaEndereco.get(EnderecoPessoa_.pessoa), pessoa));
  }

  private static CamposBusca getCamposBusca(
    Root<Pessoa> pessoa, Join<Pessoa, Pessoa> vendedor, Join<Pessoa, EnderecoPessoa> endereco,
    Join<Pessoa, Banco> banco,
    CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Expression<Integer> id = pessoa.get(Pessoa_.id);
    Expression<TipoPessoa> tipo = pessoa.get(Pessoa_.tipo);
    Expression<String> cpfCnpj = pessoa.get(Pessoa_.cpfCnpj);
    Expression<SituacaoParceiro> situacao = pessoa.get(Pessoa_.situacao);
    Expression<String> nome = pessoa.get(Pessoa_.nome);
    Expression<String> nomeFantasia = pessoa.get(Pessoa_.nomeFantasia);
    Expression<String> nomeInterno = pessoa.get(Pessoa_.nomeInterno);
    Expression<String> cidade = endereco.get(EnderecoPessoa_.cidade);
    Expression<String> codigoCidade = endereco.get(EnderecoPessoa_.codigoCidade);
    Expression<String> uf = endereco.get(EnderecoPessoa_.uf);
    Expression<String> ufNome = endereco.get(EnderecoPessoa_.ufNome);
    Expression<String> telefone = pessoa.get(Pessoa_.telefone);
    Expression<Integer> vendedorId = vendedor.get(Pessoa_.id);
    Expression<String> vendedorNomeFantasia = vendedor.get(Pessoa_.nomeFantasia);
    Expression<String[]> relacaoComercial = subQueryRelacaoComercial(pessoa, query, builder);
    Expression<String> nomeTitularConta = pessoa.get(Pessoa_.nomeTitularConta);
    Expression<String> cpfTitularConta = pessoa.get(Pessoa_.cpfTitularConta);
    Expression<String> chavePixConta = pessoa.get(Pessoa_.chavePixConta);
    Expression<Integer> bancoId = banco.get(Banco_.id);
    Expression<String> bancoCodigo = banco.get(Banco_.codigo);
    Expression<String> bancoNome = banco.get(Banco_.nome);
    Expression<String> agenciaConta = pessoa.get(Pessoa_.agenciaConta);
    Expression<String> numeroConta = pessoa.get(Pessoa_.numeroConta);
    Expression<String> email = builder.coalesce(
      pessoa.get(Pessoa_.emailNotaFiscal), pessoa.get(Pessoa_.emailCobranca)
    );
    Expression<String> emailCobranca = pessoa.get(Pessoa_.emailCobranca);
    Expression<String> emailNotaFiscal = pessoa.get(Pessoa_.emailNotaFiscal);
    Expression<Boolean> existeEnderecoCadastrado = subQueryExisteEndereco(pessoa, query, builder);
    return new CamposBusca(
      id, tipo, cpfCnpj, situacao, nome, nomeFantasia, nomeInterno, cidade, codigoCidade, uf,
      ufNome, telefone, vendedorId, vendedorNomeFantasia, relacaoComercial, nomeTitularConta,
      cpfTitularConta, chavePixConta, bancoId, bancoCodigo, bancoNome, agenciaConta, numeroConta,
      email, emailCobranca, emailNotaFiscal, existeEnderecoCadastrado
    );
  }

  @Override
  @Transactional(readOnly = true)
  public Page<PessoaApiExterna> buscaApiExterna(PessoaApiExternaFilter filter, Pageable pageable) {
    if (pageable.getSort().isUnsorted()) {
      pageable = PageRequest.of(
        pageable.getPageNumber(), pageable.getPageSize(),
        Direction.ASC, "nome"
      );
    }

    CriteriaBuilder builder = entityManager.getCriteriaBuilder();

    CriteriaQuery<Long> countQuery = builder.createQuery(Long.class);
    Root<Pessoa> countPessoa = countQuery.from(Pessoa.class);
    CamposBuscaApiExterna camposCount = getCamposBuscaApiExterna(countPessoa, countQuery, builder);
    countQuery.select(builder.count(countPessoa));
    countQuery.where(toPredicate(filter, camposCount, countPessoa, countQuery, builder));

    Long count = entityManager.createQuery(countQuery).getSingleResult();

    if (count == 0) {
      return Page.empty(pageable);
    }

    CriteriaQuery<PessoaApiExterna> query = builder.createQuery(PessoaApiExterna.class);
    Root<Pessoa> pessoa = query.from(Pessoa.class);
    CamposBuscaApiExterna campos = getCamposBuscaApiExterna(pessoa, query, builder);

    query.select(builder.construct(
      PessoaApiExterna.class,
      campos.uuid().alias("uuid"),
      campos.tipo().alias("tipo"),
      campos.cpfCnpj().alias("cpfCnpj"),
      campos.nome().alias("nome"),
      campos.nomeFantasia().alias("nomeFantasia"),
      campos.telefone().alias("telefone"),
      campos.emailCobranca().alias("emailCobranca"),
      campos.emailNotaFiscal().alias("emailNotaFiscal"),
      campos.dataNascimento().alias("dataNascimento"),
      campos.situacao().alias("situacao"),
      campos.relacaoComercial().alias("relacaoComercial"),
      campos.enderecos().alias("enderecos")
    ));
    query.where(toPredicate(filter, campos, pessoa, query, builder));
    query.orderBy(toOrderBy(pageable, campos, builder));

    TypedQuery<PessoaApiExterna> typedQuery = entityManager.createQuery(query);
    typedQuery.setFirstResult((int) pageable.getOffset());
    typedQuery.setMaxResults(pageable.getPageSize());

    List<PessoaApiExterna> list = typedQuery.getResultList();
    return new PageImpl<>(list, pageable, count);
  }

  @Override
  @Transactional(readOnly = true)
  public PessoaApiExterna buscaPorIdApiExterna(UUID id) {
    CriteriaBuilder builder = entityManager.getCriteriaBuilder();

    CriteriaQuery<PessoaApiExterna> query = builder.createQuery(PessoaApiExterna.class);
    Root<Pessoa> pessoa = query.from(Pessoa.class);
    CamposBuscaApiExterna campos = getCamposBuscaApiExterna(pessoa, query, builder);

    query.select(builder.construct(
      PessoaApiExterna.class,
      campos.uuid().alias("uuid"),
      campos.tipo().alias("tipo"),
      campos.cpfCnpj().alias("cpfCnpj"),
      campos.nome().alias("nome"),
      campos.nomeFantasia().alias("nomeFantasia"),
      campos.telefone().alias("telefone"),
      campos.emailCobranca().alias("emailCobranca"),
      campos.emailNotaFiscal().alias("emailNotaFiscal"),
      campos.dataNascimento().alias("dataNascimento"),
      campos.situacao().alias("situacao"),
      campos.relacaoComercial().alias("relacaoComercial"),
      campos.enderecos().alias("enderecos")
    ));
    query.where(builder.equal(campos.uuid(), id));

    TypedQuery<PessoaApiExterna> typedQuery = entityManager.createQuery(query);

    try {
      return typedQuery.getSingleResult();
    } catch (jakarta.persistence.NoResultException e) {
      throw new ResourceNotFoundException("Parceiro com ID " + id + " não encontrado");
    }
  }

  record CamposBuscaApiExterna(
    Expression<UUID> uuid,
    Expression<TipoPessoa> tipo,
    Expression<String> cpfCnpj,
    Expression<String> nome,
    Expression<String> nomeFantasia,
    Expression<String> telefone,
    Expression<String> emailCobranca,
    Expression<String> emailNotaFiscal,
    Expression<LocalDate> dataNascimento,
    Expression<SituacaoParceiro> situacao,
    Expression<String[]> relacaoComercial,
    Expression<String[]> enderecos
  ) {}

  private static Subquery<String[]> subQueryRelacaoComercial(
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<String[]> subQuery = query.subquery(String[].class);
    Root<PessoaRelacaoComercial> pessoaRelacaoComercial = subQuery.from(PessoaRelacaoComercial.class);
    return subQuery
      .select(builder.function(
        "array_agg", String[].class,
        pessoaRelacaoComercial.get(PessoaRelacaoComercial_.relacaoComercial)
      ))
      .where(builder.equal(
        pessoaRelacaoComercial.join(PessoaRelacaoComercial_.pessoa).get(Pessoa_.id),
        pessoa.get(Pessoa_.id)
      ));
  }

  private static Subquery<String[]> subQueryEnderecos(
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Subquery<String[]> subQuery = query.subquery(String[].class);
    Root<EnderecoPessoa> pessoaEndereco = subQuery.from(EnderecoPessoa.class);
    return subQuery
      .select(builder.function(
        "array_agg", String[].class,
        builder.function(
          "json_build_object", String.class,
          builder.literal("complemento"), pessoaEndereco.get(EnderecoPessoa_.complemento),
          builder.literal("numero"), pessoaEndereco.get(EnderecoPessoa_.numero),
          builder.literal("logradouro"), pessoaEndereco.get(EnderecoPessoa_.endereco),
          builder.literal("bairro"), pessoaEndereco.get(EnderecoPessoa_.bairro),
          builder.literal("cep"), pessoaEndereco.get(EnderecoPessoa_.cep),
          builder.literal("cidadeCodigo"), pessoaEndereco.get(EnderecoPessoa_.codigoCidade),
          builder.literal("cidadeNome"), pessoaEndereco.get(EnderecoPessoa_.cidade),
          builder.literal("uf"), pessoaEndereco.get(EnderecoPessoa_.uf)
        )
      ))
      .where(builder.equal(
        pessoaEndereco.join(EnderecoPessoa_.pessoa).get(Pessoa_.id),
        pessoa.get(Pessoa_.id)
      ));
  }

  private static CamposBuscaApiExterna getCamposBuscaApiExterna(
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    Expression<UUID> uuid = pessoa.get(Pessoa_.uuid);
    Expression<TipoPessoa> tipo = pessoa.get(Pessoa_.tipo);
    Expression<String> cpfCnpj = pessoa.get(Pessoa_.cpfCnpj);
    Expression<String> nome = pessoa.get(Pessoa_.nome);
    Expression<String> nomeFantasia = pessoa.get(Pessoa_.nomeFantasia);
    Expression<String> telefone = pessoa.get(Pessoa_.telefone);
    Expression<String> emailCobranca = pessoa.get(Pessoa_.emailCobranca);
    Expression<String> emailNotaFiscal = pessoa.get(Pessoa_.emailNotaFiscal);
    Expression<LocalDate> dataNascimento = pessoa.get(Pessoa_.dataNascimento);
    Expression<SituacaoParceiro> situacao = pessoa.get(Pessoa_.situacao);
    Expression<String[]> relacaoComercial = subQueryRelacaoComercial(pessoa, query, builder);
    Expression<String[]> enderecos = subQueryEnderecos(pessoa, query, builder);
    return new CamposBuscaApiExterna(
      uuid, tipo, cpfCnpj, nome, nomeFantasia, telefone, emailCobranca, emailNotaFiscal,
      dataNascimento, situacao, relacaoComercial, enderecos
    );
  }

  private static Predicate toPredicate(
    PessoaFilter filter, CamposBusca campos,
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    return PredicateBuilder.create(builder)
      .add(filter.getNome(), (String e) ->
        builder.or(
          iLike(builder, campos.nome(), e),
          iLike(builder, campos.nomeFantasia(), e),
          iLike(builder, campos.nomeInterno(), e)
        ))
      .add(filter.getNomeInterno(), (String e) ->
        iLike(builder, campos.nomeInterno(), e))
      .add(filter.getEmail(), (String e) ->
        builder.or(
          iLike(builder, campos.emailNotaFiscal(), e),
          iLike(builder, campos.emailCobranca(), e)
        ))
      .add(filter.getCriterio(), (String e) -> {
        if (e.trim().matches("\\d+")) {
          return builder.like(campos.cpfCnpj(), "%" + removePunctuation(e) + "%");
        }
        return builder.or(
          iLike(builder, campos.nome(), e),
          iLike(builder, campos.nomeFantasia(), e),
          iLike(builder, campos.nomeInterno(), e)
        );
      })
      .add(filter.getEstado(), (String e) ->
        builder.or(
          iLike(builder, campos.ufNome(), e),
          iLike(builder, campos.uf(), e)
        ))
      .add(filter.getCidade(), (String e) ->
        builder.or(
          iLike(builder, campos.cidade(), e),
          iLike(builder, campos.codigoCidade(), e)
        ))
      .add(filter.getId(), (Integer e) ->
        builder.equal(campos.id(), e))
      .add(filter.getSituacao(), (SituacaoParceiro e) ->
        builder.equal(campos.situacao(), e))
      .add(StringUtils.replaceAll(filter.getCpfCnpj()), (String e) ->
        iLike(builder, campos.cpfCnpj(), e))
      .add(filter.getRelacionamentos(), (List<TipoRelacionamento> e) -> {
        Subquery<Integer> subquery = query.subquery(Integer.class);
        Root<PessoaRelacaoComercial> subqueryRoot = subquery.from(PessoaRelacaoComercial.class);
        return builder.exists(
          subquery.select(builder.literal(1))
            .where(builder.and(
              builder.equal(subqueryRoot.join(PessoaRelacaoComercial_.pessoa), pessoa),
              subqueryRoot.get(PessoaRelacaoComercial_.relacaoComercial).in(e)
            ))
        );
      })
      .add(filter.getDataInicioUltimaCompra(), (LocalDate e) -> {
        Subquery<Integer> subquery = query.subquery(Integer.class);
        Root<Pedido> subqueryRoot = subquery.from(Pedido.class);
        return builder.exists(
          subquery.select(builder.literal(1))
            .where(builder.and(
              builder.equal(subqueryRoot.get(Pedido_.cliente), pessoa),
              builder.between(
                subqueryRoot.get(Pedido_.dataPedido),
                LocalDateTime.of(e, LocalTime.MIN),
                LocalDateTime.of(filter.getDataFinalUltimaCompra(), LocalTime.MAX)
              ),
              builder.equal(subqueryRoot.get(Pedido_.situacao), NF_EMITIDA)
            ))
        );
      })
      .add(filter.getTelefone(), (String e) ->
        iLike(builder, campos.telefone(), removePunctuation(e)))
      .add(filter.getIdsRetorno(), (List<Integer> e) -> campos.id().in(e))
      .add(filter.getIdPessoasIgnoradas(), (List<Integer> e) -> builder.not(campos.id().in(e)))
      .and();
  }

  private static Predicate toPredicate(
    PessoaApiExternaFilter filter, CamposBuscaApiExterna campos,
    Root<Pessoa> pessoa, CriteriaQuery<?> query, CriteriaBuilder builder
  ) {
    return PredicateBuilder.create(builder)
      .add(filter.criterio(), v -> builder.or(
        iLike(builder, campos.cpfCnpj(), v),
        iLike(builder, campos.nome(), v), iLike(builder, campos.nomeFantasia(), v)
      ))
      .add(filter.tipo(), v -> builder.equal(campos.tipo(), v))
      .add(filter.cpfCnpj(), v -> iLike(builder, campos.cpfCnpj(), v))
      .add(filter.nome(), v -> iLike(builder, campos.nome(), v))
      .add(filter.nomeFantasia(), v -> iLike(builder, campos.nomeFantasia(), v))
      .add(filter.situacao(), v -> builder.equal(campos.situacao(), v))
      .add(filter.relacaoComercial(), v -> {
        Subquery<Integer> subQuery = query.subquery(Integer.class);
        Root<PessoaRelacaoComercial> pessoaRelacaoComercial = subQuery.from(PessoaRelacaoComercial.class);
        return builder.exists(subQuery
          .select(builder.literal(1))
          .where(builder.and(
            builder.equal(
              pessoaRelacaoComercial.join(PessoaRelacaoComercial_.pessoa).get(Pessoa_.id),
              pessoa.get(Pessoa_.id)
            ),
            pessoaRelacaoComercial.get(PessoaRelacaoComercial_.relacaoComercial).in(v)
          )));
      })
      .and();
  }

  private List<Order> toOrderBy(
    Pageable pageable, CamposBusca campos, CriteriaBuilder builder
  ) {
    List<String> disponiveis = List.of(
      "tipo", "cpfCnpj", "situacao", "nome", "nomeFantasia", "nomeInterno", "cidade",
      "codigoCidade", "estado", "uf", "ufNome", "telefone", "vendedorNomeFantasia", "nomeTitularConta",
      "cpfTitularConta", "chavePixConta", "bancoCodigo", "bancoNome", "agenciaConta",
      "numeroConta", "email", "emailCobranca", "emailNotaFiscal"
    );
    return pageable.getSort().stream()
      .filter(o -> disponiveis.contains(o.getProperty()))
      .map(o -> switch (o.getProperty()) {
        case "tipo" -> o.getDirection().isDescending()
          ? builder.desc(campos.tipo())
          : builder.asc(campos.tipo());
        case "cpfCnpj" -> o.getDirection().isDescending()
          ? builder.desc(campos.cpfCnpj())
          : builder.asc(campos.cpfCnpj());
        case "situacao" -> o.getDirection().isDescending()
          ? builder.desc(campos.situacao())
          : builder.asc(campos.situacao());
        case "nome" -> o.getDirection().isDescending()
          ? builder.desc(campos.nome())
          : builder.asc(campos.nome());
        case "nomeFantasia" -> o.getDirection().isDescending()
          ? builder.desc(campos.nomeFantasia())
          : builder.asc(campos.nomeFantasia());
        case "nomeInterno" -> o.getDirection().isDescending()
          ? builder.desc(campos.nomeInterno())
          : builder.asc(campos.nomeInterno());
        case "cidade" -> o.getDirection().isDescending()
          ? builder.desc(campos.cidade())
          : builder.asc(campos.cidade());
        case "codigoCidade" -> o.getDirection().isDescending()
          ? builder.desc(campos.codigoCidade())
          : builder.asc(campos.codigoCidade());
        case "estado", "uf" -> o.getDirection().isDescending()
          ? builder.desc(campos.uf())
          : builder.asc(campos.uf());
        case "ufNome" -> o.getDirection().isDescending()
          ? builder.desc(campos.ufNome())
          : builder.asc(campos.ufNome());
        case "telefone" -> o.getDirection().isDescending()
          ? builder.desc(campos.telefone())
          : builder.asc(campos.telefone());
        case "vendedorNomeFantasia" -> o.getDirection().isDescending()
          ? builder.desc(campos.vendedorNomeFantasia())
          : builder.asc(campos.vendedorNomeFantasia());
        case "nomeTitularConta" -> o.getDirection().isDescending()
          ? builder.desc(campos.nomeTitularConta())
          : builder.asc(campos.nomeTitularConta());
        case "cpfTitularConta" -> o.getDirection().isDescending()
          ? builder.desc(campos.cpfTitularConta())
          : builder.asc(campos.cpfTitularConta());
        case "chavePixConta" -> o.getDirection().isDescending()
          ? builder.desc(campos.chavePixConta())
          : builder.asc(campos.chavePixConta());
        case "bancoCodigo" -> o.getDirection().isDescending()
          ? builder.desc(campos.bancoCodigo())
          : builder.asc(campos.bancoCodigo());
        case "bancoNome" -> o.getDirection().isDescending()
          ? builder.desc(campos.bancoNome())
          : builder.asc(campos.bancoNome());
        case "agenciaConta" -> o.getDirection().isDescending()
          ? builder.desc(campos.agenciaConta())
          : builder.asc(campos.agenciaConta());
        case "numeroConta" -> o.getDirection().isDescending()
          ? builder.desc(campos.numeroConta())
          : builder.asc(campos.numeroConta());
        case "email" -> o.getDirection().isDescending()
          ? builder.desc(campos.emailCobranca())
          : builder.asc(campos.emailCobranca());
        case "emailCobranca" -> o.getDirection().isDescending()
          ? builder.desc(campos.emailCobranca())
          : builder.asc(campos.emailCobranca());
        case "emailNotaFiscal" -> o.getDirection().isDescending()
          ? builder.desc(campos.emailNotaFiscal())
          : builder.asc(campos.emailNotaFiscal());
        default -> throw new IllegalStateException("Unexpected value: " + o.getProperty());
      }).toList();
  }

  private List<Order> toOrderBy(
    Pageable pageable, CamposBuscaApiExterna campos, CriteriaBuilder builder
  ) {
    var disponiveis = List.of("tipo", "cpfCnpj", "nome", "nomeFantasia", "situacao");
    return pageable.getSort().stream()
      .filter(o -> disponiveis.contains(o.getProperty()))
      .map(o -> switch (o.getProperty()) {
        case "tipo" -> o.getDirection().isDescending()
          ? builder.desc(campos.tipo())
          : builder.asc(campos.tipo());
        case "cpfCnpj" -> o.getDirection().isDescending()
          ? builder.desc(campos.cpfCnpj())
          : builder.asc(campos.cpfCnpj());
        case "nome" -> o.getDirection().isDescending()
          ? builder.desc(campos.nome())
          : builder.asc(campos.nome());
        case "nomeFantasia" -> o.getDirection().isDescending()
          ? builder.desc(campos.nomeFantasia())
          : builder.asc(campos.nomeFantasia());
        case "situacao" -> o.getDirection().isDescending()
          ? builder.desc(campos.situacao())
          : builder.asc(campos.situacao());
        default -> throw new IllegalStateException("Unexpected value: " + o.getProperty());
      }).toList();
  }

}
