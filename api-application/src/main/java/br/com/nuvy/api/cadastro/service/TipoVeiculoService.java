package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.model.TipoVeiculo;
import br.com.nuvy.api.cadastro.repository.TipoVeiculoRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import org.springframework.stereotype.Service;

@Service
public class TipoVeiculoService extends
  PageableServiceAdapter<Tipo<PERSON><PERSON><PERSON><PERSON>, Integer, NoFilter, TipoVeiculoRepository> {

  public TipoVeiculo findTipoVeiculoOutros() {
    return repository.findTipoVeiculoOutros();
  }
}
