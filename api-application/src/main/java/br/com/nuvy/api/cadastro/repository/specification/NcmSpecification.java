package br.com.nuvy.api.cadastro.repository.specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;
import static br.com.nuvy.common.query.CriteriaUtils.iLikeIniciaCom;

import br.com.nuvy.api.cadastro.filter.NcmFilter;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.model.Ncm_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
public class NcmSpecification implements Specification<Ncm> {

  private final NcmFilter filter;

  @Override
  public Predicate toPredicate(Root<Ncm> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    var predicate = predicateBuilder
      .add(filter.getDescricao(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.get(Ncm_.descricao)),
          e.toUpperCase() + "%"))
      .add(filter.getIdNcmsIgnorados(),
        e -> criteriaBuilder.not(root.get(Ncm_.id).in(e)))
      .add(filter.getCriterio(), e -> criteriaBuilder.or(
        iLike(criteriaBuilder, root.get(Ncm_.descricao), e),
        iLikeIniciaCom(criteriaBuilder, root.get(Ncm_.id), e)));
    if (filter.getIsOitoDigitos() != null && filter.getIsOitoDigitos()) {
      predicate.add(true, e -> criteriaBuilder.equal(criteriaBuilder.length(root.get(Ncm_.id)), 8));
    }
    return predicate.and();
  }
}
