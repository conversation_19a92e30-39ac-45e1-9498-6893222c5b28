package br.com.nuvy.api.estoque.repository;

import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.estoque.model.Movimento;
import br.com.nuvy.api.estoque.model.Rastreabilidade;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MovimentoRepository extends NuvyRepository<Movimento, Integer> {

  Integer countByRastreabilidadeId(Integer rastreabilidadeId);

  Movimento findTopByProdutoAndIdNotOrderByDataMovimentacaoDesc(
    Produto produto, Integer movimentoId
  );

  List<Movimento> findByRastreabilidade(Rastreabilidade rastreabilidade);

  List<Movimento> findByProdutoId(Integer produtoId);

  boolean existsByProdutoId(Integer id);

  List<Movimento> findByProdutoIdAndDepositoIdAndRastreabilidadeNull(
    Integer produtoId, Integer depositoId
  );

  List<Movimento> findByProdutoIdAndDepositoIdAndRastreabilidadeId(
    Integer produtoId, Integer depositoId, Integer rastreabilidadeId
  );

  Optional<Movimento> findByPedidoItemEstoqueId(UUID pedidoItemEstoqueId);

  boolean existsByPedidoItemEstoqueId(UUID pedidoItemEstoqueId);

  @Query("""
    SELECT
       pro.id
    FROM
       Produto pro
    INNER JOIN pro.categoriaProduto cp
    INNER JOIN pro.unidadeMedida um
    WHERE
      pro.situacao = 'ATIVO' AND
      pro.variacaoPai = false AND
      cp.situacao = 'ATIVO'
    """)
  List<Integer> findAllProdutosSincronizarPos();

  @Query("""
    SELECT
       pro.id
    FROM
       Produto pro
    INNER JOIN pro.categoriaProduto cp
    WHERE
      cp.id = :idCategoria AND
      cp.situacao = 'ATIVO' AND
      pro.situacao = 'ATIVO' AND
      pro.variacaoPai = false
    """)
  List<Integer> findAllProdutosCategoriaSincronizarPosPdv(Integer idCategoria);

  @Query("""
    SELECT
        cp.id
    FROM
        CategoriaProduto cp
    WHERE
        cp.situacao = 'ATIVO' AND
        EXISTS (
            SELECT 1 FROM Produto pro
            INNER JOIN
                pro.categoriaProduto procp
            WHERE
                pro.situacao = 'ATIVO' AND
                procp.id = cp.id
        )
    """)
  List<Integer> findAllCategoriasSincronizarPosPdv();

  boolean existsByPosPdvVendaItemId(Integer posPdvVendaItemId);

  Optional<Movimento> findFirstByPosPdvVendaItemId(Integer posPdvVendaItemId);

  Optional<Movimento> findByNotaFiscalEntradaItemEstoqueId(
    UUID notaFiscalEntradaItemEstoqueId
  );

  @Query("""
    SELECT CASE WHEN COUNT(m) > 0 THEN true ELSE false END
    FROM Movimento m
    WHERE m.produto.id = :produtoId
      AND m.dataMovimentacao > :dataMovimentacao
      AND m.id != :id
  """)
  boolean existsMovimentoPosterior(@Param("id") Integer id,
    @Param("produtoId") Integer produtoId,
    @Param("dataMovimentacao") LocalDateTime dataMovimentacao);

  Movimento findFirstByProdutoIdAndDepositoIdOrderByDataMovimentacaoDesc(
    Integer produtoId,
    Integer depositoId
  );

  void deleteByProdutoId(Integer produtoId);

  int deleteByPedidoItemEstoqueIdIn(List<UUID> pedidoItemEstoqueIds);

  int deleteByPosPdvVendaItemIdIn(List<Integer> posPdvVendaItemIds);

  int deleteByNotaFiscalEntradaItemEstoqueIdIn(List<UUID> notaFiscalEntradaItemEstoqueIds);
}
