package br.com.nuvy.api.cadastro.importacaonfe.models;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class eICMSTot {

  private String vProd;
  private String vFrete;
  private String vDesc;
  private String vICMS;
  private String vST;
  private String vIPI;
  private String vNF;
  private String vFCP;
  private String vFCPUFDest;
  private String vICMSUFDest;
  private String vFCPST;

  public String getVProd() {
    return vProd != null && !vProd.isEmpty() ? vProd : "0";
  }

  public String getVFrete() {
    return vFrete != null && !vFrete.isEmpty() ? vFrete : "0";
  }

  public String getVDesc() {
    return vDesc != null && !vDesc.isEmpty() ? vDesc : "0";
  }

  public String getVICMS() {
    return vICMS != null && !vICMS.isEmpty() ? vICMS : "0";
  }

  public String getVST() {
    return vST != null && !vST.isEmpty() ? vST : "0";
  }

  public String getVIPI() {
    return vIPI != null && !vIPI.isEmpty() ? vIPI : "0";
  }

  public String getVNF() {
    return vNF != null && !vNF.isEmpty() ? vNF : "0";
  }

  public String getVFCP() {
    return vFCP != null && !vFCP.isEmpty() ? vFCP : "0";
  }

  public String getVFCPUFDest() {
    return vFCPUFDest != null && !vFCPUFDest.isEmpty() ? vFCPUFDest : "0";
  }

  public String getVICMSUFDest() {
    return vICMSUFDest != null && !vICMSUFDest.isEmpty() ? vICMSUFDest : "0";
  }

  public String getVFCPST() {
    return vFCPST != null && !vFCPST.isEmpty() ? vFCPST : "0";
  }
}
