package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.outrasintegracoes.ConfiguracaoOutrasIntegracoes;
import br.com.nuvy.api.cadastro.model.outrasintegracoes.PessoaOutrasIntegracoes;
import br.com.nuvy.api.cadastro.repository.ConfiguracaoOutrasIntegracoesRepository;
import br.com.nuvy.api.cadastro.repository.PessoaOutrasIntegracoesRepository;
import br.com.nuvy.api.cadastro.repository.PessoaRepository;
import br.com.nuvy.api.enums.TipoRelacionamento;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PessoaOutrasIntegracoesService {

  private final ConfiguracaoOutrasIntegracoesRepository configuracaoRepository;

  private final PessoaOutrasIntegracoesRepository repository;

  private final PessoaRepository pessoaRepository;

  public Optional<PessoaOutrasIntegracoes> findByConfiguracaoIdAndPessoaIdAndTipo(
    Long idConfiguracao, Integer idPessoa, TipoRelacionamento tipo
  ) {
    return repository.findByConfiguracaoIdAndPessoaIdAndTipo(idConfiguracao, idPessoa, tipo);
  }

  @Transactional
  public void inserir(
    Long idConfiguracao, Integer idPessoa, String idExterno,
    TipoRelacionamento tipo
  ) {
    repository.save(new PessoaOutrasIntegracoes(
      pessoaRepository.getReferenceById(idPessoa),
      configuracaoRepository.getReferenceById(idConfiguracao),
      idExterno, tipo
    ));
  }

  @Transactional
  public PessoaOutrasIntegracoes inserir(
    ConfiguracaoOutrasIntegracoes configuracao, Pessoa pessoa, String idExterno,
    TipoRelacionamento tipo
  ) {
    return repository.save(new PessoaOutrasIntegracoes(
      pessoaRepository.getReferenceById(pessoa.getId()),
      configuracaoRepository.getReferenceById(configuracao.getId()),
      idExterno, tipo
    ));
  }

  @Transactional
  public void alterar(
    Long id, String idExterno
  ) {
    repository.updateIdExternoById(id, idExterno, LocalDateTime.now());
  }

  @Transactional
  public void alterar(
    PessoaOutrasIntegracoes entidade, String idExterno
  ) {
    repository.updateIdExternoById(entidade.getId(), idExterno, LocalDateTime.now());
  }
}
