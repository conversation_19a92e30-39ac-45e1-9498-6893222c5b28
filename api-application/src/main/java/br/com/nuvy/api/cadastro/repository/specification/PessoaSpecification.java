package br.com.nuvy.api.cadastro.repository.specification;

import static br.com.nuvy.api.notafiscal.dto.SituacaoPedido.NF_EMITIDA;
import static br.com.nuvy.common.query.CriteriaUtils.iLike;
import static br.com.nuvy.common.utils.StringUtils.removePunctuation;

import br.com.nuvy.api.cadastro.filter.PessoaFilter;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa_;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.Pedido_;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.common.utils.StringUtils;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.ListJoin;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.SetJoin;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "of")
public class PessoaSpecification implements Specification<Pessoa> {

  private final transient PessoaFilter filter;

  @Override
  public Predicate toPredicate(Root<Pessoa> root, CriteriaQuery<?> query,
    CriteriaBuilder cb) {
    query.distinct(true);
    PredicateBuilder predicateBuilder = PredicateBuilder.create(cb);
    var predicateOr = predicateBuilder
      .add(filter.getEmail(),
        e -> iLike(cb, root.get(Pessoa_.email), e.toUpperCase()))
      .add(filter.getEmail(),
        e -> iLike(cb, root.get(Pessoa_.emailCobranca), e.toUpperCase()))
      .add(filter.getNomeInterno(),
        e -> iLike(cb, root.get(Pessoa_.nomeInterno), e.toUpperCase()))
      .add(filter.getEmail(),
        e -> iLike(cb, root.get(Pessoa_.emailNotaFiscal), e.toUpperCase()));
    if (filter.getNome() != null && filter.getNome().trim().matches("\\d+")) {
      predicateOr.add(removePunctuation(filter.getNome()),
        e -> cb.like(root.get(Pessoa_.cpfCnpj), "%" + e + "%"));
    } else {
      predicateOr.add(filter.getNome(),
        e -> iLike(cb, root.get(Pessoa_.nome), e.toUpperCase()));
      predicateOr.add(filter.getNome(),
        e -> iLike(cb, root.get(Pessoa_.nomeFantasia), e.toUpperCase()));
    }

    if (filter.getCriterio() != null && filter.getCriterio().trim().matches("\\d+")) {
      predicateOr.add(removePunctuation(filter.getCriterio()),
        e -> cb.like(root.get(Pessoa_.cpfCnpj), "%" + e + "%"));
    } else {
      predicateOr.add(filter.getCriterio(),
        e -> iLike(cb, root.get(Pessoa_.nome), e.toUpperCase()));
      predicateOr.add(filter.getCriterio(),
        e -> iLike(cb, root.get(Pessoa_.nomeFantasia), e.toUpperCase()));
      predicateOr.add(filter.getCriterio(),
        e -> iLike(cb, root.get(Pessoa_.nomeInterno), e.toUpperCase()));
    }

    ListJoin<Pessoa, EnderecoPessoa> enderecosUf = root.join(Pessoa_.enderecos, JoinType.LEFT);
    Predicate enderecoFiscal = cb.equal(enderecosUf.get(EnderecoPessoa_.tipo), TipoEndereco.FISCAL);
    var predicateUfOr = PredicateBuilder.create(cb)
      .add(filter.getEstado(),
        e -> cb.and(enderecoFiscal,
          iLike(cb, enderecosUf.get(EnderecoPessoa_.ufNome), e.toUpperCase() + "%")))
      .add(filter.getEstado(),
        e -> cb.and(enderecoFiscal,
          iLike(cb, enderecosUf.get(EnderecoPessoa_.uf), e.toUpperCase() + "%")))
      .or();
    var predicateCidadeOr = PredicateBuilder.create(cb)
      .add(filter.getCidade(),
        e -> cb.and(enderecoFiscal,
          iLike(cb, enderecosUf.get(EnderecoPessoa_.cidade), e.toUpperCase() + "%")))
      .add(filter.getCidade(),
        e -> cb.and(enderecoFiscal,
          iLike(cb, enderecosUf.get(EnderecoPessoa_.codigoCidade), e.toUpperCase() + "%")))
      .or();
    var predicateAnd = PredicateBuilder.create(cb)
      .add(filter.getId(),
        e -> cb.equal(root.get(Pessoa_.id), e))
      .add(filter.getSituacao(),
        e -> cb.equal(root.get(Pessoa_.situacao), e))
      .add(StringUtils.replaceAll(filter.getCpfCnpj()),
        e -> iLike(cb, root.get(Pessoa_.cpfCnpj), e))
      .add(filter.getRelacionamentos(),
        e -> {
          SetJoin<Pessoa, TipoRelacionamento> relacionamentos = root.join(Pessoa_.relacaoComercial);
          return relacionamentos.in(e);
        })
      .add(Objects.nonNull(filter.getDataInicioUltimaCompra()) ? LocalDateTime.of(
          filter.getDataInicioUltimaCompra(), LocalTime.MIDNIGHT) : null,
        e -> {
          ListJoin<Pessoa, Pedido> pedidoListJoin = root.join(Pessoa_.pedido, JoinType.LEFT);
          return cb.and(cb.between(pedidoListJoin.get(Pedido_.dataPedido), e,
              LocalDateTime.of(filter.getDataFinalUltimaCompra(), LocalTime.MIDNIGHT)),
            cb.equal(pedidoListJoin.get(Pedido_.situacao), NF_EMITIDA));
        })
      .add(StringUtils.replaceAll(filter.getTelefone()),
        e -> iLike(cb, root.get(Pessoa_.telefone), e))
      .add(filter.getIdsRetorno(),
        e -> root.get(Pessoa_.id).in(e))
      .add(filter.getIdPessoasIgnoradas(),
        e -> cb.not(root.get(Pessoa_.id).in(e)))
      .and();
    return cb.and(predicateOr.or(), predicateUfOr, predicateCidadeOr, predicateAnd);
  }
}