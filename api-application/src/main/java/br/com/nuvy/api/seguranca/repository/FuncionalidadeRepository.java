package br.com.nuvy.api.seguranca.repository;

import br.com.nuvy.api.seguranca.model.Funcionalidade;
import br.com.nuvy.api.seguranca.model.PerfilFuncionalidade;
import br.com.nuvy.common.base.repository.NuvyRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface FuncionalidadeRepository extends NuvyRepository<Funcionalidade, String> {

  List<Funcionalidade> findAllByModuloId(String moduloId);


}
