package br.com.nuvy.api.anuncio.repository;

import br.com.nuvy.api.anuncio.Catalogo;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CatalogoRepository extends NuvyRepository<Catalogo, Integer> {

  @Query("""
    SELECT c FROM Catalogo c
    WHERE c.situacao = 'ATIVO' AND SIZE(c.canalVendas) > 0 AND
       (c IN (SELECT cp.catalogo FROM CatalogoProduto cp WHERE cp.produto.id = :produtoId AND cp.deveDesconsiderar = false)
       OR EXISTS (SELECT cc FROM CatalogoCategoria cc WHERE cc.catalogo = c AND cc.categoria IN
       (SELECT p.categoriaProduto FROM Produto p WHERE p.id = :produtoId AND NOT EXISTS
           (SELECT cp FROM CatalogoProduto cp WHERE cp.produto = p AND cp.deveDesconsiderar = true)
       )))""")
  List<Catalogo> findCatalogosByProdutoIdAndContainingCanaisVenda(@Param("produtoId") Integer produtoId);

  @Query("""
    SELECT COALESCE(
        (SELECT CASE WHEN
            EXISTS (SELECT 1 FROM CatalogoProduto cp WHERE cp.produto.id = p.id AND cp.deveDesconsiderar = false) OR
            (p.categoriaProduto IS NOT NULL AND EXISTS (SELECT 1 FROM CatalogoCategoria cc WHERE cc.categoria.id = p.categoriaProduto.id))
        THEN TRUE ELSE FALSE END
        FROM Produto p
        WHERE p.id = :produtoId),
        FALSE
    )""")
  Boolean isProdutoRelatedToCatalogo(@Param("produtoId") Integer produtoId);

}
