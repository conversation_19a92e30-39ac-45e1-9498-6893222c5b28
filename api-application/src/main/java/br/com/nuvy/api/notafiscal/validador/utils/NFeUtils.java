package br.com.nuvy.api.notafiscal.validador.utils;

import java.util.Map;

public class NFeUtils {

  /**
   * Retorna o codigo da UF
   *
   * @param siglaUf Sigla da UF
   * @return string com o codigo da UF
   */
  public static String getCUF(String siglaUf) {

    Map<String, String> ufs = Map.ofEntries(
      Map.entry("AC", "12"),
      Map.entry("AL", "27"),
      Map.entry("AM", "13"),
      Map.entry("AP", "16"),
      Map.entry("BA", "29"),
      Map.entry("CE", "23"),
      Map.entry("DF", "53"),
      Map.entry("ES", "32"),
      Map.entry("GO", "52"),
      Map.entry("MA", "21"),
      Map.entry("MG", "31"),
      Map.entry("MS", "50"),
      Map.entry("MT", "51"),
      Map.entry("PA", "15"),
      Map.entry("PB", "25"),
      Map.entry("PE", "26"),
      Map.entry("PI", "22"),
      Map.entry("PR", "41"),
      Map.entry("RJ", "33"),
      Map.entry("RN", "24"),
      Map.entry("RO", "11"),
      Map.entry("RR", "14"),
      Map.entry("RS", "43"),
      Map.entry("SC", "42"),
      Map.entry("SE", "28"),
      Map.entry("SP", "35"),
      Map.entry("TO", "17"));

    return ufs.get(siglaUf);

  }

}
