package br.com.nuvy.api.estoque.service;


import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.api.estoque.model.Movimento;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.BadgeContext;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class AjusteEstoque implements EditorMovimentoEstoque, CriadorMovimentoEstoque {

  private final SaldoService saldoService;
  private final MovimentoService movimentoService;
  private final DepositoService depositoService;

  @Transactional
  public void executeCriador(
    Movimento entidade, Deposito depositoDestino, Boolean primeiroMovimento
  ) {
    var saldoTotal = saldoService.calculaEstoque(
      entidade.getProduto(), entidade.getDeposito(), entidade.getRastreabilidade()
    );
    var ajuste = entidade.getQuantidade();
    var novaQuantidade = saldoTotal.subtract(ajuste);
    var custoNovaEntrada = entidade.getValorCusto();
    entidade.setQuantidade(novaQuantidade.negate());
    entidade.setQuantidadeInformada(entidade.getQuantidade());
    entidade.setValorCustoMedio(BigDecimal.ZERO);
    entidade.setQuantidadeAtual(BigDecimal.ZERO);
    entidade.setIsOriginadoPedido(false);
    movimentoService.create(entidade);
    saldoService.atualizarSaldo(entidade);
    movimentoService.atualizaProduto(entidade, custoNovaEntrada, false, BigDecimal.ZERO);
  }

  @Transactional
  public void executeEditor(Movimento oldEntity, Movimento newEntity, Deposito depositoDestino) {
    BigDecimal quantidade = newEntity.getQuantidade();
    BigDecimal novaQuantidade = saldoService.calculaEstoque(
        oldEntity.getProduto(), oldEntity.getDeposito()
      )
      .subtract(oldEntity.getQuantidade())
      .subtract(quantidade);
    BigDecimal custoNovaEntrada = newEntity.getValorCusto();
    oldEntity.setQuantidade(novaQuantidade.negate());
    oldEntity.setQuantidadeInformada(quantidade);
    Deposito deposito = depositoService.findById(newEntity.getDeposito().getId())
      .orElseThrow(() -> new ResourceNotFoundException("Deposito não encontrado"));
    oldEntity.setDeposito(deposito);
    oldEntity.setRastreabilidade(newEntity.getRastreabilidade());
    oldEntity.setUsuario(BadgeContext.getUsuario());
    oldEntity.setDataMovimentacao(newEntity.getDataMovimentacao());
    oldEntity.setObservacao(newEntity.getObservacao());
    oldEntity.setValorCusto(newEntity.getValorCusto());
    oldEntity.setIsOriginadoPedido(oldEntity.getIsOriginadoPedido());
    movimentoService.update(oldEntity.getId(), oldEntity);
    saldoService.atualizarSaldo(oldEntity);
    movimentoService.atualizaProdutoEditor(oldEntity, BigDecimal.ZERO, custoNovaEntrada);
  }
}