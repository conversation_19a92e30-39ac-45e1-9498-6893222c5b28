package br.com.nuvy.api.adaptadores.saida.integracao.assas.mapeador;

import br.com.nuvy.api.adaptadores.saida.integracao.assas.dto.JsonBoletoAssas;
import br.com.nuvy.integracao.assas.dominio.entidade.BoletoAssas;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface MapeadorBoletoAssas {

  @Mapping(target = "isEnviaCobrancaCorreios", source = "enviaCobrancaCorreios")
  JsonBoletoAssas converte(BoletoAssas entidade);

  @Mapping(target = "isEnviaCobrancaCorreios", ignore = true)
  @Mapping(target = "idClienteAssas", ignore = true)
  @Mapping(target = "formaPagamento", ignore = true)
  @Mapping(target = "descricao", ignore = true)
  @Mapping(target = "diasAposVencimentoParaCancelamentoRegistro", ignore = true)
  @Mapping(target = "referenciaExterna", ignore = true)
  JsonBoletoAssas converteAlteracao(BoletoAssas entidade);
}
