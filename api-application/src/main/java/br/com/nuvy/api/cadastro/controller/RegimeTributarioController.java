package br.com.nuvy.api.cadastro.controller;

import br.com.nuvy.api.cadastro.dto.RegimeTributarioDto;
import br.com.nuvy.api.cadastro.model.RegimeTributario;
import br.com.nuvy.api.cadastro.service.RegimeTributarioService;
import br.com.nuvy.common.base.controller.ControllerBase;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Cadastro / Regime Tributário")
@RestController
@RequestMapping("/v1/cadastro/regime-tributario")
@RequiredArgsConstructor
public class RegimeTributarioController extends ControllerBase<RegimeTributario> {

  private final RegimeTributarioService service;

  @GetMapping
  @Operation(summary = "Recuperar todos", description = "Lista todos os regimes tributários")
  public List<RegimeTributarioDto> findAll(@ParameterObject Sort sort) {
    if (sort.isUnsorted()) {
      sort = Sort.by("id");
    }
    List<RegimeTributarioDto> result = service.findAll(sort).stream()
      .map(RegimeTributarioDto::from)
      .collect(Collectors.toList());
    return result;
  }

  @GetMapping("/{id}")
  @Operation(summary = "Recuperar por Id", description = "Recupera o recurso conforme o id informado")
  public RegimeTributarioDto findById(@PathVariable Integer id) {
    Optional<RegimeTributario> result = service.findById(id);
    if (result.isEmpty()) {
      throw new ResourceNotFoundException();
    }
    fireSingleResourceRetrievedEvent();
    return result.map(RegimeTributarioDto::from)
      .get();
  }
}
