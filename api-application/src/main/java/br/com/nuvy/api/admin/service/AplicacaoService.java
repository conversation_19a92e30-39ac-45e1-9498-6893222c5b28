package br.com.nuvy.api.admin.service;

import br.com.nuvy.api.admin.Aplicacao;
import br.com.nuvy.api.admin.filter.AplicacaoFilter;
import br.com.nuvy.api.admin.repository.AplicacaoRepository;
import br.com.nuvy.api.admin.repository.specification.AplicacaoSpecification;
import java.util.List;
import java.util.Optional;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AplicacaoService {

  private final AplicacaoRepository aplicacaoRepository;

  public List<Aplicacao> findAll() {
    return aplicacaoRepository.findAll();
  }

  protected AplicacaoSpecification configureSpecification(AplicacaoFilter filter) {
    return new AplicacaoSpecification(filter);
  }

  public Page<Aplicacao> find(AplicacaoFilter filter, Pageable pageable) {
    AplicacaoSpecification specification = configureSpecification(filter);
    return aplicacaoRepository.findAll(specification, pageable);
  }

  public Optional<Aplicacao> findById(String id) {
    return aplicacaoRepository.findById(id);
  }

  public void updateSituacaoAplicacaoPorIdApp(String idAplicacao, String situacao) {
    aplicacaoRepository.updateAplicacaoPorIdApp(idAplicacao, situacao);
  }

  public void updateNomeAplicacaoPorIdApp(String idAplicacao, String nome) {
    aplicacaoRepository.updateNomeAplicacaoPorIdApp(idAplicacao, nome);
  }

  public Aplicacao required(String id, String message) {
    Optional<Aplicacao> value = findById(id);
    if (value.isEmpty()) {
      throw new ResourceNotFoundException(message);
    }
    return value.get();
  }

  public Aplicacao required(String id) {
    return required(id, ResourceNotFoundException.DEFAULT_MESSAGE);
  }
}
