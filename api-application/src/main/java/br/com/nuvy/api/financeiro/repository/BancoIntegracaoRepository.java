package br.com.nuvy.api.financeiro.repository;

import br.com.nuvy.api.financeiro.dto.BancoIntegracaoDto;
import br.com.nuvy.api.financeiro.model.BancoIntegracao;
import br.com.nuvy.common.base.repository.NuvyRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface BancoIntegracaoRepository extends NuvyRepository<BancoIntegracao, Integer> {

  List<BancoIntegracao> findByBancoId(Integer bancoId);


}
