package br.com.nuvy.api.financeiro.repository;

import br.com.nuvy.api.enums.SituacaoBoleto;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBoleto;
import br.com.nuvy.base.AplicacaoIdEmpresaId;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import feign.Param;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TituloBoletoRepository extends NuvyRepository<TituloBoleto, Integer> {

  Optional<TituloBoleto> findByTituloId(Integer tituloId);

  Optional<TituloBoleto> findByTituloIdAndSituacaoNot(Integer tituloId, SituacaoBoleto situacao);

  Optional<TituloBoleto> findByTituloIdAndSituacao(Integer tituloId, SituacaoBoleto situacao);

  Optional<TituloBoleto> findByTituloIdAndSituacaoIn(Integer tituloId, List<SituacaoBoleto> situacao);

  Optional<TituloBoleto> findByIdServicoBoleto(String servicoBoletoId);

  List<TituloBoleto> findByIdRemessaGerada(Integer remessaId);

  Boolean existsByTituloId(Integer tituloId);

  Boolean existsByTituloIdAndSituacao(Integer tituloId, SituacaoBoleto situacao);

  List<TituloBoleto> findAllByTituloInAndSituacao(List<Titulo> titulos, SituacaoBoleto situacao);

  @Query(value = "SELECT a.id_aplicacao AS idAplicacao, a.id_empresa AS idEmpresa FROM fn_titulo_boleto a WHERE a.id_servico_boleto = :idServicoBoleto", nativeQuery = true)
  Optional<AplicacaoIdEmpresaId> findAplicacaoIdByServicoBoletoId(@Param("idServicoBoleto") String idServicoBoleto);

  @Query(value = """
    SELECT DISTINCT ON (t.id_pedido) t.id_pedido as idPedido,
           CASE
               WHEN EXISTS (SELECT 1
                            FROM fn_titulo_boleto tb
                                     INNER JOIN fn_titulo t2 ON tb.id_titulo = t2.id_titulo
                            WHERE tb.situacao_boleto = 'RECEBIDO'
                              AND t2.id_pedido = t.id_pedido) THEN TRUE
               ELSE FALSE
           END as existeBoleto
    FROM fn_titulo t
    WHERE t.id_pedido IN :ids
    """, nativeQuery = true)
  List<Object[]> verificaSeContemBoleto(List<UUID> ids);

  List<TituloBoleto> findByTituloIdAndSituacaoAndBoletoCarneNotNull(Integer tituloId, SituacaoBoleto situacao);

}
