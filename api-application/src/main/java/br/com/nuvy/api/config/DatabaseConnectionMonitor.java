package br.com.nuvy.api.config;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.util.Map;

/**
 * Monitora as estatísticas do pool de conexões Hikari.
 * Registra logs periódicos com informações sobre o pool de conexões.
 * Funciona mesmo quando o DataSource está envolvido em um AbstractRoutingDataSource (multi-tenancy).
 */
@Component
@EnableScheduling
@Slf4j
@ConditionalOnProperty(
  name = "nuvy.jobs.database-connection-monitor.enabled",
  havingValue = "true"
)
public class DatabaseConnectionMonitor {

    private final DataSource dataSource;

    @Value("${nuvy.jobs.database-connection-monitor.fixedrate}")
    private long fixedRate;

    @Autowired
    public DatabaseConnectionMonitor(DataSource dataSource) {
        this.dataSource = dataSource;
        log.info("Job Scheduled {} habilitado", DatabaseConnectionMonitor.class.getSimpleName());
        logPoolStats(); // Log inicial ao iniciar a aplicação
    }

    /**
     * Registra estatísticas do pool de conexões a cada minuto.
     * Mostra o total de conexões, conexões ativas, conexões ociosas e threads esperando.
     */
    @Scheduled(fixedRateString = "${nuvy.jobs.database-connection-monitor.fixed-rate}")
    public void logPoolStats() {
        try {
            // Verifica se é um DataSource direto
            if (dataSource instanceof HikariDataSource) {
                monitorHikariDataSource((HikariDataSource) dataSource);
            }
            // Verifica se é um AbstractRoutingDataSource (para multi-tenancy)
            else if (dataSource instanceof AbstractRoutingDataSource) {
                log.info("Detectado AbstractRoutingDataSource (sistema multi-tenant). Tentando acessar o HikariDataSource subjacente...");
                monitorRoutingDataSource();
            }
            else {
                log.warn("O DataSource {} não é uma instância suportada para monitoramento", dataSource.getClass().getName());
            }
        } catch (Exception e) {
            log.error("Erro ao monitorar o pool de conexões", e);
        }
    }

    /**
     * Monitora um HikariDataSource diretamente
     */
    private void monitorHikariDataSource(HikariDataSource hikariDS) {
        HikariPoolMXBean poolMXBean = hikariDS.getHikariPoolMXBean();

        if (poolMXBean != null) {
            log.info("Pool de conexões - Total: {}, Ativas: {}, Ociosas: {}, Esperando: {}",
                    poolMXBean.getTotalConnections(),
                    poolMXBean.getActiveConnections(),
                    poolMXBean.getIdleConnections(),
                    poolMXBean.getThreadsAwaitingConnection());

            // Alerta se o pool estiver com alta utilização
            if (poolMXBean.getActiveConnections() > hikariDS.getMaximumPoolSize() * 0.8) {
                log.warn("ALERTA: Pool de conexões com alta utilização ({}%). Configurado para max={}.",
                        (poolMXBean.getActiveConnections() * 100 / hikariDS.getMaximumPoolSize()),
                        hikariDS.getMaximumPoolSize());
            }
        } else {
            log.warn("Não foi possível obter estatísticas do pool Hikari (HikariPoolMXBean é null)");
        }

        // Mostra as configurações do pool
        log.info("Configuração do pool - Maximum: {}, Minimum Idle: {}, Connection Timeout: {}ms, Idle Timeout: {}ms",
                hikariDS.getMaximumPoolSize(),
                hikariDS.getMinimumIdle(),
                hikariDS.getConnectionTimeout(),
                hikariDS.getIdleTimeout());
    }

    /**
     * Tenta acessar o HikariDataSource dentro de um AbstractRoutingDataSource
     * usando reflexão para acessar os data sources subjacentes
     */
    private void monitorRoutingDataSource() {
        try {
            // Acessar o campo "resolvedDataSources" por reflexão
            Field resolvedDataSourcesField = AbstractRoutingDataSource.class.getDeclaredField("resolvedDataSources");
            resolvedDataSourcesField.setAccessible(true);

            Map<Object, DataSource> dataSources = (Map<Object, DataSource>) resolvedDataSourcesField.get(dataSource);

            log.info("Número de DataSources no AbstractRoutingDataSource: {}", dataSources.size());

            int i = 0;
            for (Map.Entry<Object, DataSource> entry : dataSources.entrySet()) {
                log.info("DataSource [{}] - Chave: {}, Tipo: {}",
                        i++, entry.getKey(), entry.getValue().getClass().getName());

                if (entry.getValue() instanceof HikariDataSource) {
                    HikariDataSource hikariDS = (HikariDataSource) entry.getValue();
                    log.info("HikariDataSource para chave {} encontrado:", entry.getKey());
                    monitorHikariDataSource(hikariDS);
                }
            }

            // Tentar acessar também o defaultTargetDataSource
            Field defaultTargetDataSourceField = AbstractRoutingDataSource.class.getDeclaredField("defaultTargetDataSource");
            defaultTargetDataSourceField.setAccessible(true);
            DataSource defaultDS = (DataSource) defaultTargetDataSourceField.get(dataSource);

            if (defaultDS != null) {
                log.info("DefaultTargetDataSource é do tipo: {}", defaultDS.getClass().getName());

                if (defaultDS instanceof HikariDataSource) {
                    log.info("DefaultTargetDataSource é HikariDataSource:");
                    monitorHikariDataSource((HikariDataSource) defaultDS);
                }
            }
        } catch (Exception e) {
            log.error("Erro ao acessar os DataSources subjacentes por reflexão", e);
        }
    }
}
