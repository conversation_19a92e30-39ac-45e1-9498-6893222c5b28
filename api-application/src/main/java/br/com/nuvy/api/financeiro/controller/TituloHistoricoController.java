package br.com.nuvy.api.financeiro.controller;

import br.com.nuvy.api.financeiro.dto.TituloHistoricoDto;
import br.com.nuvy.api.financeiro.service.TituloHistoricoServiceDto;
import br.com.nuvy.common.base.controller.PageableControllerAdapter;
import br.com.nuvy.common.base.filter.NoFilter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Financeiro / TítuloHistorico")
@RestController
@RequestMapping("/v1/financeiro/titulo/historico")
public class TituloHistoricoController extends
  PageableControllerAdapter<TituloHistoricoDto, Integer, NoFilter, TituloHistoricoServiceDto> {

}
