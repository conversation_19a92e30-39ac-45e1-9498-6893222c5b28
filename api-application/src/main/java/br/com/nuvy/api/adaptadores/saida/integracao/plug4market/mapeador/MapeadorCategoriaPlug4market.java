package br.com.nuvy.api.adaptadores.saida.integracao.plug4market.mapeador;

import br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto.JsonCategoriaPlug4market;
import br.com.nuvy.integracao.plug4market.dominio.entidade.CategoriaPlug4market;
import org.springframework.stereotype.Component;

@Component
public class MapeadorCategoriaPlug4market {

  public JsonCategoriaPlug4market converte(CategoriaPlug4market entidade) {
    return JsonCategoriaPlug4market.builder()
      .name(entidade.getNome())
      .alternativeId(entidade.getAlternativeId())
      .fatherAlternativeId(entidade.getFatherAlternativeId())
      .build();
  }
}
