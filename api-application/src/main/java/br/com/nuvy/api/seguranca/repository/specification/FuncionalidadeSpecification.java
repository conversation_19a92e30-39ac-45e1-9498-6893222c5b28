package br.com.nuvy.api.seguranca.repository.specification;

import br.com.nuvy.api.seguranca.dto.FuncionalidadeFilter;
import br.com.nuvy.api.seguranca.model.Funcionalidade;
import br.com.nuvy.api.seguranca.model.Funcionalidade_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import static br.com.nuvy.common.query.CriteriaUtils.iLike;

@RequiredArgsConstructor(staticName = "from")
@EqualsAndHashCode
public class FuncionalidadeSpecification implements Specification<Funcionalidade> {

  private final FuncionalidadeFilter filter;

  @Override
  public Predicate toPredicate(Root<Funcionalidade> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getId(),
        e -> iLike(criteriaBuilder, root.get(Funcionalidade_.id), e))
      .add(filter.getNome(),
        e -> iLike(criteriaBuilder, root.get(Funcionalidade_.nome), e))
      .add(filter.getSituacao(),
        e -> criteriaBuilder.equal(root.get(Funcionalidade_.situacao), e))
      .and();
  }
}