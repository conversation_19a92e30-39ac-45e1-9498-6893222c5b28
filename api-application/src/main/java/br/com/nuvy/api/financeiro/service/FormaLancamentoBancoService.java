package br.com.nuvy.api.financeiro.service;

import br.com.nuvy.api.financeiro.dto.FormaLancamentoBancoDto;
import br.com.nuvy.api.financeiro.model.FormaLancamentoBanco;
import br.com.nuvy.api.financeiro.repository.FormaLancamentoBancoRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FormaLancamentoBancoService extends
  PageableServiceAdapter<FormaLancamentoBanco, Integer, NoFilter, FormaLancamentoBancoRepository> {

  public List<FormaLancamentoBancoDto> findFormaLancamentoBanco() {
    List<FormaLancamentoBanco> formasLancamentosBanco = findAll();
    return formasLancamentosBanco.stream().map(FormaLancamentoBancoDto::from)
      .collect(Collectors.toList());
  }

}
