package br.com.nuvy.api.venda.event.listener;

import static br.com.nuvy.api.enums.SituacaoPedido.PEDIDO_AUTORIZADO;
import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.config.BadgeContext.getAplicacao;
import static br.com.nuvy.config.BadgeContext.getEmpresa;
import static br.com.nuvy.config.BadgeContext.getUsuario;

import br.com.nuvy.api.admin.service.NotificacaoService;
import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.enums.OrigemNotificacao;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoComprovante;
import br.com.nuvy.api.enums.TipoNotificacao;
import br.com.nuvy.api.estoque.service.MovimentoEstoquePorPedidoService;
import br.com.nuvy.api.financeiro.service.TituloPorPedidoService;
import br.com.nuvy.api.integration.service.ServicoNotificacao;
import br.com.nuvy.api.notificacao.dto.PayloadPedido;
import br.com.nuvy.api.notificacao.model.Notificacao;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.repository.UsuarioRepository;
import br.com.nuvy.api.sqs.dto.PedidoNotificationDto;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoHistorico;
import br.com.nuvy.api.venda.model.PedidoItem;
import br.com.nuvy.api.venda.model.PedidoRecebimento;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.api.venda.service.PedidoHistoricoService;
import br.com.nuvy.api.venda.service.PedidoRecebimentoService;
import br.com.nuvy.events.venda.AlteracaoSituacaoPedidoEvent;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.EnviarEmailFaturadoSqs;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoHistoricoDto;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Hibernate;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PedidoFaturadoListener implements ApplicationListener<AlteracaoSituacaoPedidoEvent> {

  private static final String DESCRICAO = "Faturamento de pedido";
  private static final String DETALHE = "Pedido faturado e enviado para emissão de nota fiscal.";

  private final PedidoRepository pedidoRepository;
  private final ServicoNotificacao servicoNotificacaoSqs;
  private final PedidoHistoricoService pedidoHistoricoService;
  private final NotificacaoService notificacaoService;
  private final PedidoRecebimentoService pedidoRecebimentoService;
  private final MovimentoEstoquePorPedidoService movimentoEstoqueService;
  private final TituloPorPedidoService tituloPorPedidoService;
  private final UsuarioRepository usuarioRepository;

  @Override
  public void onApplicationEvent(AlteracaoSituacaoPedidoEvent event) {
    if (event.getSituacaoNova() != SituacaoPedido.FATURADO) {
      return;
    }
    faturaPedido(event);
  }

  private void faturaPedido(AlteracaoSituacaoPedidoEvent event) {
    Pedido pedido = pedidoRepository.required(event.getPedidoId());
    Hibernate.initialize(pedido.getItens());
    Hibernate.initialize(pedido.getCentrosCusto());
    Hibernate.initialize(pedido.getRecebimentos());
    pedido.getItens().forEach((PedidoItem item) -> Hibernate.initialize(item.getProduto()));
    Nop nop = pedido.getNop();
//    try {
    if (nop.isGeraNfe()) {
      iniciaGeracaoNfe(event, pedido);
    } else {
      finalizaPedido(event, pedido);
    }

    atualizarParcelasPedido(pedido, event);

    criaNotificacao(pedido, DETALHE);

    PedidoHistorico historico = criaPedidoHistorico(event);
    historico.setDescricao(DESCRICAO);
    historico.setDetalhe(DETALHE);
    pedidoHistoricoService.create(historico);
//    } catch (Exception ex) {
//      log.error("Erro ao faturar pedido!", ex);
//      historico.setDescricao(DESCRICAO);
//      historico.setDetalhe(ex.getMessage());
//      mensagem = getMensagemErroNotificacao(nop.isGeraNfe());
//      pedidoHistoricoService.create(historico);
//      criaNotificacao(pedido, mensagem);
//      throw ex;
//    }
  }

  private void finalizaPedido(AlteracaoSituacaoPedidoEvent event, Pedido pedido) {
    Usuario user = usuarioRepository.findById(event.getUsuario()).orElse(null);
    if (Objects.equals(PEDIDO_AUTORIZADO, pedido.getSituacao())) {
      return;
    }
    pedidoRepository.alterarSituacaoETipoComprovante(pedido.getId(), PEDIDO_AUTORIZADO.name(),
      TipoComprovante.RECIBO.name(), pedido.getAplicacao());

    tituloPorPedidoService.geraContaReceberPorPedido(pedido, Boolean.TRUE);
    movimentoEstoqueService.movimentaEstoque(pedido);
    enviarEmail(pedido);
    createPedidoHistorico(pedido.getId(), PEDIDO_AUTORIZADO, null, user);
  }

  private void iniciaGeracaoNfe(AlteracaoSituacaoPedidoEvent event, Pedido pedido) {
    pedidoRepository.alterarSituacao(event.getPedidoId(), event.getSituacaoNova());
    movimentoEstoqueService.movimentaEstoque(pedido);
    enviaNotificacaoPedidoParaEmissaoNfe(event, pedido);
  }

  private void enviaNotificacaoPedidoParaEmissaoNfe(
    AlteracaoSituacaoPedidoEvent event, Pedido pedido
  ) {
    log.debug("enfileirar pedido para emissão de nfe: {}", event.getPedidoId());
    servicoNotificacaoSqs.enfilerarQueueGeradorNfe(toPedidoNotification(pedido));
  }

  private PedidoHistorico criaPedidoHistorico(AlteracaoSituacaoPedidoEvent event) {
    var historico = new PedidoHistorico();
    historico.setDataHistorico(LocalDateTime.now());
    historico.setSituacao(event.getSituacaoNova());
    historico.setPedido(Pedido.of(event.getPedidoId()));
    historico.setUsuario(Usuario.of(event.getUsuario()));
    return historico;
  }

  private void criaNotificacao(Pedido pedido, String mensagem) {
    try {
      Notificacao notificacao = new Notificacao();
      notificacao.setAplicacao(pedido.getAplicacao());
      notificacao.setEmpresa(pedido.getEmpresa());
      notificacao.setUsuario(pedido.getUsuario());
      notificacao.setTitulo(DESCRICAO);
      notificacao.setMensagem(mensagem);
      notificacao.setTipo(TipoNotificacao.USUARIO);
      notificacao.setDataEnvio(LocalDateTime.now());
      notificacao.setOrigem(OrigemNotificacao.PEDIDO_FATURADO);

      // Monta a notificação para o front
      var payloadPedidoNotificacao = PayloadPedido.builder()
        .titulo(notificacao.getTitulo())
        .mensagem(notificacao.getMensagem())
        .pedidoId(pedido.getId())
        .build();

      notificacaoService.createNotification(notificacao, payloadPedidoNotificacao);
    } catch (Exception e) {
      log.error("Erro ao gerar notificação de pedido para emissão de NFE: " + e.getMessage());
    }
  }

  private PedidoNotificationDto toPedidoNotification(Pedido pedido) {
    var pedidoNotificationDto = new PedidoNotificationDto();
    pedidoNotificationDto.setPedidoId(pedido.getId());
    pedidoNotificationDto.setAplicacao(pedido.getAplicacao());
    pedidoNotificationDto.setStatus(pedido.getSituacao().name());
    pedidoNotificationDto.setEmpresaId(pedido.getEmpresa().getId());
    pedidoNotificationDto.setUsuarioId(pedido.getUsuario().getId());
    return pedidoNotificationDto;
  }

  public void atualizarParcelasPedido(Pedido pedido, AlteracaoSituacaoPedidoEvent event) {
    if (event.getIsAtualizaParcelas()) {
      var dataVencimentoInicial = event.getDataInicial();
      var recebimentos = pedido.getRecebimentos();
      if (dataVencimentoInicial == null) {
        dataVencimentoInicial = LocalDate.now();
      }
      recebimentos = recebimentos.stream()
        .sorted(Comparator.comparing(PedidoRecebimento::getNumeroParcela)).toList();
      var months = 0;
      for (PedidoRecebimento recebimento : recebimentos) {
        recebimento.setDataVencimento(dataVencimentoInicial.plusMonths(months));
        pedidoRecebimentoService.update(recebimento.getId(), recebimento);
        months++;
      }
    }
  }

  public String getMensagemErroNotificacao(Boolean isGeraNfe) {
    if (isGeraNfe) {
      return "Ocorreu um erro ao enviar o pedido de emissão da nota fiscal. Verifique o histórico do pedido para obter mais detalhes.";
    } else {
      return "Ocorreu um erro ao faturar o pedido. Verifique o histórico do pedido para obter mais detalhes.";
    }
  }

  public void createPedidoHistorico(UUID pedidoId, SituacaoPedido situacao, String descricao,
    Usuario usuario) {
    var pedidoHistorico = PedidoHistoricoDto.builder()
      .dataHistorico(LocalDateTime.now())
      .situacao(situacao)
      .pedidoId(pedidoId)
      .descricao(descricao)
      .usuarioId(get(usuario, Usuario::getId))
      .usuarioNome(get(usuario, Usuario::getNome))
      .build();
    pedidoHistoricoService.create(pedidoHistorico.toEntity());
  }

//  public void procesaErroGeracaoRecibo(Pedido pedido, Exception exception, Usuario user){
//    try{
//      pedidoRepository.alterarSituacao(pedido.getId(), PEDIDO_REJEITADO);
//      createPedidoHistorico(pedido.getId(), PEDIDO_REJEITADO,exception.getMessage(),user);
//    }catch (Exception ex){
//      log.error("Erro ao processar erro de geracao de recibo: {}", ex.getMessage());
//    }
//  }

  public void enviarEmail(Pedido pedido){
    var enviarEmailFaturado = EnviarEmailFaturadoSqs.builder()
      .aplicacaoId(getAplicacao().getId())
      .empresaId(getEmpresa().getId())
      .usuarioId(getUsuario().getId())
      .pedidoId(pedido.getId())
      .build();
    servicoNotificacaoSqs.enfileirarQueueEnvioEmailPedidoFaturado(enviarEmailFaturado);
  }

}
