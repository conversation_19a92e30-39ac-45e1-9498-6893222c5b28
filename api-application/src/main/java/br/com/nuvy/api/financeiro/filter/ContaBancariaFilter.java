package br.com.nuvy.api.financeiro.filter;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.base.filter.Filter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springdoc.core.annotations.ParameterObject;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ParameterObject
public class ContaBancariaFilter implements Filter {

  private Integer id;
  private Integer empresaId;
  private Situacao situacao;
  private String nome;
  private String agencia;
  private String numConta;
  private String tpContaNome;
  private String bancoCodigo;
  private String bancoNome;
  private String empresasNome;
  private Boolean permiteGerarRemessa;
  private List<Integer> idsIgnorado;
}
