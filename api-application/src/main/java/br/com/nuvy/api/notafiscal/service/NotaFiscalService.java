package br.com.nuvy.api.notafiscal.service;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.notafiscal.dto.DadosCancelamentoDto;
import br.com.nuvy.api.notafiscal.dto.DadosCorrecaoDto;
import br.com.nuvy.api.notafiscal.dto.PedidoNfeDto;
import br.com.nuvy.api.venda.model.Inutilizacao;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.common.exception.BusinessException;
import br.com.nuvy.common.exception.NotaFiscalException;
import br.com.nuvy.facade.estoque.eq1recebimentonfecompra.manifestonfe.model.ManifestoDto;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.CartaCorrecaoDto;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class NotaFiscalService {

  @Value("${nuvy-nfe.nfe-service-url}")
  private String servicoNfeUrl;
  private static final String XAPIKEY = "x-api-key";

  private ObjectMapper jsonMapper() {
    return Jackson2ObjectMapperBuilder.json()
      .featuresToDisable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
      .featuresToEnable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
      .visibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE)
      .visibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY)
      .serializationInclusion(JsonInclude.Include.NON_NULL)
      .build();
  }

  private RestTemplate restTemplate() {
    var convertes = List.<HttpMessageConverter<?>>of(
      new MappingJackson2HttpMessageConverter(jsonMapper())
    );
    return new RestTemplate(convertes);
  }

  public void emitirNotaFiscal(Empresa empresa, PedidoNfeDto pedido) {
    validarAoEnviarNfe(empresa, pedido);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set(XAPIKEY, empresa.getTokenNfe());

    StringBuilder nfeUrl = new StringBuilder();
    nfeUrl.append(servicoNfeUrl).append(pedido.getAmbiente());
    nfeUrl.append("/notas-fiscais/pedidos/nuvy");

    HttpEntity<PedidoNfeDto> request = new HttpEntity<>(pedido, headers);
    restTemplate().postForObject(nfeUrl.toString(), request, Void.class);
    log.info("pedido enviado para emissão de nota fiscal");
  }

  public void corrigirNotaFiscal(Object entidade, CartaCorrecaoDto cartaCorrecao) {
    var dadosCorrecao = new DadosCorrecaoDto();
    if (entidade instanceof Pedido pedido) {
      dadosCorrecao = DadosCorrecaoDto.fromPedido(pedido);
    } else if (entidade instanceof NotaFiscalEntrada notaFiscalEntrada) {
      dadosCorrecao = DadosCorrecaoDto.fromNotaEntrada(notaFiscalEntrada);
    }
    validaCartaCorrecaoAoEnviar(dadosCorrecao);

    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set(XAPIKEY, dadosCorrecao.getTokenNfe());
    Map<String, String> mapJson = new HashMap<>();
    mapJson.put("cartaCorrecao", cartaCorrecao.getCartaCorrecao());

    StringBuilder nfeUrl = new StringBuilder();
    nfeUrl.append(servicoNfeUrl);
    nfeUrl.append(dadosCorrecao.getAmbiente().name());
    nfeUrl.append("/notas-fiscais/").append(dadosCorrecao.getNumeroNotaFiscal());
    nfeUrl.append("/").append(dadosCorrecao.getSerieNotaFiscal());
    nfeUrl.append("/correcoes");

    HttpEntity<Map<String, String>> request = new HttpEntity<>(mapJson, headers);
    restTemplate().postForObject(nfeUrl.toString(), request, Void.class);
    log.info("Corrigindo nfe: OK");
  }

  public void inutilizarNfe(Inutilizacao inutilizacao) {
    validarAoEnviarInutilizacao(inutilizacao);

    log.info("enviando inutilizacao para nfe-service: {}", inutilizacao.getId());
    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set(XAPIKEY, inutilizacao.getEmpresa().getTokenNfe());

    Map<String, String> payload = new HashMap<>();
    payload.put("motivo", inutilizacao.getMotivo());
    payload.put("inutilizacaoId", inutilizacao.getId().toString());

    StringBuilder uri = new StringBuilder();
    uri.append(servicoNfeUrl);
    uri.append(inutilizacao.getAmbiente().name());
    uri.append("/notas-fiscais/inutilizacoes");
    uri.append("/").append(inutilizacao.getSerieNota()).append("/");
    uri.append(inutilizacao.getNumeroInicial()).append("-").append(inutilizacao.getNumeroFinal());

    HttpEntity<Object> request = new HttpEntity<>(payload, headers);
    restTemplate().put(uri.toString(), request);
    log.info("Inutilizando nfe: OK");
  }

  public void manifestarNotaFiscal(Empresa empresa, ManifestoDto manifesto) {
    validarAoEnviarManifesto(empresa, manifesto);

    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set(XAPIKEY, empresa.getTokenNfe());

    StringBuilder uri = new StringBuilder();
    uri.append(servicoNfeUrl);
    uri.append(manifesto.getAmbiente().name());
    uri.append("/notas-fiscais/manifestacao");

    HttpEntity<ManifestoDto> request = new HttpEntity<>(manifesto, headers);
    restTemplate().postForObject(uri.toString(), request, String.class);
    log.info("manifestação de nfe: OK");
  }

  public void cancelarNotaFiscal(Object entidade) {
    var dadosCancelamento = new DadosCancelamentoDto();
    if (entidade instanceof Pedido pedido) {
      dadosCancelamento = DadosCancelamentoDto.fromPedido(pedido);
    } else if (entidade instanceof NotaFiscalEntrada notaFiscalEntrada) {
      dadosCancelamento = DadosCancelamentoDto.fromNotaEntrada(notaFiscalEntrada);
    }
    validaAoEnviarCancelamentoNfe(dadosCancelamento);

    var headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);

    headers.set(XAPIKEY, dadosCancelamento.getTokenNfe());
    Map<String, String> mapJson = new HashMap<>();
    mapJson.put("motivoCancelamento", dadosCancelamento.getMotivoCancelamento());
    var rest = new RestTemplate();

    StringBuilder uri = new StringBuilder();
    uri.append(servicoNfeUrl);
    uri.append(dadosCancelamento.getAmbiente().name());
    uri.append("/notas-fiscais");
    uri.append("/").append(dadosCancelamento.getNumeroNotaFiscal());
    uri.append("/").append(dadosCancelamento.getSerieNotaFiscal());

    log.info("Enviando nota fiscal para cancelamento: {}", uri);
    HttpEntity<Object> request = new HttpEntity<>(mapJson, headers);
    ResponseEntity<String> response = rest.exchange(uri.toString(), HttpMethod.DELETE, request,
      String.class);
    log.info("Cancelando nota fiscal: {}", response.getBody());
  }

  private void validarAoEnviarManifesto(Empresa empresa, ManifestoDto manifesto) {
    if (StringUtils.isBlank(empresa.getTokenNfe())) {
      throw new BusinessException("empresa.mandante.token.invalid");
    }
    if (manifesto.getAmbiente() == null) {
      throw new BusinessException("ambiente.emissao.required");
    }
  }

  private void validarAoEnviarNfe(Empresa empresa, PedidoNfeDto pedido) {
    if (StringUtils.isBlank(empresa.getTokenNfe())) {
      throw new BusinessException("empresa.mandante.token.invalid");
    }
    if (pedido.getAmbiente() == null) {
      throw new BusinessException("ambiente.emissao.required");
    }
  }

  private void validaCartaCorrecaoAoEnviar(DadosCorrecaoDto dadosCorrecao) {
    if (dadosCorrecao.getAmbiente() == null) {
      throw new BusinessException("ambiente.emissao.required");
    }
    if (StringUtils.isBlank(dadosCorrecao.getTokenNfe())) {
      throw new BusinessException("empresa.mandante.token.invalid");
    }
  }

  private void validaAoEnviarCancelamentoNfe(DadosCancelamentoDto dadosCancelaMento) {
    if (dadosCancelaMento.getNumeroNotaFiscal() == null) {
      throw new NotaFiscalException("pedido.nfe.sem.notafiscal");
    }
    if (StringUtils.isEmpty(dadosCancelaMento.getMotivoCancelamento()) ||
      dadosCancelaMento.getMotivoCancelamento().length() < 15 ||
      dadosCancelaMento.getMotivoCancelamento().length() > 256) {
      throw new NotaFiscalException(
        "Motivo de cancelando de nota fiscal deve possuir entre 15-256 caracteres");
    }
    if (StringUtils.isBlank(dadosCancelaMento.getTokenNfe())) {
      throw new BusinessException("empresa.mandante.token.invalid");
    }
    if (dadosCancelaMento.getAmbiente() == null) {
      throw new BusinessException("ambiente.emissao.required");
    }
  }

  public void validarAoEnviarInutilizacao(Inutilizacao inutilizacao) {
    if (inutilizacao.getAmbiente() == null) {
      throw new BusinessException("ambiente.emissao.required");
    }
    if (StringUtils.isBlank(inutilizacao.getEmpresa().getTokenNfe())) {
      throw new BusinessException("empresa.mandante.token.invalid");
    }
  }

  @Data
  public static class RegistrarMandanteResponse {

    private Mandante mandante;

    @Data
    public static class Mandante {

      //      private Certificado certificado;
//      private Set<AmbienteDto> ambientes;
//      private String cnpj;
      private String apiKey;
//      private boolean ativo = true;
//      private BigInteger ultimoNumeroLote;
//      private String idExterno;

//      @Data
//      public static class Certificado {
//        @JsonProperty("base64ArquivoPfx")
//        private String arquivoPfx;
//        private String senha;
//      }
    }
  }

}