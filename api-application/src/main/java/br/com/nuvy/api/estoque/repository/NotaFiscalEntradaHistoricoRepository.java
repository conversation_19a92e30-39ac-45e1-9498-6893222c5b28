package br.com.nuvy.api.estoque.repository;

import br.com.nuvy.api.estoque.model.NotaFiscalEntradaHistorico;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.UUID;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface NotaFiscalEntradaHistoricoRepository extends
  NuvyRepository<NotaFiscalEntradaHistorico, UUID> {

  @Query(value = """
    SELECT CAST(COALESCE(h.id_usuario, n.id_usuario) AS VARCHAR)
    FROM eq_nf_entrada n
             LEFT JOIN eq_nf_entrada_hist h ON n.id_nf_entrada = h.id_nf_entrada
    WHERE n.id_nf_entrada = :idNota
    ORDER BY h.data_hist DESC
    LIMIT 1;
    """, nativeQuery = true)
  String findUsuarioIdByNotaFiscalId(UUID idNota);

}
