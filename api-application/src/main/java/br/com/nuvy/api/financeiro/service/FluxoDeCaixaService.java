package br.com.nuvy.api.financeiro.service;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.enums.TipoTransacao;
import br.com.nuvy.api.financeiro.model.BancoContaSaldoDiario;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancaria;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBaixa;
import br.com.nuvy.api.financeiro.model.Transacao;
import br.com.nuvy.api.financeiro.repository.MovimentacaoBancariaRepository;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.financeiro.repository.TransacaoRepository;
import br.com.nuvy.api.financeiro.repository.specification.FluxoCaixaSpecification;
import br.com.nuvy.config.datasource.DataSource;
import br.com.nuvy.config.datasource.DataSourceType;
import br.com.nuvy.facade.relatorio.rl4financeiro.model.FluxoDiarioDto;
import br.com.nuvy.facade.relatorio.rl4financeiro.model.TituloFluxoFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FluxoDeCaixaService {

  private final BancoContaSaldoDiarioService bancoContaSaldoDiarioService;
  private final TituloRepository tituloRepository;
  private final TransacaoRepository transacaoRepository;
  private final MovimentacaoBancariaRepository movimentacaoBancariaRepository;

  @DataSource(DataSourceType.READ)
  @Transactional(readOnly = true)
  public List<FluxoDiarioDto> findFluxoCaixa(TituloFluxoFilter filter) {
    FluxoCaixaSpecification specification = new FluxoCaixaSpecification(filter);
    var saldos = bancoContaSaldoDiarioService.findAll(specification);

    if (saldos.isEmpty() && filter.getDataFinal().isAfter(LocalDate.now())) {
      return processarFuturosSemSaldos(filter.getDataInicial(), filter.getDataFinal());
    }

    List<SituacaoTitulo> situacoesReceitas = Arrays.asList(SituacaoTitulo.RECEBIDO, SituacaoTitulo.RECEBIDO_PARCIAL, SituacaoTitulo.CONCILIADO, SituacaoTitulo.ABERTO);
    List<SituacaoTitulo> situacoesDespesas = Arrays.asList(SituacaoTitulo.PAGO, SituacaoTitulo.PAGO_PARCIAL, SituacaoTitulo.CONCILIADO, SituacaoTitulo.ABERTO);

    Map<ContaBancaria, List<BancoContaSaldoDiario>> saldosPorConta = saldos.stream()
      .sorted(Comparator.comparing(BancoContaSaldoDiario::getDataSaldo))
      .collect(Collectors.groupingBy(BancoContaSaldoDiario::getContaBancaria));

    LocalDate sysdate = LocalDate.now();

    return saldosPorConta.entrySet().stream()
      .filter(entry -> !entry.getKey().getIndControle())
      .flatMap(entry -> processarConta(entry.getKey(), entry.getValue(), situacoesReceitas, situacoesDespesas, filter.getDataInicial(), filter.getDataFinal(), sysdate).stream())
      .filter(dto -> dto.getReceitas().compareTo(BigDecimal.ZERO) != 0 || dto.getDespesas().compareTo(BigDecimal.ZERO) != 0 || dto.getIsSaldoInicial())
      .collect(Collectors.toList());
  }

  private List<FluxoDiarioDto> processarConta(ContaBancaria conta, List<BancoContaSaldoDiario> saldosList,
    List<SituacaoTitulo> situacoesReceitas, List<SituacaoTitulo> situacoesDespesas,
    LocalDate dataInicial, LocalDate dataFinal, LocalDate sysdate) {
    BigDecimal saldoInicial = BigDecimal.ZERO;


    saldosList.sort(Comparator.comparing(BancoContaSaldoDiario::getDataSaldo));


    BancoContaSaldoDiario ultimoSaldo = saldosList.get(saldosList.size() - 1);
    if (dataInicial.isAfter(ultimoSaldo.getDataSaldo())) {
      saldoInicial = ultimoSaldo.getValorSaldo();
    }

    var receitasPorConta = buscarTitulosPorConta(TipoTitulo.RECEBER, situacoesReceitas, conta);
    var despesasPorConta = buscarTitulosPorConta(TipoTitulo.PAGAR, situacoesDespesas, conta);
    var transferenciasOrigem = transacaoRepository.findByTipoAndMovimentacaoAndContaOrigem(TipoTransacao.TRANSFERENCIA_CONTAS, conta);
    var transferenciasDestino = transacaoRepository.findByTipoAndMovimentacaoAndContaDestino(TipoTransacao.TRANSFERENCIA_CONTAS, conta);
    var lancamentosManuais = buscarMovimentacoesPorTransacaoAndConta(conta);
    var receitasLancamentosManuais = lancamentosManuais.stream()
      .filter(movimentacao -> movimentacao.getValorMovimento().compareTo(BigDecimal.ZERO) > 0)
      .toList();
    var despesasLancamentosManuais = lancamentosManuais.stream()
      .filter(movimentacao -> movimentacao.getValorMovimento().compareTo(BigDecimal.ZERO) < 0)
      .toList();

    List<FluxoDiarioDto> fluxoDiarioList = new ArrayList<>();

    for (BancoContaSaldoDiario saldo : saldosList) {
      if (saldo.getDataSaldo().isBefore(dataInicial)) {
        saldoInicial = saldo.getValorSaldo();
        continue;
      }

      if (conta.getDtSaldoInicial() != null && saldo.getDataSaldo().equals(conta.getDtSaldoInicial())) {
        saldoInicial = saldoInicial.add(conta.getSaldoInicial());

        fluxoDiarioList.add(new FluxoDiarioDto(
          saldo.getContaBancaria().getEmpresas().stream().map(Empresa::getNome).collect(Collectors.joining(", ")),
          saldo.getContaBancaria().getNome(),
          saldo.getDataSaldo(),
          saldo.getContaBancaria().getId(),
          conta.getSaldoInicial(),
          BigDecimal.ZERO,
          BigDecimal.ZERO,
          conta.getSaldoInicial(),
          Boolean.TRUE
        ));
      }

      BigDecimal receitas = calcularReceitas(saldo, receitasPorConta, transferenciasDestino, sysdate, receitasLancamentosManuais);
      BigDecimal despesas = calcularDespesas(saldo, despesasPorConta, transferenciasOrigem, sysdate, despesasLancamentosManuais);

      BigDecimal saldoAcumulado = saldoInicial.add(receitas).subtract(despesas);

      fluxoDiarioList.add(new FluxoDiarioDto(
        saldo.getContaBancaria().getEmpresas().stream().map(Empresa::getNome).collect(Collectors.joining(", ")),
        saldo.getContaBancaria().getNome(),
        saldo.getDataSaldo(),
        saldo.getContaBancaria().getId(),
        saldoInicial,
        receitas,
        despesas,
        saldoAcumulado,
        Boolean.FALSE
      ));

      saldoInicial = saldoAcumulado;
    }

    if (dataFinal.isAfter(sysdate)) {
      List<FluxoDiarioDto> futuros = processarFuturos(conta, receitasPorConta, despesasPorConta,
        transferenciasOrigem, transferenciasDestino, saldoInicial, sysdate, dataFinal,
        receitasLancamentosManuais, despesasLancamentosManuais);
      fluxoDiarioList.addAll(futuros);
    }

    return fluxoDiarioList;
  }

  private List<FluxoDiarioDto> processarFuturos(ContaBancaria conta, List<Titulo> receitasPorConta,
    List<Titulo> despesasPorConta, List<Transacao> transferenciasOrigem, List<Transacao> transferenciasDestino,
    BigDecimal saldoInicial, LocalDate sysdate, LocalDate dataFinal,
    List<MovimentacaoBancaria> receitasLancamentosManuais,
    List<MovimentacaoBancaria> despesasLancamentosManuais) {

    List<FluxoDiarioDto> futuros = new ArrayList<>();

    LocalDate dataAtual = sysdate.plusDays(1);
    while (!dataAtual.isAfter(dataFinal)) {
      BigDecimal receitas = calcularReceitasParaDataFutura(dataAtual, receitasPorConta, transferenciasDestino, receitasLancamentosManuais);
      BigDecimal despesas = calcularDespesasParaDataFutura(dataAtual, despesasPorConta, transferenciasOrigem, despesasLancamentosManuais);

      if (receitas.compareTo(BigDecimal.ZERO) != 0 || despesas.compareTo(BigDecimal.ZERO) != 0) {
        BigDecimal saldoAcumulado = saldoInicial.add(receitas).subtract(despesas);

        futuros.add(new FluxoDiarioDto(
          conta.getEmpresas().stream().map(Empresa::getNome).collect(Collectors.joining(", ")),
          conta.getNome(),
          dataAtual,
          conta.getId(),
          saldoInicial,
          receitas,
          despesas,
          saldoAcumulado,
          Boolean.FALSE
        ));

        saldoInicial = saldoAcumulado;
      }
      dataAtual = dataAtual.plusDays(1);
    }

    return futuros;
  }

  private List<FluxoDiarioDto> processarFuturosSemSaldos(LocalDate dataInicial, LocalDate dataFinal) {
    List<FluxoDiarioDto> futuros = new ArrayList<>();
    BigDecimal saldoInicial = BigDecimal.ZERO;

    LocalDate dataAtual = dataInicial.isAfter(LocalDate.now()) ? dataInicial : LocalDate.now().plusDays(1);
    while (!dataAtual.isAfter(dataFinal)) {
      BigDecimal receitas = BigDecimal.ZERO;
      BigDecimal despesas = BigDecimal.ZERO;

      BigDecimal saldoAcumulado = saldoInicial.add(receitas).subtract(despesas);

      futuros.add(new FluxoDiarioDto(
        "Conta",
        "Conta",
        dataAtual,
        0,
        saldoInicial,
        receitas,
        despesas,
        saldoAcumulado,
        Boolean.FALSE
      ));

      saldoInicial = saldoAcumulado;
      dataAtual = dataAtual.plusDays(1);
    }

    return futuros;
  }

  private List<Titulo> buscarTitulosPorConta(TipoTitulo tipo, List<SituacaoTitulo> situacoes, ContaBancaria conta) {
    return tituloRepository.findWithBaixasByTipoAndSituacaoInAndContaBancariaIdAndIndTituloExcluido(
      tipo, situacoes, conta.getId(), Boolean.FALSE);
  }


  private List<MovimentacaoBancaria> buscarMovimentacoesPorTransacaoAndConta(ContaBancaria conta) {
    return movimentacaoBancariaRepository.findByContaBancariaAndTransacaoTipo(conta, TipoTransacao.LANCAMENTO_MANUAL);
  }

  private BigDecimal calcularReceitas(BancoContaSaldoDiario saldo, List<Titulo> receitasPorConta, List<Transacao> transferenciasDestino, LocalDate sysdate, List<MovimentacaoBancaria> lancamentosManuais) {
    BigDecimal receitasAdicionais = receitasPorConta.stream()
      .filter(titulo -> !(titulo.getSituacao().equals(SituacaoTitulo.ABERTO) && (titulo.getDataVencimento().equals(sysdate) || titulo.getDataVencimento().isBefore(sysdate))))
      .filter(titulo -> titulo.getDataVencimento().equals(saldo.getDataSaldo()) ||
        titulo.getTituloBaixas().stream().anyMatch(baixa -> baixa.getDataBaixa().equals(saldo.getDataSaldo())))
      .map(titulo -> {
        if (!titulo.getTituloBaixas().isEmpty()) {
          return titulo.getTituloBaixas().stream()
            .filter(baixa -> baixa.getDataBaixa().equals(saldo.getDataSaldo()))
            .map(TituloBaixa::getValorCalculado)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
          return titulo.getValorLiquido();
        }
      })
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal receitasTransferencias = transferenciasDestino.stream()
      .filter(transacao -> transacao.getTransferencia().getMovimentacaoDestino().getDataMovimento().toLocalDate().equals(saldo.getDataSaldo()))
      .map(Transacao::getValorTransacao)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal receitasLancamentosManuais = lancamentosManuais.stream()
      .filter(lancamento -> lancamento.getDataMovimento().toLocalDate().equals(saldo.getDataSaldo()))
      .map(MovimentacaoBancaria::getValorMovimento)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    receitasAdicionais = receitasAdicionais.add(receitasLancamentosManuais);

    return receitasAdicionais.add(receitasTransferencias);
  }

  private BigDecimal calcularDespesas(BancoContaSaldoDiario saldo, List<Titulo> despesasPorConta, List<Transacao> transferenciasOrigem, LocalDate sysdate, List<MovimentacaoBancaria> lancamentosManuais) {
    BigDecimal despesasAdicionais = despesasPorConta.stream()
      .filter(titulo -> !(titulo.getSituacao().equals(SituacaoTitulo.ABERTO) && (titulo.getDataVencimento().equals(sysdate) || titulo.getDataVencimento().isBefore(sysdate))))
      .filter(titulo -> titulo.getDataVencimento().equals(saldo.getDataSaldo()) ||
        titulo.getTituloBaixas().stream().anyMatch(baixa -> baixa.getDataBaixa().equals(saldo.getDataSaldo())))
      .map(titulo -> {
        if (!titulo.getTituloBaixas().isEmpty()) {
          return titulo.getTituloBaixas().stream()
            .filter(baixa -> baixa.getDataBaixa().equals(saldo.getDataSaldo()))
            .map(TituloBaixa::getValorCalculado)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
          return titulo.getValorLiquido();
        }
      })
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal despesasTransferencias = transferenciasOrigem.stream()
      .filter(transacao -> transacao.getTransferencia().getMovimentacaoOrigem().getDataMovimento().toLocalDate().equals(saldo.getDataSaldo()))
      .map(Transacao::getValorTransacao)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal despesasLancamentosManuais = lancamentosManuais.stream()
      .filter(lancamento -> lancamento.getDataMovimento().toLocalDate().equals(saldo.getDataSaldo()))
      .map(lancamento -> lancamento.getValorMovimento().abs())
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    despesasAdicionais = despesasAdicionais.add(despesasLancamentosManuais);

    return despesasAdicionais.add(despesasTransferencias);
  }

  private BigDecimal calcularReceitasParaDataFutura(LocalDate data, List<Titulo> receitasPorConta,
    List<Transacao> transferenciasDestino, List<MovimentacaoBancaria> lancamentosManuais) {
    BigDecimal receitasAdicionais = receitasPorConta.stream()
      .filter(titulo -> titulo.getDataVencimento().equals(data) ||
        titulo.getTituloBaixas().stream().anyMatch(baixa -> baixa.getDataBaixa().equals(data)))
      .map(titulo -> {
        if (!titulo.getTituloBaixas().isEmpty()) {
          return titulo.getTituloBaixas().stream()
            .filter(baixa -> baixa.getDataBaixa().equals(data))
            .map(TituloBaixa::getValorCalculado)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
          return titulo.getValorLiquido();
        }
      })
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal receitasTransferencias = transferenciasDestino.stream()
      .filter(transacao -> transacao.getTransferencia().getMovimentacaoDestino().getDataMovimento().toLocalDate().equals(data))
      .map(Transacao::getValorTransacao)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal receitasLancamentosManuais = lancamentosManuais.stream()
      .filter(lancamento -> lancamento.getDataMovimento().toLocalDate().equals(data))
      .map(MovimentacaoBancaria::getValorMovimento)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    receitasAdicionais = receitasAdicionais.add(receitasLancamentosManuais);

    return receitasAdicionais.add(receitasTransferencias);
  }

  private BigDecimal calcularDespesasParaDataFutura(LocalDate data, List<Titulo> despesasPorConta, List<Transacao> transferenciasOrigem, List<MovimentacaoBancaria> lancamentosManuais) {
    BigDecimal despesasAdicionais = despesasPorConta.stream()
      .filter(titulo -> titulo.getDataVencimento().equals(data) ||
        titulo.getTituloBaixas().stream().anyMatch(baixa -> baixa.getDataBaixa().equals(data)))
      .map(titulo -> {
        if (!titulo.getTituloBaixas().isEmpty()) {
          return titulo.getTituloBaixas().stream()
            .filter(baixa -> baixa.getDataBaixa().equals(data))
            .map(TituloBaixa::getValorCalculado)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
          return titulo.getValorLiquido();
        }
      })
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal despesasTransferencias = transferenciasOrigem.stream()
      .filter(transacao -> transacao.getTransferencia().getMovimentacaoOrigem().getDataMovimento().toLocalDate().equals(data))
      .map(Transacao::getValorTransacao)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal despesasLancamentosManuais = lancamentosManuais.stream()
      .filter(lancamento -> lancamento.getDataMovimento().toLocalDate().equals(data))
      .map(lancamento -> lancamento.getValorMovimento().abs())
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    despesasAdicionais = despesasAdicionais.add(despesasLancamentosManuais);

    return despesasAdicionais.add(despesasTransferencias);
  }


}
