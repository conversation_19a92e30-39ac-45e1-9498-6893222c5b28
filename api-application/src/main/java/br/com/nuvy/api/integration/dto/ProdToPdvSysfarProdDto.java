package br.com.nuvy.api.integration.dto;

import br.com.nuvy.api.cadastro.model.Cfop;
import br.com.nuvy.api.cadastro.model.PosPdv;
import br.com.nuvy.api.cadastro.model.PosPdvProduto;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.model.nop.RegraCofinsPadrao;
import br.com.nuvy.api.cadastro.model.nop.RegraIcmsItem;
import br.com.nuvy.api.cadastro.model.nop.RegraIpi;
import br.com.nuvy.api.cadastro.model.nop.RegraPis;
import br.com.nuvy.api.estoque.model.Deposito;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Builder
public class ProdToPdvSysfarProdDto {

  private Produto produto;
  private BigDecimal saldo;

  private Deposito deposito;
  private PosPdv posPdv;
  private PosPdvProduto posPdvProduto;
  private RegraIcmsItem regraIcmsItem;
  private RegraPis regraPis;
  private RegraCofinsPadrao regraCofins;
  private Cfop cfop;
  private RegraIpi regraIpi;
}
