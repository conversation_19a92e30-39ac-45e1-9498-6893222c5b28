package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.nop.RegraPisExcecao;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RegraPisExcecaoRepository extends NuvyRepository<RegraPisExcecao, Integer> {

  Optional<RegraPisExcecao> findFirstByRegraIdAndProdutoId(Integer idRegraPis, Integer idProduto);
  @Query(
    """
    SELECT e FROM RegraPisExcecao e
    JOIN e.origensMercadoria om
    JOIN FETCH e.ncm
    JOIN FETCH e.situacaoTributaria
    WHERE e.regra.id = :idRegraPis
    AND e.ncm.id = :idNcm
    AND om.id = :idOrigemMercadoria
    """)
  Optional<RegraPisExcecao> findFirstRegraNcm(
    @Param("idRegraPis") Integer idRegraPis,
    @Param("idNcm") String idNcm,
    @Param("idOrigemMercadoria") Integer idOrigemMercadoria
  );
}
