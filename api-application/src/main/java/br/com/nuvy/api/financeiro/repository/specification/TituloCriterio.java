package br.com.nuvy.api.financeiro.repository.specification;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.financeiro.model.Banco_;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.ContaBancaria_;
import br.com.nuvy.api.financeiro.model.TipoReceitaDespesa;
import br.com.nuvy.api.financeiro.model.TipoReceitaDespesa_;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.Titulo_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import java.math.BigDecimal;
import static br.com.nuvy.common.query.CriteriaUtils.iLike;

@RequiredArgsConstructor
public class TituloCriterio implements Specification<Titulo> {
  private final transient String criterio;
  private final transient TipoTitulo tipoTitulo;
  @Override
  public Predicate toPredicate(Root<Titulo> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    query.distinct(true);
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);

    Predicate or = predicateBuilder
      .add(criterio,
        e -> {
          Join<Titulo, Pessoa> join = root.join(Titulo_.fornecedor, JoinType.LEFT);
          return iLike(criteriaBuilder, join.get(Pessoa_.nome), e);
        })
      .add(criterio,
        e -> {
          Join<Titulo, TipoReceitaDespesa> join = root.join(Titulo_.tipoReceitaDespesa,
            JoinType.LEFT);
          return iLike(criteriaBuilder, join.get(TipoReceitaDespesa_.nome), e);
        })
      .add(criterio, e -> {
        if (StringUtils.isNumeric(e)) {
          return criteriaBuilder.equal(root.get(Titulo_.valorTotal),
            BigDecimal.valueOf(Double.parseDouble(e)));
        }
        return predicateBuilder.or();
      })
      .add(criterio,
        e -> {
          Join<Titulo, ContaBancaria> join = root.join(Titulo_.contaBancaria, JoinType.LEFT);
          return iLike(criteriaBuilder, join.get(ContaBancaria_.banco).get(Banco_.nome), e);
        })
      .or();

    var predicateAnd = PredicateBuilder.create(criteriaBuilder)
      .add(tipoTitulo,
        e -> criteriaBuilder.equal(root.get(Titulo_.tipo), e))
      .and();

    return criteriaBuilder.and(or, predicateAnd);
  }
}
