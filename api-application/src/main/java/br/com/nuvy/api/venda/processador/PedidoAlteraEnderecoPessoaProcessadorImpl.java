package br.com.nuvy.api.venda.processador;

import br.com.nuvy.api.cadastro.repository.PessoaRepository;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.enums.TipoEnderecoPedido;
import br.com.nuvy.api.venda.comando.PedidoPessoaAlteraEndereco;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoEndereco;
import br.com.nuvy.api.venda.repository.PedidoEnderecoRepository;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.List;
import static br.com.nuvy.api.enums.SituacaoPedido.APROVADO;
import static br.com.nuvy.api.enums.SituacaoPedido.NF_REJEITADA;
import static br.com.nuvy.api.enums.SituacaoPedido.ORCAMENTO;
import static br.com.nuvy.api.enums.SituacaoPedido.PENDENTE;
import static br.com.nuvy.api.enums.SituacaoPedido.SEPARACAO;

@Component
@RequiredArgsConstructor
public class PedidoAlteraEnderecoPessoaProcessadorImpl  implements PedidoAlteraEnderecoPessoaProcessador{

  private final PedidoRepository repository;
  private final PessoaRepository pessoaRepository;
  private final PedidoEnderecoRepository pedidoEnderecoRepository;


  @Override
  public void processaComando(PedidoPessoaAlteraEndereco pedidoAlteraEndereco) {
      var pessoa = pessoaRepository.findById(pedidoAlteraEndereco.getIdPessoa())
        .orElseThrow(() -> new ResourceNotFoundException("pessoa.nao.encontrada"));
      var enderecoPrincipal = pessoa.getEnderecos().stream()
        .filter(endereco -> TipoEndereco.FISCAL.equals(endereco.getTipo()))
        .findFirst()
        .orElseThrow(() -> new PreconditionException("cliente.com.endereco.vazio"));
      var pedidoList = repository.findAllByClienteAndSituacaoIn(pessoa, List.of(PENDENTE,ORCAMENTO,APROVADO, SEPARACAO, NF_REJEITADA));
      for (Pedido pedido : pedidoList) {
        for (PedidoEndereco pedidoEndereco : pedido.getEnderecos()) {
          if (TipoEnderecoPedido.COBRANCA_FATURAMENTO.equals(pedidoEndereco.getTipo())){
            pedidoEndereco.setCep(enderecoPrincipal.getCep());
            pedidoEndereco.setEndereco(enderecoPrincipal.getEndereco());
            pedidoEndereco.setNumero(enderecoPrincipal.getNumero());
            pedidoEndereco.setComplemento(enderecoPrincipal.getComplemento());
            pedidoEndereco.setBairro(enderecoPrincipal.getBairro());
            pedidoEndereco.setCodigoCidade(enderecoPrincipal.getCodigoCidade());
            pedidoEndereco.setCidade(enderecoPrincipal.getCidade());
            pedidoEndereco.setCodigoUf(enderecoPrincipal.getCodigoUf());
            pedidoEndereco.setUf(enderecoPrincipal.getUf());
            pedidoEndereco.setUfNome(enderecoPrincipal.getUfNome());
            pedidoEndereco.setPais(enderecoPrincipal.getPais());
            pedidoEnderecoRepository.save(pedidoEndereco);
          }
        }
      }
  }
}
