package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.dto.UnidadeMedidaEmpresaDto;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.UnidadeMedida;
import br.com.nuvy.api.cadastro.model.UnidadeMedidaEmpresa;
import br.com.nuvy.api.cadastro.model.UnidadeMedidaIntegracao;
import br.com.nuvy.api.cadastro.repository.UnidadeMedidaEmpresaRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import java.time.LocalDateTime;
import java.util.UUID;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.cadastro.cd16pos.unidademedida.model.UnidadeMedidaEmpresaInDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UnidadeMedidaEmpresaService extends
  PageableServiceAdapterDto<UnidadeMedidaEmpresa, UnidadeMedidaEmpresaDto, UUID, NoFilter, UnidadeMedidaEmpresaRepository> {

  private final Badge badge;

  public void inserir(UnidadeMedidaEmpresaInDto payload) {

    UnidadeMedidaEmpresa unidadeMedidaEmpresa = repository
      .findByUnidadeMedidaIntegracaoIdAndUnidadeMedidaId(payload.getUnidadeMedidaIntegracaoId(),
        payload.getUnidadeMedidaId());

    if(unidadeMedidaEmpresa == null){
      unidadeMedidaEmpresa = new UnidadeMedidaEmpresa();
      unidadeMedidaEmpresa.setCreatedAt(LocalDateTime.now());
    }
    unidadeMedidaEmpresa.setEmpresa(Empresa.of(badge.getEmpresa().getId()));
    unidadeMedidaEmpresa.setUnidadeMedida(UnidadeMedida.of(payload.getUnidadeMedidaId()));
    unidadeMedidaEmpresa.setUnidadeMedidaIntegracao(UnidadeMedidaIntegracao
      .of(payload.getUnidadeMedidaIntegracaoId()));
    unidadeMedidaEmpresa.setUpdatedAt(LocalDateTime.now());

    repository.save(unidadeMedidaEmpresa);
  }

}
