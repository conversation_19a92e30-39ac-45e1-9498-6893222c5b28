package br.com.nuvy.api.sqs.servico;

import br.com.nuvy.api.notafiscal.dto.registrarcertificado.MandanteNfe;
import br.com.nuvy.facade.cadastro.cd2certificadodigital.CD2CertificadoDigitalService;
import br.com.nuvy.multitenent.TenantContext;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(
    name = {"aws.sqs.enabled", "aws.sqs.queue.nfe-mandante-registro.enabled"},
    havingValue = "true"
)
@RequiredArgsConstructor
public class NfeMandanteRegistroListener {

  private final ObjectMapper objectMapper;
  private final CD2CertificadoDigitalService certificadoService;

  @JmsListener(destination = "${aws.sqs.queue.nfe-mandante-registro.name}")
  public void nfeMandanteRegistroListener(Message message) {
    try {
      String payload = ((SQSTextMessage) message).getText();
      MandanteNfe mandanteNfe = objectMapper.readValue(payload, MandanteNfe.class);

      TenantContext.setTenant(mandanteNfe.getIdExternoOther());

      log.debug("Payload recebido: {}", payload);
      certificadoService.inserirTokenMandanteNfe(mandanteNfe);
      log.debug(" token inserido: {}", payload);
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }
}