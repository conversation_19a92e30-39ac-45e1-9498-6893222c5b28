package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.config.BadgeContext.getEmpresa;
import static com.webcohesion.ofx4j.domain.data.common.TransactionType.CREDIT;
import static com.webcohesion.ofx4j.domain.data.common.TransactionType.DEBIT;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.SituacaoMovimentacaoBancaria;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.SituacaoTituloBaixa;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.enums.TipoTransacao;
import br.com.nuvy.api.financeiro.mapper.TituloModelMapper;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancaria;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBaixa;
import br.com.nuvy.api.financeiro.model.Transacao;
import br.com.nuvy.api.financeiro.model.TransacaoTransferencia;
import br.com.nuvy.api.financeiro.repository.ContaBancariaRepository;
import br.com.nuvy.api.financeiro.repository.TituloBaixaRepository;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.financeiro.repository.TransacaoRepository;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.Preconditions;
import br.com.nuvy.common.utils.StringUtils;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.TituloBaixaDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.BaixaImportacaoDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.ExtratoImportacaoDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.ExtratoImportacaoDtoOut;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.RealizaBaixaDtoIn;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.TituloImportacaoDto;
import br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model.TransferenciaImportacaoDto;
import com.webcohesion.ofx4j.domain.data.ResponseEnvelope;
import com.webcohesion.ofx4j.domain.data.ResponseMessageSet;
import com.webcohesion.ofx4j.domain.data.banking.BankStatementResponse;
import com.webcohesion.ofx4j.domain.data.banking.BankStatementResponseTransaction;
import com.webcohesion.ofx4j.domain.data.banking.BankingResponseMessageSet;
import com.webcohesion.ofx4j.domain.data.common.Transaction;
import com.webcohesion.ofx4j.domain.data.common.TransactionList;
import com.webcohesion.ofx4j.domain.data.common.TransactionType;
import com.webcohesion.ofx4j.io.AggregateUnmarshaller;
import com.webcohesion.ofx4j.io.OFXParseException;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExtratoConciliacaoService {

  private static final String OFX_EXTENSION = ".ofx";

  private final TituloRepository tituloRepository;

  private final TituloBaixaService tituloBaixaService;
  private final TituloBaixaRepository tituloBaixaRepository;

  private final TransacaoRepository transacaoRepository;
  private final ContaBancariaRepository contaBancariaRepository;
  private final MovimentacaoBancariaService movimentacaoBancariaService;

  private final TituloModelMapper modelMapper;

  private static class OFXInputStream extends FilterInputStream {

    private byte[] overflowBuffer = null;
    private int overflowPosition = 0;

    private OFXInputStream(InputStream in) {
      super(in);
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
      // Se houver dados no overflowBuffer, leia primeiro
      if (overflowBuffer != null && overflowPosition < overflowBuffer.length) {
        int bytesToCopy = Math.min(len, overflowBuffer.length - overflowPosition);
        System.arraycopy(overflowBuffer, overflowPosition, b, off, bytesToCopy);
        overflowPosition += bytesToCopy;

        // Retorna a quantidade de bytes copiados do overflowBuffer
        if (overflowPosition == overflowBuffer.length) {
          overflowBuffer = null; // Limpa o buffer de overflow
          overflowPosition = 0;
        }
        return bytesToCopy;
      }

      // Lê do InputStream original
      int bytesRead = super.read(b, off, len);
      if (bytesRead == -1) {
        return -1; // Fim do stream
      }

      // Converte os bytes lidos em String
      String conteudo = new String(b, off, bytesRead);

      // Substitui os caracteres não escapados
      conteudo = conteudo.replaceAll("&(?!amp;|lt;|gt;|quot;|apos;)", "&amp;");

      // Converte o conteúdo corrigido de volta para bytes
      byte[] conteudoCorrigido = conteudo.getBytes();

      // Verifica se o conteúdo corrigido cabe no buffer original
      if (conteudoCorrigido.length <= len) {
        // Copia o conteúdo corrigido diretamente para o buffer original
        System.arraycopy(conteudoCorrigido, 0, b, off, conteudoCorrigido.length);
        return conteudoCorrigido.length;
      } else {
        // Preenche o buffer original com a parte que cabe
        System.arraycopy(conteudoCorrigido, 0, b, off, len);

        // Armazena o restante no overflowBuffer
        overflowBuffer = conteudoCorrigido;
        overflowPosition = len;

        return len;
      }
    }

    @Override
    public void close() throws IOException {
      super.close();
    }
  }

  public ExtratoImportacaoDtoOut processaArquivoExtratoConciliacao(
    Integer contaBancariaId, LocalDate dataInicial, LocalDate dataFinal, MultipartFile file
  ) throws IOException, OFXParseException {
    boolean isOfxFile = Objects.requireNonNull(file.getOriginalFilename())
      .toLowerCase().endsWith(OFX_EXTENSION);
    if (!isOfxFile) {
      throw new PreconditionException("O arquivo não possui a extensão .ofx");
    }

    ResponseEnvelope re;
    try (InputStream is = file.getInputStream()) {
      re = new AggregateUnmarshaller<>(ResponseEnvelope.class)
        .unmarshal(new OFXInputStream(is));
    }

    ExtratoImportacaoDto[] extratos = new ExtratoImportacaoDto[0];
    TransactionList transactionList = null;
    ContaBancaria contaBancaria = null;

    if (re != null) {
      SortedSet<ResponseMessageSet> messageSets = re.getMessageSets();
      if (messageSets != null && !messageSets.isEmpty()) {
        for (ResponseMessageSet messageSet : messageSets) {
          if (messageSet instanceof BankingResponseMessageSet bankingSet) {
            List<BankStatementResponseTransaction> bankResponses = bankingSet.getStatementResponses();

            if (bankResponses != null && !bankResponses.isEmpty()) {
              BankStatementResponse bankStatementResponse = bankResponses.getFirst().getMessage();
              if (bankStatementResponse != null) {
                contaBancaria = fetchBankAccount(contaBancariaId);

                transactionList = bankStatementResponse.getTransactionList();
                List<Transaction> transactions = getOrElse(transactionList.getTransactions(), List.of());
                extratos = new ExtratoImportacaoDto[transactions.size()];
                processTransactions(transactions, contaBancaria, extratos, dataInicial, dataFinal);
              }
            }
          }
        }
      }
    }

    return buildExtratoImportacaoDtoOut(
      contaBancariaId, dataInicial, dataFinal,
      contaBancaria, transactionList, extratos
    );
  }

  private enum FaseProcessamento {
    JA_PROCESSADO,
    DATA_VALOR_IGUAL,
    VALOR_DIFERENTE,
    DATA_DIFERENTE
  }

  private void processTransactions(
    List<Transaction> transactions, ContaBancaria contaBancaria,
    ExtratoImportacaoDto[] extratos,
    LocalDate dataInicial, LocalDate dataFinal
  ) {
    Empresa empresa = getEmpresa();
    List<Titulo> titulos = tituloRepository.findTitulosParaConciliacaoExtrato(
      contaBancaria, empresa, dataInicial, dataFinal
    );
    List<TituloBaixa> baixas = tituloBaixaRepository.findBaixasParaConciliacaoExtrato(
      contaBancaria, empresa, dataInicial, dataFinal
    );
    List<Transacao> transferencias = transacaoRepository.findTransferenciasParaConciliacaoExtrato(
      contaBancaria, TipoTransacao.TRANSFERENCIA_CONTAS,
      dataInicial.atStartOfDay(), dataFinal.atTime(23, 59, 59)
    );

    for (int i = 0; i < transactions.size(); i++) {
      Transaction transaction = transactions.get(i);
      List<TituloImportacaoDto> titulosTransacao = new ArrayList<>();
      List<BaixaImportacaoDto> baixasTransacao = new ArrayList<>();
      List<TransferenciaImportacaoDto> transferenciasTransacao = new ArrayList<>();
      boolean found = processaBaixas(baixas,
        transaction, baixasTransacao, FaseProcessamento.JA_PROCESSADO);
      found = processaTransferencias(transferencias, transaction, contaBancaria,
        transferenciasTransacao, FaseProcessamento.JA_PROCESSADO) || found;
      if (found) {
        extratos[i] = buildExtratoImportacaoDto(transaction, titulosTransacao, baixasTransacao,
          transferenciasTransacao, FaseProcessamento.JA_PROCESSADO);
      }
    }

    baixas.removeIf((TituloBaixa b) -> SituacaoTituloBaixa.CONCILIADO.equals(b.getSituacao()));
    transferencias.removeIf((Transacao t) ->
      SituacaoMovimentacaoBancaria.CONCILIADO.equals(t.getTransferencia().getMovimentacaoOrigem().getSituacao())
        || SituacaoMovimentacaoBancaria.CONCILIADO.equals(t.getTransferencia().getMovimentacaoDestino().getSituacao()));

    for (int i = 0; i < transactions.size(); i++) {
      if (extratos[i] == null) {
        Transaction transaction = transactions.get(i);
        List<TituloImportacaoDto> titulosTransacao = new ArrayList<>();
        List<BaixaImportacaoDto> baixasTransacao = new ArrayList<>();
        List<TransferenciaImportacaoDto> transferenciasTransacao = new ArrayList<>();
        boolean found = processaTitulos(titulos,
          transaction, titulosTransacao, FaseProcessamento.DATA_VALOR_IGUAL);
        found = processaBaixas(baixas,
          transaction, baixasTransacao, FaseProcessamento.DATA_VALOR_IGUAL) || found;
        found = processaTransferencias(transferencias, transaction, contaBancaria,
          transferenciasTransacao, FaseProcessamento.DATA_VALOR_IGUAL) || found;
        if (found) {
          extratos[i] = buildExtratoImportacaoDto(transaction, titulosTransacao, baixasTransacao,
            transferenciasTransacao, FaseProcessamento.DATA_VALOR_IGUAL);
        }
      }
    }

    for (int i = 0; i < transactions.size(); i++) {
      if (extratos[i] == null) {
        Transaction transaction = transactions.get(i);
        List<TituloImportacaoDto> titulosTransacao = new ArrayList<>();
        List<BaixaImportacaoDto> baixasTransacao = new ArrayList<>();
        List<TransferenciaImportacaoDto> transferenciasTransacao = new ArrayList<>();
        boolean found = processaTitulos(titulos,
          transaction, titulosTransacao, FaseProcessamento.VALOR_DIFERENTE);
        found = processaBaixas(baixas,
          transaction, baixasTransacao, FaseProcessamento.VALOR_DIFERENTE) || found;
        if (found) {
          extratos[i] = buildExtratoImportacaoDto(transaction, titulosTransacao, baixasTransacao,
            transferenciasTransacao, FaseProcessamento.VALOR_DIFERENTE);
        }
      }
    }

    for (int i = 0; i < transactions.size(); i++) {
      if (extratos[i] == null) {
        Transaction transaction = transactions.get(i);
        List<TituloImportacaoDto> titulosTransacao = new ArrayList<>();
        List<BaixaImportacaoDto> baixasTransacao = new ArrayList<>();
        List<TransferenciaImportacaoDto> transferenciasTransacao = new ArrayList<>();
        boolean found = processaTitulos(titulos,
          transaction, titulosTransacao, FaseProcessamento.DATA_DIFERENTE);
        found = processaBaixas(baixas,
          transaction, baixasTransacao, FaseProcessamento.DATA_DIFERENTE) || found;
        found = processaTransferencias(transferencias, transaction, contaBancaria,
          transferenciasTransacao, FaseProcessamento.DATA_DIFERENTE) || found;
        if (found) {
          extratos[i] = buildExtratoImportacaoDto(transaction, titulosTransacao, baixasTransacao,
            transferenciasTransacao, FaseProcessamento.DATA_DIFERENTE);
        }
      }
    }

    // todo: implementar a busca de combinações de valores aqui
    // DATA_VALOR_IGUAL apenas
    // apenas baixas e titulos
    // se encontrar uma combinação está ok (não precisa encontrar várias combinações)

    for (int i = 0; i < transactions.size(); i++) {
      if (extratos[i] == null) {
        extratos[i] = buildExtratoImportacaoDto(transactions.get(i));
      }
    }
  }

  private ExtratoImportacaoDto buildExtratoImportacaoDto(Transaction transaction) {
    return ExtratoImportacaoDto.builder()
      .extratoId(transaction.getId())
      .extratoMemo(transaction.getMemo())
      .extratoTipo(getTipoExtrato(transaction))
      .extratoData(transaction.getDatePosted().toInstant()
        .atOffset(ZoneOffset.UTC).toLocalDate())
      .extratoValor(transaction.getBigDecimalAmount())
      .tituloNaoEncontrado(true)
      .tituloEncontradoDataValor(false)
      .tituloEncontradoPercentualDiferente(false)
      .tituloEncontradoDataDiferente(false)
      .build();
  }

  private boolean processaTitulos(
    List<Titulo> titulos, Transaction transaction, List<TituloImportacaoDto> matchedTitulos,
    FaseProcessamento fase
  ) {
    LocalDate transactionDate = transaction.getDatePosted().toInstant()
      .atOffset(ZoneOffset.UTC).toLocalDate();
    BigDecimal transactionValue = transaction.getBigDecimalAmount().abs();

    TipoTitulo tipoTitulo = switch (transaction.getTransactionType()) {
      case DEBIT -> TipoTitulo.PAGAR;
      case CREDIT -> TipoTitulo.RECEBER;
      default -> transaction.getBigDecimalAmount().compareTo(BigDecimal.ZERO) < 0
        ? TipoTitulo.PAGAR : TipoTitulo.RECEBER;
    };
    List<Titulo> titulosBusca = titulos.stream()
      .filter((Titulo t) -> tipoTitulo.equals(t.getTipo()))
      .toList();

    switch (fase) {
      case FaseProcessamento.DATA_VALOR_IGUAL -> {
        for (Titulo titulo : titulosBusca) {
          if (
            titulo.getDataVencimento().isEqual(transactionDate)
              && titulo.getValorCalculado().compareTo(transactionValue) == 0
          ) {
            matchedTitulos.add(buildTituloImportacaoDto(titulo));
            List<Integer> matchedTituloIds = matchedTitulos.stream()
              .map(TituloImportacaoDto::getTituloId).toList();
            titulos.removeIf((Titulo t) -> matchedTituloIds.contains(t.getId()));
          }
        }
      }
      case FaseProcessamento.VALOR_DIFERENTE -> {
        BigDecimal transactionValueMin = transactionValue.subtract(transactionValue.multiply(
          BigDecimal.valueOf(20).divide(BigDecimal.valueOf(100), 2, RoundingMode.FLOOR)));
        BigDecimal transactionValueMax = transactionValue.add(transactionValue.multiply(
          BigDecimal.valueOf(20).divide(BigDecimal.valueOf(100), 2, RoundingMode.CEILING)));
        for (Titulo titulo : titulosBusca) {
          if (
            titulo.getDataVencimento().isEqual(transactionDate)
              && (titulo.getValorCalculado().compareTo(transactionValueMin) >= 0
              && titulo.getValorCalculado().compareTo(transactionValueMax) <= 0)
          ) {
            matchedTitulos.add(buildTituloImportacaoDto(titulo));
            List<Integer> matchedTituloIds = matchedTitulos.stream()
              .map(TituloImportacaoDto::getTituloId).toList();
            titulos.removeIf((Titulo t) -> matchedTituloIds.contains(t.getId()));
          }
        }
      }
      case FaseProcessamento.DATA_DIFERENTE -> {
        LocalDate minDate = transactionDate.minusDays(5);
        LocalDate maxDate = transactionDate.plusDays(5);
        for (Titulo titulo : titulosBusca) {
          if (
            titulo.getValorCalculado().compareTo(transactionValue) == 0
              && (titulo.getDataVencimento().isEqual(minDate) || titulo.getDataVencimento().isAfter(minDate))
              && (titulo.getDataVencimento().isEqual(maxDate) || titulo.getDataVencimento().isBefore(maxDate))
          ) {
            matchedTitulos.add(buildTituloImportacaoDto(titulo));
            List<Integer> matchedTituloIds = matchedTitulos.stream()
              .map(TituloImportacaoDto::getTituloId).toList();
            titulos.removeIf((Titulo t) -> matchedTituloIds.contains(t.getId()));
          }
        }
      }
    }

    return !matchedTitulos.isEmpty();
  }

  private boolean processaBaixas(
    List<TituloBaixa> baixas, Transaction transaction,
    List<BaixaImportacaoDto> matchedBaixas, FaseProcessamento fase
  ) {
    LocalDate transactionDate = transaction.getDatePosted().toInstant()
      .atOffset(ZoneOffset.UTC).toLocalDate();
    BigDecimal transactionValue = transaction.getBigDecimalAmount().abs();

    TipoTitulo tipoTitulo = switch (transaction.getTransactionType()) {
      case DEBIT -> TipoTitulo.PAGAR;
      case CREDIT -> TipoTitulo.RECEBER;
      default -> transaction.getBigDecimalAmount().compareTo(BigDecimal.ZERO) < 0
        ? TipoTitulo.PAGAR : TipoTitulo.RECEBER;
    };
    List<TituloBaixa> baixasBusca = baixas.stream()
      .filter((TituloBaixa b) -> tipoTitulo.equals(b.getTitulo().getTipo()))
      .toList();

    switch (fase) {
      case FaseProcessamento.JA_PROCESSADO -> {
        for (TituloBaixa baixa : baixasBusca) {
          if (
            SituacaoTituloBaixa.CONCILIADO.equals(baixa.getSituacao())
              && StringUtils.isNotBlank(baixa.getFitId())
              && baixa.getFitId().equals(transaction.getId())
              && StringUtils.isNotBlank(baixa.getMemo())
              && baixa.getMemo().equals(transaction.getMemo())
          ) {
            matchedBaixas.add(buildBaixaImportacaoDto(baixa));
            List<Integer> matchedBaixaIds = matchedBaixas.stream()
              .map(BaixaImportacaoDto::getBaixaId).toList();
            baixas.removeIf((TituloBaixa b) -> matchedBaixaIds.contains(b.getId()));
          }
        }
      }
      case FaseProcessamento.DATA_VALOR_IGUAL -> {
        for (TituloBaixa baixa : baixasBusca) {
          if (
            baixa.getDataBaixa().isEqual(transactionDate)
              && baixa.getValorBaixa().compareTo(transactionValue) == 0
          ) {
            matchedBaixas.add(buildBaixaImportacaoDto(baixa));
            List<Integer> matchedBaixaIds = matchedBaixas.stream()
              .map(BaixaImportacaoDto::getBaixaId).toList();
            baixas.removeIf((TituloBaixa b) -> matchedBaixaIds.contains(b.getId()));
          }
          }
      }
      case FaseProcessamento.VALOR_DIFERENTE -> {
        BigDecimal transactionValueMin = transactionValue.subtract(transactionValue.multiply(
          BigDecimal.valueOf(20).divide(BigDecimal.valueOf(100), 2, RoundingMode.FLOOR)));
        BigDecimal transactionValueMax = transactionValue.add(transactionValue.multiply(
          BigDecimal.valueOf(20).divide(BigDecimal.valueOf(100), 2, RoundingMode.CEILING)));
        for (TituloBaixa baixa : baixasBusca) {
          if (
            baixa.getDataBaixa().isEqual(transactionDate)
              && (baixa.getValorBaixa().compareTo(transactionValueMin) >= 0
              && baixa.getValorBaixa().compareTo(transactionValueMax) <= 0)
          ) {
            matchedBaixas.add(buildBaixaImportacaoDto(baixa));
            List<Integer> matchedBaixaIds = matchedBaixas.stream()
              .map(BaixaImportacaoDto::getBaixaId).toList();
            baixas.removeIf((TituloBaixa b) -> matchedBaixaIds.contains(b.getId()));
          }
        }
      }
      case FaseProcessamento.DATA_DIFERENTE -> {
        LocalDate minDate = transactionDate.minusDays(5);
        LocalDate maxDate = transactionDate.plusDays(5);
        for (TituloBaixa baixa : baixasBusca) {
          if (
            baixa.getValorBaixa().compareTo(transactionValue) == 0
              && (baixa.getDataBaixa().isEqual(minDate) || baixa.getDataBaixa().isAfter(minDate))
              && (baixa.getDataBaixa().isEqual(maxDate) || baixa.getDataBaixa().isBefore(maxDate))
          ) {
            matchedBaixas.add(buildBaixaImportacaoDto(baixa));
            List<Integer> matchedBaixaIds = matchedBaixas.stream()
              .map(BaixaImportacaoDto::getBaixaId).toList();
            baixas.removeIf((TituloBaixa b) -> matchedBaixaIds.contains(b.getId()));
          }
        }
      }
    }
    return !matchedBaixas.isEmpty();
  }

  private boolean processaTransferencias(
    List<Transacao> transacoes, Transaction transaction, ContaBancaria contaBancaria,
    List<TransferenciaImportacaoDto> matchedTransferencias, FaseProcessamento fase
  ) {
    LocalDate transactionDate = transaction.getDatePosted().toInstant()
      .atOffset(ZoneOffset.UTC).toLocalDate();
    BigDecimal transactionValue = transaction.getBigDecimalAmount().abs();

    for (Transacao transacao : transacoes) {
      TransacaoTransferencia transferencia = transacao.getTransferencia();
      MovimentacaoBancaria movimentacaoOrigem = transferencia.getMovimentacaoOrigem();
      MovimentacaoBancaria movimentacaoDestino = transferencia.getMovimentacaoDestino();

      if (movimentacaoOrigem.getContaBancaria().getId().equals(contaBancaria.getId())) {
        if (DEBIT.equals(getTipoExtrato(transaction))) {
          LocalDate dataTransacaoOrigem = movimentacaoOrigem.getDataMovimento().toLocalDate();
          BigDecimal valorTransacaoOrigem = movimentacaoOrigem.getValorMovimento().abs();
          boolean matches = switch (fase) {
            case FaseProcessamento.JA_PROCESSADO ->
              SituacaoMovimentacaoBancaria.CONCILIADO.equals(movimentacaoOrigem.getSituacao())
                && StringUtils.isNotBlank(movimentacaoOrigem.getFitId())
                && movimentacaoOrigem.getFitId().equals(transaction.getId())
                && StringUtils.isNotBlank(movimentacaoOrigem.getMemo())
                && movimentacaoOrigem.getMemo().equals(transaction.getMemo());
            case FaseProcessamento.DATA_VALOR_IGUAL ->
              dataTransacaoOrigem.equals(transactionDate)
                && valorTransacaoOrigem.compareTo(transactionValue) == 0;
            case FaseProcessamento.DATA_DIFERENTE ->
              valorTransacaoOrigem.compareTo(transactionValue) == 0
                && (dataTransacaoOrigem.isAfter(transactionDate.minusDays(5))
                && dataTransacaoOrigem.isBefore(transactionDate.plusDays(5)));
            default -> false;
          };
          if (matches) {
            matchedTransferencias.add(buildTransferenciaOrigemImportacaoDto(transacao));
            List<Integer> matchedTransferenciaIds = matchedTransferencias.stream()
              .map(TransferenciaImportacaoDto::getIdTransacao).toList();
            transacoes.removeIf((Transacao t) -> matchedTransferenciaIds.contains(t.getId()));
          }
        }
      }
      if (movimentacaoDestino.getContaBancaria().getId().equals(contaBancaria.getId())) {
        if (CREDIT.equals(getTipoExtrato(transaction))) {
          LocalDate dataTransacaoDestino = movimentacaoDestino.getDataMovimento().toLocalDate();
          BigDecimal valorTransacaoDestino = movimentacaoDestino.getValorMovimento();
          boolean matches = switch (fase) {
            case FaseProcessamento.JA_PROCESSADO ->
              SituacaoMovimentacaoBancaria.CONCILIADO.equals(movimentacaoDestino.getSituacao())
                && StringUtils.isNotBlank(movimentacaoDestino.getFitId())
                && movimentacaoDestino.getFitId().equals(transaction.getId())
                && StringUtils.isNotBlank(movimentacaoDestino.getMemo())
                && movimentacaoDestino.getMemo().equals(transaction.getMemo());
            case FaseProcessamento.DATA_VALOR_IGUAL ->
              dataTransacaoDestino.equals(transactionDate) &&
                valorTransacaoDestino.compareTo(transactionValue) == 0;
            case FaseProcessamento.DATA_DIFERENTE ->
              valorTransacaoDestino.compareTo(transactionValue) == 0
                && (dataTransacaoDestino.isAfter(transactionDate.minusDays(5))
                && dataTransacaoDestino.isBefore(transactionDate.plusDays(5)));
            default -> false;
          };
          if (matches) {
            matchedTransferencias.add(buildTransferenciaDestinoImportacaoDto(transacao));
            List<Integer> matchedTransferenciaIds = matchedTransferencias.stream()
              .map(TransferenciaImportacaoDto::getIdTransacao).toList();
            transacoes.removeIf((Transacao t) -> matchedTransferenciaIds.contains(t.getId()));
          }
        }
      }
    }
    return !matchedTransferencias.isEmpty();
  }

  private ExtratoImportacaoDto buildExtratoImportacaoDto(
    Transaction transaction,
    List<TituloImportacaoDto> titulos, List<BaixaImportacaoDto> baixas,
    List<TransferenciaImportacaoDto> transferencias,
    FaseProcessamento fase
  ) {
    return ExtratoImportacaoDto.builder()
      .extratoId(transaction.getId())
      .extratoMemo(transaction.getMemo())
      .extratoTipo(getTipoExtrato(transaction))
      .extratoData(transaction.getDatePosted().toInstant()
        .atOffset(ZoneOffset.UTC).toLocalDate())
      .extratoValor(transaction.getBigDecimalAmount())
      .titulos(titulos)
      .baixas(baixas)
      .transferencias(transferencias)
      .tituloNaoEncontrado(false)
      .tituloProcessado(FaseProcessamento.JA_PROCESSADO.equals(fase))
      .tituloEncontradoDataValor(FaseProcessamento.DATA_VALOR_IGUAL.equals(fase))
      .tituloEncontradoPercentualDiferente(FaseProcessamento.VALOR_DIFERENTE.equals(fase))
      .tituloEncontradoDataDiferente(FaseProcessamento.DATA_DIFERENTE.equals(fase))
      .build();
  }

  private ContaBancaria fetchBankAccount(Integer contaBancariaId) {
    return contaBancariaRepository.findById(contaBancariaId)
      .orElseThrow(() -> new PreconditionException("Conta bancária não encontrada."));
  }

  private ExtratoImportacaoDtoOut buildExtratoImportacaoDtoOut(
    Integer contaBancariaId, LocalDate dataInicial, LocalDate dataFinal,
    ContaBancaria contaBancaria, TransactionList transactionList,
    ExtratoImportacaoDto[] extratos
  ) {
    return ExtratoImportacaoDtoOut.builder()
      .contaBancariaId(contaBancariaId)
      .dataInicial(dataInicial)
      .dataFinal(dataFinal)
      .periodoExtratoInicial(get(transactionList, TransactionList::getStart))
      .periodoExtratoFinal(get(transactionList, TransactionList::getEnd))
      .extratoBancoId(get(contaBancaria, ContaBancaria::getBanco, Banco::getId))
      .extratoBancoCodigo(get(contaBancaria, ContaBancaria::getBanco, Banco::getCodigo))
      .extratoBancoNome(get(contaBancaria, ContaBancaria::getBanco, Banco::getNome))
      .extratoAgencia(get(contaBancaria, ContaBancaria::getAgencia))
      .extratoContaCorrente(get(contaBancaria, ContaBancaria::getNumConta))
      .extratoContaCorrenteId(get(contaBancaria, ContaBancaria::getId))
      .extratoContaCorrenteNome(get(contaBancaria, ContaBancaria::getNome))
      .extratos(Arrays.asList(extratos))
      .build();
  }

  private TituloImportacaoDto buildTituloImportacaoDto(Titulo titulo) {
    return TituloImportacaoDto.builder()
      .tituloId(titulo.getId())
      .tituloSituacao(titulo.getSituacao())
      .isTituloConciliado(SituacaoTitulo.CONCILIADO.equals(titulo.getSituacao()))
      .fornecedorId(titulo.getFornecedor().getId())
      .fornecedorNome(titulo.getFornecedor().getNome())
      .valor(titulo.getValorCalculado())
      .dataVencimento(titulo.getDataVencimento())
      .build();
  }

  private BaixaImportacaoDto buildBaixaImportacaoDto(TituloBaixa baixa) {
    Titulo titulo = baixa.getTitulo();
    return BaixaImportacaoDto.builder()
      .tituloId(titulo.getId())
      .tituloSituacao(titulo.getSituacao())
      .baixaId(baixa.getId())
      .baixaSituacao(baixa.getSituacao())
      .isBaixaConciliada(SituacaoTituloBaixa.CONCILIADO.equals(baixa.getSituacao()))
      .fornecedorId(titulo.getFornecedor().getId())
      .fornecedorNome(titulo.getFornecedor().getNome())
      .dataVencimento(titulo.getDataVencimento())
      .dataPagamento(baixa.getDataBaixa())
      .valor(baixa.getValorBaixa())
      .build();
  }

  private TransferenciaImportacaoDto buildTransferenciaOrigemImportacaoDto(Transacao transacao) {
    MovimentacaoBancaria mo = transacao.getTransferencia().getMovimentacaoOrigem();
    MovimentacaoBancaria md = transacao.getTransferencia().getMovimentacaoDestino();
    return TransferenciaImportacaoDto.builder()
      .idTransferencia(transacao.getId())
      .valorTransferencia(transacao.getValorTransacao())
      .dataTransferencia(mo.getDataMovimento().toLocalDate())
      .isTransferenciaConciliada(SituacaoMovimentacaoBancaria.CONCILIADO.equals(mo.getSituacao()))
      .contaOrigem(mo.getContaBancaria().getNome())
      .contaDestino(md.getContaBancaria().getNome())
      .situacaoMovimentacaoBancaria(mo.getSituacao())
      .build();
  }

  private TransferenciaImportacaoDto buildTransferenciaDestinoImportacaoDto(Transacao transacao) {
    MovimentacaoBancaria mo = transacao.getTransferencia().getMovimentacaoOrigem();
    MovimentacaoBancaria md = transacao.getTransferencia().getMovimentacaoDestino();
    return TransferenciaImportacaoDto.builder()
      .idTransferencia(md.getId())
      .valorTransferencia(transacao.getValorTransacao())
      .dataTransferencia(md.getDataMovimento().toLocalDate())
      .isTransferenciaConciliada(SituacaoMovimentacaoBancaria.CONCILIADO.equals(md.getSituacao()))
      .contaOrigem(mo.getContaBancaria().getNome())
      .contaDestino(md.getContaBancaria().getNome())
      .situacaoMovimentacaoBancaria(md.getSituacao())
      .build();
  }

  public void realizaConciliacaoExtrato(RealizaBaixaDtoIn payload) {
    List<Integer> tituloIds = getOrElse(payload.getTitulos(), List.of());
    List<Integer> baixaIds = getOrElse(payload.getBaixas(), List.of());
    List<Integer> transferenciaIds = getOrElse(payload.getTransferencias(), List.of());

    Preconditions.checkTrue(
      !tituloIds.isEmpty() || !baixaIds.isEmpty() || !transferenciaIds.isEmpty(),
      "É necessário informar ao menos um título, baixa ou transferência para conciliar."
    );
    Preconditions.checkTrue(
      tituloIds.isEmpty() && baixaIds.isEmpty() || transferenciaIds.isEmpty(),
      "Não é possível conciliar títulos e baixas com transferências."
    );
    if (!transferenciaIds.isEmpty()) {
      Preconditions.checkTrue(
        transferenciaIds.size() == 1,
        "Não é possível conciliar mais de uma transferência."
      );
    }

    List<Titulo> titulos = tituloRepository.findTitulosParaConciliacaoExtrato(tituloIds);
    Preconditions.checkTrue(tituloIds.size() == titulos.size(),
      ("Divergência na busca dos títulos selecionados com o banco de dados. " +
        "Títulos informados: %s, Titulos encontrados: %s").formatted(
          tituloIds.size(), titulos.size()));
    List<TituloBaixa> baixas = tituloBaixaRepository.findBaixasParaConciliacaoExtrato(baixaIds);
    Preconditions.checkTrue(baixas.size() == baixaIds.size(),
      "Divergência na busca das baixas selecionadas com o banco de dados. " +
        "Baixas informadas: %s, Baixas encontradas: %s".formatted(
          baixaIds.size(), baixas.size()));
    List<Transacao> transferencias
      = transacaoRepository.findTransferenciasParaConciliacaoExtrato(transferenciaIds);
    Preconditions.checkTrue(transferencias.size() == transferenciaIds.size(),
      "Divergência na busca das transferências selecionadas com o banco de dados. " +
        "Transferências informadas: %s, Transferências encontradas: %s".formatted(
          transferenciaIds.size(), transferencias.size()));

    if (
      Boolean.FALSE.equals(payload.getAtualizaValor())
        || Objects.isNull(payload.getAtualizaValor())
    ) {
      validaValorExtrato(titulos, baixas, transferencias, payload.getValorBaixa().abs());
    }

    for (Titulo titulo : titulos) {
      TituloBaixaDto baixa = tituloBaixaService.createBaixa(
        titulo.getId(), titulo.getTipo(), Boolean.FALSE,
        TituloBaixaDto.builder()
          .titulo(modelMapper.map(titulo))
          .dataBaixa(payload.getDataVencimento())
          .valorBaixa(titulo.getValorCalculado())
          .fitId(payload.getFitId())
          .memo(payload.getMemo())
          .valorJuros(Boolean.TRUE.equals(payload.getAtualizaValor())
            ? getOrElse(payload.getValorJuros(), BigDecimal.ZERO) : BigDecimal.ZERO)
          .valorMulta(Boolean.TRUE.equals(payload.getAtualizaValor())
            ? getOrElse(payload.getValorMulta(), BigDecimal.ZERO) : BigDecimal.ZERO)
          .valorAbatimento(Boolean.TRUE.equals(payload.getAtualizaValor())
            ? getOrElse(payload.getValorAbatimento(), BigDecimal.ZERO) : BigDecimal.ZERO)
          .valorDesconto(Boolean.TRUE.equals(payload.getAtualizaValor())
            ? getOrElse(payload.getValorDesconto(), BigDecimal.ZERO) : BigDecimal.ZERO)
          .contaBancariaId(titulo.getContaBancaria().getId())
          .formaPagamentoId(getOrElse(
            titulo.getFormaPagamento(), FormaPagamento::getId,
            FormaPagamento.ID_FORMA_PAGAMENTO_DINHEIRO))
          .build());
      tituloBaixaService.findById(baixa.getId())
        .ifPresent(tituloBaixaService::conciliacaoBaixaPorUploadDeExtrato);
    }

    for (TituloBaixa baixa : baixas) {
      if (
        payload.getDataVencimento() != null
        && Boolean.TRUE.equals(payload.getAtualizaDataVencimento())
      ) {
        baixa.setDataBaixa(payload.getDataVencimento());
      }
      if (Boolean.TRUE.equals(payload.getAtualizaValor())) {
        baixa.setValorJuros(getOrElse(payload.getValorJuros(), BigDecimal.ZERO));
        baixa.setValorMulta(getOrElse(payload.getValorMulta(), BigDecimal.ZERO));
        baixa.setValorAbatimento(getOrElse(payload.getValorAbatimento(), BigDecimal.ZERO));
        baixa.setValorDesconto(getOrElse(payload.getValorDesconto(), BigDecimal.ZERO));
        baixa.setValorBaixa(payload.getValorBaixa());
      }
      tituloBaixaService.conciliacaoBaixaPorUploadDeExtrato(
        baixa, payload.getFitId(), payload.getMemo());
    }

    for (Transacao transferencia : transferencias) {
      switch (payload.getTipo()) {
        case DEBIT -> {
          MovimentacaoBancaria movimentacaoOrigem = transferencia.getTransferencia()
            .getMovimentacaoOrigem();
          LocalDateTime dataVencimentoOrigem = payload.getDataVencimento() != null
            && Boolean.TRUE.equals(payload.getAtualizaDataVencimento())
            ? payload.getDataVencimento().atStartOfDay() : movimentacaoOrigem.getDataMovimento();
          movimentacaoBancariaService.conciliacaoTransferenciaPorUploadDeExtrato(
            movimentacaoOrigem, dataVencimentoOrigem, payload.getFitId(), payload.getMemo());
        }
        case CREDIT -> {
          MovimentacaoBancaria movimentacaoDestino = transferencia.getTransferencia()
            .getMovimentacaoDestino();
          LocalDateTime dataVencimentoDestino = payload.getDataVencimento() != null
            && Boolean.TRUE.equals(payload.getAtualizaDataVencimento())
            ? payload.getDataVencimento().atStartOfDay() : movimentacaoDestino.getDataMovimento();
          movimentacaoBancariaService.conciliacaoTransferenciaPorUploadDeExtrato(
            movimentacaoDestino, dataVencimentoDestino, payload.getFitId(), payload.getMemo());
        }
      }
    }
  }

  private void validaValorExtrato(
    List<Titulo> titulos, List<TituloBaixa> baixas, List<Transacao> transferencias,
    BigDecimal valorExtrato
  ) {
    BigDecimal valorCalculado = BigDecimal.ZERO
      .add(titulos.stream().map(Titulo::getValorCalculado)
        .reduce(BigDecimal.ZERO, BigDecimal::add))
      .add(baixas.stream().map(TituloBaixa::getValorCalculado)
        .reduce(BigDecimal.ZERO, BigDecimal::add))
      .add(transferencias.stream().map(Transacao::getValorTransacao)
        .reduce(BigDecimal.ZERO, BigDecimal::add));
    if (valorCalculado.compareTo(valorExtrato.abs()) != 0) {
//      log.debug("valorCalculado: {}, valorExtrato: {}, valorTitulos: {}, valorBaixas: {}, valorTransferencias: {}",
//        valorCalculado, valorExtrato,
//        titulos.stream().map(Titulo::getValorCalculado).reduce(BigDecimal.ZERO, BigDecimal::add),
//        baixas.stream().map(TituloBaixa::getValorCalculado).reduce(BigDecimal.ZERO, BigDecimal::add),
//        transferencias.stream().map(MovimentacaoBancaria::getValorMovimento).reduce(BigDecimal.ZERO, BigDecimal::add)
//      );
      throw new RuntimeException(
        "A soma das títulos, baixas e transferências é diferente do valor do extrato.");
    }
  }

  private TransactionType getTipoExtrato(Transaction transaction) {
    return switch (transaction.getTransactionType()) {
      case DEBIT -> DEBIT;
      case CREDIT -> CREDIT;
      default -> transaction.getBigDecimalAmount().compareTo(BigDecimal.ZERO) < 0
        ? DEBIT : CREDIT;
    };
  }
}
