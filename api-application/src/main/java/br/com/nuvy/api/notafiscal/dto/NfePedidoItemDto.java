package br.com.nuvy.api.notafiscal.dto;

import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NfePedidoItemDto {

  private UUID id;

  @Size(max = 255)
  private String descricaoComercial;

  private String cfop;
  private String ean;
  private String eanTrib;

  private String codigo;
  private String observacao;
  private BigDecimal quantidade;
  private BigDecimal precoUnitario;
  private BigDecimal valorDesconto;
  private BigDecimal precoTotal;
  private BigDecimal valorTotal;
  private BigDecimal quantidadeTributavel;
  private BigDecimal valorUnitarioTributavel;
  private BigDecimal valorTributavel;

  private BigDecimal valorFrete;
  private BigDecimal valorOutro;
  private BigDecimal valorSeguro;

  private BigDecimal valorTotalTributos;

  private String csosn;
  private String cst;
  private String origem;

  private List<NfePedidoItemEstoqueDto> rastros;

  private String fci;
  private String cest;
  private String cnpjFabricante;
  private String codigoBeneficioFiscal;
  private String ncm;

  private Boolean pedidoCompraCliente;
  private String numeroPedidoCompraCliente;
  private Integer numeroItemPedidoCompraCliente;

  private BigDecimal aliquotaIcms;
  private BigDecimal aliquotaCreditoIcms;
  private BigDecimal aliquotaIcmsInterna;
  private BigDecimal aliquotaFcp;

  private String extTIPI;
  private String cstIpi;
  private BigDecimal aliquotaIpi;
  private BigDecimal valorIpi;
  private BigDecimal valorIpiDevolvido;
  private BigDecimal valorBaseCalculoIpi;
  private String codigoEnquadramentoIpi;
  private String codigoSeloIpi;
  private String cnpjProdutorIpi;
  private BigInteger quantidadeSeloIpi;

  private String cstPis;
  private String cstCofins;
  private BigDecimal aliquotaPis;
  private BigDecimal aliquotaCofins;
  private BigDecimal valorUnitarioPis;
  private BigDecimal valorUnitarioCofins;
  private BigDecimal percentualBaseCalculoPis;
  private BigDecimal percentualBaseCalculoCofins;
  private BigDecimal valorPis;
  private BigDecimal valorCofins;

  // Tributos devolvidos
  private BigDecimal percentualBaseCalculoIcms;
  private BigDecimal percentualBaseCalculoIcmsPropria;
  private BigDecimal percentualMva;
  private BigDecimal percentualBaseCalculoSt;
  private BigDecimal percentualBaseCalculoIpi;

  private BigDecimal percentualBaseCalculoDifal;
  private BigDecimal percentualFcpDifal;
  private String modalidadeBaseCalculo;
  private String modalidadeBaseCalculoSt;
  private BigDecimal valorIcms;
  private BigDecimal valorIcmsDesonerado;
  private String motivoDesoneracaoIcms;
  private BigDecimal percentualCreditoSimplesNacional;
  private BigDecimal valorCreditoSimplesNacional;
  private BigDecimal valorIcmsSt;
  private BigDecimal valorFcp;
  private BigDecimal valorFcpSt;
  private BigDecimal valorIcmsInterna;
  private BigDecimal percentualDiferimento;
  private BigDecimal valorIcmsDiferimento;

  private BigDecimal valorBaseCalculoIcms;
  private BigDecimal valorBaseCalculoIcmsSt;
  private BigDecimal valorBaseCalculoIcmsPropria;
  private BigDecimal valorMva;
  private BigDecimal valorBaseCalculoSt;

  private BigDecimal valorBaseCalculoPis;
  private BigDecimal valorBaseCalculoCofins;
  private BigDecimal valorBaseCalculoFcp;
  private BigDecimal valorBaseCalculoDifal;
  private BigDecimal valorBaseCalculoFcpDifal;
  private BigDecimal valorIcmsDifal;
  private BigDecimal valorFcpDifal;
  private BigDecimal valorUnidadeTibutavelPis;
  private BigDecimal valorUnidadeTibutavelCofins;
  private BigDecimal quantidadeUnidadeTributavelPis;
  private BigDecimal quantidadeUnidadeTributavelCofins;

  private String unidadeMedida;
  private String unidadeMedidaTributavel;

  // Partilha do ICMS
  private BigDecimal valorBaseCalculoDestino;
  private BigDecimal valorBCFundoCombatePobrezaDestino;
  private BigDecimal percentualRelativoFundoCombatePobrezaDestino;
  private BigDecimal percentualAliquotaInternaDestino;
  private BigDecimal percentualInterestadual;
  private BigDecimal percentualProvisorioPartilha;
  private BigDecimal valorRelativoFundoCombatePobrezaDestino;
  private BigDecimal valorICMSInterestadualDestino;
  private BigDecimal valorICMSInterestadualRemetente;

  // CSOSN 500 - ICMS Retido por ST
  private BigDecimal percentualICMSSTRetido;
  private BigDecimal valorBCICMSSTRetido;
  private BigDecimal valorICMSSubstituto;
  private BigDecimal valorICMSSTRetido;
  private BigDecimal valorBCFundoCombatePobrezaRetidoST;
  private BigDecimal percentualFundoCombatePobrezaRetidoST;
  private BigDecimal valorFundoCombatePobrezaRetidoST;

}
