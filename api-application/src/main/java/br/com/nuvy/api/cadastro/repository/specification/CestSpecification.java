package br.com.nuvy.api.cadastro.repository.specification;

import br.com.nuvy.api.cadastro.filter.CestFilter;
import br.com.nuvy.api.cadastro.model.Cest;
import br.com.nuvy.api.cadastro.model.Cest_;
import br.com.nuvy.common.query.PredicateBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;

@RequiredArgsConstructor(staticName = "from")
public class CestSpecification implements Specification<Cest> {

  private final CestFilter filter;

  @Override
  public Predicate toPredicate(Root<Cest> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getDescricao(),
        e -> iLike(criteriaBuilder, root.get(Cest_.descricao), e))
      .and();
  }
}
