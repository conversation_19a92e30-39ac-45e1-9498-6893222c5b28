package br.com.nuvy.api.seguranca.service;

import br.com.nuvy.api.seguranca.model.Usuario;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Service
public class TokenService {

  private String secret = "2567a5ec9705eb7ac2c984033e06189d";
  private String issuer = "API nuvy";

  public String gerarToken(Usuario usuario) {
    try {
      var algorithm = Algorithm.HMAC256(secret);
      return JWT.create()
        .withIssuer(issuer)
        .withSubject(usuario.getId().toString())
        .withExpiresAt(dataExpiracao())
        .withClaim("id", String.valueOf(usuario.getId()))
        .withClaim("nome", usuario.getNome())
        .withClaim("email", usuario.getEmail())
        .sign(algorithm);
    } catch (JWTCreationException exception) {
      throw new RuntimeException("erro ao gerrar token jwt", exception);
    }
  }

  public String getSubject(String tokenJWT) {
    try {
      var algoritmo = Algorithm.HMAC256(secret);
      return JWT.require(algoritmo)
        .withIssuer(issuer)
        .build()
        .verify(tokenJWT)
        .getSubject();
    } catch (JWTVerificationException exception) {
      throw new RuntimeException("Token JWT inválido ou expirado!");
    }
  }

  private Instant dataExpiracao() {
    return LocalDateTime.now().plusYears(10).toInstant(ZoneOffset.of("-03:00"));
  }

}


