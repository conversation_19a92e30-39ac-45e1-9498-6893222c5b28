package br.com.nuvy.api.financeiro.repository;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.financeiro.model.CentroCusto;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CentroCustoRepository extends NuvyRepository<CentroCusto, Integer> {

  @Query(value = "SELECT cc FROM CentroCusto cc WHERE cc.parent IS NULL")
  List<CentroCusto> findCentroCustoPai();

  List<CentroCusto> findByParentId(Integer id);

  Optional<CentroCusto> findByIdAndParentIsNull(Integer id);

  Optional<CentroCusto> findByNomeAndParentIsNull(String nome);

  Optional<CentroCusto> findByNomeAndParentId(String nome, Integer id);

  List<CentroCusto> findAllBySituacao(Situacao situacao);


}
