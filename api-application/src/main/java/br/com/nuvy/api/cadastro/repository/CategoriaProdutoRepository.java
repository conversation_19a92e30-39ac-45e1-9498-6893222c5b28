package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.CategoriaProduto;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CategoriaProdutoRepository extends NuvyRepository<CategoriaProduto, Integer> {

  List<CategoriaProduto> findByCategoriaPaiId(Integer categoriaPaiId);

  @Query("select c from CategoriaProduto c where c.categoriaPai = null")
  List<CategoriaProduto> findGrupos();

  List<CategoriaProduto> findByNome(String nome);

  Optional<CategoriaProduto> findFirstByNomeAndCategoriaPaiId(String nome, Integer categoriaPaiId);

  Page<CategoriaProduto> findByCategoriaPaiIsNull(Pageable pageable);

  List<CategoriaProduto> findByCategoriaPaiIsNull();

  List<CategoriaProduto> findByCategoriaPaiIsNullAndSituacao(Situacao situacao);

  @Query("""
    SELECT c.id
    FROM CategoriaProduto c
    WHERE c.situacao = 'ATIVO'
    AND EXISTS(
      SELECT 1
      FROM Catalogo cat
      LEFT JOIN cat.categoriasRelacionadas ccr
      LEFT JOIN ccr.categoria ccrc
      WHERE ccrc.id = c.id
      AND EXISTS(
        SELECT 1
        FROM CanalVenda cv
        INNER JOIN cv.origem cvo
        WHERE cv.id = :canalVendaId
        AND cv.catalogo = cat
        AND cvo.integrador = 'PLUG4MARKET'
        AND cvo.situacao = true
        AND cv.situacao = 'ATIVO'
      )
    )
    ORDER BY c.id
    """)
  List<Integer> findCategoriasSincronizarPlug4market(Integer canalVendaId);

}
