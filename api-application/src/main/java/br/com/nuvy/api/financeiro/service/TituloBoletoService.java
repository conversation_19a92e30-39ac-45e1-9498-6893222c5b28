package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.api.enums.SituacaoBoleto.RECEBIDO;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;

import br.com.nuvy.api.enums.SituacaoBoleto;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.model.TituloBoleto;
import br.com.nuvy.api.financeiro.repository.TituloBoletoRepository;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.sqs.dto.RegistroBoletoPagamentoRecebidoBodyDto;
import br.com.nuvy.api.sqs.dto.RegistroBoletoPagamentoRecebidoBodyDto.BoletoRecebidoKobanaWebhookDto;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.base.AplicacaoIdEmpresaId;
import br.com.nuvy.client.dto.BoletoDto;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.exception.SneakyException;
import br.com.nuvy.common.file.File;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.BoletoDownloadDtoOut;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class TituloBoletoService extends
  PageableServiceAdapter<TituloBoleto, Integer, NoFilter, TituloBoletoRepository> {

  private final TituloRepository tituloRepository;

  public TituloBoleto save(TituloBoleto tituloBoleto) {
    return repository.save(tituloBoleto);
  }

  public TituloBoleto saveAndFlush(TituloBoleto tituloBoleto) {
    return repository.saveAndFlush(tituloBoleto);
  }

  public Optional<TituloBoleto> findAbertoByTituloId(Integer tituloId) {
    return repository.findByTituloIdAndSituacaoIn(
      tituloId,
      List.of(SituacaoBoleto.CRIADO, SituacaoBoleto.RECEBIDO)
    );
  }

  public Optional<TituloBoleto> findCancelamentoByTituloId(Integer tituloId) {
    return repository.findByTituloIdAndSituacaoIn(
      tituloId,
      List.of(SituacaoBoleto.RECEBIDO, SituacaoBoleto.EM_CANCELAMENTO)
    );
  }

  public TituloBoleto findByTituloId(Integer tituloId) {
    return repository.findByTituloIdAndSituacaoNot(tituloId, SituacaoBoleto.CANCELADO)
      .orElseThrow(ResourceNotFoundException::new);
  }

  public TituloBoleto findByTituloIdAndSituacao(Integer tituloId, SituacaoBoleto situacao) {
    return repository.findByTituloIdAndSituacao(tituloId, situacao)
      .orElseThrow(ResourceNotFoundException::new);
  }

  public Optional<TituloBoleto> findByTituloIdAndSituacaoRecebido(Integer tituloId) {
    return repository.findByTituloIdAndSituacao(tituloId, RECEBIDO);
  }

  public TituloBoleto findByServicoBoletoId(String servicoBoletoId) {
    return repository.findByIdServicoBoleto(servicoBoletoId)
      .orElseThrow(ResourceNotFoundException::new);
  }

  public List<TituloBoleto> findByRemessaGeradaId(Integer remessaId) {
    return repository.findByIdRemessaGerada(remessaId);
  }

  public BoletoDownloadDtoOut geraDownloadBoleto(Integer tituloBoleto, Boolean isCarne) {
    var boleto = repository.findByTituloIdAndSituacao(tituloBoleto, SituacaoBoleto.RECEBIDO)
      .orElseThrow(ResourceNotFoundException::new);
    if (isCarne && boleto.getBoletoCarne() != null && !boleto.getBoletoCarne().isBlank()) {
      return BoletoDownloadDtoOut.builder().urlBoleto(boleto.getBoletoCarne().trim()).build();
    } else {
      return BoletoDownloadDtoOut.builder().urlBoleto(boleto.getBoletoPdf().trim()).build();
    }
  }

  public Boolean existsByTituloId(Integer tituloId) {
    return repository.existsByTituloId(tituloId);
  }

  public Boolean existsByTituloIdAndSituacao(Integer tituloId, SituacaoBoleto situacao) {
    return repository.existsByTituloIdAndSituacao(tituloId, situacao);
  }

  public void adicionaBoletosParaEnvioEmail(List<File> anexos, Pedido pedido) {
    var titulos = tituloRepository.findAllByPedidoId(pedido.getId());

    adicionaBoletosParaEnvioEmail(anexos, titulos);
  }

  public void adicionaBoletosParaEnvioEmail(List<File> anexos, OrdemServico ordemServico) {
    log.debug("Adicionando boletos ao email, se houver");
    var titulos = tituloRepository.findAllByOrdemServico(ordemServico);
    adicionaBoletosParaEnvioEmail(anexos, titulos);
  }

  @Transactional
  public void adicionaBoletosParaEnvioEmail(List<File> anexos, Titulo titulo) {
    var titulos = tituloRepository.findAllByTituloReferenciaIdOrId(titulo.getId(), titulo.getId());
    adicionaBoletosParaEnvioEmail(anexos, titulos);
  }

  private void adicionaBoletosParaEnvioEmail(List<File> anexos, List<Titulo> titulos) {
    log.debug("Inicio da adição de boletos ao email");

    Map<Titulo, TituloBoleto> titulosBoletosMap = titulos.stream()
      .collect(Collectors.toMap(titulo -> titulo,
        titulo -> titulo.getTituloBoletos().stream()
          .filter(boleto -> boleto.getSituacao().equals(RECEBIDO))
          .findFirst().orElse(new TituloBoleto())));

    List<File> boletosAnexos = new ArrayList<>();
    if (titulosBoletosMap.values().stream().noneMatch((TituloBoleto boleto) -> boleto.getId() == null)) {
      titulosBoletosMap.forEach(
        (Titulo titulo, TituloBoleto boleto) -> {
          File file = montaBoletos(titulo, boleto);
            boletosAnexos.add(file);
        });
    } else {
      log.debug("Não foi possível adicionar todos os boletos ao email");
    }

    if (boletosAnexos.isEmpty()) {
      return;
    }

    if (boletosAnexos.stream().anyMatch(Objects::isNull)) {
      log.debug("Não foi possível adicionar todos os boletos ao email");
      throw new SneakyException("enviando.email.para.retry");
    }
    anexos.addAll(boletosAnexos);
    log.debug("Fim da adição de boletos ao email");
  }

  private File montaBoletos(Titulo titulo, TituloBoleto boleto) {
    if (boleto.getBoletoPdf() == null) {
      return null;
    }

    File boletoFile = null;
    try {
      URL url = new URI(boleto.getBoletoPdf()).toURL();
      URLConnection urlConnection = url.openConnection();
      urlConnection.setRequestProperty("User-Agent", "Mozilla/5.0");
      InputStream inputStream = urlConnection.getInputStream();
      boletoFile = File.builder()
        .fileName("boleto" + "(" + titulo.getNumeroParcela() + ").pdf")
        .bytes(inputStream.readAllBytes())
        .type("pdf")
        .inputStream(inputStream)
        .build();
      inputStream.close();
    } catch (Exception e) {
      log.error("Erro ao adicionar boleto ao email: ", e);
    }
    return boletoFile;
  }

  public List<BoletoDto> buscaBoletosParaImpressao(Pedido pedido) {
    boolean deveGerarBoleto = FormaPagamento.ID_FORMA_PAGAMENTO_BOLETO
      .equals(pedido.getFormaRecebimento().getId());

    Map<Titulo, String> titulosBoletosMap = new HashMap<>();
    if (deveGerarBoleto) {
      titulosBoletosMap = tituloRepository.findAllTitulosAndBoletosByPedidoId(pedido.getId())
        .stream()
        .collect(Collectors.toMap(titulo -> titulo, titulo -> titulo.getTituloBoletos().stream()
          .filter(boleto -> boleto.getSituacao().equals(RECEBIDO)).findFirst()
          .map(TituloBoleto::getBoletoPdf)
          .orElse("")));
    }
    List<BoletoDto> boletos = new ArrayList<>();
    for (var tituloMap : titulosBoletosMap.entrySet()) {
      if (!tituloMap.getValue().isBlank()) {
        boletos.add(BoletoDto.builder().url(tituloMap.getValue())
          .numeroBoleto(tituloMap.getKey().getNumeroParcela()).build());
      }
    }
    return boletos;
  }

  public void adicionarCarnesParaEnvioEmail(List<File> anexos, Pedido pedido) {
    log.debug("Adicionando carnês ao email, se houver");
    var titulos = tituloRepository.findAllByPedidoAndCarneIdNotNull(pedido);
    adicionarCarnesParaEnvioEmail(anexos, titulos);
  }

  public void adicionarCarnesParaEnvioEmail(List<File> anexos, OrdemServico ordemServico) {
    log.debug("Adicionando carnês ao email, se houver");
    var titulos = tituloRepository.findAllByOrdemServicoAndCarneIdNotNull (ordemServico);
    adicionarCarnesParaEnvioEmail(anexos, titulos);
  }

  private void adicionarCarnesParaEnvioEmail(List<File> anexos, List<Titulo> titulos) {
    log.debug("Inicio da adição de carnês ao email");

    if (titulos.isEmpty()) {
      log.debug("Não foram encontrados carnês para envio");
      return;
    }

    Map<Integer, Titulo> titulosPorCarneId = titulos.stream()
      .filter(titulo -> titulo.getCarneId() != null)
      .collect(Collectors.toMap(
        Titulo::getCarneId,
        titulo -> titulo,
        (titulo1, titulo2) -> titulo1
      ));

    List<File> carnesAnexos = new ArrayList<>();

    for (Titulo titulo : titulosPorCarneId.values()) {
      List<TituloBoleto> boletosCarne = repository.findByTituloIdAndSituacaoAndBoletoCarneNotNull(
        titulo.getId(), SituacaoBoleto.RECEBIDO);

      if (!boletosCarne.isEmpty()) {
        TituloBoleto boletoCarne = boletosCarne.getFirst();
        File carnePdf = montaCarne(titulo, boletoCarne);
        if (carnePdf != null) {
          carnesAnexos.add(carnePdf);
        }
      }
    }

    if (carnesAnexos.isEmpty()) {
      log.debug("Nenhum carnê disponível para envio");
      return;
    }

    if (carnesAnexos.stream().anyMatch(Objects::isNull)) {
      log.debug("Não foi possível adicionar todos os carnês ao email");
      throw new SneakyException("enviando.email.para.retry");
    }

    anexos.addAll(carnesAnexos);
    log.debug("Fim da adição de carnês ao email: {} carnês adicionados", carnesAnexos.size());
  }

  private File montaCarne(Titulo titulo, TituloBoleto boleto) {
    if (boleto.getBoletoCarne() == null) {
      log.debug("Carnê não encontrado para o título {}", titulo.getId());
      return null;
    }

    File carneFile = null;
    try {
      URL url = new URI(boleto.getBoletoCarne()).toURL();
      URLConnection urlConnection = url.openConnection();
      urlConnection.setRequestProperty("User-Agent", "Mozilla/5.0");
      InputStream inputStream = urlConnection.getInputStream();
      carneFile = File.builder()
        .fileName("carne_" + titulo.getCarneId() + ".pdf")
        .bytes(inputStream.readAllBytes())
        .type("pdf")
        .inputStream(inputStream)
        .build();
      inputStream.close();
      log.debug("Carnê montado com sucesso para o título {}", titulo.getId());
    } catch (Exception e) {
      log.error("Erro ao adicionar carnê ao email: ", e);
    }
    return carneFile;
  }

  public Optional<AplicacaoIdEmpresaId> findAplicacaoIdByServicoBoletoId(String id) {
    return repository.findAplicacaoIdByServicoBoletoId(id);
  }

  public List<Map<UUID, Boolean>> verificaSeContemBoleto(List<UUID> ids) {
    return repository.verificaSeContemBoleto(ids).stream()
      .map(obj -> Map.of((UUID) obj[0], (Boolean) obj[1]))
      .toList();
  }

  @Transactional
  public void geraBoletosCarne(RegistroBoletoPagamentoRecebidoBodyDto body) {
    log.debug("Inicializando processo de geração de boletos do carne {}", body.getObject().getId());

    var titulo = tituloRepository.findByCarneId(body.getObject().getId())
      .orElseThrow(ResourceNotFoundException::new);

    var titulos = tituloRepository.findAllByTituloReferenciaIdOrId(titulo.getId(), titulo.getId());
    log.debug("Títulos encontrados no banco de dados: {}", titulos.size());

    List<BoletoRecebidoKobanaWebhookDto> bankBillets = body.getObject().getBank_billets();
    log.debug("Boletos recebidos: {}", bankBillets.size());

    bankBillets.sort(Comparator.comparing(BoletoRecebidoKobanaWebhookDto::getExpire_at));

    titulos.sort(Comparator.comparing(Titulo::getDataVencimento));

    int size = Math.min(bankBillets.size(), titulos.size());
    for (int i = 0; i < size; i++) {
      BoletoRecebidoKobanaWebhookDto boletoRecebido = bankBillets.get(i);
      Titulo t = titulos.get(i);

      Optional.ofNullable(boletoRecebido.getProcessed_our_number_raw())
        .ifPresent(titulo::setNossoNumero);
      Optional.ofNullable(boletoRecebido.getBarcode())
        .ifPresent(titulo::setCodigoBarras);
      Optional.ofNullable(boletoRecebido.getDocument_number())
        .ifPresent(titulo::setNumeroDocumento);

      boolean shouldSave = Stream.of(
        boletoRecebido.getProcessed_our_number_raw(),
        boletoRecebido.getBarcode(),
        boletoRecebido.getDocument_number()
      ).anyMatch(Objects::nonNull);

      if (shouldSave) {
        tituloRepository.save(titulo);
      }

      var boleto = TituloBoleto.builder()
        .aplicacao(titulo.getAplicacao())
        .empresa(titulo.getEmpresa())
        .titulo(t)
        .idServicoBoleto(boletoRecebido.getId().toString())
        .dataRecebimento(LocalDateTime.now())
        .situacao(SituacaoBoleto.RECEBIDO)
        .boletoPdf(boletoRecebido.getFormats().getPdf())
        .boletoHibrido(boletoRecebido.getFormats().getBoleto_hibrido())
        .boletoPix(boletoRecebido.getFormats().getBoleto_pix())
        .boletoCodigoBarras(boletoRecebido.getFormats().getBarcode())
        .boletoLinhaDigitavel(boletoRecebido.getFormats().getLine())
        .boletoCarne(boletoRecebido.getFormats().getCarne())
        .build();
      save(boleto);
    }
  }

}
