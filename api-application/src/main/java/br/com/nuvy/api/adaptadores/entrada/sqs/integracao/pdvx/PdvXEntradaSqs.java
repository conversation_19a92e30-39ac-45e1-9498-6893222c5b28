package br.com.nuvy.api.adaptadores.entrada.sqs.integracao.pdvx;

import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaClientePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaEstoquePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaProdutoPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.EnviaVendedorPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.comando.InsereVendaPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.processador.ProcessadorClientePdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.processador.ProcessadorProdutoPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.processador.ProcessadorVendaPdvX;
import br.com.nuvy.integracao.pdvx.aplicacao.processador.ProcessadorVendedorPdvX;
import br.com.nuvy.integracao.pdvx.dominio.evento.ClientePdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.EstoquePdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.ProdutoPdvXRecebido;
import br.com.nuvy.integracao.pdvx.dominio.evento.VendaPdvXRecebida;
import br.com.nuvy.integracao.pdvx.dominio.evento.VendedorPdvXRecebido;
import br.com.nuvy.multitenent.TenantContext;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(
  name = "aws.sqs.queue.pdvx.enabled",
  havingValue = "true"
)
class PdvXEntradaSqs {

  private static final Logger log = LoggerFactory.getLogger(PdvXEntradaSqs.class);

  private final ObjectMapper objectMapper;

  private final ProcessadorProdutoPdvX processadorProduto;
  private final ProcessadorClientePdvX processadorCliente;
  private final ProcessadorVendedorPdvX processadorVendedor;
  private final ProcessadorVendaPdvX processadorVenda;

  PdvXEntradaSqs(
    ObjectMapper objectMapper,
    ProcessadorProdutoPdvX processadorProduto,
    ProcessadorClientePdvX processadorCliente,
    ProcessadorVendedorPdvX processadorVendedor,
    ProcessadorVendaPdvX processadorVenda
  ) {
    this.objectMapper = objectMapper;

    this.processadorProduto = processadorProduto;
    this.processadorCliente = processadorCliente;
    this.processadorVendedor = processadorVendedor;
    this.processadorVenda = processadorVenda;

    log.info("{} habilitado", PdvXEntradaSqs.class.getSimpleName());
  }

  @JmsListener(
    destination = "${aws.sqs.queue.pdvx.name}",
    containerFactory = "jmsListenerContainerFactoryConcorrente"
  )
  public void processaMensagem(
    SQSTextMessage message
  ) throws JMSException, JsonProcessingException {
    log.debug("mensagem recebida da fila pdvx: {}", message.getText());

    TenantContext.setTenant(message.getStringProperty("idAplicacao"));
    BadgeContext.generateBadgeContext(
      message.getStringProperty("idAplicacao"),
      message.getStringProperty("idEmpresa"),
      message.getStringProperty("idUsuario")
    );

    switch (message.getStringProperty("tipo")) {
      case "ProdutoPdvXRecebido" -> {
        ProdutoPdvXRecebido evento = objectMapper.readValue(
          message.getText(), ProdutoPdvXRecebido.class
        );
        processadorProduto.processaComando(new EnviaProdutoPdvX(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdProduto(), evento.getOcorridoAs()
        ));
      }
      case "EstoquePdvXRecebido" -> {
        EstoquePdvXRecebido evento = objectMapper.readValue(
          message.getText(), EstoquePdvXRecebido.class
        );
        processadorProduto.processaComando(new EnviaEstoquePdvX(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdProduto(), evento.getOcorridoAs()
        ));
      }
      case "ClientePdvXRecebido" -> {
        ClientePdvXRecebido evento = objectMapper.readValue(
          message.getText(), ClientePdvXRecebido.class
        );
        processadorCliente.processaComando(new EnviaClientePdvX(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdCliente(), evento.getOcorridoAs()
        ));
      }
      case "VendedorPdvXRecebido" -> {
        VendedorPdvXRecebido evento = objectMapper.readValue(
          message.getText(), VendedorPdvXRecebido.class
        );
        processadorVendedor.processaComando(new EnviaVendedorPdvX(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getIdVendedor(), evento.getOcorridoAs()
        ));
      }
      case "VendaPdvXRecebida" -> {
        VendaPdvXRecebida evento = objectMapper.readValue(
          message.getText(), VendaPdvXRecebida.class
        );
        processadorVenda.processaComando(new InsereVendaPdvX(
          UUID.randomUUID(),
          evento.getIdAplicacao(), evento.getIdEmpresa(), evento.getIdUsuario(),
          evento.getJsonVenda(), evento.getOcorridoAs()
        ));
      }
      default -> throw new UnsupportedOperationException(
        "Tipo de evento não identificado na fila pdvx! Tipo: %s"
          .formatted(message.getStringProperty("tipo"))
      );
    }
  }
}
