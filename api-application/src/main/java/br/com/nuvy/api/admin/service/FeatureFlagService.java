package br.com.nuvy.api.admin.service;

import br.com.nuvy.api.admin.filter.GetFeatureFlagDto;
import br.com.nuvy.api.admin.repository.FeatureFlagRepository;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.config.BadgeContext;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeatureFlagService {

  private final FeatureFlagRepository featureFlagRepository;

  public GetFeatureFlagDto find(GetFeatureFlagDto dto) {
    if (dto.getFlags() == null || dto.getFlags().isEmpty()) {
      return dto;
    }

    Usuario usuario = BadgeContext.getUsuario();

    List<String> result = new ArrayList<>();

    featureFlagRepository.findAllByFlagIn(dto.getFlags()).forEach(e -> {
      if (e.isAtivo()) {
        if (e.getUsuario() != null) {
          if (usuario.equals(e.getUsuario())) {
            result.add(e.getFlag());
          }
        } else {
          result.add(e.getFlag());
        }
      }
    });

    return new GetFeatureFlagDto(new HashSet<>(result));
  }
}
