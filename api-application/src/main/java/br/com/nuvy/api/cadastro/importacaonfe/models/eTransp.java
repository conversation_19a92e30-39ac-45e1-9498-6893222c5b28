package br.com.nuvy.api.cadastro.importacaonfe.models;

import com.thoughtworks.xstream.annotations.XStreamImplicit;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
public class eTransp {

  private String modFrete;
  private String cnpj;
  private String xNome;
  private String ie;
  private String xEnder;
  private String xMun;
  private String UF;
//  private String qVol;
//  private String esp;
//  private String pesoL;
//  private String pesoB;
  private eTransporta transporta;
  @XStreamImplicit(itemFieldName = "vol")
  private List<eVol> vol;

}
