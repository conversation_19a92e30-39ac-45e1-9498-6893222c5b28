package br.com.nuvy.api.financeiro.service;

import br.com.nuvy.api.financeiro.dto.TituloRateioDto;
import br.com.nuvy.api.financeiro.model.TituloRateio;
import br.com.nuvy.api.financeiro.repository.TituloRateioRepository;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import org.springframework.stereotype.Service;

@Service
public class TituloRateioService extends
        PageableServiceAdapterDto<TituloRateio, TituloRateioDto, Integer, NoFilter, TituloRateioRepository> {
}
