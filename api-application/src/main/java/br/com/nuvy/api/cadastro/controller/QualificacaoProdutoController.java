package br.com.nuvy.api.cadastro.controller;

import br.com.nuvy.api.cadastro.dto.QualificacaoProdutoDto;
import br.com.nuvy.api.cadastro.filter.QualificacaoFilter;
import br.com.nuvy.api.cadastro.service.QualificacaoProdutoService;
import br.com.nuvy.common.base.controller.PageableControllerAdapter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "Cadastro / Qualificação Produto")
@RestController
@RequestMapping("/v1/cadastro/qualificacao-produto")
@RequiredArgsConstructor
public class QualificacaoProdutoController extends
        PageableControllerAdapter<QualificacaoProdutoDto, Integer, QualificacaoFilter, QualificacaoProdutoService> {

  @GetMapping("/{id}/utilizacoes")
  public Integer countUsages(@PathVariable Integer id) {
    return service.countUsages(id);
  }

  @DeleteMapping("/{idQualificacao}")
  public void delete(@PathVariable Integer idQualificacao) {
    service.deleteQualificação(idQualificacao);
  }
}
