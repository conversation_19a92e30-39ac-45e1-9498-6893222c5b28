package br.com.nuvy.api.financeiro.repository.specification;

import br.com.nuvy.api.financeiro.filter.BancoIntegracaoFilter;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.financeiro.model.BancoIntegracao;
import br.com.nuvy.api.financeiro.model.BancoIntegracao_;
import br.com.nuvy.api.financeiro.model.Banco_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor
public class BancoIntegracaoSpecification implements Specification<BancoIntegracao> {

  private final BancoIntegracaoFilter bancoIntegracaoFilter;


  @Override
  public Predicate toPredicate(Root<BancoIntegracao> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(bancoIntegracaoFilter.getBancoId(),
        e -> {
        Join<BancoIntegracao, Banco> bancoJoin = root.join(BancoIntegracao_.banco, JoinType.LEFT);
        return criteriaBuilder.equal(bancoJoin.get(Banco_.id), e);
        })
      .add(bancoIntegracaoFilter.getCriterio(),
        e -> criteriaBuilder.like(criteriaBuilder.upper(root.get(BancoIntegracao_.carteira)), "%" + e.toUpperCase() + "%" ))
      .and();
  }
}