package br.com.nuvy.api.venda.repository;

import br.com.nuvy.api.cadastro.model.AtributoMarketplace;
import br.com.nuvy.api.cadastro.model.AtributoValorMarketplace;
import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AtributoValorMarketplaceRepository extends
  NuvyRepository<AtributoValorMarketplace, Integer> {

  @Query("SELECT avm FROM AtributoValorMarketplace avm JOIN FETCH avm.valorErp WHERE avm.atributoMarketplace IN (:atributoMarketplace)")
  List<AtributoValorMarketplace> findByAtributoMarketplaceIn(
    List<AtributoMarketplace> atributoMarketplace);

  void deleteAllByAtributoMarketplace(AtributoMarketplace atributoMarketplace);

  void deleteAllByAtributoMarketplaceCanalVenda(CanalVenda canalVenda);

}
