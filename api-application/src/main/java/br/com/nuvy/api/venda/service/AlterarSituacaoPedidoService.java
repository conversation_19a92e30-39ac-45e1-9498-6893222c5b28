package br.com.nuvy.api.venda.service;

import static br.com.nuvy.api.enums.SituacaoPedido.APROVADO;
import static br.com.nuvy.api.enums.SituacaoPedido.EM_CANCELAMENTO;
import static br.com.nuvy.api.enums.SituacaoPedido.EM_PROCESSAMENTO_SEFAZ;
import static br.com.nuvy.api.enums.SituacaoPedido.FATURADO;
import static br.com.nuvy.api.enums.SituacaoPedido.ORCAMENTO;
import static br.com.nuvy.api.enums.SituacaoPedido.PENDENTE;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static br.com.nuvy.config.BadgeContext.getUsuario;

import br.com.nuvy.api.admin.service.NotificacaoService;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.OrigemNotificacao;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoNotificacao;
import br.com.nuvy.api.enums.TipoPedido;
import br.com.nuvy.api.estoque.service.MovimentoEstoquePorPedidoService;
import br.com.nuvy.api.notificacao.dto.PayloadPadrao;
import br.com.nuvy.api.notificacao.model.Notificacao;
import br.com.nuvy.api.portalcontador.service.FechamentoService;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoItem;
import br.com.nuvy.api.venda.model.PedidoItemEstoque;
import br.com.nuvy.api.venda.model.PedidoRecebimento;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.common.exception.BusinessException;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.Preconditions;
import br.com.nuvy.events.venda.AlteracaoSituacaoPedidoEvent;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.chrono.ChronoLocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AlterarSituacaoPedidoService {

  private final PedidoRepository pedidoRepository;
  private final ApplicationEventPublisher pedidoEvent;
  private final FechamentoService fechamentoService;
  private final NotificacaoService notificacaoService;
  private final MovimentoEstoquePorPedidoService movimentoEstoquePedidoService;

  public void alterarSituacao(
    Pedido pedido, SituacaoPedido situacaoNova, boolean isAtualizaParcelas
  ) {
    validaPedidoAoAlterar(pedido, situacaoNova, isAtualizaParcelas);
    switch (situacaoNova) {
      case APROVADO, FATURADO, NF_EMITIDA, CANCELADO, FINALIZADO -> pedidoEvent.publishEvent(
        new AlteracaoSituacaoPedidoEvent(
          this, pedido.getId(), situacaoNova, pedido.getSituacao(),
          true, isAtualizaParcelas, null, getUsuario().getId()
        )
      );
      case PENDENTE, SEPARACAO, ORCAMENTO, ENVIADO, ENTREGUE -> {
        if (
          pedido.getSituacao().equals(APROVADO)
            && (situacaoNova.equals(PENDENTE) || situacaoNova.equals(ORCAMENTO))
        ) {
          movimentoEstoquePedidoService.estornaReservaEstoque(pedido);
        }
        pedido.setSituacao(situacaoNova);
        pedidoRepository.save(pedido);
        inserirNotificacao(pedido);
      }
      default -> log.info("nenhuma situação encontrada.");
    }
  }

  private void validaPedidoAoAlterar(
    Pedido pedido, SituacaoPedido situacaoNova, boolean isAtualizaParcelas
  ) {
    if (isPedidoSemItens(pedido) && !ORCAMENTO.equals(situacaoNova)) {
      throw new PreconditionException("pedido.sem.itens");
    }
    Preconditions.checkNonNull(
      pedido.getNop(), "Não é possível aterar pedido sem Natureza de operação!"
    );
    Preconditions.checkNonNull(
      pedido.getPlanoConta(),
      pedido.getTipoPedido().isSaida()
        ? "Não é possível aterar pedido sem Tipo de receita!"
        : "Não é possível aterar pedido sem Tipo de despesa!"
    );
    pedido.getItens().forEach((PedidoItem item) -> {
      Preconditions.checkNonNull(
        item.getNop(), "Não é possível aterar pedido sem Natureza de operação!"
      );
      Preconditions.checkNonNull(
        item.getTipoReceita(),
        pedido.getTipoPedido().isSaida()
          ? "Não é possível aterar pedido sem Tipo de receita!"
          : "Não é possível aterar pedido sem Tipo de despesa!"
      );
    });
    var situacaoAnterior = pedidoRepository.findSituacaoById(pedido.getId());
    if (situacaoAnterior == situacaoNova) {
      throw new PreconditionException("pedido.situacao.igual");
    }

//    // validar se o pedido está com data de parcela no passado
//    pedido.getRecebimentos().stream()
//      .filter(pedidoRecebimento -> Objects.nonNull(pedidoRecebimento.getDataVencimento()))
//      .filter(pedidoRecebimento -> pedidoRecebimento.getDataVencimento().isBefore(
//        ChronoLocalDate.from(LocalDateTime.now())))
//      .findFirst()
//      .ifPresent(pedidoRecebimento -> {
//        throw new BusinessException("pedido.data.vencimento.menor.data.atual");
//      });

    if (
      (
        situacaoAnterior.equals(SituacaoPedido.NF_REJEITADA)
          || situacaoAnterior.equals(SituacaoPedido.NF_EMITIDA)
      )
        && !situacaoNova.equals(SituacaoPedido.CANCELADO)
    ) {
      var atributo = situacaoAnterior.equals(SituacaoPedido.NF_REJEITADA) ? "rejeitada" : "emitida";
      throw new PreconditionException("nf.rejeitada.ou.emitida", atributo);
    }

    if (List.of(FATURADO, EM_CANCELAMENTO, EM_PROCESSAMENTO_SEFAZ).contains(pedido.getSituacao())) {
      throw new BusinessException("pedido.faturado.alteracao.invalida");
    }

    if (pedido.getSituacao().equals(APROVADO) && situacaoNova.equals(SituacaoPedido.SEPARACAO)) {
      log.debug("validando estoque do pedido");
      List<PedidoItem> itens = pedido.getItens();
      for (PedidoItem umItem : itens) {
        BigDecimal quantidadeItem = getOrElse(umItem.getQuantidade(), BigDecimal.ZERO);

        List<PedidoItemEstoque> itemsEstoque = umItem.getItemsEstoque();
        if (itemsEstoque.isEmpty()) {
          throw new BusinessException("pedido.item.configuracao.deposito.invalida",
            umItem.getProduto().getDescricao());
        }
        if (
          umItem.getProduto().getControlaLoteValidade()
            && umItem.getProduto().getOutrosControles()
            && itemsEstoque.stream().anyMatch(item -> Objects.isNull(item.getRastreabilidade()))
        ) {
          throw new BusinessException(
            "Existem produtos com controle de lote ativados que precisam ser configurados no seu pedido.",
            umItem.getProduto().getDescricao()
          );
        }

        BigDecimal qtdEstoqueTotalItem = itemsEstoque.stream()
          .map(PedidoItemEstoque::getQuantidade)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (qtdEstoqueTotalItem.compareTo(quantidadeItem) != 0) {
          throw new BusinessException(
            "pedido.item.configuracao.deposito.quantidade.invalida",
            umItem.getProduto().getDescricao()
          );
        }
      }
    }

    if (situacaoNova.equals(FATURADO)) {
      fechamentoService.validarPeriodoBloqueado(pedido.getEmpresa().getId(), LocalDate.now());
    }

    if (pedido.getSituacao().equals(SituacaoPedido.NF_INUTILIZADA)) {
      throw new BusinessException("pedido.inutilizacao.warning");
    }

    // Para pedidos faturados, deverá validar se a data de vencimento da parcela não
    // é menor que a data de faturamento
    if (
      !isAtualizaParcelas && SituacaoPedido.FATURADO.equals(situacaoNova)
        && Objects.nonNull(pedido.getRecebimentos()) && !pedido.getRecebimentos().isEmpty()
        && Objects.nonNull(pedido.getDataFaturamento())
    ) {
      Stream<PedidoRecebimento> parcelaVencida = pedido.getRecebimentos().stream()
        .filter(pedidoRecebimento ->
          Objects.nonNull(pedidoRecebimento.getDataVencimento())
            && pedidoRecebimento.getDataVencimento()
            .isBefore(pedido.getDataFaturamento().toLocalDate())
        );
      if (parcelaVencida.findFirst().isPresent()) {
        throw new BusinessException("pedido.data.vencimento.menor.data.faturamento");
      }
    }
  }

  private boolean isPedidoSemItens(Pedido pedido) {
    return pedido.getItens() == null || pedido.getItens().isEmpty();
  }

  private void inserirNotificacao(Pedido pedido) {
    try {
      Notificacao notificacao = new Notificacao();
      notificacao.setAplicacao(pedido.getAplicacao());
      notificacao.setEmpresa(Empresa.of(pedido.getEmpresa().getId()));
      notificacao.setUsuario(Usuario.of(pedido.getUsuario().getId()));
      notificacao.setTitulo("Pedido aprovado");
      notificacao.setMensagem("O pedido " + pedido.getId() + " foi aprovado.");
      notificacao.setDetalhe("Confira o pedido no sistema.");
      notificacao.setTipo(TipoNotificacao.USUARIO);
      notificacao.setDataEnvio(LocalDateTime.now());
      notificacao.setOrigem(OrigemNotificacao.ORCAMENTO_APROVADO);

      // Monta a notificação para o front
      var payload = PayloadPadrao.builder()
        .titulo(notificacao.getTitulo())
        .mensagem(notificacao.getMensagem())
        .build();

      notificacaoService.createNotification(notificacao, payload);
    } catch (Exception e) {
      log.error("Erro ao inserir notificação de alteração de pedido: " + e.getMessage());
    }
  }
}
