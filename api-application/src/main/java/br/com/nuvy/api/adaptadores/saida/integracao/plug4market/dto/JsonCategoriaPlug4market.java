package br.com.nuvy.api.adaptadores.saida.integracao.plug4market.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JsonCategoriaPlug4market {

  @JsonProperty("name")
  private String name;

  @JsonProperty("alternativeId")
  private String alternativeId;

  @JsonProperty("fatherAlternativeId")
  private String fatherAlternativeId;
}
