package br.com.nuvy.api.financeiro.service;

import static br.com.nuvy.api.enums.SituacaoPedido.APROVADO;
import static br.com.nuvy.api.enums.SituacaoPedido.FATURADO;
import static br.com.nuvy.api.enums.SituacaoPedido.FINALIZADO;
import static br.com.nuvy.api.enums.SituacaoPedido.NF_CANCELADA;
import static br.com.nuvy.api.enums.SituacaoPedido.NF_EMITIDA;
import static br.com.nuvy.api.enums.SituacaoPedido.NF_REJEITADA;
import static br.com.nuvy.api.enums.SituacaoPedido.ORCAMENTO;
import static br.com.nuvy.api.enums.SituacaoPedido.PEDIDO_AUTORIZADO;
import static br.com.nuvy.api.enums.SituacaoPedido.PENDENTE;
import static br.com.nuvy.api.enums.SituacaoPedido.SEPARACAO;
import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.api.cadastro.repository.PosPdvVendaRepository;
import br.com.nuvy.api.cadastro.repository.ProdutoRepository;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.TipoPedido;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.estoque.model.Movimento;
import br.com.nuvy.api.estoque.repository.MovimentoRepository;
import br.com.nuvy.api.estoque.repository.specification.MovimentoRelatorioSpecification;
import br.com.nuvy.api.financeiro.PedidoMapper;
import br.com.nuvy.api.financeiro.dto.TituloRelatorioDto;
import br.com.nuvy.api.financeiro.filter.TituloRelatorioFilter;
import br.com.nuvy.api.financeiro.filter.TituloRelatorioPagarReceberFilter;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.repository.TituloRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoCentroCustoRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoNotaFiscalRepository;
import br.com.nuvy.api.servico.repository.OrdemServicoRepository;
import br.com.nuvy.api.venda.model.OrdemServico;
import br.com.nuvy.api.venda.model.OrdemServicoCentroCusto;
import br.com.nuvy.api.venda.model.OrdemServicoItem;
import br.com.nuvy.api.venda.model.OrdemServicoNotaFiscal;
import br.com.nuvy.api.venda.model.OrdemServicoParcela;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.model.PedidoItem;
import br.com.nuvy.api.venda.model.PedidoItemEstoque;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.api.venda.repository.specification.OrdemServicoRelatorioSpecification;
import br.com.nuvy.api.venda.repository.specification.PedidoRelatorioSpecification;
import br.com.nuvy.api.venda.repository.specification.PosPdvVendaRelatorioSpecification;
import br.com.nuvy.api.venda.repository.specification.RecorrenciaRelatorioSpecification;
import br.com.nuvy.api.venda.service.PedidoItemEstoqueService;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.utils.MathUtils;
import br.com.nuvy.common.utils.MathUtils.TipoRestoRateioSimples;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.config.datasource.DataSource;
import br.com.nuvy.config.datasource.DataSourceType;
import br.com.nuvy.facade.relatorio.rl2comprasestoque.model.EstoqueDetalhadoDto;
import br.com.nuvy.facade.relatorio.rl2comprasestoque.model.EstoqueMinimoDto;
import br.com.nuvy.facade.relatorio.rl2comprasestoque.model.EstoqueRelatorioFilter;
import br.com.nuvy.facade.relatorio.rl3vendas.model.ItemMargemVendaDto;
import br.com.nuvy.facade.relatorio.rl3vendas.model.OrcamentosDto;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PedidoMargemDto;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PedidoRelatorioFilter;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PedidoRelatorioFilterResumido;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PosPdvVendasRelatorioFilter;
import br.com.nuvy.facade.relatorio.rl4financeiro.model.TituloInadimplenteDto;
import br.com.nuvy.facade.relatorio.rl4financeiro.model.TituloPagarReceberRelatorioDto;
import br.com.nuvy.facade.relatorio.rl5servicos.model.OrdemServicoRelatorioFilter;
import br.com.nuvy.facade.relatorio.rl5servicos.model.RecorrenciaFilter;
import br.com.nuvy.facade.relatorio.rl5servicos.model.RecorrenciaRelatorioDto;
import br.com.nuvy.facade.relatorio.rl5servicos.model.ServicosDto;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoItemEstoqueDto;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class RelatorioService {

  private final TituloService tituloService;
  private final TituloRepository tituloRepository;
  private final PedidoRepository pedidoRepository;
  private final ProdutoRepository produtoRepository;
  private final PosPdvVendaRepository posPdvVendaRepository;
  private final MovimentoRepository movimentoRepository;
  private final PedidoItemEstoqueService pedidoItemEstoqueService;
  private final OrdemServicoRepository ordemServicoRepository;
  private final OrdemServicoCentroCustoRepository ordemServicoCentroCustoRepository;
  private final OrdemServicoNotaFiscalRepository ordemServicoNotaFiscalRepository;
  private final EmpresaService empresaService;

  private final PedidoMapper pedidoMapper;

  @DataSource(DataSourceType.READ)
  public List<TituloInadimplenteDto> findTitulosInadimplentes(TituloRelatorioFilter filter) {
    validaDatasFiltro(filter.getDataVencimentoInicial(), filter.getDataVencimentoFinal());

    // Busca títulos inadimplentes diretamente no repositório
    List<Titulo> titulos = tituloRepository.findTitulosInadimplentes(
      TipoTitulo.RECEBER,
      filter.getDataVencimentoInicial(),
      filter.getDataVencimentoFinal(),
      BadgeContext.getAplicacao().getId()
    );

    // Converte os títulos para DTOs de relatório
    List<TituloRelatorioDto> titulosRelatorio = new ArrayList<>();
    for (Titulo titulo : titulos) {
      titulosRelatorio.addAll(tituloService.createTituloRelatorioDtos(titulo));
    }

    // Converte para DTOs de inadimplência
    return titulosRelatorio.stream()
      .map(TituloInadimplenteDto::tituloRelatorioToInadimplente)
      .toList();
  }

  @DataSource(DataSourceType.READ)
  public List<OrcamentosDto> findPedidosToRelatorio(PedidoRelatorioFilterResumido filter) {
    if (filter.getDataPedidoInicial() != null && filter.getDataPedidoFinal() != null) {
      validaDatasFiltro(filter.getDataPedidoInicial(), filter.getDataPedidoFinal());
    }
    var finalFilter = PedidoRelatorioFilter.builder()
      .empresasId(filter.getEmpresasId())
      .dataPedidoInicial(filter.getDataPedidoInicial())
      .dataPedidoFinal(filter.getDataPedidoFinal())
      .dataFaturamentoInicial(filter.getDataFaturamentoInicial())
      .dataFaturamentoFinal(filter.getDataFaturamentoFinal())
      .excluido(Boolean.FALSE)
      .situacaoPedido(
        List.of(PENDENTE, ORCAMENTO, APROVADO, FINALIZADO, SEPARACAO, FATURADO, NF_EMITIDA,
          NF_REJEITADA, NF_CANCELADA, PEDIDO_AUTORIZADO))
      .tipoPedido(TipoPedido.NORMAL)

      .build();

    PedidoRelatorioSpecification specification = new PedidoRelatorioSpecification(finalFilter);
    List<Pedido> pedidos = pedidoRepository.findAll(specification);

    return pedidos.stream().flatMap(pedidoMapper::convert).toList();
  }

  @DataSource(DataSourceType.READ)
  public List<PedidoMargemDto> findMargemVenda(PedidoRelatorioFilter filter) {
    validaDatasFiltro(filter.getDataPedidoInicial(), filter.getDataPedidoFinal());

    PedidoRelatorioSpecification specification = new PedidoRelatorioSpecification(filter);
    List<PedidoMargemDto> pedidos = pedidoRepository.findAll(specification).stream()
      .map(PedidoMargemDto::from).toList();

    for (PedidoMargemDto pedido : pedidos) {
      var valorCusto = pedido.getItens().stream()
        .peek(item -> {
          item.setValorCusto(
            getOrElse(item.getItensEstoque(), List.<PedidoItemEstoqueDto>of()).stream().findAny()
              .map(PedidoItemEstoqueDto::getId)
              .flatMap(movimentoRepository::findByPedidoItemEstoqueId)
              .map(Movimento::getValorCusto)
              .or(() -> Optional.ofNullable(item.getProdutoValorCustoMedio()))
              .orElse(BigDecimal.ZERO).multiply(item.getQuantidade())
          );
        })
        .map(ItemMargemVendaDto::getValorCusto)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
      pedido.setValorCusto(valorCusto);
      pedido.setValorMargem(pedido.getValorNotaFiscal().subtract(valorCusto));
    }
    return pedidos;
  }

  @DataSource(DataSourceType.READ)
  public List<PedidoMargemDto> findMargemVendaComPdv(PedidoRelatorioFilter filter, PosPdvVendasRelatorioFilter pdvFilter) {
    validaDatasFiltro(filter.getDataPedidoInicial(), filter.getDataPedidoFinal());

    PedidoRelatorioSpecification specification = new PedidoRelatorioSpecification(filter);
    List<PedidoMargemDto> pedidos = pedidoRepository.findAll(specification).stream()
      .map(PedidoMargemDto::from).toList();

    for (PedidoMargemDto pedido : pedidos) {
      var valorCusto = pedido.getItens().stream()
        .peek(item -> {
          item.setValorCusto(
            getOrElse(item.getItensEstoque(), List.<PedidoItemEstoqueDto>of()).stream().findAny()
              .map(PedidoItemEstoqueDto::getId)
              .flatMap(movimentoRepository::findByPedidoItemEstoqueId)
              .map(Movimento::getValorCusto)
              .or(() -> Optional.ofNullable(item.getProdutoValorCustoMedio()))
              .orElse(BigDecimal.ZERO).multiply(item.getQuantidade())
          );
        })
        .map(ItemMargemVendaDto::getValorCusto)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
      pedido.setValorCusto(valorCusto);
      pedido.setValorMargem(pedido.getValorNotaFiscal().subtract(valorCusto));
    }

    List<PedidoMargemDto> resultado = new ArrayList<>();
    resultado.addAll(pedidos);

    // buscar vendas PDV
    PosPdvVendaRelatorioSpecification pdvSpecification = new PosPdvVendaRelatorioSpecification(pdvFilter);

    List<PedidoMargemDto> pedidosPdv = posPdvVendaRepository.findAll(pdvSpecification).stream()
      .map(PedidoMargemDto::from).toList();

    for (PedidoMargemDto pedido : pedidosPdv) {
      pedido.setNumeroNotaFiscal(getOrElse(pedido, PedidoMargemDto::getNumeroNotaFiscal, pedido.getCodigoPedido()));

      var valorCusto = pedido.getItens().stream()
        .peek(item -> {
          item.setValorCusto(
            Optional.ofNullable(item.getProdutoValorCustoMedio())
              .orElse(BigDecimal.ZERO).multiply(item.getQuantidade())
          );
        })
        .map(ItemMargemVendaDto::getValorCusto)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
      pedido.setValorCusto(valorCusto);
      pedido.setValorMargem(pedido.getValorNotaFiscal().subtract(valorCusto));
    }
    resultado.addAll(pedidosPdv);

    return resultado;
  }

  @DataSource(DataSourceType.READ)
  private void validaDatasFiltro(LocalDate dataInicial, LocalDate dataFinal) {
    if (dataFinal.isAfter(LocalDate.now())) {
      throw new PreconditionException("data.final.maior.que.data.atual");
    }
    if (dataInicial.isAfter(dataFinal)) {
      throw new PreconditionException("data.inicial.maior.que.data.final");
    }
  }

  @DataSource(DataSourceType.READ)
  public List<EstoqueMinimoDto> findEstoqueMinimo(List<Integer> empresasId) {
    var aplicacao = BadgeContext.getBadge().getAplicacao().getId();
    String empresasString = "";
    if (ObjectUtils.isNotEmpty(empresasId)) {
      empresasString = empresasId.stream().map(String::valueOf).collect(Collectors.joining(","));
    }
    return produtoRepository.findProdutosToRelatorio(empresasString, aplicacao).stream()
      .map(projection -> EstoqueMinimoDto.builder()
        .empresaNome(projection[0].toString())
        .depositoNome(projection[1].toString())
        .produtoCodigo(projection[2].toString())
        .produtoDescricao(projection[3].toString())
        .produtoUnidadeMedida(projection[4].toString())
        .produtoSaldoAtual(new BigDecimal(projection[5].toString()))
        .produtoSaldoMinimo(new BigDecimal(projection[6].toString()))
        .estoqueMinimoXSaldoEstoque(new BigDecimal(projection[7].toString()))
        .produtoTipoMercadoria(projection[8].toString())
        .produtoNcmCodigo(projection[9].toString())
        .produtoNcmDescricao(projection[10].toString())
        .produtoPesoLiquido(new BigDecimal(projection[11].toString()))
        .produtoPesoBruto(new BigDecimal(projection[12].toString()))
        .produtoLote(projection[13].toString())
        .produtoQualificacao(projection[14].toString())
        .produtoCategoria(projection[15].toString())
        .produtoSituacao(projection[16].toString())
        .produtoValorCustoMedio(projection[17] != null ? new BigDecimal(projection[17].toString()) : null)
        .build())
      .collect(Collectors.toList());
  }


  @DataSource(DataSourceType.READ)
  public List<EstoqueDetalhadoDto> findEstoqueDetalhado(EstoqueRelatorioFilter filter) {
    validaDatasFiltro(filter.getDataMovimentacaoInicial(), filter.getDataMovimentacaoFinal());

    if (filter.getDepositosId() != null && filter.getDepositosId().stream().anyMatch(id -> id == 0)) {
      filter.setDepositosId(new ArrayList<>());
    }
    if (filter.getEmpresasId() != null && filter.getEmpresasId().stream().anyMatch(id -> id == 0)) {
      filter.setEmpresasId(new ArrayList<>());
    }


    MovimentoRelatorioSpecification specification = new MovimentoRelatorioSpecification(filter);
    List<EstoqueDetalhadoDto> movimentos = movimentoRepository.findAll(specification).stream()
      .map(EstoqueDetalhadoDto::from)
      .sorted(Comparator.comparing(EstoqueDetalhadoDto::getDataMovimentacao))
      .collect(Collectors.toList());

    movimentos.forEach(movimento -> {
      if (movimento.getPedidoItemEstoqueId() != null) {
        var itemEstoquePedido = pedidoItemEstoqueService.findById(
          movimento.getPedidoItemEstoqueId()).orElse(null);
        var numeroNota = get(itemEstoquePedido,
          PedidoItemEstoque::getPedidoItem,
          PedidoItem::getPedido,
          Pedido::getNumeroNotaFiscal);
        movimento.setNumNota(numeroNota);
      }
      movimento.setEmpresa(empresaService.findById(
        movimento.getDeposito().getEmpresa().getId()).orElse(new Empresa()).getNome()
      );
    });
    return movimentos;
  }

  @DataSource(DataSourceType.READ)
  public List<OrcamentosDto> findPedidosToRelatorioOutros(PedidoRelatorioFilter filter) {
    PedidoRelatorioSpecification specification = new PedidoRelatorioSpecification(filter);
    List<Pedido> pedidos = pedidoRepository.findAll(specification);
    return pedidos.stream().map(OrcamentosDto::from).toList();
  }


  @DataSource(DataSourceType.READ)
  public List<ServicosDto> findServicos(OrdemServicoRelatorioFilter filter) {
    OrdemServicoRelatorioSpecification specification = new OrdemServicoRelatorioSpecification(filter);
    List<OrdemServico> servicos = ordemServicoRepository.findAll(specification);

    return servicos.stream()
      .filter(ordemServico -> ordemServico.getParcelas().size() >= 1)
      .flatMap(ordemServico -> ordemServico.getItens().stream().flatMap(item -> {
        var ordemServicoNotaFiscal = ordemServicoNotaFiscalRepository.findFirstByOrdemServico(ordemServico);
        var endereco = ordemServico.getCliente().getEnderecos().stream().findFirst().orElse(null);

        List<OrdemServicoCentroCusto> centrosCustos = ordemServicoCentroCustoRepository.findAllByOrdemServico(ordemServico);

        Stream<ServicosDto> servicosDtoStream = centrosCustos.isEmpty()
          ? Stream.of(createRelatorioServicos(ordemServico, item, ordemServicoNotaFiscal, endereco))
          : centrosCustos.stream().map(centroCusto -> {
            ServicosDto servicosDto = createRelatorioServicos(ordemServico, item, ordemServicoNotaFiscal, endereco);
            servicosDto.setCentroCustoNome(centroCusto.getCentroCusto().getNome());
            BigDecimal percentualCentroCusto = centroCusto.getPercentualRateio();
            BigDecimal valorProporcionalCentroCusto = ordemServico.getTotalServicos().multiply(percentualCentroCusto);
            servicosDto.setValorCentroCusto(valorProporcionalCentroCusto);
            return servicosDto;
          });

        BigDecimal valorTotalItem = item.getValorTotalItem();
        BigDecimal valorTotalNF = ordemServico.getValorNf();
        int numeroParcelas = ordemServico.getParcelas().size();

        List<BigDecimal> valoresParcelas = MathUtils.rateia(valorTotalItem, numeroParcelas, TipoRestoRateioSimples.MAIORES_NO_COMECO);
        List<BigDecimal> valoresTotalNF = MathUtils.rateia(valorTotalNF, numeroParcelas, TipoRestoRateioSimples.MAIORES_NO_COMECO);


        return servicosDtoStream.flatMap(servicosDto -> {
          List<OrdemServicoParcela> parcelas = ordemServico.getParcelas();
          return IntStream.range(0, numeroParcelas).mapToObj(i -> {
            OrdemServicoParcela parcela = parcelas.get(i);

            ServicosDto novoServicosDto = servicosDto.toBuilder().build();

            novoServicosDto.setDataVencimentoParcelaOrdemServico(parcela.getVencimento());
            novoServicosDto.setFormaRecebimentoParcelaOrdemServico(parcela.getFormaRecebimento().getNome());
            novoServicosDto.setContaCorrenteParcelaOrdemServico(parcela.getContaCorrente().getNome());
            novoServicosDto.setObservacoesServico(ordemServico.getObservacoes());

            BigDecimal valorParcelaAjustado = valoresParcelas.get(i);

            novoServicosDto.setValorTotalServico(valorParcelaAjustado);
            novoServicosDto.setValorTotalOrdemServico(valoresTotalNF.get(i));
            System.out.println("Item ID: " + item.getId() + ", Parcela: " + parcela.getNumero() + ", Valor: " + novoServicosDto.getValorTotalServico());
            return novoServicosDto;
          });
        });
      }))
      .collect(Collectors.toList());
  }


  @DataSource(DataSourceType.READ)
  public List<RecorrenciaRelatorioDto> findAllForReport(RecorrenciaFilter filter) {
    RecorrenciaRelatorioSpecification specification = new RecorrenciaRelatorioSpecification(
      filter);
    var recorrencias =  ordemServicoRepository.findAllRecorrencias(specification);
    recorrencias.forEach(RecorrenciaRelatorioDto::calcularProximoFaturamento);
    return recorrencias;
  }


  private ServicosDto createRelatorioServicos(OrdemServico ordemServico, OrdemServicoItem item,
    OrdemServicoNotaFiscal ordemServicoNotaFiscal, EnderecoPessoa endereco) {

    return ServicosDto.builder()
      .id(ordemServico.getCodigo())
      .empresaNome(ordemServico.getEmpresa().getNome())
      .clienteNome(ordemServico.getCliente().getNome())
      .clienteNomeInterno(ordemServico.getCliente().getNomeInterno())
      .clienteNomeFantasia(ordemServico.getCliente().getNomeFantasia())
      .clienteCpfCnpj(ordemServico.getCliente().getCpfCnpj())
      .clienteEstado(endereco != null ? endereco.getUf() : null)
      .clienteCidade(endereco != null ? endereco.getCidade() : null)
      .dataCriacao(ordemServico.getDataCriacao())
      .situacaoOrdemServico(getSituacaoDescricao(ordemServico))
      .planoContaNome(ordemServico.getPlanoConta().getNome())
      .valorTotalServico(ordemServico.getTotalServicos())
      .valorTotalOrdemServico(ordemServico.getValorNf().divide(
        BigDecimal.valueOf(ordemServico.getItens().size()), 2, BigDecimal.ROUND_HALF_UP))
      .vendedorNome(
        ordemServico.getVendedor() != null ? ordemServico.getVendedor().getNome() : null)
      .tecnicoNome(ordemServico.getTecnico() != null ? ordemServico.getTecnico().getNome() : null)
      .numeroNotaFiscal(
        ordemServicoNotaFiscal != null ? ordemServicoNotaFiscal.getNumeroNfse() : null)
      .consideracoesOrdemServico(ordemServico.getConsideracoes())
      .observacoesNotaFiscal(ordemServico.getObservacoesNf())
      .dataEmissaoNotaFiscal(
        ordemServicoNotaFiscal != null ? ordemServicoNotaFiscal.getDataEmissao() : null)
      .dataFaturamentoRps(ordemServico.getDataFaturamentoRps() != null
        ? ordemServico.getDataFaturamentoRps()
        : null)
      .quantidadeServico(item.getQuantidade())
      .valorIssServico(item.getIss())
      .issRetido(item.getIssRetido())
      .valorPisServico(item.getPis())
      .pisRetido(item.getPisRetido())
      .valorCofinsServico(item.getCofins())
      .cofinsRetido(item.getCofinsRetido())
      .valorIrServico(item.getIr())
      .irRetido(item.getIrRetido())
      .valorInssServico(item.getInss())
      .inssRetido(item.getInssRetido())
      .valorCsllServico(item.getCsll())
      .csllRetido(item.getCsllRetido())
      .codigoArt(item.getCodigoArt())
      .codigoObra(item.getCodigoObraCo())
      .cadastroEspecificoInss(item.getCadastroEspecificoInss())
      .recorrencia(ordemServico.getRecorrencia())
      .diaFaturamento(ordemServico.getDiaFaturamento())
      .diaVencimento(ordemServico.getDiaVencimento())
      .codigoServico(item.getServico() != null ? item.getServico().getCodigo() : null)
      .descricaoServico(item.getServico() != null ? item.getServico().getDescricao() : null)
      .descricaoCompletaServico(
        item.getServico() != null ? item.getServico().getDescricaoCompleta() : null)
      .codigoCnaeServico(item.getServico() != null ? item.getServico().getCnae() : null)
      .codigoLc116Servico(item.getServico() != null ? item.getServico().getIdLc116() : null)
      .codigoNbsServico(item.getServico() != null ? item.getServico().getIdNbs() : null)
      .valorUnitarioServico(item.getServico() != null ? item.getServico().getValor() : null)
      .unidadeServico(item.getServico() != null ? item.getServico().getUnidade() : null)
      .issPercentualServico(item.getServico() != null ? item.getServico().getIss() : null)
      .inssPercentualServico(item.getServico() != null ? item.getServico().getInss() : null)
      .cofinsPercentualServico(item.getServico() != null ? item.getServico().getCofins() : null)
      .irPercentualServico(item.getServico() != null ? item.getServico().getIr() : null)
      .pisPercentualServico(item.getServico() != null ? item.getServico().getPis() : null)
      .csllPercentualServico(item.getServico() != null ? item.getServico().getCsll() : null)
      .observacoesServico(item.getServico() != null ? item.getServico().getObservacao() : null)
      .descontosOrdemServico(ordemServico.getTotalDescontos())
      .impostosRetidosOrdemServico(ordemServico.getTotalImpostosRetidos())
      .build();

  }

  @Transactional(readOnly = true)
  public List<TituloPagarReceberRelatorioDto> findTitulosPagarReceber(
    TituloRelatorioPagarReceberFilter filter) {

    List<Object[]> projections = tituloRepository.findTitulosPagarReceberProjection(
      filter.getTipoTitulo() != null ? filter.getTipoTitulo().name() : null,
      filter.getDataVencimentoInicial(),
      filter.getDataVencimentoFinal(),
      BadgeContext.getAplicacao().getId()
    );

    return projections.stream()
      .map(projection -> TituloPagarReceberRelatorioDto.builder()
        .empresaId(toInteger(projection[0]))
        .empresaNome(toString(projection[1]))
        .situacao(toString(projection[2]))
        .fornecedorNome(toString(projection[3]))
        .fornecedorNomeInterno(toString(projection[4]))
        .planoContaId(toInteger(projection[5]))
        .planoContaNome(toString(projection[6]))
        .planoContaPercentual(toBigDecimal(projection[7]))
        .planoContaValorCalculado(toBigDecimal(projection[8]))
        .dataVencimento(toLocalDate(projection[9]))
        .contaBancariaId(toInteger(projection[10]))
        .contaBancariaNome(toString(projection[11]))
        .impostosRetidos(toBigDecimal(projection[12]))
        .totalValorPis(toBigDecimal(projection[13]))
        .totalValorCofins(toBigDecimal(projection[14]))
        .totalValorInss(toBigDecimal(projection[15]))
        .totalValorCsll(toBigDecimal(projection[16]))
        .totalValorIss(toBigDecimal(projection[17]))
        .totalValorIr(toBigDecimal(projection[18]))
        .valorTotalImpostosRetidos(toBigDecimal(projection[19]))
        .valorOriginal(toBigDecimal(projection[20]))
        .valorTotal(toBigDecimal(projection[21]))
        .valorJuros(toBigDecimal(projection[22]))
        .valorMultas(toBigDecimal(projection[23]))
        .valorDescontos(toBigDecimal(projection[24]))
        .valorAbatimentos(toBigDecimal(projection[25]))
        .valorTotalPago(toBigDecimal(projection[26]))
        .dataEmissao(toLocalDateTime(projection[27]))
        .centroCustoId(toInteger(projection[28]))
        .centroCustoDescricao(toString(projection[29]))
        .centroCustoPercentual(toBigDecimal(projection[30]))
        .centroCustovalorCalculado(toBigDecimal(projection[31]))
        .tipoTitulo(toString(projection[32]))
        .possuiRecorrencia(toBoolean(projection[33]))
        .fornecedorId(toInteger(projection[34]))
        .fornecedorTipoPessoa(toString(projection[35]))
        .fornecedorCpfCnpj(toString(projection[36]))
        .valorPis(toBigDecimal(projection[37]))
        .valorCofins(toBigDecimal(projection[38]))
        .valorInss(toBigDecimal(projection[39]))
        .valorCsll(toBigDecimal(projection[40]))
        .valorIss(toBigDecimal(projection[41]))
        .valorIr(toBigDecimal(projection[42]))
        .dataGeracao(toLocalDateTime(projection[43]))
        .dataPagamento(toLocalDate(projection[44]))
        .tipo(toString(projection[45]))
        .dataHistorico(toLocalDateTime(projection[46]))
        .acao(toString(projection[47]))
        .usuarioId(toUUID(projection[48]))
        .usuarioNome(toString(projection[49]))
        .numeroNotaFiscal(toString(projection[50]))
        .observacao(toString(projection[51]))
        .codigoBarras(toString(projection[52]))
        .numeroDocumento(toString(projection[53]))
        .numeroParcela(toInteger(projection[54]))
        .formaPagamentoId(toInteger(projection[55]))
        .formaPagamentoNome(toString(projection[56]))
        .build())
      .collect(Collectors.toList());
  }

  private String getSituacaoDescricao(OrdemServico ordemServico) {
    String descricao;
    var ordemServicoNotaFiscal = ordemServicoNotaFiscalRepository.findFirstByOrdemServico(ordemServico);
    if (ordemServicoNotaFiscal != null) {
      String situacaoNotaFiscal = ordemServicoNotaFiscal.getSituacao();
      if ("NF_CANCELADA".equals(situacaoNotaFiscal)) {
        return "NF. Cancelada";
      } else if ("NF_REJEITADA".equals(situacaoNotaFiscal)) {
        return "NF. Rejeitada";
      }
    }

    switch (ordemServico.getSituacao()) {
      case ORCAMENTO -> descricao = "Orçamento";
      case APROVADO -> descricao = "Aprovado";
      case RECORRENCIA -> descricao = "Recorrência";
      case CANCELADO -> descricao = "Cancelado";
      case FATURADO, FINALIZADO -> descricao = "Faturado";
      default -> descricao = ordemServico.getSituacao().name().toLowerCase(); // Converte o enum para string
    }

    return descricao;
  }



  private Integer toInteger(Object value) {
    return value != null ? Integer.valueOf(value.toString()) : null;
  }

  private String toString(Object value) {
    return value != null ? value.toString() : null;
  }

  private BigDecimal toBigDecimal(Object value) {
    return value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO;
  }

  private LocalDate toLocalDate(Object value) {
    if (value == null) {
      return null;
    }
    if (value instanceof java.sql.Date) {
      return ((java.sql.Date) value).toLocalDate();
    }
    return LocalDate.parse(value.toString());
  }

  private LocalDateTime toLocalDateTime(Object value) {
    if (value == null) {
      return null;
    }
    if (value instanceof java.sql.Timestamp) {
      return ((java.sql.Timestamp) value).toLocalDateTime();
    }
    return LocalDateTime.parse(value.toString());
  }

  private Boolean toBoolean(Object value) {
    return value != null ? Boolean.valueOf(value.toString()) : null;
  }

  private UUID toUUID(Object value) {
    return value != null ? UUID.fromString(value.toString()) : null;
  }



}
