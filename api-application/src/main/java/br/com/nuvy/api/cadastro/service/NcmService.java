package br.com.nuvy.api.cadastro.service;

import br.com.nuvy.api.cadastro.dto.NcmDto;
import br.com.nuvy.api.cadastro.filter.NcmFilter;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.repository.NcmRepository;
import br.com.nuvy.api.cadastro.repository.specification.NcmSpecification;
import br.com.nuvy.common.base.service.PageableServiceAdapterDto;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class NcmService extends
  PageableServiceAdapterDto<Ncm, NcmDto, String, NcmFilter, NcmRepository> {

  @Override
  protected Specification<Ncm> configureSpecification(NcmFilter filter) {
    return NcmSpecification.from(filter);
  }

  @Override
  public Page<NcmDto> findDto(NcmFilter filter, Pageable pageable) {
    return repository.findAll(configureSpecification(filter), pageable).map(NcmDto::from);
  }

  public Page<NcmDto> findNcmProdutos(NcmFilter filter, Pageable pageable) {
    Page<NcmDto> ncmsCriterio = findDto(filter, pageable);
    Page<NcmDto> ncmsProduto = repository.findNcmContaisProduto(filter, pageable).map(NcmDto::from);
    if (Boolean.TRUE.equals(filter.getRelacionamentoProdutos())) {
      return ncmsProduto;
    }
    return ncmsCriterio;
  }

  public Page<Ncm> findNcm(String criterio, Pageable pageable) {
    NcmFilter ncmFilter = NcmFilter.builder()
      .criterio(criterio)
      .build();

    List<Ncm> ncms = find(ncmFilter).stream().toList();
    List<Ncm> ncmFiltro = ncms.stream().filter(n -> n.getId().length() >= 8)
      .collect(Collectors.toList());

    var inicio = (int) pageable.getOffset();
    int fim = Math.min((inicio + pageable.getPageSize()), ncmFiltro.size());
    List<Ncm> paginaAtual = ncmFiltro.subList(inicio, fim);
    return new PageImpl<>(paginaAtual, pageable, ncmFiltro.size());
  }

}
