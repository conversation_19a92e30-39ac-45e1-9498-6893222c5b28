package br.com.nuvy.api.cadastro.repository;

import br.com.nuvy.api.cadastro.model.TipoFrete;
import br.com.nuvy.common.base.repository.NuvyRepository;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface TipoFreteRepository extends NuvyRepository<TipoFrete, Integer> {

  Optional<TipoFrete> findTipoFreteByCodigoModalidadeFreteNf(String codigoModalidadeFreteNf);

  @Query("select tf from TipoFrete tf where tf.codigoModalidadeFreteNf = '9'")
  TipoFrete findTipoFreteSemOcorrencia();

}
