package br.com.nuvy.api.sqs.servico;

import br.com.nuvy.callface.callface.dto.CallfaceReadDto;
import br.com.nuvy.callface.callface.model.CallfaceChamadaHistorico;
import br.com.nuvy.callface.callface.service.CallfaceChamadaHistoricoService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ConditionalOnProperty(
    name = {"aws.sqs.enabled", "aws.sqs.queue.callface.enabled"},
    havingValue = "true"
)
@RequiredArgsConstructor
public class CallfaceListener {

  private final ObjectMapper objectMapper;
  private final CallfaceChamadaHistoricoService callfaceChamadaHistoricoService;

  @JmsListener(destination = "${aws.sqs.queue.callface.name}")
  public void callfaceListener(String message) {
    try {
      log.debug("Recebendo mensagem: {}", message);
      var payload = objectMapper.readValue(message, CallfaceReadDto.class);
      callfaceChamadaHistoricoService.create(CallfaceChamadaHistorico
        .builder()
        .id(UUID.randomUUID())
        .callfaceId(payload.Id())
        .chamadaId(payload.CallId())
        .duracaoChamada(payload.TalkingDuration())
        .leadId(payload.LeadId())
        .usuarioId(Integer.valueOf(payload.UserId()))
        .telefoneDestino(payload.CallDestination())
        .organizacaoId(Integer.valueOf(payload.OrgId()))
        .dataChamada(LocalDateTime.parse(payload.EventDate(),
          DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
        .tempoFaturado(payload.BilledTime())
        .tipoTelefone(payload.PhoneType())
        .urlGravacao(payload.UrlGravacao())
        .build());
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}