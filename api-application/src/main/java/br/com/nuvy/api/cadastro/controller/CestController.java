package br.com.nuvy.api.cadastro.controller;

import br.com.nuvy.api.cadastro.filter.CestFilter;
import br.com.nuvy.api.cadastro.service.CestService;
import br.com.nuvy.api.cadastro.dto.CestDto;
import br.com.nuvy.common.base.controller.PageableControllerAdapter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Cadastro / Cest")
@RestController
//TODO mudar a rota pra /v1/cadastro/cest
@RequestMapping("/v1/cadastro/cest")
public class CestController extends
  PageableControllerAdapter<CestDto, String, CestFilter, CestService> {

}
