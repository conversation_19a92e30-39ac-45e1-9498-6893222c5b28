package br.com.nuvy.api.estoque.service;

import org.springframework.stereotype.Service;

@Service
public class EstoqueService {

  private final MovimentoService movimentoService;
  private final SaldoService saldoService;

  EstoqueService(MovimentoService movimentoService, SaldoService saldoService) {
    this.movimentoService = movimentoService;
    this.saldoService = saldoService;
  }

  public void excluiProduto(Integer produtoId) {
    movimentoService.excluiProduto(produtoId);
    saldoService.excluiProduto(produtoId);
  }
}
