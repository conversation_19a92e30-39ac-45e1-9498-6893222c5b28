package br.com.nuvy.api.cadastro.repository.specification;

import br.com.nuvy.api.cadastro.filter.PosPdvIntegracaoFilter;
import br.com.nuvy.api.cadastro.model.PosPdvIntegracao;
import br.com.nuvy.api.cadastro.model.PosPdvIntegracao_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
public class PosPdvIntegracaoSpecification implements Specification<PosPdvIntegracao> {

  private final PosPdvIntegracaoFilter filter;

  @Override
  public Predicate toPredicate(Root<PosPdvIntegracao> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);

    predicateBuilder
      .add(filter.getSituacao(),
        e -> criteriaBuilder.equal(root.get(PosPdvIntegracao_.situacao), e));

    if(filter.getIntegracoesIds() !=null && !filter.getIntegracoesIds().isEmpty()){
      predicateBuilder.add(filter.getIntegracoesIds(), e -> criteriaBuilder.not(
        root.get(PosPdvIntegracao_.id).in(filter.getIntegracoesIds())));
    }

    return predicateBuilder.and();
  }
}