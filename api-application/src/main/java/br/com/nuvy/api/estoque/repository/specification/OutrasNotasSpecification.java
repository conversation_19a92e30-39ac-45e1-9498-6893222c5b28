package br.com.nuvy.api.estoque.repository.specification;

import static br.com.nuvy.common.utils.StringUtils.getIlikeStringUpper;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Pessoa_;
import br.com.nuvy.api.estoque.filter.OutrasNotasFilter;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada;
import br.com.nuvy.api.estoque.model.NotaFiscalEntrada_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "of")
public class OutrasNotasSpecification implements Specification<NotaFiscalEntrada> {

  private final OutrasNotasFilter filter;

  @Override
  public Predicate toPredicate(Root<NotaFiscalEntrada> root, CriteriaQuery<?> query,
    CriteriaBuilder criteriaBuilder) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(criteriaBuilder);
    return predicateBuilder
      .add(filter.getFornecedorNome(),
        e ->
        {
          Join<NotaFiscalEntrada, Pessoa> fornecedorJoin = root.join(NotaFiscalEntrada_.fornecedor,
            JoinType.LEFT);
          return criteriaBuilder.or(
            criteriaBuilder.like(criteriaBuilder.upper(fornecedorJoin.get(Pessoa_.nome)),
              getIlikeStringUpper(e)),
            criteriaBuilder.like(criteriaBuilder.upper(fornecedorJoin.get(Pessoa_.nomeFantasia)),
              getIlikeStringUpper(e)));
        })
      .add(filter.getNumeroNotaFiscal(),
        e -> criteriaBuilder.like(root.get(NotaFiscalEntrada_.numeroNota), e + "%"))
      .add(filter.getSituacao(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get(NotaFiscalEntrada_.situacao).as(String.class)),
          getIlikeStringUpper(e)))
      .add(filter.getDataCriacao(),
        e -> criteriaBuilder.equal(root.get(NotaFiscalEntrada_.dataRegistro).as(LocalDate.class),
          e))
      .add(filter.getFinalidadeNotaFiscal(),
        e -> criteriaBuilder.like(
          criteriaBuilder.upper(root.get(NotaFiscalEntrada_.tipoNota).as(String.class)),
          getIlikeStringUpper(e)))
      .add(filter.getNumeroNotaOrigem(),
        e -> {
          Join<NotaFiscalEntrada, NotaFiscalEntrada> notaOrigemJoin = root.join(
            NotaFiscalEntrada_.notaFiscalOrigem,
            JoinType.LEFT);
          return criteriaBuilder.like(notaOrigemJoin.get(NotaFiscalEntrada_.numeroNota), e + "%");
        })
      .add(filter.getOrigem(),
        e -> {
          Join<NotaFiscalEntrada, NotaFiscalEntrada> notaOrigemJoin = root.join(
            NotaFiscalEntrada_.notaFiscalOrigem, JoinType.LEFT);
          Predicate condition = criteriaBuilder.isNull(notaOrigemJoin);

          Expression<String> dataFormatada = criteriaBuilder.function(
            "to_char", String.class,
            notaOrigemJoin.get(NotaFiscalEntrada_.dataEmissao),
            criteriaBuilder.literal("DD/MM/YYYY")
          );

          Expression<String> numeroNota = notaOrigemJoin.get(NotaFiscalEntrada_.numeroNota);
          Expression<String> origem = criteriaBuilder.selectCase()
            .when(condition, criteriaBuilder.literal("Manual"))
            .otherwise(criteriaBuilder.concat("NF-e nº ", criteriaBuilder.concat(numeroNota,
              criteriaBuilder.concat(criteriaBuilder.literal(", de "), dataFormatada)))).as(String.class);

          return criteriaBuilder.like(criteriaBuilder.upper(origem), getIlikeStringUpper(e));
        })

      .add(filter.getIgnorarTiposNotas(),
        e -> criteriaBuilder.not(root.get(NotaFiscalEntrada_.tipoNota).in(e)))
      .add(filter.getEmpresaId(),
        e -> criteriaBuilder.equal(root.get(NotaFiscalEntrada_.empresa).get("id"), e))
      .and();
  }
}
