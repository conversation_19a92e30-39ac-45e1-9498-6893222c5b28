package br.com.nuvy.api.seguranca.repository.specification;

import static br.com.nuvy.common.query.CriteriaUtils.iLike;
import static br.com.nuvy.common.utils.StringUtils.getIlikeStringUpper;

import br.com.nuvy.api.seguranca.filter.UsuarioAplicacaoFilter;
import br.com.nuvy.api.seguranca.model.Perfil;
import br.com.nuvy.api.seguranca.model.Perfil_;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao;
import br.com.nuvy.api.seguranca.model.UsuarioAplicacao_;
import br.com.nuvy.api.seguranca.model.Usuario_;
import br.com.nuvy.common.query.PredicateBuilder;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@RequiredArgsConstructor(staticName = "from")
@EqualsAndHashCode
public class UsuarioAplicacaoSpecification implements Specification<UsuarioAplicacao> {

  private final UsuarioAplicacaoFilter filter;

  @Override
  public Predicate toPredicate(Root<UsuarioAplicacao> root, CriteriaQuery<?> query,
    CriteriaBuilder cb) {
    PredicateBuilder predicateBuilder = PredicateBuilder.create(cb);
    return predicateBuilder
      .add(filter.getId(), e -> cb.equal(root.get(UsuarioAplicacao_.id), e))
      .add(filter.getNome(),
        e -> iLike(cb, cb.upper(root.get(UsuarioAplicacao_.usuario).get(Usuario_.nome)),
          getIlikeStringUpper(e)))
      .add(filter.getEmail(),
        e -> iLike(cb, cb.upper(root.get(UsuarioAplicacao_.usuario).get(Usuario_.email)),
          getIlikeStringUpper(e)))
      .add(filter.getSituacao(),
        e -> cb.like(cb.upper(root.get(UsuarioAplicacao_.situacao.getName())),
          getIlikeStringUpper(e)))
      .add(filter.getPerfilNome(), e -> {
        Join<UsuarioAplicacao, Perfil> perfil = root.join(UsuarioAplicacao_.perfilsAplicacao);
        return iLike(cb, cb.upper(perfil.get(Perfil_.nome)), getIlikeStringUpper(e));
      })
      .add(filter.getPerfilId(), e -> {
        Join<UsuarioAplicacao, Perfil> perfil = root.join(UsuarioAplicacao_.perfilsAplicacao);
        return cb.equal(perfil.get(Perfil_.id), e);
      })
      .add(filter.getCallface(),
        e -> cb.equal(root.get(UsuarioAplicacao_.callface), e))
      .add(filter.getRamalCallFace(),
        e -> cb.equal(root.get(UsuarioAplicacao_.ramalCallFace), e))
      .add(filter.getAtivo(),
        e -> {
          if (e) {
            return cb.like(root.get(UsuarioAplicacao_.situacao).as(String.class), "ATIVO");
          } else {
            return root.get(UsuarioAplicacao_.situacao).as(String.class).in("INATIVO", "PENDENTE");
          }
        })
      .and();
  }
}