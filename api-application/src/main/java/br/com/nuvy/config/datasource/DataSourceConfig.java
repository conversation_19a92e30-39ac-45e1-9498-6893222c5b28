package br.com.nuvy.config.datasource;

import java.util.HashMap;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
class DataSourceConfig {

  @Bean
  DataSourceAspect dataSourceAspect() {
    return new DataSourceAspect();
  }

  @Bean
  @Primary
  DataSource dataSource(
    @Qualifier("defaultDs") DataSource defaultDs,
    @Qualifier("readDs") @Autowired(required = false) DataSource readDs
  ) {
    var targetDataSources = new HashMap<DataSourceType, DataSource>();
    targetDataSources.put(DataSourceType.DEFAULT, defaultDs);
//    if (readDs != null) {
//      targetDataSources.put(DataSourceType.READ, readDs);
//    }
    return new DynamicDataSource(defaultDs, targetDataSources);
  }

  @Configuration
  static class DefaultDsConfig {

    @Bean
    @ConfigurationProperties("spring.datasource")
    DataSourceProperties defaultDsProperties() {
      return new DataSourceProperties();
    }

    @Bean
    DataSource defaultDs(@Qualifier("defaultDsProperties") DataSourceProperties props) {
      return props.initializeDataSourceBuilder().build();
    }
  }

  @Configuration
  @ConditionalOnProperty(prefix = "spring.datasource.read-replica", name = "enabled", havingValue = "true")
  static class ReadDsConfig {

    @Bean
    @ConfigurationProperties("spring.datasource.read-replica")
    DataSourceProperties readDsProperties() {
      return new DataSourceProperties();
    }

    @Bean
    DataSource readDs(@Qualifier("readDsProperties") DataSourceProperties props) {
      return props.initializeDataSourceBuilder().build();
    }
  }
}
