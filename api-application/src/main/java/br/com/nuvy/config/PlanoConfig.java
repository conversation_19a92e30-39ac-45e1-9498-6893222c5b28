package br.com.nuvy.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "nuvy.planos")
public class PlanoConfig {

  private Plano free;
  private Plano iniciante;
  private Plano inicianteSemestral;
  private Plano avancado;
  private Plano avancadoSemestral;
  private Plano pdv;
  private Plano pdvSemestral;

  @Getter
  @Setter
  public static class Plano {
    private String id;
    private Integer usuarios;
  }
}
