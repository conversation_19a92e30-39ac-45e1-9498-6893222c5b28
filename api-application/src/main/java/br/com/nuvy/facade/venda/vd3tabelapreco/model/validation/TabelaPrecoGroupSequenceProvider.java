package br.com.nuvy.facade.venda.vd3tabelapreco.model.validation;

import br.com.nuvy.facade.venda.vd3tabelapreco.model.TabelaPrecoDto;
import br.com.nuvy.validation.groups.TabelaPrecoValidadeGroup;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TabelaPrecoGroupSequenceProvider implements DefaultGroupSequenceProvider<TabelaPrecoDto> {

  @Override
  public List<Class<?>> getValidationGroups(TabelaPrecoDto tabelaPrecoDto) {
    List<Class<?>> groups = new ArrayList<>();
    groups.add(TabelaPrecoDto.class);
    if (Objects.isNull(tabelaPrecoDto)) {
      return groups;
    }
    if (tabelaPrecoDto.getValidade()) {
      groups.add(TabelaPrecoValidadeGroup.class);
    }
    return groups;
  }

}

