package br.com.nuvy.facade.financeiro.fn8relatorios;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.PosPdvVenda;
import br.com.nuvy.api.cadastro.model.PosPdvVendaItem;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.cadastro.repository.PosPdvRepository;
import br.com.nuvy.api.cadastro.repository.PosPdvVendaRepository;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.SituacaoPosPdvVenda;
import br.com.nuvy.api.venda.filter.RelatorioCurvaAbcFilter;
import br.com.nuvy.api.venda.model.Pedido;
import br.com.nuvy.api.venda.repository.PedidoRepository;
import br.com.nuvy.api.venda.repository.specification.PedidoRelatorioSpecification;
import br.com.nuvy.api.venda.repository.specification.PosPdvVendaRelatorioSpecification;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.facade.financeiro.fn8relatorios.model.RelatorioCurvaAbcDto;
import br.com.nuvy.facade.financeiro.fn8relatorios.model.TipoCurvaAbc;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PedidoRelatorioFilter;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import br.com.nuvy.facade.relatorio.rl3vendas.model.PosPdvVendasRelatorioFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FN8RelatoriosService {

  private final PedidoRepository pedidoRepository;
  private final PosPdvRepository posPdvRepository;
  private final PosPdvVendaRepository posPdvVendaRepository;

  public List<RelatorioCurvaAbcDto> findCurvaAbcPorQuantidade(RelatorioCurvaAbcFilter filter) {
    validaDatasFiltro(filter.getDataInicial(), filter.getDataFinal());
    var pedidos = getPedidosRelatorioCurvaAbc(filter);
    Map<Empresa,Map<Produto, BigDecimal>> mapProdutosEmpresa = new HashMap<>();
    pedidos.forEach(pedido -> pedido.getItens().forEach(
      pedidoItem -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if (v == null) {
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO));
          } else {
            v.compute(pedidoItem.getProduto(), (j, m) -> (m == null) ?  getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO)
              : m.add(getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO)));
          }
          return v;
        })));

    return montaDtoCurvaAbcRelatorio(mapProdutosEmpresa);
  }

  public List<RelatorioCurvaAbcDto> findCurvaAbcPdvPorQuantidade(RelatorioCurvaAbcFilter filter) {
    // buscando vendas pedido
    validaDatasFiltro(filter.getDataInicial(), filter.getDataFinal());
    var pedidos = getPedidosRelatorioCurvaAbc(filter);
    Map<Empresa,Map<Produto, BigDecimal>> mapProdutosEmpresa = new HashMap<>();
    pedidos.forEach(pedido -> pedido.getItens().forEach(
      pedidoItem -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if (v == null) {
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO));
          } else {
            v.compute(pedidoItem.getProduto(), (j, m) -> (m == null) ?  getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO)
              : m.add(getOrElse(pedidoItem.getQuantidade(), BigDecimal.ZERO)));
          }
          return v;
        })));

    // buscar vendas PDV
    List<PosPdvVenda> pedidosPdv = getVendasPdvRelatorioCurvaAbc(filter);
    pedidosPdv.forEach(pedido -> pedido.getItens().forEach(
      (PosPdvVendaItem pedidoItem) -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if(v == null){
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getQuantidade(),BigDecimal.ZERO));
          }else{
            v.compute(pedidoItem.getProduto(),(j,m)->(m == null)?getOrElse(pedidoItem.getQuantidade(),BigDecimal.ZERO)
              :m.add(getOrElse(pedidoItem.getQuantidade(),BigDecimal.ZERO)));
          }
          return v;
        }
      )));

    return montaDtoCurvaAbcRelatorio(mapProdutosEmpresa);
  }

  /**
   * Os produtos no relatório devem ser categorizados em 3 grupos:
   * - A = <= 20% da participação
   * - B = >20% e <= 80% da participação
   * - C = >80% da participação
   * Exceções:
   * - Caso não tenha produto anterior, fica na cateria A.
   * - Caso o último produto foi categoria A, e o percentual foi maior que 80%,
   * fica na categoria B.
   *
   * @param ultimaCurvaAbc Ultima curva ABC
   * @param curvaAbcAtual  Curva ABC atual
   * @return TipoCurvaAbc
   */
  private TipoCurvaAbc getTipoCurvaAbc(
      RelatorioCurvaAbcDto ultimaCurvaAbc, RelatorioCurvaAbcDto curvaAbcAtual) {
    var vintePorcento = new BigDecimal(20);
    var oitentaPorcento = new BigDecimal(80);
    if (Objects.isNull(ultimaCurvaAbc) || curvaAbcAtual.getPercentualAcumulado().compareTo(vintePorcento) < 1) {
      return TipoCurvaAbc.A;
    } else if ((TipoCurvaAbc.A.equals(ultimaCurvaAbc.getAbc())
        && oitentaPorcento.compareTo(curvaAbcAtual.getPercentualAcumulado()) < 1) ||
        (curvaAbcAtual.getPercentualAcumulado().compareTo(vintePorcento) > 0
            && curvaAbcAtual.getPercentualAcumulado().compareTo(oitentaPorcento) < 1)) {
      return TipoCurvaAbc.B;
    } else {
      return TipoCurvaAbc.C;
    }

  }

  public List<RelatorioCurvaAbcDto> findCurvaAbcPorValor(RelatorioCurvaAbcFilter filter) {
    validaDatasFiltro(filter.getDataInicial(), filter.getDataFinal());
    var pedidos = getPedidosRelatorioCurvaAbc(filter);
    Map<Empresa,Map<Produto, BigDecimal>> mapProdutosEmpresa = new HashMap<>();
    pedidos.forEach(pedido -> pedido.getItens().forEach(
      pedidoItem -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if(v == null){
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO));
          }else{
            v.compute(pedidoItem.getProduto(),(j,m)->(m == null)?getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)
              :m.add(getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)));
          }
          return v;
        }
        )));
    return montaDtoCurvaAbcRelatorio(mapProdutosEmpresa);
  }

  public List<RelatorioCurvaAbcDto> findCurvaAbcPdvPorValor(RelatorioCurvaAbcFilter filter) {
    // buscando vendas pedido
    validaDatasFiltro(filter.getDataInicial(), filter.getDataFinal());
    var pedidos = getPedidosRelatorioCurvaAbc(filter);
    Map<Empresa,Map<Produto, BigDecimal>> mapProdutosEmpresa = new HashMap<>();
    pedidos.forEach(pedido -> pedido.getItens().stream()
      .filter(it -> Objects.nonNull(it.getProduto()))
      .forEach(pedidoItem -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if(v == null){
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO));
          }else{
            v.compute(pedidoItem.getProduto(),(j,m)->(m == null)?getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)
              :m.add(getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)));
          }
          return v;
        }
      )));

    // buscar vendas PDV
    List<PosPdvVenda> pedidosPdv = getVendasPdvRelatorioCurvaAbc(filter);
    pedidosPdv.forEach(pedido -> pedido.getItens().stream()
      .filter(it -> Objects.nonNull(it.getProduto()))
      .forEach((PosPdvVendaItem pedidoItem) -> mapProdutosEmpresa.compute(
        pedido.getEmpresa(), (k, v) -> {
          if(v == null){
            v = new HashMap<>();
            v.put(pedidoItem.getProduto(), getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO));
          }else{
            v.compute(pedidoItem.getProduto(),(j,m)->(m == null)?getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)
              :m.add(getOrElse(pedidoItem.getValorTotal(),BigDecimal.ZERO)));
          }
          return v;
        }
      )));

    return montaDtoCurvaAbcRelatorio(mapProdutosEmpresa);
  }

  private List<Pedido> getPedidosRelatorioCurvaAbc(RelatorioCurvaAbcFilter filter) {
    PedidoRelatorioFilter pedidoRelatorioFilter = new PedidoRelatorioFilter();
    pedidoRelatorioFilter.setEmpresasId(filter.getEmpresasId());
    pedidoRelatorioFilter.setSituacaoPedido(List.of(SituacaoPedido.NF_EMITIDA, SituacaoPedido.PEDIDO_AUTORIZADO, SituacaoPedido.ENTREGUE, SituacaoPedido.ENVIADO));
    pedidoRelatorioFilter.setDataPedidoInicial(filter.getDataInicial());
    pedidoRelatorioFilter.setDataPedidoFinal(filter.getDataFinal());
    PedidoRelatorioSpecification specification = new PedidoRelatorioSpecification(pedidoRelatorioFilter);
    return pedidoRepository.findAll(specification);
  }

  private List<PosPdvVenda> getVendasPdvRelatorioCurvaAbc(RelatorioCurvaAbcFilter filter) {
    PosPdvVendasRelatorioFilter posPdvVendasRelatorioFilter = new PosPdvVendasRelatorioFilter();
    posPdvVendasRelatorioFilter.setSituacaoPedido(List.of(SituacaoPosPdvVenda.ATIVO));
    //posPdvVendasRelatorioFilter.setAmbiente("PRODUCAO");
    posPdvVendasRelatorioFilter.setDataPedidoInicial(filter.getDataInicial());
    posPdvVendasRelatorioFilter.setDataPedidoFinal(filter.getDataFinal());
    PosPdvVendaRelatorioSpecification specification = new PosPdvVendaRelatorioSpecification(posPdvVendasRelatorioFilter);
    return posPdvVendaRepository.findAll(specification);
  }

  private void validaDatasFiltro(LocalDate dataInicial, LocalDate dataFinal) {
    if (Objects.nonNull(dataFinal) && dataFinal.isAfter(LocalDate.now())) {
      throw new PreconditionException("data.final.maior.que.data.atual");
    }
    if (Objects.nonNull(dataInicial) && Objects.nonNull(dataFinal) && dataInicial.isAfter(dataFinal)) {
      throw new PreconditionException("data.inicial.maior.que.data.final");
    }
  }

  private String createDescricaoProduto(Produto produto) {
    var variacoes = produto.getVariacoes();

    List<String> variacoesValores = variacoes.stream()
        .map(v -> v.getVariacaoProdutoValor().getValor())
        .toList();

    List<String> descricoes = new ArrayList<>();
    descricoes.add(produto.getDescricao());
    descricoes.addAll(variacoesValores);

    return descricoes.stream()
        .filter(Objects::nonNull)
        .collect(Collectors.joining(" - "));
  }

  private List<RelatorioCurvaAbcDto> montaDtoCurvaAbcRelatorio(Map<Empresa,Map<Produto, BigDecimal>> mapEmpresaProduto){
    var cem = new BigDecimal(100);
    List<RelatorioCurvaAbcDto> curvaAbcDtos = new ArrayList<>();
    mapEmpresaProduto.forEach((empresa, mapProdutos) -> {
      var valorTotal = mapProdutos.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ONE);
      AtomicReference<RelatorioCurvaAbcDto> ultimoCurvaAbc = new AtomicReference<>(null);
      mapProdutos.entrySet().stream().sorted(Collections.reverseOrder(Map.Entry.comparingByValue())).collect(Collectors.toMap(
        Map.Entry::getKey,
        Map.Entry::getValue,
        (oldValue, newValue) -> oldValue, LinkedHashMap::new)).forEach((produto, valor) -> {
        var curvaAbc = new RelatorioCurvaAbcDto(produto);
        curvaAbc.setProdutoDescricao(createDescricaoProduto(produto));
        curvaAbc.setQuantidadeValorVenda(valor);
        curvaAbc.setPercentualParticipacao(valor.divide(valorTotal, RoundingMode.HALF_DOWN).multiply(cem));
        curvaAbc.setPercentualAcumulado(Objects.isNull(ultimoCurvaAbc.get()) ? curvaAbc.getPercentualParticipacao() : ultimoCurvaAbc.get().getPercentualAcumulado().add(curvaAbc.getPercentualParticipacao()));
        curvaAbc.setAbc(getTipoCurvaAbc(ultimoCurvaAbc.get(), curvaAbc));
        curvaAbc.setEmpresa(empresa.getNome());
        ultimoCurvaAbc.set(curvaAbc);
        curvaAbcDtos.add(curvaAbc);
      });
    });
    return curvaAbcDtos;
  }
}
