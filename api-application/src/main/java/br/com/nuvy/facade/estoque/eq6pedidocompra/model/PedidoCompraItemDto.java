package br.com.nuvy.facade.estoque.eq6pedidocompra.model;

import br.com.nuvy.api.pedidocompra.model.PedidoCompraItem;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompraItem;
import br.com.nuvy.common.base.dto.Dto;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(of = "produtoId")
public class PedidoCompraItemDto implements Dto<PedidoCompraItem> {

  private Long id;
  private UUID uuid;
  private Integer produtoId;
  private String produtoDescricao;
  private String produtoCodigo;
  private String descricao;
  private BigDecimal quantidade;
  private BigDecimal valorUnitario;
  private BigDecimal valorTotal;
}