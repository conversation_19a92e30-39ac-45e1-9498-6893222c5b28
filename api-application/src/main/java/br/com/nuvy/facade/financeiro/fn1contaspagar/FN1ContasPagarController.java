package br.com.nuvy.facade.financeiro.fn1contaspagar;

import br.com.nuvy.api.cadastro.dto.PessoaFNDTO;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.financeiro.dto.ConciliacaoDto;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoFNDto;
import br.com.nuvy.api.financeiro.dto.FormaLancamentoDto;
import br.com.nuvy.api.financeiro.dto.ImportacaoContasDto;
import br.com.nuvy.api.financeiro.dto.RecorrenciaDto;
import br.com.nuvy.api.financeiro.dto.TipoPagamentoDto;
import br.com.nuvy.api.financeiro.dto.TituloBaixaResumoDto;
import br.com.nuvy.api.financeiro.dto.TituloDto;
import br.com.nuvy.api.financeiro.dto.TituloDtoOut;
import br.com.nuvy.api.financeiro.dto.TituloHistoricoDto;
import br.com.nuvy.api.financeiro.dto.TituloRelatorioDto;
import br.com.nuvy.api.financeiro.dto.TituloResumoDto;
import br.com.nuvy.api.financeiro.filter.ConciliadoFilter;
import br.com.nuvy.api.financeiro.filter.TituloFilter;
import br.com.nuvy.api.financeiro.filter.TituloHistoricoFilter;
import br.com.nuvy.api.financeiro.filter.TituloRelatorioFilter;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.config.datasource.DataSource;
import br.com.nuvy.config.datasource.DataSourceType;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.FN1ContasPagarDto;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.FinalidadeTedDto;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.SimularRecorrenciaDto;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.TituloBaixaDto;
import br.com.nuvy.facade.financeiro.fn1contaspagar.model.TituloBaixaDtoOut;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.TitulosDtoOut;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.TitulosResumidosDtoOut;
import br.com.nuvy.validation.ErrorDetail;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import static br.com.nuvy.config.BadgeContext.getEmpresa;

@Tag(name = "@Financeiro / FN1 - Contas a Pagar")
@RestController
@RequestMapping(value = "/financeiro/fn1")
@RequiredArgsConstructor
public class FN1ContasPagarController {

  private final FN1ContasPagarService service;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Busca todas as infomações necessárias para a tela de Contas a Pagar",
    description = "É a busca de informações necessárias como Contas Corrente, Centros de Custos, Tipos de Despesas e Contas a Pagar"
  )
  @GetMapping(value = "/iniciar", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public FN1ContasPagarDto iniciar() {
    return service.iniciar();
  }

  @Operation(
    summary = "Permite buscar Fornecedores por nome",
    description = "Realiza a busca de Fornecedores por nome para a tela de cadastro de Contas a Pagar"
  )
  @GetMapping(value = "/fornecedor")
  @ResponseStatus(HttpStatus.OK)
  public List<PessoaFNDTO> findFornecedor(@RequestParam(required = false) String criterio,
    @RequestParam(required = false) SituacaoParceiro situacao, @ParameterObject Pageable pageable) {
    Page<PessoaFNDTO> result = service.findFornecedor(criterio, situacao, pageable);
    return discoverer.handlePaginatedResults(pageable, result);
  }

  @Operation(
    summary = "Permite buscar Finalidades de TED",
    description = "Realiza busca de Finalidades de TED para a tela de cadastro de Contas a Pagar"
  )
  @GetMapping(value = "/finalidade-ted")
  @ResponseStatus(HttpStatus.OK)
  public List<FinalidadeTedDto> findFinalidadeTed() {
    return service.listFinalidadeTed();
  }

  @Operation(
    summary = "Permite buscar Conta a Pagar por Id",
    description = "Através do ID é possível buscar uma Conta a Pagar"
  )
  @GetMapping(value = "/conta-pagar/{id}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public TituloDtoOut findContaPagarById(@PathVariable Integer id) {
    return service.findContaPagarById(id);
  }

  @Operation(
    summary = "Permite buscar Contas a Pagar",
    description = "É a busca de Contas a Pagar com filtro, para uma busca específica"
  )
  @GetMapping(value = "/conta-pagar", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public TitulosResumidosDtoOut findContaPagar(@ParameterObject @Valid TituloFilter filter,
    @ParameterObject Pageable pageable) {
    TitulosResumidosDtoOut result = service.findContaPagar(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result.getPage());
    return result;
  }

  @Operation(
    summary = "Permite buscar Contas a Pagar criterio",
    description = "É a busca de Contas a Pagar criterio"
  )
  @GetMapping(value = "/conta-pagar/criterio", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public TitulosDtoOut findContaPagarCriterio(@ParameterObject @Valid String criterio,
    @ParameterObject Pageable pageable) {
    Page<TituloResumoDto> contaPagar = service.findContaPagarCriterio(criterio, pageable);
    discoverer.handlePaginatedResults(pageable, contaPagar);
    return service.getContaPagarDtoOut(contaPagar.toList());
  }

  @Operation(
    summary = "Permite buscar Historico de Conta a Pagar",
    description = "Através do filter é possível buscar o histórico de todas as alterações"
  )
  @GetMapping(value = "/historicos/conta-pagar/{id}")
  @ResponseStatus(HttpStatus.OK)
  public List<TituloHistoricoDto> findHistoricoContaPagar(@PathVariable Integer id,
    @ParameterObject @Valid TituloHistoricoFilter filter,
    @ParameterObject Pageable pageable) {
    return service.findHistoricoContaPagar(id, filter, pageable);
  }

  @Operation(
    summary = "Permite simular as recorrências",
    description = "Realiza a simulação das recorrências de Contas a Pagar"
  )
  @GetMapping(value = "/recorrencia-simular")
  @ResponseStatus(HttpStatus.OK)
  public List<RecorrenciaDto> simularRecorrencia(
    @Valid @ParameterObject SimularRecorrenciaDto payload) {
    return service.simularRecorrencia(payload);
  }

  @Operation(
    summary = "Permite alterar uma Conta a Pagar",
    description = "Através do ID é possível alterar uma Conta a Pagar"
  )
  @PutMapping(value = "/conta-pagar/{id}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public void updateContaPagar(@PathVariable Integer id, @RequestBody @Valid TituloDto payload) {
    service.updateContaPagar(id, payload);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Permite criar Conta a Pagar",
    description = "Através do payload é possível criar uma Conta a Pagar"
  )
  @PostMapping(value = "/conta-pagar", headers = "empresa")
  @ResponseStatus(HttpStatus.CREATED)
  public ResponseEntity<List<ErrorDetail>> createContaPagar(@RequestBody @Valid TituloDto payload) {
    List<ErrorDetail> errorDetails = service.validContaPagar(payload);
    if (!errorDetails.isEmpty()) {
      return ResponseEntity.badRequest().body(errorDetails);
    }
    var result = service.createContaPagar(payload);
    discoverer.handleCreatedResource(result);
    return ResponseEntity.status(HttpStatus.CREATED).build();
  }

  @Operation(
    summary = "Permite incluir anexo no Conta a Pagar",
    description = "É a alteração de uma Conta a Pagar para incluir o anexo"
  )
  @PutMapping(value = "/conta-pagar/{id}/anexo", headers = "empresa", consumes = {
    MediaType.MULTIPART_FORM_DATA_VALUE})
  @ResponseStatus(HttpStatus.CREATED)
  public ResponseEntity<String> updateContaPagarIncluirAnexo(
    @RequestPart List<MultipartFile> files, @PathVariable Integer id) {
    service.createAnexo(id, files);
    return ResponseEntity.status(HttpStatus.CREATED).build();
  }

  @Operation(
    summary = "Permite dar Baixa na Conta a Pagar",
    description = "Deve passar o Id de Conta a Pagar e os dados da baixa, para que seja possível dar Baixa na Conta a Pagar"
  )
  @PostMapping(value = "/conta-pagar/{id}/baixa")
  @ResponseStatus(HttpStatus.CREATED)
  public TituloBaixaDto baixaContaPagar(@PathVariable Integer id,
    @Valid @RequestBody TituloBaixaDto payload) {
    var result = service.createBaixaPagar(id, payload);
    discoverer.handleCreatedResource(result.getId());
    return result;
  }

  @Operation(
    summary = "Permite Conciliar a Conta a Pagar",
    description = "Deve passar o ID de Conta a Receber para que seja possível Conciliar a Conta a Pagar"
  )
  @PostMapping(value = "/conciliacao/{id}")
  @ResponseStatus(HttpStatus.CREATED)
  public void conciliacaoContaPagar(@PathVariable Integer id) {
    service.conciliacao(id);
  }

  @Operation(
    summary = "Permite Conciliar Contas a Pagar por uma lista de ids",
    description = "Deve passar os IDs de Conta a Receber para que seja possível Conciliar a Conta a Pagar"
  )
  @PostMapping(value = "/conciliacao/lote")
  @ResponseStatus(HttpStatus.CREATED)
  public void conciliacaoContaPagarEmLote(@RequestBody List<Integer> ids) {
    for (Integer id : ids) {
      service.conciliacao(id);
    }
  }

  @Operation(
    summary = "Permite conciliar a Conta a Receber por id da baixa",
    description = "Deve passar o ID de Conta a Receber e o id da baixa para que seja possível Conciliar a Conta a Receber"
  )
  @PostMapping(value = "/conciliacao/{id}/baixa/{idBaixa}")
  @ResponseStatus(HttpStatus.CREATED)
  public void conciliacaoContaPagarPorBaixa(@PathVariable Integer id, @PathVariable Integer idBaixa) {
    service.conciliacaoPorBaixa(id, idBaixa);
  }

  @Operation(
    summary = "Permite Buscar titulo para conciliar no Conta a Pagar",
    description = "Permite Buscar titulo para conciliar no Conta a Pagar"
  )
  @GetMapping(value = "/conciliacao")
  @ResponseStatus(HttpStatus.OK)
  public List<ConciliacaoDto> findConciliacaoContaPagar(
    @ParameterObject @Valid ConciliadoFilter filter,
    @ParameterObject Pageable pageable) {
    Page<ConciliacaoDto> titulos = service.buscarConciliacao(filter, pageable);
    discoverer.handlePaginatedResults(pageable, titulos);
    return titulos.getContent();
  }

  @Operation(
    summary = "Permite deletar uma Conta a Pagar por Id",
    description = "Deleta uma Conta a Pagar por Id"
  )
  @DeleteMapping(value = "/conta-pagar/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteContaPagar(@PathVariable Integer id) {
    service.deleteContaPagar(id);
  }

  @Operation(
    summary = "Permite retornar a situação do titulo para a situação anterior",
    description = "Retorna a situação do titulo para a situação anterior"
  )
  @PutMapping(value = "/alterar-situacao/{id}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public void alterarSituacao(@PathVariable Integer id) {
    service.retornarSituacaoContaPagar(id);
  }

  @Operation(
    summary = "Permite retornar a situação do titulo para a situação anterior por baixa",
    description = "Retorna a situação do titulo para a situação anterior"
  )
  @PutMapping(value = "/alterar-situacao/{idTitulo}/baixa/{idBaixa}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public void alterarSituacao(@PathVariable("idTitulo") Integer idTitulo, @PathVariable("idBaixa") Integer idBaixa) {
    service.retornarSituacaoContaPagarPorBaixa(idTitulo, idBaixa);
  }

  @Operation(
    summary = "Permite retornar um resumo de todas as contas",
    description = "Permite retornar um resumo de todas as contas"
  )
  @GetMapping(value = "/conta-pagar/resumo")
  @ResponseStatus(HttpStatus.OK)
  @DataSource(DataSourceType.READ)
  public List<ContaBancariaResumoFNDto> findcontasPagarResumo(@ParameterObject Pageable pageable) {
    Page<ContaBancariaResumoFNDto> result = service.findContasPagarResumo(pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite listar os tipos de pagamento",
    description = "Retorna uma listagem de tipos de pagamento"
  )
  @GetMapping(value = "/tipo_pagamento")
  public List<TipoPagamentoDto> getTipoPagamento(Integer idBanco) {
    return service.getTipoPagamento(idBanco);
  }

  @Operation(
    summary = "Permite listar as formas de lançamento",
    description = "Retorna uma listagem de formas de lançamento"
  )
  @GetMapping(value = "/forma_lancamento")
  public List<FormaLancamentoDto> getFormasLancamento(Integer idBanco, String codTipoPagamento) {
    return service.getFormasLancamento(idBanco, codTipoPagamento);
  }

  @Operation(
    summary = "Permite buscar um anexo vinculado ao Historico de contas a pagar",
    description = "Utilizando o id do historico de contas a pagar, é possível buscar o anexo"
  )
  @GetMapping(value = "/historicos/conta-pagar/anexo/{idHistorico}")
  @ResponseStatus(HttpStatus.OK)
  public ResponseEntity<InputStreamResource> findHistoricAttachments(
    @PathVariable Integer idHistorico)
    throws IOException {
    var result = service.findHistoricAttachments(idHistorico);
    return ResponseEntity.ok()
      .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "arquivos.zip")
      .contentType(MediaType.parseMediaType("application/zip"))
      .body(result);
  }

  @Operation(
    summary = "Permite buscar as baixas de uma conta a pagar",
    description = "Permite buscar todas as baixas de uma conta a pagar"
  )
  @GetMapping(value = "/conta-pagar/{id}/baixas")
  @ResponseStatus(HttpStatus.OK)
  public TituloBaixaDtoOut findBaixasContaPagar(@PathVariable Integer id) {
    return service.findBaixas(id);
  }

  @Operation(
    summary = "Permite buscar as recorrencias de uma conta a pagar",
    description = "Permite buscar todas as recorrencias de uma conta a pagar"
  )
  @GetMapping(value = "/conta-pagar/{id}/recorrencias")
  @ResponseStatus(HttpStatus.OK)
  public List<RecorrenciaDto> findRecorrenciasContaPagar(@PathVariable Integer id) {
    return service.findRecorrencias(id);
  }

  @Operation(
    summary = "Permite deletar uma Baixa de Conta a Pagar",
    description = "Deve passar o ID de Baixa de Conta a Pagar para que seja possível deletar a Baixa"
  )
  @DeleteMapping(value = "/conta-pagar/baixa/{tituloBaixaId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteBaixaContaReceber(@PathVariable Integer tituloBaixaId,
    @RequestHeader String empresa) {
    service.deleteBaixaPagar(tituloBaixaId);
  }

  @Operation(
    summary = "Permite alterar uma Baixa de uma Conta a Pagar",
    description = "É a alteração de uma Baixa de uma Conta a Pagar"
  )
  @PutMapping(value = "/conta-pagar/baixa/{tituloBaixaId}")
  @ResponseStatus(HttpStatus.OK)
  public void updateBaixaContaReceber(@PathVariable Integer tituloBaixaId,
    @Valid @RequestBody TituloBaixaDto payload, @RequestHeader String empresa) {
    service.updateBaixaPagar(tituloBaixaId, payload);
    discoverer.handleUpdatedResource(tituloBaixaId);
  }

  @Operation(
    summary = "Permite buscar uma baixa de uma conta a Pagar",
    description = "Através do id da baixa é possível buscar uma baixa de uma conta a Pagar"
  )
  @GetMapping(value = "/conta-pagar/baixa/{id}")
  @ResponseStatus(HttpStatus.OK)
  public TituloBaixaResumoDto findBaixaContaPagar(@PathVariable Integer id,
    @RequestHeader String empresa) {
    return service.findBaixaById(id);
  }

  @Operation(
    summary = "Permite deletar os arquivos de um historico",
    description = "Permite deletar todos os arquivos de um historico"
  )
  @DeleteMapping(value = "/historicos/conta-receber/anexo/{idHistorico}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteAnexosHistorico(@PathVariable Integer idHistorico) {
    service.deleteAnexoHistorico(idHistorico);
  }

  @Operation(
    summary = "Permite deletar um arquivo de um historico",
    description = "Permite deletar um arquivo de um historico pelo historico id e anexo id"
  )
  @DeleteMapping(value = "/historicos/conta-receber/historico/{idHistorico}/anexo/{idAnexo}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteAnexoById(@PathVariable Integer idHistorico, @PathVariable Integer idAnexo) {
    service.deleteAnexoById(idHistorico, idAnexo);
  }

  @Operation(
    summary = "Importa as contas à pagar através da planilha",
    description = "Permite realizar a importação de contas a pagar através da planilha, incluindo o ID da empresa como parte da requisição."
  )
  @PostMapping(path = "/conta-pagar/importacao", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public void salvarPorArquivo(@ModelAttribute("file") @Valid @NotNull MultipartFile file) {
    ImportacaoContasDto importacaoContasDto = ImportacaoContasDto.builder()
      .empresaId(getEmpresa().getId())
      .file(file)
      .build();
    service.importContaPagar(importacaoContasDto);
  }

  @Operation(
    summary = "Permite buscar os títulos à pagar para o relatório",
    description = "Permite buscar os títulos à pagar para o relatório financeiro"
  )
  @GetMapping(path = "/conta-pagar/relatorio")
  @DataSource(DataSourceType.READ)
  public List<TituloRelatorioDto> findAllForReport(
    @ParameterObject @Valid TituloRelatorioFilter filter) {
    return service.findAllForReport(filter);
  }

  @Operation(
    summary = "Permite buscar Contas a Pagar para o Kanban",
    description = "É a busca de Contas a Pagar com filtro, para uma busca específica de resultados do Kanban"
  )
  @GetMapping(value = "/empresa/conta-pagar/busca", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public TitulosResumidosDtoOut findContasReceberResumo(
    @ParameterObject @Valid TituloFilter filter,
    @ParameterObject Pageable pageable
  ) {
    TitulosResumidosDtoOut result = service.findContaPagarKanban(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result.getPage());
    return result;
  }

  @Operation(
    summary = "Permite buscar Contas a Pagar para o Extrato e Conciliação",
    description = "É a busca de Contas a Pagar com filtro, para uma busca específica de resultados do Extrato e Conciliação"
  )
  @GetMapping(value = "/empresa/conta-pagar/busca-extrato-conciliacao", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public TitulosResumidosDtoOut findContasReceberResumoExtratoConciliacao(
    @ParameterObject @Valid TituloFilter filter,
    @ParameterObject Pageable pageable
  ) {
    TitulosResumidosDtoOut result = service.findContaPagarExtratoConciliacao(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result.getPage());
    return result;
  }
}
