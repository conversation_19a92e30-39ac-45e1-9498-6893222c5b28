package br.com.nuvy.facade.cadastro.cd3parceiros.model;

import br.com.nuvy.api.cadastro.dto.ContatoPessoaDto;
import br.com.nuvy.api.cadastro.dto.EnderecoDto;
import br.com.nuvy.api.cadastro.dto.QualificacaoPessoaDto;
import br.com.nuvy.api.cadastro.model.ContatoPessoa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.enums.TipoIE;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.common.utils.StringUtils;
import br.com.nuvy.common.validator.InscricaoEstadual;
import br.com.nuvy.common.validator.Numeric;
import br.com.nuvy.facade.cadastro.model.validation.ParceiroGroupSequenceProvider;
import br.com.nuvy.validation.groups.ClienteGroup;
import br.com.nuvy.validation.groups.CnpjTitularContaGroup;
import br.com.nuvy.validation.groups.ColaboradorGroup;
import br.com.nuvy.validation.groups.CpfTitularContaGroup;
import br.com.nuvy.validation.groups.DadosBancarioGroup;
import br.com.nuvy.validation.groups.FornecedorGroup;
import br.com.nuvy.validation.groups.PessoaFisicaGroup;
import br.com.nuvy.validation.groups.PessoaJuridicaGroup;
import br.com.nuvy.validation.groups.TransportadoraGroup;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.br.CNPJ;
import org.hibernate.validator.constraints.br.CPF;
import org.hibernate.validator.group.GroupSequenceProvider;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GroupSequenceProvider(ParceiroGroupSequenceProvider.class)
public class PessoaDto implements Dto<Pessoa> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;

  @NotNull(groups = {ClienteGroup.class, FornecedorGroup.class, TransportadoraGroup.class,
    ColaboradorGroup.class})
  private TipoPessoa tipo;

  @Size(min = 11, max = 14, groups = {PessoaFisicaGroup.class, PessoaJuridicaGroup.class})
  @CPF(groups = PessoaFisicaGroup.class)
  @CNPJ(groups = PessoaJuridicaGroup.class)
  private String cpfCnpj;

  @Size(max = 25)
  @InscricaoEstadual
  private String inscricaoEstadual;

  private TipoIE tipoIe;

  @Size(max = 25)
  private String inscricaoMunicipal;

  @Size(max = 255)
  private String nomeFantasia;

  @Size(max = 255)
  @NotNull(groups = {ClienteGroup.class, FornecedorGroup.class, TransportadoraGroup.class,
    ColaboradorGroup.class})
  private String nome;

  private String nomeInterno;

  @Size(max = 100)
  @Email
  @NotNull(groups = {TransportadoraGroup.class,
    ColaboradorGroup.class})
  private String emailcobranca;

  @Size(max = 100)
  @Email
  @NotNull(groups = {TransportadoraGroup.class,
    ColaboradorGroup.class})
  private String emailNotaFiscal;

  @Size(max = 150)
  private String site;

  @NotNull(groups = {TransportadoraGroup.class})
  @Size(max = 50)
  private String telefone;

  private String observacoesInternas;

  private String observacaoNotaFiscal;

  private Double limiteCredito;

  private Boolean incluirDadosBancarios = false;

  @Size(max = 150, groups = DadosBancarioGroup.class)
  @NotNull(groups = DadosBancarioGroup.class)
  private String nomeTitularConta;

  @CPF(groups = CpfTitularContaGroup.class)
  @CNPJ(groups = CnpjTitularContaGroup.class)
  private String cpfTitularConta;

  private String chavePixConta;

  @Size(max = 20, groups = DadosBancarioGroup.class)
  @NotNull(groups = DadosBancarioGroup.class)
  private String agenciaConta;

  @Size(max = 20, groups = DadosBancarioGroup.class)
  @NotNull(groups = DadosBancarioGroup.class)
  private String numeroConta;

  @NotNull(groups = DadosBancarioGroup.class)
  private Integer bancoId;

  @JsonProperty(access = Access.READ_ONLY)
  private String bancoCodigo;

  @JsonProperty(access = Access.READ_ONLY)
  private String bancoNome;

  @Builder.Default
  private Boolean contribuinteIcms = false;

  @Size(min = 8, max = 9)
  @Numeric
  private String suframa;

  private String funcao;
  private LocalDate dataNascimento;
  private SituacaoParceiro situacao;
  private String foto;
  private Boolean enderecoUnico;
  private Boolean importado;
  private Integer vendedorId;
  private String vendedorNome;
  private String vendedorCpfCnpj;
  private Integer regimeTributarioId;
  private String cnaeId;
  private String cnaeDescricao;
  private Integer segmentoId;
  private Integer condicaoPagamentoId;

  @Valid
  @NotNull(groups = {TransportadoraGroup.class,
    ColaboradorGroup.class})
  private List<EnderecoDto> enderecos;

  @Valid
  private List<ContatoPessoaDto> contatos;

  @NotEmpty
  private Set<TipoRelacionamento> relacaoComercial;

  private Set<QualificacaoPessoaDto> qualificacoes;

  @JsonIgnore
  private String email;

  @JsonProperty(access = Access.READ_ONLY)

  private Boolean existeEnderecoCadastrado = false;

  @Override
  public Pessoa toEntity() {
    Pessoa entity = ObjectUtils.convert(this, Pessoa.class);
    
    if (Objects.nonNull(this.getContatos())) {
      for (ContatoPessoa c : entity.getContatos()) {
        c.setPessoa(entity);
      }
    }
    if (entity.getTipo().equals(TipoPessoa.FISICA) && (Objects.isNull(entity.getTipoIe()))) {
      entity.setTipoIe(TipoIE.NAO_CONTRIBUINTE);
    }
    entity.setVendedor(Pessoa.of(this.getVendedorId()));
    return entity;
  }

  public static PessoaDto from(Pessoa pessoa) {
    var dto = ObjectUtils.convert(pessoa, PessoaDto.class);

    if (!pessoa.getEnderecos().isEmpty()) {
      dto.setExisteEnderecoCadastrado(true);
    }

    return dto;
  }

  public void setCpfCnpj(String cpfCnpj) {
    this.cpfCnpj = (cpfCnpj == null)?null:(cpfCnpj.isEmpty()) ? null : cpfCnpj;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    this.inscricaoEstadual = StringUtils.limparInscricaoEstadual(inscricaoEstadual);
  }

}
