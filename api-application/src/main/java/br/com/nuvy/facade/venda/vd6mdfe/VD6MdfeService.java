package br.com.nuvy.facade.venda.vd6mdfe;

import br.com.nuvy.api.venda.filter.PedidoMdfeFilter;
import br.com.nuvy.api.venda.service.PedidoService;
import br.com.nuvy.facade.venda.vd6mdfe.model.PedidoMdfeDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class VD6MdfeService {

  private final PedidoService pedidoService;

  @Transactional(readOnly = true)
  public Page<PedidoMdfeDto> findPedidosMdfe(
    PedidoMdfeFilter filter, Pageable pageable
  ) {
    return pedidoService.findPedidosMdfe(filter, pageable);
  }

}
