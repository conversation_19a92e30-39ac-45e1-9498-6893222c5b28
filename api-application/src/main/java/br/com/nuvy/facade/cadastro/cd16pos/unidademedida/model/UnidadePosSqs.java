package br.com.nuvy.facade.cadastro.cd16pos.unidademedida.model;

import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class UnidadePosSqs {

  private List<UnitTypes> unidades;
  private Integer empresaId;
  private String token;
  private String chaveApi;
  private LocalDateTime dataToken;


  @Getter
  @Setter
  public static class UnitTypes {
    private String id;
    private String name;
  }
}
