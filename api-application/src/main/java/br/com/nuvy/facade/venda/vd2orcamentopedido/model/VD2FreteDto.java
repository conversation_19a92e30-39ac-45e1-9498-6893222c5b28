package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.cadastro.model.TipoCarroceria;
import br.com.nuvy.api.cadastro.model.TipoFrete;
import br.com.nuvy.api.cadastro.model.TipoVeiculo;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VD2FreteDto {

  private List<TipoFrete> tiposFrete;
  private List<TipoVeiculo> tiposVeiculo;
  private List<TipoCarroceria> tiposCarroceria;

}
