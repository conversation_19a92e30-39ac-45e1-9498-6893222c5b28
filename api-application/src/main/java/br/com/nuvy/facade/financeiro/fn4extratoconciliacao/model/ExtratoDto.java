package br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model;

import br.com.nuvy.api.enums.SituacaoMovimentacaoBancaria;
import br.com.nuvy.api.enums.TipoExtratoTitulo;
import br.com.nuvy.api.financeiro.dto.TituloDespesaDto;
import br.com.nuvy.api.financeiro.model.MovimentacaoBancaria;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtratoDto implements Dto<MovimentacaoBancaria> {

  private Integer id;
  private Integer transacaoId;
  @JsonProperty(value = "bancoId")
  private Integer contaBancariaBancoId;
  @JsonProperty(value = "bancoCodigo")
  private String contaBancariaBancoCodigo;
  @JsonProperty(value = "bancoNome")
  private String contaBancariaBancoNome;
  @JsonProperty(value = "parceiroId")
  private Integer tituloFornecedorId;
  @JsonProperty(value = "parceiroNome")
  private String tituloFornecedorNome;
  @JsonProperty(value = "parceiroCpfCnpj")
  private String tituloFornecedorCpfCnpj;
  private BigDecimal valorBaixa;
  private TipoExtratoTitulo tipo;
  private LocalDate dataBaixa;
  @JsonProperty(value = "vencimento")
  private LocalDate tituloDataVencimento;
  private SituacaoMovimentacaoBancaria status;
  @JsonProperty(value = "tipoDespesaReceita")
  private List<TituloDespesaDto> tituloDespesas;

  public static ExtratoDto from(MovimentacaoBancaria entidade) {
    return ObjectUtils.convert(entidade, ExtratoDto.class);
  }

}
