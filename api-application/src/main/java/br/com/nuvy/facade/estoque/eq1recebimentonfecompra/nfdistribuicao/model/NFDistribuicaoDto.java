package br.com.nuvy.facade.estoque.eq1recebimentonfecompra.nfdistribuicao.model;

import br.com.nuvy.api.enums.TipoAmbienteEmissaoNota;
import br.com.nuvy.api.estoque.model.NFDistribuicao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
public class NFDistribuicaoDto implements Dto<NFDistribuicao> {

  private UUID id;
  private Long numeroNota;
  private String chave;
  private LocalDateTime dataEmissao;
  private BigDecimal valorTotal;
  private String xmlNotaFiscal;
  private String emitente;

  private Integer empresaId;
  private String empresaNome;
  private String cnpj;
  private Integer tipoOperacao;
  private LocalDateTime dataAutorizacao;
  private String protocolo;
  private String situacao;
  private TipoAmbienteEmissaoNota ambiente;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  public static NFDistribuicaoDto from(NFDistribuicao entity) {
    return ObjectUtils.convert(entity, NFDistribuicaoDto.class);
  }

}
