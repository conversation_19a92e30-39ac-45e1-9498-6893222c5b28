package br.com.nuvy.facade.configuracao.cf2usuario.model;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;

import br.com.nuvy.api.enums.SituacaoRecebedorNfe;
import br.com.nuvy.api.enums.SituacaoUsuario;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UsuarioDto {

  @Size(max = 255)
  @NotNull
  private String nome;
  @Size(max = 255)
  @NotNull
  private String email;
  @NotEmpty
  private List<Integer> perfilIds;
  private LocalDate dataNascimento;
  private SituacaoRecebedorNfe situacaoRecebedorNfe;
  private Integer setorId;
  @JsonProperty(access = READ_ONLY)
  private SituacaoUsuario situacao;

}