package br.com.nuvy.facade.anuncio.an2anunciomarketplace;

import br.com.nuvy.api.anuncio.filter.AnuncioFilter;
import br.com.nuvy.api.anuncio.filter.HistoricoHubFilter;
import br.com.nuvy.api.cadastro.dto.ProdutoResumoDto;
import br.com.nuvy.api.cadastro.filter.ProdutoDepositoFilter;
import br.com.nuvy.api.enums.AcaoHistoricoCatalogo;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.integracaohub.dto.AnnouncementDto;
import br.com.nuvy.api.integracaohub.enums.TipoSincronizacaoHub;
import br.com.nuvy.api.venda.filter.CanalVendaFilter;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CanalVendaDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.IntegracaoDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.AnuncioOutDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.CategoriaAnuncioDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.Fn2Dto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.HistoricoHubOutDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.ProdutoBuscaHubDtoOut;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.SincronizaProdutoHubDtoIn;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.SincronizaProdutoHubDtoOut;
import br.com.nuvy.facade.cadastro.cd15categoriaprodutos.model.CategoriaProdutoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "@Anúncio / AN2 - Anúncio Marketplace")
@RestController
@RequestMapping(value = "/anuncio/an2", headers = "empresa")
@RequiredArgsConstructor
public class AN2AnuncioMarketplaceController {

  private final AN2AnuncioMarketplaceService service;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Inicialização do cadastro de marketplace e anúncios",
    description = "Inicialização do cadastro de e-commerce no sistema, retornando as informações e listagens necessários para o cadastro de anúncios"
  )
  @GetMapping("/iniciar")
  public Fn2Dto iniciar() {
    return service.iniciar();
  }

  @Operation(
    summary = "Listar Canais de Venda de Marketplaces e E-commerces",
    description = "Permite listar Canais de Venda de Marketplaces e E-commerces no sistema paginados e resumidos"
  )
  @GetMapping("/canal-venda")
  @ResponseStatus(HttpStatus.OK)
  public List<CanalVendaDto> findCanaisMarketplace(@ParameterObject CanalVendaFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findCanaisMarketplace(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Buscar pelo ID um Canal de Venda de Marketplace ou E-commerce",
    description = "Permite buscar um Marketplace ou E-commerce pelo seu Identificador Único no sistema"
  )
  @GetMapping("/canal-venda/{id}")
  @ResponseStatus(HttpStatus.OK)
  public CanalVendaDto findCanalMarketplaceById(@PathVariable Integer id) {
    return service.findCanalMarketplaceById(id);
  }

  @Operation(
    summary = "Cadastrar um Canal de Venda de Marketplace ou E-commerce",
    description = "Permite cadastrar um Canal de Venda de Marketplace ou E-commerce no sistema"
  )
  @PostMapping("/canal-venda")
  @ResponseStatus(HttpStatus.CREATED)
  public void createCanalMarketplace(@RequestBody @Valid CanalVendaDto canalVendaDto) {
    var result = service.createCanalMarketplace(canalVendaDto);
    discoverer.handleCreatedResource(result);
  }

  @Operation(
    summary = "Atualizar Canal de Venda de Marketplace ou E-commerce",
    description = "Permite atualizar um Canal de Venda de Marketplace ou E-commerce no sistema, passando o corpo com as alterações"
  )
  @PutMapping("/canal-venda/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateCanalMarketplace(@PathVariable Integer id,
    @RequestBody @Valid CanalVendaDto canalVendaDto) {
    service.updateCanalMarketplace(id, canalVendaDto);
  }

  @Operation(
    summary = "Alterar situacao de um Canal de Venda de Marketplace ou E-commerce",
    description = "Permite alterar a situacao de um Canal de Venda de Marketplace ou E-commerce no sistema, passando a nova situacao"
  )
  @PatchMapping("/canal-venda/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateSituacaoCanalMarketplace(@PathVariable Integer id,
    @RequestParam Situacao situacao) {
    service.updateSituacaoCanalMarketplace(id, situacao);
  }

  @Operation(
    summary = "Permite listar os vínculos de categorias do ERP com um Canal de Venda",
    description = "Permite listar os vínculos de categorias do ERP com um Canal de Venda no sistema"
  )
  @GetMapping("/canal-venda/{id}/categoria")
  @ResponseStatus(HttpStatus.OK)
  public List<CategoriaAnuncioDto> findCategoriasVinculadas(@PathVariable Integer id) {
    return service.findCategoriasByCanal(id);
  }

  @Operation(
    summary = "Permite buscar um vinculo de categoria do ERP pelo ID",
    description = "Permite buscar um vinculo de categoria de anúncio pelo seu Identificador Único no sistema"
  )
  @GetMapping("/canal-venda/categoria/{idCategoriaErp}")
  @ResponseStatus(HttpStatus.OK)
  public CategoriaAnuncioDto findCategoriaVinculadaById(@PathVariable Integer idCategoriaErp) {
    return service.findCategoriaById(idCategoriaErp);
  }

  @Operation(
    summary = "Permite cadastrar um vinculo entre categoria do ERP com Marketplace ou E-commerce",
    description = "Permite cadastrar um vinculo entre categoria do ERP com Marketplace ou E-commerce no sistema"
  )
  @PostMapping("/canal-venda/categoria")
  @ResponseStatus(HttpStatus.CREATED)
  public void createVinculoCategoria(@RequestBody @Valid CategoriaAnuncioDto dto) {
    var result = service.createVinculoCategoria(dto);
    discoverer.handleCreatedResource(result);
  }

  @Operation(
    summary = "Permite atualizar um vinculo entre categoria do ERP com Marketplace ou E-commerce",
    description = "Permite atualizar um vinculo entre categoria do ERP com Marketplace ou E-commerce no sistema"
  )
  @PutMapping("/canal-venda/categoria/{idVinculoCategoria}")
  @ResponseStatus(HttpStatus.OK)
  public void updateVinculoCategoria(@PathVariable Integer idVinculoCategoria,
    @RequestBody @Valid CategoriaAnuncioDto dto) {
    service.updateVinculoCategoria(idVinculoCategoria, dto);
  }

  @Operation(
    summary = "Permite excluir um vinculo de categoria do ERP com Marketplace ou E-commerce",
    description = "Permite excluir um vinculo de categoria do ERP com Marketplace ou E-commerce no sistema"
  )
  @DeleteMapping("/canal-venda/categoria/{idCategoriaVinculo}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteVinculoCategoria(@PathVariable Integer idCategoriaVinculo) {
    service.deleteVinculoCategoria(idCategoriaVinculo);
  }

  @Operation(
    summary = "Permite listar as categorias de produtos do ERP",
    description = "Permite listar as categorias de produtos do ERP no sistema"
  )
  @GetMapping("/categoria-produto")
  @ResponseStatus(HttpStatus.OK)
  public List<CategoriaProdutoDto> findCategoriasProduto(@ParameterObject NoFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findCategoriasErp(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite buscar uma categoria de produto do ERP pelo ID",
    description = "Permite buscar uma categoria de produto do ERP pelo seu Identificador Único no sistema"
  )
  @GetMapping("/categoria-produto/{id}")
  @ResponseStatus(HttpStatus.OK)
  public CategoriaProdutoDto findCategoriaProdutoById(@PathVariable Integer id) {
    return service.findCategoriaErpById(id);
  }

  @Operation(
    summary = "Lista as integrações do HUB (Marketplaces e E-commerces)",
    description = "Permite realizar a listagem de integrações para vincular com um Canal de Venda no sistema"
  )
  @GetMapping("/integracao")
  @ResponseStatus(HttpStatus.OK)
  public List<IntegracaoDto> findIntegracoes() {
    return service.findIntegracoes();
  }

  @Operation(
    summary = "Busca uma integração de API (Marketplace ou E-commerce) pelo ID",
    description = "Permite buscar uma integração (Marketplace ou E-commerce) pelo seu Identificador Único no sistema"
  )
  @GetMapping("/integracao/{id}")
  @ResponseStatus(HttpStatus.OK)
  public IntegracaoDto findIntegracaoById(@PathVariable Integer id) {
    return service.findIntegracaoById(id);
  }

  @Operation(
    summary = "Cria uma integração de API (Marketplace ou E-commerce)",
    description = "Permite criar uma integração (Marketplace ou E-commerce) no sistema"
  )
  @PostMapping("/integracao")
  @ResponseStatus(HttpStatus.CREATED)
  public void createIntegracao(@RequestBody @Valid IntegracaoDto dto) {
    var result = service.createIntegracao(dto);
    discoverer.handleCreatedResource(result);
  }

  @Operation(
    summary = "Atualiza uma integração de API (Marketplace ou E-commerce)",
    description = "Permite atualizar uma integração (Marketplace ou E-commerce) no sistema"
  )
  @PutMapping("/integracao/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateIntegracao(@PathVariable Integer id, @RequestBody @Valid IntegracaoDto dto) {
    service.updateIntegracao(id, dto);
  }

  @Operation(
    summary = "Exclui uma integração de API (Marketplace ou E-commerce)",
    description = "Permite excluir uma integração (Marketplace ou E-commerce) no sistema"
  )
  @DeleteMapping("/integracao/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteIntegracao(@PathVariable Integer id) {
    service.deleteIntegracao(id);
  }

  @Operation(
    summary = "Cria um anúncio no marketplace, ou e-commerce",
    description = "Permite criar um anúncio no marketplace, ou e-commerce, já vinculando o produto ao catálogo"
  )
  @PostMapping("/canal-venda/{id}/anuncio")
  @ResponseStatus(HttpStatus.CREATED)
  public void createAnuncio(@PathVariable("id") Integer canalVendaId,
    @RequestBody @Valid AnnouncementDto dto) {
    discoverer.handleCreatedResource(service.createAnuncio(canalVendaId, dto));
  }

  @Operation(
    summary = "Remover um anúncio no marketplace, ou e-commerce",
    description = "Permite remover um anúncio no marketplace, ou e-commerce, removendo o produto do catálogo"
  )
  @DeleteMapping("/canal-venda/{id}/anuncio")
  @ResponseStatus(HttpStatus.OK)
  public void deleteAnuncio(@PathVariable("id") Integer canalVendaId,
    @RequestBody @Valid AnnouncementDto dto) {
    service.removeAnuncio(canalVendaId, dto);
  }

  @Operation(
    summary = "Buscar pelo ID um Canal de Venda os produtos vinculados",
    description = "Permite buscar os produtos relacionados a um canal de venda"
  )
  @GetMapping("/canal-venda/{id}/produto")
  @ResponseStatus(HttpStatus.OK)
  public List<ProdutoResumoDto> findAllProdutoCanalVendaById(@PathVariable Integer id,
    @ParameterObject ProdutoDepositoFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.buscarProdutosCanalDeVenda(id, filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Buscar os anúncios gravados no erp pelo canal de venda",
    description = "Permite buscar os anúncios gravados no erp pelo canal de venda"
  )
  @GetMapping("/canal-venda/{canalId}/anuncios-erp")
  @ResponseStatus(HttpStatus.OK)
  public List<AnuncioOutDto> findAnunciosErpByCanalVenda(@PathVariable Integer canalId,
    @ParameterObject Pageable pageable, @ParameterObject AnuncioFilter filter) {
    var result = service.findAnunciosErpByCanalVenda(canalId, pageable, filter);
    return discoverer.handlePaginatedResults(pageable, result);
  }

  @Operation(
    summary = "Buscar o histórico de um anúncio",
    description = "Permite buscar os históricos de um anúncio pelo id do anúncio"
  )
  @GetMapping("/{idAnuncio}/historico")
  @ResponseStatus(HttpStatus.OK)
  public List<HistoricoHubOutDto> findHistoricoAnuncio(@PathVariable UUID idAnuncio,
    @ParameterObject Pageable pageable, @ParameterObject HistoricoHubFilter filter) {
    var result = service.findHistoricoAnuncio(idAnuncio, pageable, filter);
    return discoverer.handlePaginatedResults(pageable, result);
  }

  @Operation(
    summary = "Buscar o histórico de um catálogo pelo canal de venda",
    description = "Permite buscar os históricos de um catálogo pelo canal de venda"
  )
  @GetMapping("/canal-venda/{canalId}/historico")
  @ResponseStatus(HttpStatus.OK)
  public List<HistoricoHubOutDto> findHistoricoCatalogo(@PathVariable Integer canalId,
    @ParameterObject Pageable pageable, @ParameterObject HistoricoHubFilter filter) {
    var result = service.findHistoricoCatalogoCanalVenda(canalId, pageable, filter);
    return discoverer.handlePaginatedResults(pageable, result);
  }

  @Operation(
    summary = "Permite sincronizar os produtos com o marketplace",
    description = "Permite sincronizar os produtos com o marketplace"
  )
  @PutMapping("/canal-venda/{id}/sincroniza")
  @ResponseStatus(HttpStatus.OK)
  public void sincronizaHub(
    @PathVariable("id") Integer canalVendaId,
    @RequestParam TipoSincronizacaoHub tipo
  ) {
    service.sincronizaHub(canalVendaId, tipo);
  }

  @Operation(
    summary = "Informa se houve edições no anúncio pelo id do anúncio",
    description = "Permite informar se houve edições no anúncio, ativação ou desativação para gerar o histórico"
  )
  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateAnuncio(@PathVariable UUID id, @RequestParam AcaoHistoricoCatalogo acao) {
    service.editarAnuncio(id, acao);
  }

  @Operation(
    summary = "Permite buscar o saldo de apenas um produto em um depósito",
    description = "Permite buscar o saldo de apenas um produto em um depósito"
  )
  @GetMapping("/produto/saldo-por-deposito")
  @ResponseStatus(HttpStatus.OK)
  public BigDecimal buscarSaldoProdutoDeposito(@RequestParam Integer produtoId, @RequestParam Integer depositoId) {
    return service.buscarSaldoProdutoDeposito(produtoId, depositoId);
  }

  @Operation(
    summary = "Buscar produtos do marketplace",
    description = "Permite buscar produtos disponíveis no marketplace para importação"
  )
  @GetMapping("/canal-venda/{id}/produtos-hub")
  @ResponseStatus(HttpStatus.OK)
  public List<ProdutoBuscaHubDtoOut> findProdutosHub(
    @PathVariable("id") Integer canalVendaId,
    @ParameterObject Pageable pageable) {
    var result = service.buscarProdutosHub(canalVendaId, pageable);
    return discoverer.handlePaginatedResults(pageable, result);
  }

  @Operation(
    summary = "Criar produto via importação de canal de venda",
    description = "Cria um novo produto de canal de venda"
  )
  @PostMapping(value = "/canal-venda/{id}/sincronizar-produtos-hub", headers = "empresa")
  @ResponseStatus(HttpStatus.CREATED)
  public void sincronizaHubProduto(
    @PathVariable("id") Integer canalVendaId,
    @RequestBody @Valid SincronizaProdutoHubDtoIn dto) {
    service.sincronizaHubProduto(canalVendaId, dto);
  }
}
