package br.com.nuvy.facade.home.dto;

import br.com.nuvy.api.cadastro.dto.EmpresaDto;
import br.com.nuvy.api.seguranca.dto.ModuloDto;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.facade.home.model.Acesso;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcessoDto implements Dto<Acesso> {

  private Integer id;
  private Integer contador;
  private ModuloDto modulo;
  private EmpresaDto empresa;
}