package br.com.nuvy.facade.cadastro.model.validation;

import br.com.nuvy.facade.cadastro.cd3parceiros.model.EnderecoDto;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EnderecoGroupSequenceProvider implements DefaultGroupSequenceProvider<EnderecoDto> {

  @Override
  public List<Class<?>> getValidationGroups(EnderecoDto dto) {
    List<Class<?>> groups = new ArrayList<>();
    groups.add(EnderecoDto.class);
    if (Objects.nonNull(dto) && Objects.nonNull(dto.getTipo())) {
      groups.add(dto.getTipo().getGroup());
    }
    return groups;
  }
}
