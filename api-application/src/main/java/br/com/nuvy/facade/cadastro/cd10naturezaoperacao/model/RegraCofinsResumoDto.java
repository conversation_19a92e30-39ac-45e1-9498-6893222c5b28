package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import br.com.nuvy.api.cadastro.model.nop.RegraCofinsPadrao;
import br.com.nuvy.api.cadastro.model.nop.RegraCofinsExcecao;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegraCofinsResumoDto implements Dto<RegraCofinsPadrao> {

  private Integer id;
  private String nome;
  private Situacao situacao;
  private BigDecimal aliquota;
  private List<RegraCofinsExcecaoResumoDto> excecoes;

  @Override
  public RegraCofinsPadrao toEntity() {
    RegraCofinsPadrao entity = ObjectUtils.convert(this, RegraCofinsPadrao.class);
    if (Objects.nonNull(this.getExcecoes())) {
      for (RegraCofinsExcecao r : entity.getExcecoes()) {
        r.setRegra(entity);
      }
    }
    return entity;
  }

  public static RegraCofinsResumoDto from(RegraCofinsPadrao entity) {
    return ObjectUtils.convert(entity, RegraCofinsResumoDto.class);
  }

}
