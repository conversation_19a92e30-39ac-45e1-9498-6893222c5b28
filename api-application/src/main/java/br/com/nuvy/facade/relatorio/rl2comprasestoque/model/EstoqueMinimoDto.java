package br.com.nuvy.facade.relatorio.rl2comprasestoque.model;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class EstoqueMinimoDto {
  private String empresaNome;
  private String depositoNome;
  private String produtoCodigo;
  private String produtoDescricao;
  private String produtoUnidadeMedida;
  private BigDecimal produtoSaldoAtual;
  private BigDecimal produtoSaldoMinimo;
  private BigDecimal estoqueMinimoXSaldoEstoque;
  private String produtoTipoMercadoria;
  private String produtoNcmCodigo;
  private String produtoNcmDescricao;
  private BigDecimal produtoPesoLiquido;
  private BigDecimal produtoPesoBruto;
  private String produtoLote;
  private String produtoQualificacao;
  private String produtoCategoria;
  private String produtoSituacao;
  private BigDecimal produtoValorCustoMedio;
}
