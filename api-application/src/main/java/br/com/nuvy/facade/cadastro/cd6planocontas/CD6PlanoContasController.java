package br.com.nuvy.facade.cadastro.cd6planocontas;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.financeiro.dto.PlanoContaDto;
import br.com.nuvy.api.financeiro.filter.PlanoContaFilter;
import br.com.nuvy.common.base.controller.ControllerBase;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "@Cadastro / CD6 - Plano de Contas")
@RestController
@RequestMapping("/cadastro/cd6")
@RequiredArgsConstructor
public class CD6PlanoContasController extends ControllerBase {

  private final CD6PlanoContasService service;

  @Operation(
    summary = "Permite listar todos os planos de contas",
    description = "Permite listar todos os planos de contas"
  )
  @GetMapping("/plano-contas")
  @ResponseStatus(HttpStatus.OK)
  public List<PlanoContaDto> listarPlanoConta(@ParameterObject PlanoContaFilter filter) {
    return service.findPlanoContas(filter);
  }

  @Operation(
    summary = "Permite buscar um plano de contas pelo id",
    description = "Permite buscar um plano de contas pelo id"
  )
  @GetMapping("/plano-contas/{id}")
  @ResponseStatus(HttpStatus.OK)
  public PlanoContaDto buscarPlanoConta(@PathVariable Integer id) {
    return service.findPlanoContasById(id);
  }

  @Operation(
    summary = "Permite salvar um plano de contas",
    description = "Permite salvar um plano de contas"
  )
  @PostMapping("/plano-contas")
  @ResponseStatus(HttpStatus.CREATED)
  public void savePlanoContas(@RequestBody @Valid PlanoContaDto planoContaDto) {
    var result = service.createPlanoContas(planoContaDto);
    super.fireResourceCreatedEvent(result);
  }

  @Operation(
    summary = "Permite atualizar um plano de contas",
    description = "Permite atualizar um plano de contas"
  )
  @PutMapping("/plano-contas/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updatePlanoContas(@PathVariable Integer id,
    @RequestBody @Valid PlanoContaDto planoContaDto) {
    var result = service.updatePlanoContas(id, planoContaDto);
    super.fireResourceUpdatedEvent(result);
  }

  @Operation(
    summary = "Permite realizar o patch em um plano de contas",
    description = "Permite realizar o patch em um plano de contas"
  )
  @PatchMapping("/plano-contas/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void patchPlanoContas(@PathVariable Integer id,
    @RequestBody @Valid PlanoContaDto planoContaDto) {
    service.patchPlanoContas(id, planoContaDto);
  }

  @Operation(
    summary = "Permite verificar se um plano de contas existe",
    description = "Permite verificar se um plano de contas existe"
  )
  @RequestMapping(value = "/plano-contas/{id}/exists", method = RequestMethod.HEAD)
  @ResponseStatus(HttpStatus.OK)
  public void exists(@PathVariable Integer id) {
    if (!service.exists(id)) {
      throw new ResourceNotFoundException();
    }
  }

  @Operation(
    summary = "Permite deletar um plano de contas",
    description = "Permite deletar um plano de contas"
  )
  @DeleteMapping("/plano-contas/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deletePlanoContas(@PathVariable Integer id) {
    service.deletePlanoContas(id);
  }

  @PatchMapping(value = "/plano-contas/alterar-situacao/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void alterarSituacaoPlanoContas(
    @PathVariable Integer id,
    @RequestBody Situacao situacao) {
    service.alterarSituacaoPlanoContas(id, situacao);
  }
}
