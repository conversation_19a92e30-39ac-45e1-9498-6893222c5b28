package br.com.nuvy.facade.venda.vd2orcamentopedido.model.hub;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NuvyHubResponseDto {

  private NuvyHubIdentityDto identity;
  private boolean hasBusinessError;
  @JsonProperty("isValid")
  private boolean isValid;
  @JsonProperty("isLocked")
  private boolean isLocked;
}