package br.com.nuvy.facade.venda.validation;

import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoItemDto;
import br.com.nuvy.validation.groups.UnidadeTributavelGroup;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PedidoItemGroupSequenceProvider implements DefaultGroupSequenceProvider<PedidoItemDto> {

  @Override
  public List<Class<?>> getValidationGroups(PedidoItemDto pedidoItemDto) {
    List<Class<?>> groups = new ArrayList<>();
    if (Objects.nonNull(pedidoItemDto) && Objects.nonNull(pedidoItemDto.getInformarUnidadeQuantidadeTributavel())) {
      if (pedidoItemDto.getInformarUnidadeQuantidadeTributavel().equals(true)) {
        groups.add(UnidadeTributavelGroup.class);
      }
    }
    groups.add(PedidoItemDto.class);
    return groups;
  }
}
