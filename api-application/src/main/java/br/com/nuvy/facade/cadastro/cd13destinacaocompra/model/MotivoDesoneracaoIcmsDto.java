package br.com.nuvy.facade.cadastro.cd13destinacaocompra.model;

import br.com.nuvy.api.enums.MotivoDesoneracaoIcms;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MotivoDesoneracaoIcmsDto {

  private String id;
  private String descricao;

  public static MotivoDesoneracaoIcmsDto of(MotivoDesoneracaoIcms entity) {
    return new MotivoDesoneracaoIcmsDto(entity.getId(), entity.getDescricao());
  }
}
