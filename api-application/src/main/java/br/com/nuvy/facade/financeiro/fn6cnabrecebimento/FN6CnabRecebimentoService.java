package br.com.nuvy.facade.financeiro.fn6cnabrecebimento;

import br.com.nuvy.api.cadastro.service.FileService;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.api.financeiro.dto.TituloDto;
import br.com.nuvy.api.financeiro.dto.TituloDtoOut;
import br.com.nuvy.api.financeiro.filter.CnabFilter;
import br.com.nuvy.api.financeiro.filter.CnabTituloFilter;
import br.com.nuvy.api.financeiro.model.Cnab;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.api.financeiro.service.CnabService;
import br.com.nuvy.api.financeiro.service.TituloService;
import br.com.nuvy.client.dto.CnabDto;
import br.com.nuvy.client.dto.CriarCNABRetornoResponseDTO;
import br.com.nuvy.client.enums.TipoCnab;
import br.com.nuvy.client.interfaces.DocumentosClient;
import br.com.nuvy.client.services.BoletoService;
import br.com.nuvy.client.services.RelatorioCnabService;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.financeiro.fn2contasreceber.FN2ContasReceberService;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.ListarRetornoDto;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.GerarRemessaDtoIn;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.GerarRemessaDtoOut;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.GerarRetornoDtoOut;
import br.com.nuvy.facade.financeiro.fn2contasreceber.model.ListarRemessaDto;
import br.com.nuvy.facade.financeiro.fn6cnabrecebimento.model.FN6CnabDto;
import br.com.nuvy.facade.financeiro.fn6cnabrecebimento.model.FN6CnabResumeDto;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
@RequiredArgsConstructor
public class FN6CnabRecebimentoService {

  private final DocumentosClient documentosClient;
  private final CnabService cnabService;
  private final Badge badge;
  private final FileService fileService;
  private final TituloService service;
  private final FN2ContasReceberService contasReceberService;
  private final BoletoService boletoService;
  private final RelatorioCnabService relatorioCnabService;

  public String salvaCnabTxt(CnabDto payload) {
    return documentosClient.salvaCnabTxt(payload);
  }

  public InputStream arquivoCnab(Integer id) {
    Cnab cnab = cnabService.findById(id).orElseThrow(ResourceNotFoundException::new);
    String nomeArquivo = cnab.getArquivo();
    String caminho =
      badge.getTenantId() + "/" + badge.getEmpresa().getId().toString() + "/cnab/" + nomeArquivo;
    return fileService.read(caminho);
  }

  public FN6CnabResumeDto iniciar(CnabTituloFilter cnabTituloFilter, Pageable pageable) {
    cnabTituloFilter.setTipoTitulo(TipoTitulo.RECEBER);
    Page<Titulo> allCnab = service.findAllCnab(cnabTituloFilter, pageable);
    if (CollectionUtils.isEmpty(allCnab.getContent())) {
      return null;
    }
    List<FN6CnabDto> fn6CnabDtos = allCnab.stream().map(FN6CnabDto::from).toList();
    BigDecimal valorTotal = fn6CnabDtos.stream().map(FN6CnabDto::getValorTotal)
      .reduce(BigDecimal.ZERO, BigDecimal::add);

    return FN6CnabResumeDto.builder()
      .dados(fn6CnabDtos)
      .valorTotal(valorTotal)
      .page(allCnab)
      .build();
  }

  public void cancelarRemessaCnab(Integer id) {
    cnabService.cancelarRemessaCnab(id);
  }

  public TituloDtoOut findCnab(Integer id) {
    return contasReceberService.findContaReceberById(id);
  }

  public TituloDto updateContaReceberCnab(Integer id, TituloDto tituloDto) {
    return contasReceberService.updateContaReceber(id, tituloDto);
  }

  public Page<Cnab> listCnab(CnabFilter cnabFilter, Pageable pageable) {
    return cnabService.find(cnabFilter, pageable);
  }

  public void deleteContaReceberCnab(Integer id) {
    contasReceberService.deleteContaReceber(id);
  }

  public List<GerarRemessaDtoOut> gerarRemessa(GerarRemessaDtoIn payload) {
    return boletoService.gerarRemessa(payload);
  }

  public List<ListarRemessaDto> listarRemessa(Integer bankBilletAccountId) {
    return boletoService.listarRemessa(bankBilletAccountId);
  }

  public List<ListarRetornoDto> listarRetorno(Integer bankBilletAccountId) {
    return boletoService.listarRetorno(bankBilletAccountId);
  }

  public List<GerarRemessaDtoOut> consultarInformacaoRemessa(Integer remessaId) {
    return boletoService.consultarInformacaoRemessa(remessaId);
  }

  public List<GerarRetornoDtoOut> consultarInformacaoRetorno(Integer retornoId) {
    return boletoService.consultarInformacaoRetorno(retornoId);
  }

  public ResponseEntity<Void> excluirRemessa(Integer remessaId) {
    return boletoService.excluirRemessa(remessaId);
  }

  public String geraDownloadArquivoRemessa(Integer remessaId) {
    return boletoService.geraArquivoDownloadRemessa(remessaId);
  }

  public ResponseEntity<byte[]> geraDownloadArquivoRetorno(Integer retornoId) {
    return boletoService.geraArquivoDownloadRetorno(retornoId);
  }

  public ResponseEntity<InputStreamResource> relatorioRemessa(Integer remessaId) {
    return relatorioCnabService.getRelatorioRemessaCnab(remessaId, TipoCnab.RECEBIMENTO);
  }

  public ResponseEntity<InputStreamResource> relatorioRetorno(Integer remessaId) {
    return relatorioCnabService.getRelatorioRetornoCnab(remessaId, TipoCnab.RECEBIMENTO);
  }

  public CriarCNABRetornoResponseDTO uploadArquivoCNABRetornoHomologacao(MultipartFile file, Integer contaCorrenteId) {
    return boletoService.uploadArquivoRetorno(file, contaCorrenteId);
  }

}
