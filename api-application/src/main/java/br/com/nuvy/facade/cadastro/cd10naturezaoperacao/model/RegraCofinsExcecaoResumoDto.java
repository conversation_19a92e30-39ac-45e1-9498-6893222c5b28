package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import br.com.nuvy.api.cadastro.model.nop.RegraCofinsExcecao;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoRegraExcecao;
import br.com.nuvy.common.base.dto.Dto;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegraCofinsExcecaoResumoDto implements Dto<RegraCofinsExcecao> {

  private Integer id;
  private Situacao situacao;
  private TipoRegraExcecao tipoExcecao;
  private BigDecimal aliquota;
  private Integer origemMercadoriaId;
  private String origemMercadoriaDescricao;
  private String origemMercadoriaNome;
  private String situacaoTributariaCodigo;
  private String situacaoTributariaDescricao;


  public Dto<RegraCofinsExcecao> fromEntity(RegraCofinsExcecao entity) {
    return Dto.super.fromEntity(entity);
  }
}
