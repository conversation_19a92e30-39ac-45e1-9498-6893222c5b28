package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoNfeNop;
import br.com.nuvy.api.enums.TipoNop;
import br.com.nuvy.common.base.dto.Dto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NopDtoResumo implements Dto<Nop> {

  private Integer id;
  private String nome;
  private Situacao situacao;
  private Boolean geraContasReceber;
  private Boolean geraBaixaEstoque;
  private Boolean comprado;
  private Boolean fabricado;
  private Integer regimeTributarioId;
  private String regimeTributarioNome;
  private Integer tipoReceitaId;
  private String tipoReceitaNome;
  private TipoNop tipoNop;
  private TipoNfeNop tipoNfe;
  private String observacaoNotaFiscal;
  private List<EmpresaDto> empresas;
}
