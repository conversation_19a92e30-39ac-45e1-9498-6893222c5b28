package br.com.nuvy.facade.estoque.eq6pedidocompra;

import br.com.nuvy.api.estoque.filter.SolicitacaoCompraFilter;
import br.com.nuvy.api.estoque.service.PedidoCompraService;
import br.com.nuvy.api.estoque.service.SolicitacaoCompraService;
import br.com.nuvy.api.pedidocompra.model.PedidoCompra;
import br.com.nuvy.api.pedidocompra.model.SituacaoSolicitacaoCompra;
import br.com.nuvy.api.pedidocompra.model.SolicitacaoCompra;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.api.seguranca.service.UsuarioService;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.MapeadorSolicitacaoCompra;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.RejeitarSolicitacaoCompraDto;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.SolicitacaoCompraDto;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.SolicitacaoCompraItemDto;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.SolicitacaoCompraOutDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class EQ6SolicitacaoCompraService {

  private final SolicitacaoCompraService solicitacaoCompraService;
  private final PedidoCompraService pedidoCompraService;
  private final UsuarioService usuarioService;
  private final MapeadorSolicitacaoCompra mapeadorSolicitacaoCompra;

  public SolicitacaoCompraOutDto findSolicitacoesCompra(SolicitacaoCompraFilter filter,
    Pageable pageable) {
    return solicitacaoCompraService.findSolicitacoesCompra(filter, pageable);
  }

  @Transactional(readOnly = true)
  public SolicitacaoCompraDto findSolicitacaoCompraById(Long id) {
    SolicitacaoCompraDto solicitacaoCompraDto = solicitacaoCompraService.findById(id)
      .map(c -> mapeadorSolicitacaoCompra.paraDto(c))
      .orElseThrow(ResourceNotFoundException::new);

    return solicitacaoCompraDto;
  }

  @Transactional
  public SolicitacaoCompra create(SolicitacaoCompraDto payload) {
    SolicitacaoCompra solicitacaoCompra = mapeadorSolicitacaoCompra.paraEntidade(payload);
    solicitacaoCompra = solicitacaoCompraService.create(solicitacaoCompra);

    return solicitacaoCompra;
  }

  @Transactional
  public void updateSolicitacaoCompra(Long id, SolicitacaoCompraDto solicitacaoCompraDto) {
    log.debug("Atualizando solicitação compra: {}", id);
    SolicitacaoCompra solicitacaoCompra = solicitacaoCompraService.required(id,
      "solicitacao.compra.nao.encontrado");
    mapeadorSolicitacaoCompra.popularEntidade(solicitacaoCompraDto, solicitacaoCompra);
    solicitacaoCompraService.update(id, solicitacaoCompra);
  }

  @Transactional
  public void deleteSolicitacaoCompra(Long id) {
    solicitacaoCompraService.delete(id);
  }

  @Transactional(readOnly = true)
  public List<SolicitacaoCompraItemDto> findItensSolicitacaoCompra(
    List<Long> solicitacoesCompraIds) {
    List<SolicitacaoCompraItemDto> itens = new ArrayList<>();
    solicitacoesCompraIds.forEach(id -> {
      SolicitacaoCompraDto solicitacaoCompraDto = solicitacaoCompraService.findById(id)
        .map(c -> mapeadorSolicitacaoCompra.paraDto(c))
        .orElseThrow(ResourceNotFoundException::new);

      itens.addAll(solicitacaoCompraDto.getItens());
    });

    return itens;
  }

  @Transactional
  public void updateAprovarSolicitacaoCompra(Long id) {
    log.debug("Aprovar solicitação de compra: {}", id);
    SolicitacaoCompra solicitacaoCompra = solicitacaoCompraService.required(id,
      "solicitacao.compra.nao.encontrado");
    solicitacaoCompra.setSituacao(SituacaoSolicitacaoCompra.APROVADA);
    solicitacaoCompraService.update(id, solicitacaoCompra);
  }

  @Transactional
  public void updateRejeitarSolicitacaoCompra(Long id,
    RejeitarSolicitacaoCompraDto rejeitarSolicitacaoCompraDto) {
    log.debug("Rejeitar solicitação de compra: {}", id);
    SolicitacaoCompra solicitacaoCompra = solicitacaoCompraService.required(id,
      "solicitacao.compra.nao.encontrado");

    Usuario usuarioRejeicao = usuarioService.required(
      rejeitarSolicitacaoCompraDto.getUsuarioRejeitacaoId(), "usuario.nao.encontrado");

    solicitacaoCompra.setUsuarioRejeicao(usuarioRejeicao);
    solicitacaoCompra.setObservacaoRejeicao(rejeitarSolicitacaoCompraDto.getMotivo());
    solicitacaoCompra.setSituacao(SituacaoSolicitacaoCompra.REJEITADA);

    solicitacaoCompraService.update(id, solicitacaoCompra);
  }

  @Transactional
  public PedidoCompra gerarPedidoCompra(List<Long> idsItensSolicitacaoCompra) {
    PedidoCompra pedidoCompra = pedidoCompraService.gerarPedidoCompra(idsItensSolicitacaoCompra);
    return pedidoCompra;
  }

}