package br.com.nuvy.facade.financeiro.fn2contasreceber.model;

import br.com.nuvy.api.financeiro.dto.TituloResumoDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Hidden;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Page;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TitulosDtoOut {

  private List<TituloResumoDto> tituloDto;
  private BigDecimal totalContas;
  @Hidden
  @JsonIgnore
  private Page<TituloResumoDto> page;
}
