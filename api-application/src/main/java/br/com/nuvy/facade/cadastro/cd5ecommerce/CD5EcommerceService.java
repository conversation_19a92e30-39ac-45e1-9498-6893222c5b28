package br.com.nuvy.facade.cadastro.cd5ecommerce;

import br.com.nuvy.api.cadastro.model.AtributoMarketplace;
import br.com.nuvy.api.cadastro.service.CategoriaProdutoService;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.FormaPagamentoService;
import br.com.nuvy.api.cadastro.service.NopService;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoPlanoConta;
import br.com.nuvy.api.estoque.dto.DepositoDto;
import br.com.nuvy.api.estoque.service.DepositoService;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoDto;
import br.com.nuvy.api.financeiro.dto.FormaPagamentoDto;
import br.com.nuvy.api.financeiro.dto.PlanoContaDto;
import br.com.nuvy.api.financeiro.service.ContaBancariaService;
import br.com.nuvy.api.financeiro.service.PlanoContaService;
import br.com.nuvy.api.venda.filter.CanalVendaFilter;
import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.api.venda.repository.AtributoMarketPlaceRepository;
import br.com.nuvy.api.venda.repository.CanalOrigemRepository;
import br.com.nuvy.api.venda.service.AtributoService;
import br.com.nuvy.api.venda.service.CanalVendaService;
import br.com.nuvy.api.venda.service.CategoriaAnuncioService;
import br.com.nuvy.api.venda.service.TabelaPrecoService;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CanalVendaDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.EmpresaResumoDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.CategoriaAnuncioDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.Fn2Dto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.NopResumoDto;
import br.com.nuvy.facade.cadastro.cd15categoriaprodutos.model.CategoriaProdutoDto;
import br.com.nuvy.facade.cadastro.cd5ecommerce.model.AtributoDto;
import br.com.nuvy.facade.cadastro.cd5ecommerce.model.AtributosDto;
import br.com.nuvy.facade.cadastro.cd5ecommerce.model.AtributosVariacaoDto;
import br.com.nuvy.facade.venda.vd3tabelapreco.model.TabelaPrecoDto;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CD5EcommerceService {

  private final CanalVendaService canalVendaService;
  private final CategoriaAnuncioService categoriaAnuncioService;
  private final CategoriaProdutoService categoriaProdutoService;
  private final ContaBancariaService contaBancariaService;
  private final TabelaPrecoService tabelaPrecoService;
  private final NopService nopService;
  private final DepositoService depositoService;
  private final PlanoContaService planoContaService;
  private final FormaPagamentoService formaPagamentoService;
  private final EmpresaService empresaService;
  private final CanalOrigemRepository canalOrigemRepository;
  private final AtributoService atributoService;
  private final AtributoMarketPlaceRepository atributoMarketPlaceRepository;

  @Transactional(readOnly = true)
  public Fn2Dto iniciar() {
    return Fn2Dto.builder()
      .tabelasPreco(tabelaPrecoService.findAll().stream().map(TabelaPrecoDto::from)
        .filter(t -> t.getSituacao().equals(Situacao.ATIVO)).toList())
      .nops(nopService.findAll().stream().map(NopResumoDto::from)
        .filter(n -> n.getSituacao().equals(Situacao.ATIVO)).toList())
      .depositos(depositoService.findAll().stream().map(DepositoDto::from).toList())
      .planoContaReceita(planoContaService.findAll().stream().map(PlanoContaDto::from)
        .filter(
          p -> p.getTipo().equals(TipoPlanoConta.RECEITA) && p.getSituacao().equals(Situacao.ATIVO))
        .toList())
      .contasBancarias(contaBancariaService.findAll().stream().map(ContaBancariaResumoDto::from)
        .filter(c -> c.getSituacao().equals(Situacao.ATIVO)).toList())
      .formasPagamento(
        formaPagamentoService.findAll().stream().map(FormaPagamentoDto::from).toList())
      .empresas(empresaService.findAll().stream().map(EmpresaResumoDto::from).toList())
      .canaisOrigem(canalOrigemRepository.findAllBySituacaoTrue())
      .build();
  }

  @Transactional(readOnly = true)
  public Page<CanalVendaDto> findCanaisIntegracoes(CanalVendaFilter filter, Pageable pageable) {
    return canalVendaService.find(filter, pageable).map(CanalVendaDto::from);
  }

  @Transactional(readOnly = true)
  public CanalVendaDto findCanalIntegracaoById(Integer id) {
    var result = canalVendaService.findById(id).orElseThrow(ResourceNotFoundException::new);
    return CanalVendaDto.from(result);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer createCanalIntegracao(CanalVendaDto canalVendaDto) {
    return canalVendaService.create(canalVendaDto.toEntity()).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateCanalIntegracao(Integer id, CanalVendaDto canalVendaDto) {
    CanalVenda canalVenda = canalVendaService.require(id);
    canalVendaService.update(id, canalVendaDto.toEntity(canalVenda));
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateSituacaoCanalIntegracao(Integer id, Situacao situacao) {
    var canalVenda = canalVendaService.findById(id).orElseThrow(ResourceNotFoundException::new);
    canalVenda.setSituacao(situacao);
    canalVendaService.update(id, canalVenda);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void deleteCanalVenda(Integer id) {
    canalVendaService.delete(id);
  }

  @Transactional(readOnly = true)
  public List<CategoriaAnuncioDto> findCategoriasByCanal(Integer id) {
    return categoriaAnuncioService.findByCanalVenda(id).stream().map(CategoriaAnuncioDto::from)
      .toList();
  }

  @Transactional(readOnly = true)
  public List<AtributoDto> findAtributosByCanalVenda(Integer canalVendaId, Integer categoriaId) {
    return atributoService.findAtributosByCanalVenda(canalVendaId, categoriaId);
  }

  @Transactional(readOnly = true)
  public List<AtributoDto> findAtributosByCanalVenda(Integer canalVendaId) {
    return atributoService.findAtributosByCanalVenda(canalVendaId);
  }

  @Transactional(readOnly = true)
  public CategoriaAnuncioDto findCategoriaById(Integer id) {
    var result = categoriaAnuncioService.findByCategoriaErpId(id)
      .orElseThrow(ResourceNotFoundException::new);
    return CategoriaAnuncioDto.from(result);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer createVinculoCategoria(CategoriaAnuncioDto categoriaAnuncioDto) {
    return categoriaAnuncioService.create(categoriaAnuncioDto.toEntity()).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void createVinculoAtributo(AtributosDto atributoDto) {
    atributoService.createVinculoCategoria(atributoDto);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void createVinculoAtributoVariacao(AtributosVariacaoDto atributoDto) {
    atributoService.createVinculoVariacaoAtributo(atributoDto);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateVinculoCategoria(Integer id, CategoriaAnuncioDto categoriaAnuncioDto) {
    categoriaAnuncioService.update(id, categoriaAnuncioDto.toEntity());
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void deleteVinculoCategoria(Integer id) {
    categoriaAnuncioService.delete(id);
  }

  @Transactional(readOnly = true)
  public Page<CategoriaProdutoDto> findCategoriasErp(NoFilter filter, Pageable pageable) {
    return categoriaProdutoService.find(filter, pageable).map(CategoriaProdutoDto::from);
  }

}
