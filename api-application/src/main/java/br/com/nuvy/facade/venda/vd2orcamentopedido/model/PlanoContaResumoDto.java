package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlanoContaResumoDto implements Dto<PlanoConta> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;
  private String nome;

  public static PlanoContaResumoDto from(PlanoConta entity) {
    return ObjectUtils.convert(entity, PlanoContaResumoDto.class);
  }
}