package br.com.nuvy.facade.cadastro.cd11produtos;

import static org.springframework.web.bind.annotation.RequestMethod.HEAD;

import br.com.nuvy.api.cadastro.dto.Imagem;
import br.com.nuvy.api.cadastro.dto.ProdutoDto;
import br.com.nuvy.api.cadastro.dto.ProdutoRelatorioDto;
import br.com.nuvy.api.cadastro.dto.ProdutoVariacaoDto;
import br.com.nuvy.api.cadastro.dto.ProdutoVariacaoInDto;
import br.com.nuvy.api.cadastro.dto.SequenceDto;
import br.com.nuvy.api.cadastro.dto.UnidadeMedidaDto;
import br.com.nuvy.api.cadastro.dto.VariacaoAtributoDto;
import br.com.nuvy.api.cadastro.filter.ProdutoFilter;
import br.com.nuvy.api.cadastro.model.Cest;
import br.com.nuvy.api.cadastro.model.ImportacaoArquivo;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.client.enums.TipoArquivo;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.datasource.DataSource;
import br.com.nuvy.config.datasource.DataSourceType;
import br.com.nuvy.facade.cadastro.cd11produtos.model.Cd11Dto;
import br.com.nuvy.facade.cadastro.cd11produtos.model.ProdutoResumoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "@Cadastro / CD11 - Produtos")
@RestController
@RequestMapping("/cadastro/cd11")
@RequiredArgsConstructor
public class CD11ProdutosController {

  private final CD11ProdutosService service;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Inicializa o facade de produtos",
    description = "Inicializa o facade de produtos"
  )
  @GetMapping("/iniciar")
  public Cd11Dto iniciar() {
    return service.iniciar();
  }

  @Operation(
    summary = "Lista todos os produtos paginados e resumidos",
    description = "Lista todos os produtos paginados e resumidos"
  )
  @GetMapping("/produto")
  public List<ProdutoResumoDto> findProdutos(@ParameterObject ProdutoFilter filter,
    @ParameterObject Pageable pageable) {
    Page<ProdutoResumoDto> result = service.findProdutos(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite realizar o download da consulta",
    description = "permite realizar o download das consultas de produtos, retornando um zip com um csv, xlsx ou pdf"
  )
  @PostMapping(value = "/consulta/download", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public ResponseEntity<InputStreamResource> downloadConsultaProduto(
    @RequestParam TipoArquivo tipoArquivo, @ParameterObject @Valid ProdutoFilter filter) {
    return service.downloadConsultaProduto(tipoArquivo, filter);
  }

  @Operation(
    summary = "Busca um produto pelo seu ID",
    description = "Busca um produto pelo seu ID"
  )
  @GetMapping("/produto/{id}")
  public ProdutoDto obterProduto(@PathVariable Integer id) {
    return service.obterProduto(id);
  }

  @Operation(
    summary = "Permite criar um novo produto",
    description = "Permite criar um novo produto"
  )
  @PostMapping(value = "/produto", headers = "empresa")
  @ResponseStatus(HttpStatus.CREATED)
  public void create(@RequestBody @Valid ProdutoDto produto) {
    var result = service.criarProduto(produto);
    discoverer.handleCreatedResource(result);
  }

  @Operation(
    summary = "Permite atualizar um produto",
    description = "Permite atualizar um produto"
  )
  @PutMapping(value = "/produto/{id}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public void update(@PathVariable Integer id, @RequestBody @Valid ProdutoDto produto) {
    service.atualizarProduto(id, produto);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Permite adicionar ou deletar fotos de um produto",
    description = "Permite adicionar ou deletar fotos de um produto"
  )
  @PutMapping(value = "/{id}/fotos", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public List<Imagem> updateFotos(@PathVariable Integer id,
    @RequestParam(value = "fotos", required = false) List<MultipartFile> fotos,
    @RequestParam(required = false) String remover) throws IOException {
    return service.updateFoto(id, fotos, remover);
  }

  @Operation(
    summary = "Permite deletar um produto pelo seu ID",
    description = "Permite deletar um produto pelo seu ID"
  )
  @DeleteMapping("/produto/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void delete(@PathVariable Integer id) {
    service.deleteProduto(id);
  }

  @Operation(
    summary = "Permite buscar o Xlsx para importação de produtos",
    description = "Permite buscar o Xlsx para importação de produtos na tela de produtos"
  )
  @GetMapping(value = "/produto/importacao")
  public ResponseEntity<Resource> findArquivoToImport() {
    InputStreamResource in = new InputStreamResource(service.findArquivoToImport());
    return ResponseEntity.ok()
      .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + "produtos.xlsx")
      .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
      .body(in);
  }

  @Operation(
    summary = "Permite realizar a importação de planilha de produtos",
    description = "Permite realizar a importação de planilha de produtos"
  )
  @PostMapping(path = "/produto/importacao", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, headers = "empresa")
  public void importProduto(@RequestPart("file") MultipartFile file) {
    service.importProduto(file);
  }

  @Operation(
    summary = "Permite buscar NCM por código e por descrição",
    description = "É a busca de NCM por código e por descrição para a tela de cadastro de produtos"
  )
  @GetMapping(value = "/ncm")
  public List<Ncm> findNcm(@ParameterObject @RequestParam(required = false) String criterio,
    @ParameterObject Pageable pageable) {
    Page<Ncm> result = service.findNcm(criterio, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite buscar CEST por código e por descrição",
    description = "É a busca de CEST por código e por descrição para a tela de cadastro de produtos"
  )
  @GetMapping(value = "/cest")
  public List<Cest> findCest(@ParameterObject @RequestParam(required = false) String criterio,
    @ParameterObject Pageable pageable) {
    Page<Cest> result = service.findCest(criterio, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite buscar a Sequence do código",
    description = "É a busca de Sequence por tipo de sequência para implementar no código de produto"
  )
  @GetMapping(value = "/sequencia")
  public SequenceDto findSequence() {
    return service.findSequence();
  }

  @Operation(
    summary = "Permite verificar se existe um produto",
    description = "É a verificação de existência de um produto através do código"
  )
  @RequestMapping(method = HEAD, value = "/produto-exists/{codigo}")
  @ResponseStatus(HttpStatus.OK)
  public void exists(@PathVariable String codigo) {
    if (!service.exists(codigo)) {
      throw new ResourceNotFoundException();
    }
  }

  @Operation(
    summary = "Permite Listar os arquivos importados",
    description = "Realiza a listagem de todos os arquivos importados disponíveis"
  )
  @GetMapping("/arquivo")
  public List<ImportacaoArquivo> findAllArquivos() {
    return service.findArquivos();
  }

  @Operation(
    summary = "Permite criar uma nova importação de arquivo",
    description = "Realiza a criação de um novo arquivo importado no banco de empresa"
  )
  @PostMapping("/arquivo")
  @ResponseStatus(HttpStatus.CREATED)
  public void createArquivo(@RequestBody ImportacaoArquivo payload) {
    var id = service.createArquivo(payload);
    discoverer.handleCreatedResource(id);
  }


  @Operation(
    summary = "Retorna as unidades de medida, podendo filtrar pelo critério de descrição",
    description = "Retorna as unidades de medida, podendo filtrar pelo critério de descrição"
  )
  @GetMapping("/unidade-medida")
  @ResponseStatus(HttpStatus.OK)
  public List<UnidadeMedidaDto> findUnidadeMedida(
    @ParameterObject @RequestParam(required = false) String criterio,
    @ParameterObject Pageable pageable) {
    Page<UnidadeMedidaDto> result = service.findUnidadeMedida(criterio, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Retorna uma lista de produto variação",
    description = "Retorna uma lista de produto variação"
  )
  @GetMapping("/variacoes/{produtoPaiId}")
  public List<ProdutoVariacaoDto> listarProdutosVariacoes(@PathVariable Integer produtoPaiId) {
    return service.listarProdutosVariacoes(produtoPaiId);
  }

  @Operation(
    summary = "Retorna uma lista de combinação de produto variação",
    description = "Retorna uma lista de combinação de produto variação"
  )
  @PostMapping("/variacoes/simulacao")
  @DataSource(DataSourceType.READ)
  public List<ProdutoVariacaoDto> obterSimulacaoProduto(
    @RequestParam(required = false) Integer produtoPaiId,
    @RequestBody List<VariacaoAtributoDto> atributosVariacoes) {
    return service.obterSimulacaoProduto(produtoPaiId, atributosVariacoes);
  }

  @Operation(
    summary = "Permite atualizar um produto variação",
    description = "Permite atualizar um produto variação"
  )
  @PutMapping("/produto/variacao/{produtoId}")
  @ResponseStatus(HttpStatus.OK)
  public void updateProdutoVariacao(@PathVariable Integer produtoId,
    @RequestBody @Valid ProdutoVariacaoInDto produtoVariacao) {
    service.updateProdutoVariacao(produtoId, produtoVariacao);
  }

  @Operation(
    summary = "Inativa um produto variação pelo seu ID",
    description = "Inativa um produto variação pelo seu ID"
  )
  @DeleteMapping("/produto/variacao/{produtoId}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void inativaProdutoVariacao(@PathVariable Integer produtoId) {
    service.inativaProdutoVariacao(produtoId);
  }

  @Operation(
    summary = "Permite atualizar o campo importado",
    description = "Permite atualizar o campo importado de um produto por id"
  )
  @PatchMapping("/produto/atualizar-importado/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void atualizarImportado(@PathVariable Integer id) {
    service.atualizarImportado(id);
  }

  @Operation(
    summary = "Permite buscar os produtos para o relatório",
    description = "Permite buscar os produto para o relatório de cadastros"
  )
  @GetMapping(path = "/produto/relatorio", headers = "empresa")
  @DataSource(DataSourceType.READ)
  public List<ProdutoRelatorioDto> findAllForReport(@ParameterObject @Valid ProdutoFilter filter) {
    return service.findAllForReport(filter);
  }
}