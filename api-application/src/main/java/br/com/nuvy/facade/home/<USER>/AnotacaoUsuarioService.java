package br.com.nuvy.facade.home.service;

import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.base.service.PageableServiceAdapter;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.home.model.AnotacaoUsuario;
import br.com.nuvy.facade.home.repository.AnotacaoUsuarioRepository;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AnotacaoUsuarioService extends
  PageableServiceAdapter<AnotacaoUsuario, Integer, NoFilter, AnotacaoUsuarioRepository> {

  private final Badge badge;

  public void createAnotacaoUsuario(String anotacao) {
    Usuario usuario = badge.getUsuario();
    Optional<AnotacaoUsuario> anotacaoExistente = repository.findByEmpresaIdAndUsuarioId(
      badge.getEmpresa().getId(), usuario.getId());
    AnotacaoUsuario anotacaoNova = AnotacaoUsuario.builder()
      .empresa(badge.getEmpresa())
      .usuario(usuario)
      .texto(anotacao)
      .build();
    if (anotacaoExistente.isPresent()) {
      update(anotacaoExistente.get().getId(), anotacaoNova);
    } else {
      create(anotacaoNova);
    }
  }

  public String findAnotacaoUsuario() {
    Optional<AnotacaoUsuario> anotacao = repository.findByEmpresaIdAndUsuarioId(
      badge.getEmpresa().getId(), badge.getUsuario().getId());
    if (anotacao.isPresent()) {
      return anotacao.get().getTexto();
    }
    return "";
  }

}
