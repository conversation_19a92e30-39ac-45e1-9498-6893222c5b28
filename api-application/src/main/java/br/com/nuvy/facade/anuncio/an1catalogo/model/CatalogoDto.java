package br.com.nuvy.facade.anuncio.an1catalogo.model;

import br.com.nuvy.api.anuncio.Catalogo;
import br.com.nuvy.api.anuncio.CatalogoCategoria;
import br.com.nuvy.api.anuncio.CatalogoProduto;
import br.com.nuvy.api.cadastro.repository.CategoriaProdutoRepository;
import br.com.nuvy.api.cadastro.repository.ProdutoRepository;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoCatalogo;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.CanalVendaResumoDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class CatalogoDto implements Dto<Catalogo> {

  private Integer id;
  private String nome;
  private TipoCatalogo tipo;
  private Situacao situacao;
  private List<CatalogoProdutoDto> produtosRelacionados;
  private List<CatalogoCategoriaDto> categoriasRelacionadas;
  private List<CanalVendaResumoDto> canalVendas;

  public static CatalogoDto from(Catalogo catalogo) {
    return ObjectUtils.convert(catalogo, CatalogoDto.class);
  }

  public Catalogo toEntity(
    ProdutoRepository produtoRepository,
    CategoriaProdutoRepository categoriaRepository
  ) {
    Catalogo catalogo = Catalogo.builder()
      .id(id)
      .nome(nome)
      .tipo(tipo)
      .situacao(situacao).build();
    catalogo.setProdutosRelacionados(
      produtosRelacionados.stream()
        .map((CatalogoProdutoDto dto) -> CatalogoProduto.builder()
          .id(dto.getId())
          .catalogo(catalogo)
          .produto(produtoRepository.getReferenceById(dto.getProdutoId()))
          .deveDesconsiderar(dto.getDeveDesconsiderar())
          .build())
        .toList());
    catalogo.setCategoriasRelacionadas(
      categoriasRelacionadas.stream()
        .map((CatalogoCategoriaDto dto) -> CatalogoCategoria.builder()
          .id(dto.getId())
          .catalogo(catalogo)
          .categoria(categoriaRepository.getReferenceById(dto.getCategoriaId()))
          .build())
        .toList());
    return catalogo;
  }
}
