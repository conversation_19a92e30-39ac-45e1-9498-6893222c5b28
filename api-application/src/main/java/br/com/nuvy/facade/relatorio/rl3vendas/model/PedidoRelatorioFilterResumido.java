package br.com.nuvy.facade.relatorio.rl3vendas.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedidoRelatorioFilterResumido {

  @JsonProperty("empresaId")
  private List<Integer> empresasId;
  private LocalDate dataPedidoInicial;
  private LocalDate dataPedidoFinal;
  private LocalDate dataFaturamentoInicial;
  private LocalDate dataFaturamentoFinal;

}
