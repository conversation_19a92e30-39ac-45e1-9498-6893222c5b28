package br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model;

import com.webcohesion.ofx4j.domain.data.common.TransactionType;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealizaBaixaDtoIn {

  @NotNull
  private BigDecimal valorBaixa;

  @NotNull
  private LocalDate dataVencimento;

  @Builder.Default
  private Boolean atualizaDataVencimento = false;

  @Builder.Default
  private Boolean atualizaValor = false;

  private BigDecimal valorJuros;
  private BigDecimal valorMulta;
  private BigDecimal valorDesconto;
  private BigDecimal valorAbatimento;

  private TransactionType tipo;
  private String fitId;
  private String memo;

  List<Integer> titulos;

  List<Integer> baixas;

  List<Integer> transferencias;
}
