package br.com.nuvy.facade.estoque.eq6pedidocompra;

import br.com.nuvy.api.estoque.filter.PedidoCompraFilter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.PedidoCompraDto;
import br.com.nuvy.facade.estoque.eq6pedidocompra.model.PedidoCompraOutDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "@Estoque EQ6 - Pedido de Compra", description = "Operações relacionadas a pedido de compra")
@RestController
@RequestMapping(value = "/estoque/eq6/pedido-compra", headers = "empresa")
@RequiredArgsConstructor
public class EQ6PedidoCompraController {

  private final EQ6PedidoCompraService service;
  public final DiscovererComponent discoverer;

  @Operation(
    summary = "Permite listar pedido de compra",
    description = "É a listagem dos pedidos de compra"
  )
  @GetMapping(value = "/empresa/buscar", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public PedidoCompraOutDto buscaPedidosCompra(@ParameterObject PedidoCompraFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findPedidosCompra(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result.getPage());
    return result;
  }

  @Operation(
    summary = "Permite buscar um pedido de compra por id",
    description = "É a busca de um pedido de compra por id"
  )
  @GetMapping(value = "/{id}", headers = "empresa")
  @ResponseStatus(HttpStatus.OK)
  public PedidoCompraDto findPedidoCompraById(@PathVariable Long id) {
    return service.findPedidoCompraById(id);
  }

}