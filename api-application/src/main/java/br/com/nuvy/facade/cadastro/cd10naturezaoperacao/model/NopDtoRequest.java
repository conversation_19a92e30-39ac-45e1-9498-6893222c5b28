package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoNfeNop;
import br.com.nuvy.api.enums.TipoNop;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.validator.AtLeastOneNotNull;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.groups.Default;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AtLeastOneNotNull(fields = {"comprado", "fabricado"})
@AtLeastOneNotNull(fields = {"geraContasReceber", "geraBaixaEstoque"})
@GroupSequenceProvider(NopDtoRequest.CreateUpdateGroupSequenceProvider.class)
public class NopDtoRequest implements Dto<Nop> {

  private Integer id;
  @Size(min = 1, max = 150)
  private String nome;
  private Boolean comprado;
  private Boolean fabricado;
  @NotNull
  private Boolean geraContasReceber;
  @NotNull
  private Boolean geraBaixaEstoque;
  @NotNull(groups = CreateGroup.class)
  private TipoNop tipoNop;

  @Builder.Default
  private TipoNfeNop tipoNfe = TipoNfeNop.SAIDA;

  @NotNull
  private Integer regimeTributarioId;
  private Integer icmsId;
  private Integer ipiId;
  private Integer pisId;
  private Integer cofinsId;
  @NotNull
  private Integer tipoReceitaId;
  private Situacao situacao;
  @NotEmpty
  private List<Integer> empresasId;
  private String observacaoNotaFiscal;

  interface CreateGroup extends Default {}
  interface UpdateGroup extends Default {}
  public static class CreateUpdateGroupSequenceProvider implements DefaultGroupSequenceProvider<NopDtoRequest> {
    public List<Class<?>> getValidationGroups(NopDtoRequest dto) {
      if (dto == null || dto.getId() == null) {
        return List.of(NopDtoRequest.class, CreateGroup.class);
      }
      return List.of(NopDtoRequest.class, UpdateGroup.class);
    }
  }
}
