package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import java.math.BigDecimal;

import br.com.nuvy.api.enums.ModalidadeBaseCalculo;
import br.com.nuvy.api.enums.ModalidadeBaseCalculoSt;
import br.com.nuvy.api.enums.ModalidadeTipoCalculo;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoRegraCalculo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public abstract class RegraIcmsItemDto {

  private String informacaoComplementar;
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;

  // - Contribuinte
  // icms simples nacional
  private String csosnId;
  private BigDecimal aliquotaCreditoIcms;
  // icms lucro real
  private String cstId;
  private ModalidadeBaseCalculo modalidadeBaseDeCalculo;
  private BigDecimal aliquotaIcms;
  private BigDecimal aliquotaIcmsEstrangeiro;
  private BigDecimal aliquotaFcp;
  private BigDecimal percentualReducaoBaseCalculo;
  private BigDecimal percentualDiferimento;
  @Builder.Default
  private Boolean desonerado = false;
  private String motivoDesoneracaoId;
  private String motivoDesoneracaoDescricao;
  // icms st
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoSt;
  private BigDecimal aliquotaInterestadualIcmsSt;
  private BigDecimal percentualMvaSt;
  private BigDecimal aliquotaFcpSt;
  private BigDecimal aliquotaInternaIcmsSt;
  private BigDecimal percentualReducaoBaseCalculoSt;
  private ModalidadeTipoCalculo modalidadeTipoCalculoSt;
  // icms produto estrangeiro
  private BigDecimal percentualMvaEstrangeiroSt;
  private BigDecimal aliquotaFcpEstrangeiroSt;
  private BigDecimal aliquotaIcmsEstrangeiroSt;
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroSt;

  // Consumidor Final - Contribuinte
  // icms simples nacional
  private String csosnConsumidorFinalId;
  @Builder.Default
  private Boolean zeraMvaStConsumidorFinal = false;
  // icms lucro real
  private String cstConsumidorFinalId;
  private ModalidadeBaseCalculo modalidadeBaseDeCalculoConsumidorFinal;
  private BigDecimal aliquotaIcmsConsumidorFinal;
  private BigDecimal aliquotaIcmsEstrangeiroConsumidorFinal;
  private BigDecimal aliquotaFcpConsumidorFinal;
  private BigDecimal percentualReducaoBaseCalculoConsumidorFinal;
  private BigDecimal percentualDiferimentoConsumidorFinal;
  @Builder.Default
  private Boolean desoneradoConsumidorFinal = false;
  private String motivoDesoneracaoConsumidorFinalId;
  private String motivoDesoneracaoConsumidorFinalDescricao;
  // icms st
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoStConsumidorFinal;
  private BigDecimal aliquotaInterestadualIcmsStConsumidorFinal;
  private BigDecimal percentualMvaStConsumidorFinal;
  private BigDecimal aliquotaFcpStConsumidorFinal;
  private BigDecimal aliquotaInternaIcmsStConsumidorFinal;
  private BigDecimal percentualReducaoBaseCalculoStConsumidorFinal;
  private ModalidadeTipoCalculo modalidadeTipoCalculoStConsumidorFinal;
  // icms produto estrangeiro
  private BigDecimal percentualMvaEstrangeiroStConsumidorFinal;
  private BigDecimal aliquotaFcpEstrangeiroStConsumidorFinal;
  private BigDecimal aliquotaIcmsEstrangeiroStConsumidorFinal;
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroStConsumidorFinal;
  // icms difal
  @Builder.Default
  private Boolean calculaDifalStConsumidorFinal = false;
  private BigDecimal aliquotaFcpDifalInterestadualConsumidorFinal;
  private BigDecimal aliquotaInternaUfDestinoConsumidorFinal;
  private BigDecimal aliquotaInterestadualConsumidorFinal;
  private BigDecimal percentualReducaoBaseCalculoDestinoConsumidorFinal;
  private BigDecimal aliquotaInterestadualProdutoEstrangeiroConsumidorFinal;
  private TipoRegraCalculo regraCalculoConsumidorFinal;

  // - Consumidor Final - Nao Contribuinte
  // icms simples nacional
  private String csosnConsumidorFinalNcId;
  // icms lucro real
  private String cstConsumidorFinalNcId;
  private ModalidadeBaseCalculo modalidadeBaseDeCalculoConsumidorFinalNc;
  private BigDecimal aliquotaIcmsConsumidorFinalNc;
  private BigDecimal aliquotaIcmsEstrangeiroConsumidorFinalNc;
  private BigDecimal aliquotaFcpConsumidorFinalNc;
  private BigDecimal percentualReducaoBaseCalculoConsumidorFinalNc;
  private BigDecimal percentualDiferimentoConsumidorFinalNc;
  @Builder.Default
  private Boolean desoneradoConsumidorFinalNc = false;
  private String motivoDesoneracaoConsumidorFinalNcId;
  private String motivoDesoneracaoConsumidorFinalNcDescricao;
  // icms st
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoStConsumidorFinalNc;
  private BigDecimal aliquotaInterestadualIcmsStConsumidorFinalNc;
  private BigDecimal percentualMvaStConsumidorFinalNc;
  private BigDecimal aliquotaFcpStConsumidorFinalNc;
  private BigDecimal aliquotaInternaIcmsStConsumidorFinalNc;
  private BigDecimal percentualReducaoBaseCalculoStConsumidorFinalNc;
  private ModalidadeTipoCalculo modalidadeTipoCalculoStConsumidorFinalNc;
  // icms produto estrangeiro
  private BigDecimal percentualMvaEstrangeiroStConsumidorFinalNc;
  private BigDecimal aliquotaFcpEstrangeiroStConsumidorFinalNc;
  private BigDecimal aliquotaIcmsEstrangeiroStConsumidorFinalNc;
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroStConsumidorFinalNc;
  // icms difal
  private BigDecimal aliquotaFcpDifalInterestadualConsumidorFinalNc;
  private BigDecimal aliquotaInternaUfDestinoConsumidorFinalNc;
  private BigDecimal aliquotaInterestadualIcmsConsumidorFinalNc;
  private BigDecimal percentualReducaoBaseCalculoDestinoConsumidorFinalNc;
  private BigDecimal aliquotaInterestadualIcmsEstrangeiroConsumidorFinalNc;
  private TipoRegraCalculo tipoRegraCalculoConsumidorFinalNc;
}
