package br.com.nuvy.facade.cadastro.cd16pos.unidademedida.model;

import br.com.nuvy.api.cadastro.model.UnidadeMedidaIntegracao;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.*;
import java.time.LocalDateTime;

@Getter
@Setter
public class UnidadeMedidaPosPdvDto {

  private Integer empresaId;
  private String name;
  private String unittyPeId;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  public static UnidadeMedidaPosPdvDto from(UnidadeMedidaIntegracao entity) {
    return ObjectUtils.convert(entity, UnidadeMedidaPosPdvDto.class);
  }
}
