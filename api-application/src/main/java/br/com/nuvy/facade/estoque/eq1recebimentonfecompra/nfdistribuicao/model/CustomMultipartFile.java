package br.com.nuvy.facade.estoque.eq1recebimentonfecompra.nfdistribuicao.model;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class CustomMultipartFile implements MultipartFile {

  private final String name;
  private final String originalFilename;
  private final String contentType;
  private byte[] input;

  public CustomMultipartFile(String name, String originalFilename, String contentType, byte[] input){
    this.name = name;
    this.originalFilename = originalFilename;
    this.contentType = contentType;
    this.input = input;
  }

  @Override
  public String getName() {
    return this.name;
  }

  @Override
  public String getOriginalFilename() {
    return this.originalFilename;
  }

  @Override
  public String getContentType() {
    return this.contentType;
  }

  @Override
  public boolean isEmpty() {
    return false;
  }

  @Override
  public long getSize() {
    return 0;
  }

  @Override
  public byte[] getBytes() throws IOException {
    return this.input;
  }

  @Override
  public InputStream getInputStream() throws IOException {
    return new ByteArrayInputStream(this.input);
  }

  @Override
  public Resource getResource() {
    return MultipartFile.super.getResource();
  }

  @Override
  public void transferTo(File destino) throws IOException, IllegalStateException {
    try(FileOutputStream fileOutputStream = new FileOutputStream(destino)){
      fileOutputStream.write(input);
    }
  }
}
