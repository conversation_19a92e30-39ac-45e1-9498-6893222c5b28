package br.com.nuvy.facade.cadastro.cd8contacorrente.model;

import br.com.nuvy.api.financeiro.dto.BancoDto;
import br.com.nuvy.api.financeiro.dto.CarteiraCobrancaDto;
import lombok.Builder;
import lombok.Data;
import java.util.List;

@Data
@Builder
public class CD8ConfiguracaoBoletoDto {
  BancoDto banco;
  CarteiraCobrancaDto carteiraCobranca;
  List<CD8ModalidadeCobrancaDto> modalidadesCobranca;

}
