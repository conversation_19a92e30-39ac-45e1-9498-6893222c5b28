package br.com.nuvy.facade.cadastro.cd16pos.integracoes;

import br.com.nuvy.api.cadastro.filter.PosPdvIntegracaoFilter;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Empresa_;
import br.com.nuvy.api.cadastro.model.PosPdv;
import br.com.nuvy.api.cadastro.model.PosPdvIntegracao;
import br.com.nuvy.api.cadastro.model.PosPdv_;
import br.com.nuvy.api.cadastro.repository.PosPdvIntegracaoRepository;
import br.com.nuvy.api.cadastro.repository.PosPdvRepository;
import br.com.nuvy.api.cadastro.repository.specification.PosPdvIntegracaoSpecification;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.credential.Badge;
import br.com.nuvy.facade.cadastro.cd16pos.integracoes.model.PosPdvIntegracaoOutDto;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.PosPdvIntegracaoDto;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import static br.com.nuvy.common.exception.Preconditions.checkNonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class CD16PosPdvIntegracaoService {

  private final Badge badge;
  private final PosPdvRepository repository;
  private final PosPdvIntegracaoRepository posPdvIntegracaoRepository;

  public List<PosPdvIntegracaoOutDto> listarDisponiveis() {

    List<PosPdv> pdvs = repository.findAll(whereWithAtivo(true));
    List<UUID> integracoesIds = pdvs.stream().map(PosPdv::getPosPdvIntegracao)
      .map(PosPdvIntegracao::getId).collect(Collectors.toList());

    var filtro = new PosPdvIntegracaoFilter();
    filtro.setSituacao(Situacao.ATIVO);
    filtro.setIntegracoesIds(integracoesIds);
    var specification = PosPdvIntegracaoSpecification.from(filtro);

    var integracoes = posPdvIntegracaoRepository.findAll(specification, Sort.by("nome"))
      .stream().map(PosPdvIntegracaoOutDto::from).collect(Collectors.toList());

    return integracoes;
  }

  public List<PosPdvIntegracaoDto> listarMinhasIntegracoes() {
    Specification<PosPdv> where = whereWithAtivoAndEmpresa(true, badge.getEmpresa().getId());
    List<PosPdv> posPdvs = repository.findAll(where);

    if(posPdvs.isEmpty()){
      return new ArrayList<>();
    }

    return posPdvs.stream().map(PosPdvIntegracaoDto::of)
      .collect(Collectors.toList());
  }

  private Specification<PosPdv> whereWithAtivoAndEmpresa(boolean ativo, Integer empresaId){
    return ((root, query, criteriaBuilder) -> {
      PredicateBuilder predicate = PredicateBuilder.create(criteriaBuilder);
      predicate.add(ativo, e -> criteriaBuilder.equal(root.get(PosPdv_.ativo), e));
      predicate.add(empresaId, e -> {
        Join<PosPdv, Empresa> join = root.join(PosPdv_.empresa, JoinType.INNER);
        return criteriaBuilder.equal(join.get(Empresa_.id), e);
      });
      return predicate.and();
    });
  }

  private Specification<PosPdv> whereWithAtivo(boolean ativo){
    var empresa =BadgeContext.getEmpresa();
    checkNonNull(empresa, "empresa.nao.existe.no.contexto");

    return ((root, query, criteriaBuilder) -> {
      PredicateBuilder predicate = PredicateBuilder.create(criteriaBuilder);
      predicate.add(ativo, e -> criteriaBuilder.equal(root.get(PosPdv_.ativo), e));
      predicate.add(empresa.getId(), e -> {
        Join<PosPdv, Empresa> join = root.join(PosPdv_.empresa, JoinType.INNER);
        return criteriaBuilder.equal(join.get(Empresa_.id), e);
      });
      return predicate.and();
    });
  }
}