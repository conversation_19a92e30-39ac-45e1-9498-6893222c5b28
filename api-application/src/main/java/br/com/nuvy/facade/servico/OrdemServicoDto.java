package br.com.nuvy.facade.servico;

import br.com.nuvy.api.cadastro.dto.ParceiroDto;
import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.api.enums.TipoComprovanteOS;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import br.com.nuvy.api.venda.dto.OrdemServicoItemDto;
import br.com.nuvy.facade.venda.vd2orcamentopedido.model.PedidoEnderecoDto;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrdemServicoDto {

  private Long id;
  private UUID uuid;
  private Long codigo;
  private Long numeroRps;
  private SituacaoOrdemServico situacao;
  private TipoComprovanteOS tipoComprovante;
  private UUID usuarioId;
  private LocalDateTime dataCriacao;
  private LocalDateTime dataAlteracao;
  private LocalDateTime dataPrevista;
  private LocalDateTime dataValidadeOrcamento;
  private LocalDateTime dataVencimento;
  private String consideracoes;
  private String observacoes;
  private String observacoesNf;

  private Integer empresaId;
  private UUID clienteUuid;
  private ParceiroDto cliente;
  private UUID vendedorUuid;
  private UUID tecnicoUuid;
  private String emailEnvioCobranca;
  private String emailEnvioNfse;

  @Valid
  private List<PedidoEnderecoDto> enderecos;

  private String cepEnderecoFiscal;
  private String ufEnderecoFiscal;
  private String cidadeEnderecoFiscal;
  private String codigoCidadeEnderecoFiscal;
  private String bairroEnderecoFiscal;
  private String enderecoFiscal;
  private String numeroEnderecoFiscal;
  private String complementoEnderecoFiscal;

  private Boolean intermediador;
  private String documentoIntermediador;
  private String nomeIntermediador;
  private String inscMunicipalIntermediador;
  private String telefoneIntermediador;
  private String emailIntermediador;
  private String cepIntermediador;
  private String ufIntermediador;
  private String cidadeIntermediador;
  private String codigoCidadeIntermediador;
  private String bairroIntermediador;
  private String enderecoIntermediador;
  private String numeroIntermediador;
  private String complementoIntermediador;
  private Boolean ratearDesconto;
  private BigDecimal valorNf;
  private BigDecimal totalServicos;
  private BigDecimal totalDescontos;
  private BigDecimal totalImpostosRetidos;

  //Usado apenas para a API Externa
  private BigDecimal valor;
  private BigDecimal quantidade;
  private Integer formaRecebimentoId;
  private Integer contaBancariaId;
  private Integer condicaoRecebimentoId;

  private List<OrdemServicoItemDto> itens;
  private List<OrdemServicoParcelaDto> parcelas;

  private Integer planoContaId;
  private Boolean recorrencia;
  private Boolean faturamentoAutomatico;
  private Boolean vencimentoProximoMes;
  private Boolean acrescentarEmail;

  private String tipoPagamento;
}
