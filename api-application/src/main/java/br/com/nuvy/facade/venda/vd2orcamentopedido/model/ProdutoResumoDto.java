package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProdutoResumoDto implements Dto<Produto> {

  private Integer id;
  private String codigo;
  private String descricao;
  private String descricaoCompleta;

  public static ProdutoResumoDto from(Produto entidade) {
    return ObjectUtils.convert(entidade, ProdutoResumoDto.class);
  }
}
