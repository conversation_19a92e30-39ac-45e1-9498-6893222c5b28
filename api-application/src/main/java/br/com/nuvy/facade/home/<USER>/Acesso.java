package br.com.nuvy.facade.home.model;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.seguranca.model.Modulo;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ad_acesso")
public class Acesso implements Entidade<Integer> {

  @Id
  @Column(name = "id_acesso")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "contador", nullable = false)
  private Integer contador;

  @ManyToOne(targetEntity = Modulo.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_modulo")
  private Modulo modulo;

  @ManyToOne(targetEntity = Empresa.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  
}
