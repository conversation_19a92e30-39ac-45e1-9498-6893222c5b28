package br.com.nuvy.facade.anuncio.an1catalogo;

import br.com.nuvy.api.anuncio.Catalogo;
import br.com.nuvy.api.anuncio.filter.CatalogoFilter;
import br.com.nuvy.api.anuncio.service.CatalogoService;
import br.com.nuvy.api.cadastro.filter.ProdutoFilter;
import br.com.nuvy.api.cadastro.repository.CategoriaProdutoRepository;
import br.com.nuvy.api.cadastro.repository.ProdutoRepository;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.ProdutoService;
import br.com.nuvy.api.enums.AcaoCatalogoProduto;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CatalogoDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CatalogoOutDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CatalogoProdutoDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CatalogoResumoDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.ProdutoResumoDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class AN1CatalogoService {

  private final CatalogoService catalogoService;
  private final ProdutoService produtoService;
  private final EmpresaService empresaService;

  private final ProdutoRepository produtoRepository;
  private final CategoriaProdutoRepository categoriaRepository;

  @Transactional(readOnly = true)
  public Page<CatalogoResumoDto> findCatalogos(CatalogoFilter filter, Pageable pageable) {
    return catalogoService.find(filter, toPageable(pageable)).map(CatalogoResumoDto::from);
  }

  @Transactional(readOnly = true)
  public CatalogoOutDto findCatalogo(Integer id) {
    Catalogo catalogo = catalogoService.findById(id).orElseThrow(ResourceNotFoundException::new);
    return CatalogoOutDto.from(catalogo);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer create(CatalogoDto catalogoDto) {
    Catalogo catalogo = catalogoDto.toEntity(
      produtoRepository, categoriaRepository);
    catalogo.setEmpresa(empresaService.getReference(BadgeContext.getEmpresa().getId()));
    return catalogoService.create(catalogo).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer update(Integer id, CatalogoDto catalogoDto) {
    return catalogoService.update(id, catalogoDto.toEntity()).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void relacionarProdutos(Integer id, CatalogoProdutoDto produto, AcaoCatalogoProduto acao) {
    catalogoService.relacionarProdutos(id, produto, acao);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void delete(Integer id) {
    catalogoService.delete(id);
  }

  @Transactional(readOnly = true)
  public List<ProdutoResumoDto> findProdutosCatalogo(Integer id) {
    return catalogoService.findAllProdutosCatalogoResumo(id);
  }

  @Transactional(readOnly = true)
  public Page<ProdutoResumoDto> findProdutosByFilter(ProdutoFilter filter, Pageable pageable) {
    return produtoService.find(filter, pageable).map(ProdutoResumoDto::from);
  }

  private Pageable toPageable(Pageable pageable) {
    if (pageable.getSort().isSorted()) {
      Map<String, String> propertys = new HashMap<>();
      propertys.put("marketplaces", "canalVendas.origem");
      Optional<Order> order = pageable.getSort().get().findFirst();
      if (order.isPresent() && (StringUtils.isNotBlank(propertys.get(order.get().getProperty())))) {
        pageable = ((PageRequest) pageable).withSort(order.get().getDirection(),
          propertys.get(order.get().getProperty()));
      }
    } else {
      pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
        Sort.by(Direction.ASC, "nome")
      );
    }
    return pageable;
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateSituacao(Integer idCatalogo) {
    catalogoService.updateSituacao(idCatalogo);
  }
}
