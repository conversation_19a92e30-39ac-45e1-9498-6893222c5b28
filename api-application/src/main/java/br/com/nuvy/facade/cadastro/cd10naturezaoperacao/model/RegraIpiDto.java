package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import static br.com.nuvy.api.enums.TipoRegraExcecao.NCM;
import static br.com.nuvy.api.enums.TipoRegraExcecao.PRODUTO;
import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;

import br.com.nuvy.api.cadastro.model.EnquadramentoIpi;
import br.com.nuvy.api.cadastro.model.nop.CstIpi;
import br.com.nuvy.api.cadastro.model.nop.RegraIpi;
import br.com.nuvy.api.cadastro.model.nop.RegraIpiExcecao;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoCalculoImposto;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(callSuper = true)
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class RegraIpiDto implements Dto<RegraIpi> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;

  @Size(max = 150)
  private String nome;

  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;

  private BigDecimal aliquota;

  private String informacaoComplementar;

  private Integer origemMercadoriaId;

  @JsonProperty(access = Access.READ_ONLY)
  private String origemMercadoriaNome;

  private String enquadramentoIpiCodigo;

  @JsonProperty(access = Access.READ_ONLY)
  private String enquadramentoIpiDescricao;

  @JsonProperty(value = "situacaoTributariaCodigo")
  private String cstIpiCodigo;

  @JsonProperty(access = Access.READ_ONLY, value = "situacaoTributariaDescricao")
  private String cstIpiDescricao;

  @JsonProperty(access = Access.READ_ONLY)
  private TipoCalculoImposto situacaoTributariaTipoCalculo;

  @Valid
  private List<RegraIpiExcecaoDto> excecoes;

  @Override
  public RegraIpi toEntity() {
    var entity = RegraIpi.builder()
        .id(id)
        .nome(nome)
        .situacao(situacao)
        .aliquota(aliquota)
        .informacaoComplementar(informacaoComplementar)
        .enquadramentoIpi(EnquadramentoIpi.of(enquadramentoIpiCodigo))
        .cstIpi(CstIpi.of(cstIpiCodigo))
        .build();
    if (excecoes != null && !excecoes.isEmpty()) {
      entity.setExcecoes(toExcecoes(excecoes, entity));
    }
    return entity;
  }

  private List<RegraIpiExcecao> toExcecoes(List<RegraIpiExcecaoDto> dtos, RegraIpi entity) {
    var entities = new ArrayList<RegraIpiExcecao>();
    dtos.stream()
        .filter(ex -> PRODUTO.equals(ex.getTipoExcecao()))
        .forEach(ex -> {
          entities.add(ex.toEntityProduto(entity));
        });
    dtos.stream()
        .filter(ex -> NCM.equals(ex.getTipoExcecao()))
        .forEach(ex -> {
          entities.addAll(ex.toEntityNcm(entity));
        });
    return entities;
  }

  public static RegraIpiDto from(RegraIpi entity) {
    var dto = ObjectUtils.convert(entity, RegraIpiDto.class);
    var excecoesDto = entity.getExcecoes().stream().map(RegraIpiExcecaoDto::from).toList();

    var excecoes = new ArrayList<>(excecoesDto.stream()
        .filter(item -> item.getTipoExcecao().equals(PRODUTO))
        .toList());

    excecoesDto.stream()
        .filter(item -> item.getTipoExcecao().equals(NCM))
        .collect(groupingBy(RegraIpiExcecaoDto::getNcmId))
        .values().forEach(exs -> {
          excecoes.add(RegraIpiExcecaoDto.toExcecaoNcmDto(exs));
        });

    dto.setExcecoes(excecoes);
    return dto;
  }
}