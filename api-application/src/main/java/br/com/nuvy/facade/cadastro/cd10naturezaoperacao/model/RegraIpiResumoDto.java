package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import br.com.nuvy.api.cadastro.model.nop.RegraIpi;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegraIpiResumoDto implements Dto<RegraIpi> {

  private Integer id;
  private Situacao situacao;
  private String nome;
  private BigDecimal aliquota;
  private Integer origemMercadoriaId;
  private String origemMercadoriaDescricao;
  private String origemMercadoriaNome;
  private String situacaoTributariaCodigo;
  private String situacaoTributariaDescricao;
  private List<RegraIpiExcecaoResumoDto> excecoes;

  public static RegraIpiResumoDto from(RegraIpi entity) {
    return ObjectUtils.convert(entity, RegraIpiResumoDto.class);
  }

}
