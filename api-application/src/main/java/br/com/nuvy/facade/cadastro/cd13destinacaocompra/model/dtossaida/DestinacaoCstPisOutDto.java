package br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.dtossaida;

import br.com.nuvy.api.compras.model.DestinacaoCstPis;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstPisDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DestinacaoCstPisOutDto implements Dto<DestinacaoCstPis> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;
  private CstPisDto cstPisOrigem;
  private CstPisDto cstPisEntrada;
  private CstPisDto cstPisDevolucao;

}
