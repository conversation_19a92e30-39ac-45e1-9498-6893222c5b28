package br.com.nuvy.facade.cadastro.cd12deposito.model;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoDeposito;
import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.validation.groups.DepositoTerceiroGroup;
import br.com.nuvy.validation.groups.DepositoTerceiroPoderGroup;
import br.com.nuvy.validation.groups.provider.DepositoGroupSequenceProvider;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import java.util.Objects;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.group.GroupSequenceProvider;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GroupSequenceProvider(DepositoGroupSequenceProvider.class)
public class DepositoInDto implements Dto<Deposito> {

  private Integer id;
  @Size(max = 255)
  private String nome;
  @Size(max = 255)
  private String descricao;
  private TipoDeposito tipo;
  private Boolean venda;
  private Situacao situacao = Situacao.ATIVO;
  @NotNull(groups = {DepositoTerceiroGroup.class, DepositoTerceiroPoderGroup.class})
  private Integer fornecedorId;
  @JsonProperty(access = Access.READ_ONLY)
  private Boolean sistema = false;

  public static DepositoInDto from(Deposito entity) {
    return ObjectUtils.convert(entity, DepositoInDto.class);
  }

  public void setAtivo(Boolean ativo) {
    if (Objects.nonNull(ativo)) {
      this.situacao = ativo ? Situacao.ATIVO : Situacao.INATIVO;
    }
  }

  public Boolean getAtivo() {
    return this.situacao == Situacao.ATIVO;
  }
}
