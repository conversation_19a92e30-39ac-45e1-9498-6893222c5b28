package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.enums.TipoEmail;
import br.com.nuvy.api.venda.model.PedidoHistoricoEmail;
import br.com.nuvy.api.venda.model.PedidoHistoricoEmailArquivo;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedidoHistoricoEmailDto implements Dto<PedidoHistoricoEmail> {

  private UUID id;
  private LocalDateTime dataHistorico;
  private String assunto;
  private UUID pedidoId;
  private UUID remetenteId;
  private String remetenteNome;
  private List<PedidoHistoricoEmailArquivo> arquivos;
  private String destinatarios;
  private TipoEmail tipoEmail;

  public static PedidoHistoricoEmailDto from(PedidoHistoricoEmail pedidoHistoricoEmail) {
    return ObjectUtils.convert(pedidoHistoricoEmail, PedidoHistoricoEmailDto.class);
  }

  @Override
  public PedidoHistoricoEmail toEntity() {
    var pedidoHistoricoEmail = ObjectUtils.convert(this, PedidoHistoricoEmail.class);
    if (Objects.nonNull(this.getArquivos())) {
      for (PedidoHistoricoEmailArquivo arquivo : pedidoHistoricoEmail.getArquivos()) {
        arquivo.setPedidoHistoricoEmail(pedidoHistoricoEmail);
      }
    }
    return pedidoHistoricoEmail;
  }
}
