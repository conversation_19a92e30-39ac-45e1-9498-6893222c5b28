package br.com.nuvy.facade.financeiro.fn4extratoconciliacao.model;

import br.com.nuvy.api.enums.SituacaoMovimentacaoBancaria;
import br.com.nuvy.api.enums.TipoExtratoTitulo;
import br.com.nuvy.api.financeiro.model.ExtratoConciliacaoView;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtratoConciliacaoDto implements Dto<ExtratoConciliacaoView> {

  private Integer id;
  private Integer transacaoId;
  @JsonProperty(value = "bancoId")
  private Integer bancoId;
  @JsonProperty(value = "bancoCodigo")
  private String bancoCodigo;
  @JsonProperty(value = "bancoNome")
  private String bancoNome;
  @JsonProperty(value = "parceiroId")
  private Integer parceiroId;
  @JsonProperty(value = "parceiroNome")
  private String parceiroNome;
  @JsonProperty(value = "parceiroCpfCnpj")
  private String parceiroCpfCnpj;
  private BigDecimal valorBaixa;
  private TipoExtratoTitulo tipo;
  private LocalDate dataBaixa;
  @JsonProperty(value = "vencimento")
  private LocalDate vencimento;
  private SituacaoMovimentacaoBancaria status;

  public static ExtratoConciliacaoDto from(ExtratoConciliacaoView entidade) {
    return ObjectUtils.convert(entidade, ExtratoConciliacaoDto.class);
  }

}
