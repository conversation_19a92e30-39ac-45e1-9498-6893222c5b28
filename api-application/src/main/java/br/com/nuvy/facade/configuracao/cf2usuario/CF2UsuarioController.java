package br.com.nuvy.facade.configuracao.cf2usuario;

import br.com.nuvy.api.seguranca.filter.UsuarioAplicacaoFilter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.facade.configuracao.cf2usuario.model.ColaboradorFilter;
import br.com.nuvy.facade.configuracao.cf2usuario.model.ColaboradorResumoDto;
import br.com.nuvy.facade.configuracao.cf2usuario.model.SituacaoUsuarioDto;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuarioAplicacaoResumoDto;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuarioDto;
import br.com.nuvy.facade.configuracao.cf2usuario.model.UsuariosResumoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "@Configuração / CF2 - Usuarios")
@RestController
@RequestMapping("/configuracao/cf2")
@RequiredArgsConstructor
public class CF2UsuarioController {

  private final CF2UsuarioService service;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Lista os usuários de forma paginada",
    description = "Lista todos os usuarios cadastrados paginados de 10 registros por PÁGINA e até N registros, ordenação padrao ordenados pelo NOME DO USUÁRIO"
  )
  @ApiResponses(value = {
    @ApiResponse(responseCode = "200", headers = {
      @Header(name = "link", description = "link para navegação nas páginas, caso exista"),
      @Header(name = "TotalPages", description = "Quantidade total de páginas obtidas"),
      @Header(name = "TotalElements", description = "quantidade total de elementos obtidos")
    })
  })
  @GetMapping("/usuario")
  public UsuariosResumoDto findAllUsuario(
    @ParameterObject @Valid UsuarioAplicacaoFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findAllUsuario(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.hasContent() ? result.getContent().get(0) : null;
  }

  @Operation(
    summary = "Realiza o convite de um usuario para o sistema",
    description = "Realiza o convite de um usuario para o sistema, envia os empresa de acesso por email para o usuário convidado"
  )
  @PostMapping("/usuario")
  @ResponseStatus(HttpStatus.OK)
  public void createUsuario(@Valid @RequestBody UsuarioDto payload) {
    var id = service.createUsuario(payload);
    discoverer.handleCreatedResource(id);
  }

  @Operation(
    summary = "Busca um usuário pelo ID",
    description = "Permite realizar a busca de um usuário passando apenas ID"
  )
  @GetMapping("/usuario/{id}")
  public UsuarioAplicacaoResumoDto findUsuarioById(@PathVariable Integer id) {
    return service.findUsuarioById(id);
  }

  @Operation(
    summary = "Permite atualizar um usuário",
    description = "Permite atualizar um usuário informando o ID e enviando o payload com os dados da tela"
  )
  @PutMapping("/usuario/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateUsuario(@PathVariable Integer id, @Valid @RequestBody UsuarioDto payload) {
    service.updateUsuario(id, payload);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Permite atualizar a situação de um usuário",
    description = "Permite realizar a atualização da situação do usuário, sendo apenas necessário enviar \"ATIVO\" ou \"INATIVO\""
  )
  @PatchMapping("/usuario/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void patchUsuario(@PathVariable Integer id, @RequestBody SituacaoUsuarioDto payload) {
    service.patchUsuario(id, payload);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Exclui o usuário informado pelo id",
    description = "Verifica se o usuario ná acessou o sistema (efetuou pelo menos um login), se sim, nao poermite excluir"
  )
  @ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "usuário excluido com sucesso"),
    @ApiResponse(responseCode = "412", description = "O usuário já acessou o sistema e não poderá ser excluído")
  })
  @DeleteMapping("/usuario/{id}")
  @ResponseStatus(value = HttpStatus.NO_CONTENT)
  public void deleteUsuario(@PathVariable Integer id) {
    service.deleteUsuario(id);
  }

  @Operation(
    summary = "Consulta um colaborador para vincular como usuário",
    description = "Realiza a busca de colaboradores para que possa ser vinculado como usuário, o resultado é paginado"
  )
  @GetMapping("/colaborador")
  public List<ColaboradorResumoDto> findColaborador(@ParameterObject ColaboradorFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findColaborador(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

}
