package br.com.nuvy.facade.administracao.ad1aplicacao.model;

import br.com.nuvy.api.admin.Aplicacao;
import br.com.nuvy.common.base.dto.Dto;
import lombok.Builder;
import lombok.Value;

import java.io.Serializable;

@Value
@Builder
public class AplicacaoInDto implements Dto<Aplicacao> {

  private String nomeEmpresa;
  private String nomeresponsavel;
  private String email;
  private String telefone;
  private String cpfCnpj;

  @Override
  public Serializable getId() {
    return null;
  }
}
