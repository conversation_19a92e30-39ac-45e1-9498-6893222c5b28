package br.com.nuvy.facade.anuncio.an2anunciomarketplace;

import static br.com.nuvy.api.enums.AcaoCatalogoProduto.ADICIONAR;
import static br.com.nuvy.api.enums.AcaoCatalogoProduto.REMOVER;
import static br.com.nuvy.api.enums.AcaoHistoricoCatalogo.CRIADO;
import static br.com.nuvy.common.utils.ObjectUtils.get;

import br.com.nuvy.api.anuncio.filter.AnuncioFilter;
import br.com.nuvy.api.anuncio.filter.HistoricoHubFilter;
import br.com.nuvy.api.anuncio.service.AnuncioService;
import br.com.nuvy.api.anuncio.service.CatalogoService;
import br.com.nuvy.api.anuncio.service.HistoricoHubService;
import br.com.nuvy.api.cadastro.dto.ProdutoResumoDto;
import br.com.nuvy.api.cadastro.event.canalvenda.CanalVendaAlterado;
import br.com.nuvy.api.cadastro.event.canalvenda.CanalVendaCriado;
import br.com.nuvy.api.cadastro.filter.ProdutoDepositoFilter;
import br.com.nuvy.api.cadastro.service.CategoriaProdutoService;
import br.com.nuvy.api.cadastro.service.EmpresaService;
import br.com.nuvy.api.cadastro.service.FormaPagamentoService;
import br.com.nuvy.api.cadastro.service.NopService;
import br.com.nuvy.api.cadastro.service.ProdutoService;
import br.com.nuvy.api.enums.AcaoHistoricoCatalogo;
import br.com.nuvy.api.enums.Marketplace;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoPlanoConta;
import br.com.nuvy.api.estoque.dto.DepositoDto;
import br.com.nuvy.api.estoque.service.DepositoService;
import br.com.nuvy.api.estoque.service.SaldoService;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoDto;
import br.com.nuvy.api.financeiro.dto.FormaPagamentoDto;
import br.com.nuvy.api.financeiro.dto.PlanoContaDto;
import br.com.nuvy.api.financeiro.service.ContaBancariaService;
import br.com.nuvy.api.financeiro.service.PlanoContaService;
import br.com.nuvy.api.integracaohub.dto.AnnouncementDto;
import br.com.nuvy.api.integracaohub.enums.TipoSincronizacaoHub;
import br.com.nuvy.api.venda.filter.CanalVendaFilter;
import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.api.venda.model.TabelaPreco;
import br.com.nuvy.api.venda.repository.CanalOrigemRepository;
import br.com.nuvy.api.venda.service.CanalVendaService;
import br.com.nuvy.api.venda.service.CategoriaAnuncioService;
import br.com.nuvy.api.venda.service.IntegracaoService;
import br.com.nuvy.api.venda.service.TabelaPrecoService;
import br.com.nuvy.client.services.HubService;
import br.com.nuvy.common.base.filter.NoFilter;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.config.BadgeContext;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CanalVendaDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.CatalogoProdutoDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.EmpresaResumoDto;
import br.com.nuvy.facade.anuncio.an1catalogo.model.IntegracaoDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.AnuncioOutDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.CategoriaAnuncioDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.Fn2Dto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.HistoricoHubOutDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.NopResumoDto;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.ProdutoBuscaHubDtoOut;
import br.com.nuvy.facade.anuncio.an2anunciomarketplace.model.SincronizaProdutoHubDtoIn;
import br.com.nuvy.facade.cadastro.cd15categoriaprodutos.model.CategoriaProdutoDto;
import br.com.nuvy.facade.venda.vd3tabelapreco.model.TabelaPrecoDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AN2AnuncioMarketplaceService {

  private final ApplicationEventPublisher eventPublisher;

  private final CanalVendaService canalVendaService;
  private final CategoriaAnuncioService categoriaAnuncioService;
  private final CategoriaProdutoService categoriaProdutoService;
  private final IntegracaoService integracaoService;
  private final ContaBancariaService contaBancariaService;
  private final TabelaPrecoService tabelaPrecoService;
  private final NopService nopService;
  private final DepositoService depositoService;
  private final PlanoContaService planoContaService;
  private final FormaPagamentoService formaPagamentoService;
  private final EmpresaService empresaService;
  private final CanalOrigemRepository canalOrigemRepository;
  private final CatalogoService catalogoService;
  private final ProdutoService produtoService;
  private final HubService hubService;
  private final AnuncioService anuncioService;
  private final HistoricoHubService historicoHubService;
  private final SaldoService saldoService;

  @Transactional(readOnly = true)
  public Fn2Dto iniciar() {
    return Fn2Dto.builder()
      .tabelasPreco(tabelaPrecoService.findAll().stream().map(TabelaPrecoDto::from)
        .filter(t -> t.getSituacao().equals(Situacao.ATIVO)).toList())
      .nops(nopService.findAll().stream().map(NopResumoDto::from)
        .filter(n -> n.getSituacao().equals(Situacao.ATIVO)).toList())
      .depositos(depositoService.findAll().stream().map(DepositoDto::from).toList())
      .planoContaReceita(planoContaService.findAll().stream().map(PlanoContaDto::from).filter(
          p -> p.getTipo().equals(TipoPlanoConta.RECEITA) && p.getSituacao().equals(Situacao.ATIVO))
        .toList())
      .contasBancarias(contaBancariaService.findAll().stream().map(ContaBancariaResumoDto::from)
        .filter(c -> c.getSituacao().equals(Situacao.ATIVO)).toList())
      .formasPagamento(
        formaPagamentoService.findAll().stream().map(FormaPagamentoDto::from).toList())
      .empresas(empresaService.findAll().stream().map(EmpresaResumoDto::from).toList())
      .canaisOrigem(canalOrigemRepository.findAllBySituacaoTrue())
      .build();
  }

  @Transactional(readOnly = true)
  public Page<CanalVendaDto> findCanaisMarketplace(CanalVendaFilter filter, Pageable pageable) {
    return canalVendaService.find(filter, pageable).map(CanalVendaDto::from);
  }

  @Transactional(readOnly = true)
  public CanalVendaDto findCanalMarketplaceById(Integer id) {
    var result = canalVendaService.findById(id).orElseThrow(ResourceNotFoundException::new);
    return CanalVendaDto.from(result);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer createCanalMarketplace(CanalVendaDto canalVendaDto) {
    CanalVenda canalVenda = canalVendaService.create(canalVendaDto.toEntity());
    if ("NUVY".equals(canalVenda.getOrigem().getIntegrador())) {
      hubService.criarOuAlterarContaCanalVenda(canalVenda);
    }
    eventPublisher.publishEvent(new CanalVendaCriado(
      UUID.randomUUID(),
      UUID.fromString(canalVenda.getAplicacao()),
      canalVenda.getEmpresa().getId(),
      BadgeContext.getUsuario().getId(),
      canalVenda.getId(),
      LocalDateTime.now()
    ));
    return canalVenda.getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateCanalMarketplace(Integer id, CanalVendaDto canalVendaDto) {
    CanalVenda canalVenda = canalVendaService.required(id);
    CanalVenda canalVendaAlterado = canalVendaService.update(
      id, canalVendaDto.toEntity(canalVenda)
    );
    if ("NUVY".equals(canalVenda.getOrigem().getIntegrador())) {
      Marketplace marketplace = hubService.resolveMarketplace(canalVendaAlterado);
      if (!marketplace.isUsaAutenticacao2FA()) {
        hubService.criarOuAlterarContaCanalVenda(canalVendaAlterado);
      }
    }
    eventPublisher.publishEvent(new CanalVendaAlterado(
      UUID.randomUUID(),
      UUID.fromString(canalVenda.getAplicacao()),
      canalVenda.getEmpresa().getId(),
      BadgeContext.getUsuario().getId(),
      canalVenda.getId(),
      new CanalVendaAlterado.Dados(get(canalVenda.getTabelaPreco(), TabelaPreco::getId)),
      new CanalVendaAlterado.Dados(get(canalVendaAlterado.getTabelaPreco(), TabelaPreco::getId)),
      LocalDateTime.now()
    ));
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateSituacaoCanalMarketplace(Integer id, Situacao situacao) {
    var canalVenda = canalVendaService.findById(id).orElseThrow(ResourceNotFoundException::new);
    canalVenda.setSituacao(situacao);
    canalVendaService.update(id, canalVenda);
  }

  @Transactional(readOnly = true)
  public List<CategoriaAnuncioDto> findCategoriasByCanal(Integer id) {
    return categoriaAnuncioService.findByCanalVenda(id).stream().map(CategoriaAnuncioDto::from)
      .toList();
  }

  @Transactional(readOnly = true)
  public CategoriaAnuncioDto findCategoriaById(Integer id) {
    var result = categoriaAnuncioService.findByCategoriaErpId(id)
      .orElseThrow(ResourceNotFoundException::new);
    return CategoriaAnuncioDto.from(result);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer createVinculoCategoria(CategoriaAnuncioDto categoriaAnuncioDto) {
    return categoriaAnuncioService.create(categoriaAnuncioDto.toEntity()).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateVinculoCategoria(Integer id, CategoriaAnuncioDto categoriaAnuncioDto) {
    categoriaAnuncioService.update(id, categoriaAnuncioDto.toEntity());
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void deleteVinculoCategoria(Integer id) {
    categoriaAnuncioService.delete(id);
  }

  @Transactional(readOnly = true)
  public Page<CategoriaProdutoDto> findCategoriasErp(NoFilter filter, Pageable pageable) {
    return categoriaProdutoService.find(filter, pageable).map(CategoriaProdutoDto::from);
  }

  @Transactional(readOnly = true)
  public CategoriaProdutoDto findCategoriaErpById(Integer id) {
    var result = categoriaProdutoService.findById(id).orElseThrow(ResourceNotFoundException::new);
    return CategoriaProdutoDto.from(result);
  }

  @Transactional(readOnly = true)
  public List<IntegracaoDto> findIntegracoes() {
    return integracaoService.findAllBySituacao(Situacao.ATIVO).stream()
      .map(IntegracaoDto::from).toList();
  }

  @Transactional(readOnly = true)
  public IntegracaoDto findIntegracaoById(Integer id) {
    var result = integracaoService.findById(id).orElseThrow(ResourceNotFoundException::new);
    return IntegracaoDto.from(result);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public Integer createIntegracao(IntegracaoDto integracaoDto) {
    return integracaoService.create(integracaoDto.toEntity()).getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void updateIntegracao(Integer id, IntegracaoDto integracaoDto) {
    integracaoService.update(id, integracaoDto.toEntity());
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void deleteIntegracao(Integer id) {
    integracaoService.delete(id);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public UUID createAnuncio(Integer canalVendaId, AnnouncementDto dto) {
    var canalVenda = canalVendaService.findById(canalVendaId)
      .orElseThrow(() -> new ResourceNotFoundException("canal.venda.nao.encontrado"));

    var catalogoProduto = CatalogoProdutoDto.builder()
      .produtoId(Integer.valueOf(dto.getProductId()))
      .deveDesconsiderar(false)
      .build();

    var anuncioErp = anuncioService.criarAnuncio(dto, canalVenda);
    historicoHubService.createHistoricoAnuncio(anuncioErp, CRIADO);

    catalogoService.relacionarProdutos(
      canalVenda.getCatalogo().getId(), catalogoProduto, ADICIONAR
    );
    return anuncioErp.getId();
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void removeAnuncio(Integer canalVendaId, AnnouncementDto dto) {
    var canalVenda = canalVendaService.findById(canalVendaId)
      .orElseThrow(() -> new ResourceNotFoundException("canal.venda.nao.encontrado"));

    var catalogoProduto = CatalogoProdutoDto.builder()
      .produtoId(Integer.valueOf(dto.getProductId()))
      .deveDesconsiderar(false)
      .build();

    var anuncio = anuncioService.findById(UUID.fromString(dto.getIdExterno())).orElse(null);
    if (Objects.nonNull(anuncio)) {
      anuncioService.delete(UUID.fromString(dto.getIdExterno()));
    }

    catalogoService.relacionarProdutos(
      canalVenda.getCatalogo().getId(), catalogoProduto, REMOVER
    );
  }

  @Transactional(readOnly = true)
  public Page<ProdutoResumoDto> buscarProdutosCanalDeVenda(
    Integer canalVendaId, ProdutoDepositoFilter filter, Pageable pageable
  ) {
    filter.setVariacao(false);
    return produtoService.findAllProdutoSaldoPositivoByDepositoId(canalVendaId, filter, pageable);
  }

  @Transactional(readOnly = true)
  public Page<AnuncioOutDto> findAnunciosErpByCanalVenda(Integer canalVendaId, Pageable pageable,
    AnuncioFilter filter) {
    filter.setCanalVendaId(canalVendaId);
    return anuncioService.find(filter, pageable).map(AnuncioOutDto::from);
  }

  @Transactional(readOnly = true)
  public Page<HistoricoHubOutDto> findHistoricoAnuncio(UUID anuncioId, Pageable pageable,
    HistoricoHubFilter filter) {
    filter.setAnuncioId(anuncioId);
    return historicoHubService.find(filter, pageable).map(HistoricoHubOutDto::from);
  }

  @Transactional(readOnly = true)
  public Page<HistoricoHubOutDto> findHistoricoCatalogoCanalVenda(Integer canalVendaId,
    Pageable pageable, HistoricoHubFilter filter) {
    var canalVenda = canalVendaService.findById(canalVendaId)
      .orElseThrow(() -> new ResourceNotFoundException("canal.venda.nao.encontrado"));
    filter.setCatalogoId(canalVenda.getCatalogo().getId());
    return historicoHubService.find(filter, pageable).map(HistoricoHubOutDto::from);
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void editarAnuncio(UUID anuncioId, AcaoHistoricoCatalogo acao) {
    var anuncio = anuncioService.findById(anuncioId)
      .orElseThrow(() -> new ResourceNotFoundException("anuncio.nao.encontrado"));
    historicoHubService.createHistoricoAnuncio(anuncio, acao);
  }

  @Transactional(readOnly = true)
  public BigDecimal buscarSaldoProdutoDeposito(Integer produtoId, Integer depositoId) {
    return saldoService.calculaEstoque(produtoId, depositoId);
  }

  @Transactional(readOnly = true)
  public void sincronizaHub(Integer canalVendaId, TipoSincronizacaoHub tipo) {
    CanalVenda canalVenda = canalVendaService.required(
      canalVendaId, "canal.venda.nao.encontrado"
    );
    canalVendaService.sincronizaHub(canalVenda, tipo);
  }

  @Transactional
  public Page<ProdutoBuscaHubDtoOut> buscarProdutosHub(Integer canalVendaId, Pageable pageable) {
    CanalVenda canalVenda = canalVendaService.required(canalVendaId);
    if (canalVenda.getSituacao() != Situacao.ATIVO) {
      throw new PreconditionException("canal.venda.inativo");
    }

    if (canalVenda.getIntegracao() == null) {
      throw new PreconditionException("canal.venda.sem.integracao");
    }

    return canalVendaService.buscarProdutosHub(canalVendaId, pageable);
  }

  public void sincronizaHubProduto(Integer canalVendaId, SincronizaProdutoHubDtoIn payload) {
    canalVendaService.sincronizaHubProduto(canalVendaId, payload);
  }


}