package br.com.nuvy.facade.cadastro.cd16pos.pospdv;

import br.com.nuvy.api.cadastro.dto.PosPdvProdutoDto;
import br.com.nuvy.api.cadastro.filter.ProdutoFilter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.facade.cadastro.cd11produtos.model.ProdutoAtacadoDtoIn;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.LimiteDescontoDtoIn;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.PosPdvInDto;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.PosPdvOutDto;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.PrecoVendaDtoIn;
import br.com.nuvy.facade.cadastro.cd16pos.produto.ProdutoPosPdvService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "@Cadastro / CD16 - POS/PDV")
@RestController
@RequestMapping(value = "/cadastro/cd16", headers = "empresa")
@RequiredArgsConstructor
public class CD16PosPdvController {

  private final CD16PosPdvService service;
  private final ProdutoPosPdvService produtoPosPdvService;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Ativar Integração pos/pdv",
    description = "Ativar Integração pos/pdv. Informar o id da integração"
  )
  @PostMapping(value = "/integracao/{idIntegracao}")
  @ResponseStatus(HttpStatus.OK)
  public void ativarPosPdv(@PathVariable UUID idIntegracao) {
    discoverer.handleCreatedResource(service.ativarPosPdv(idIntegracao));
  }

  @Operation(
    summary = "Desativa Integração pos/pdv",
    description = "Desativa Integração do pos/pdv. Informar o id do pos/pdv."
  )
  @DeleteMapping(value = "/integracao/{idPosPdv}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void desativar(@PathVariable UUID idPosPdv) {
    service.desativar(idPosPdv);
  }

  @Operation(
    summary = "Atualizar Integração pos/pdv",
    description = "Atualizar Integração pos/pdv. Informar o id do pos/pdv."
  )
  @PutMapping(value = "/integracao/{idPosPdv}")
  @ResponseStatus(HttpStatus.OK)
  public void alterar(@PathVariable UUID idPosPdv, @RequestBody @Valid PosPdvInDto posPdvDto) {
    service.alterar(idPosPdv, posPdvDto);
  }

  @Operation(summary = "Busca integração pos/pdv",
    description = "Busca integração pos/pdv pelo id"
  )
  @GetMapping(value = "/integracao/{idPosPdv}")
  @ResponseStatus(HttpStatus.OK)
  public PosPdvOutDto obter(@PathVariable UUID idPosPdv) {
    return service.obter(idPosPdv);
  }

  @Operation(
    summary = "Sincroniza pos/pdv",
    description = "Sincroniza pos/pdv. Informar o id do pos/pdv."
  )
  @PostMapping(value = "/integracao/{idPosPdv}/sincroniza")
  public void sincronizaPosPdv(@PathVariable UUID idPosPdv) {
    service.sincronizaPosPdv(idPosPdv);
  }

  @Operation(
    summary = "Permite buscar os produtos que estão no POS/PDV",
    description = "Permite buscar os produtos que estão em um POS/PDV"
  )
  @GetMapping(path = "/integracao/{idPosPdv}/produtos", headers = "empresa")
  public List<PosPdvProdutoDto> findAllForPosPdv(
    @PathVariable UUID idPosPdv,
    @ParameterObject ProdutoFilter filter,
    @ParameterObject Pageable pageable
  ) {
    Page<PosPdvProdutoDto> produtos = produtoPosPdvService.findAllProdutosPosPdv(
      idPosPdv, filter, pageable
    );
    return discoverer.handlePaginatedResults(pageable, produtos);
  }

  @Operation(
    summary = "Alterar preço de venda",
    description = "Alterar preço de venda do produto. Informar o id do pdv e id do produto"
  )
  @PatchMapping(value = "/integracao/{idPosPdv}/preco-venda/{idProduto}")
  public void alterarPrecoVenda(
    @PathVariable UUID idPosPdv,
    @PathVariable Integer idProduto,
    @RequestBody @Valid PrecoVendaDtoIn precoVendaDto
  ) {
    produtoPosPdvService.alterarPrecoVenda(idPosPdv, idProduto, precoVendaDto);
  }

  @Operation(
    summary = "Alterar limite desconto PDVx",
    description = "Alterar de desconto do produto. Informar o id do pdv e id do produto"
  )
  @PatchMapping(value = "/integracao/{idPosPdv}/limite-desconto/{idProduto}")
  public void alterarLimiteDesconto(
    @PathVariable UUID idPosPdv,
    @PathVariable Integer idProduto,
    @RequestBody @Valid LimiteDescontoDtoIn limiteDescontoDtoIn
  ) {
    produtoPosPdvService.alterarLimiteDesconto(idPosPdv, idProduto, limiteDescontoDtoIn);
  }

  @Operation(
    summary = "Alterar venda atacado",
    description = "Alterar venda atacado. Informar o id do pos/pdv."
  )
  @PatchMapping(value = "/integracao/{idPosPdv}/atacado/{idProduto}")
  public void alterarVendaAtacado(
    @PathVariable UUID idPosPdv,
    @PathVariable Integer idProduto,
    @RequestBody @Valid ProdutoAtacadoDtoIn produtoAtacadoDto
  ) {
    produtoPosPdvService.alterarVendaAtacado(idPosPdv, idProduto, produtoAtacadoDto);
  }
}
