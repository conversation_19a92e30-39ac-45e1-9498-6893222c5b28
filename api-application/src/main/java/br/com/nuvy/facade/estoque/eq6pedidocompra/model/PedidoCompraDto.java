package br.com.nuvy.facade.estoque.eq6pedidocompra.model;

import br.com.nuvy.api.pedidocompra.model.PedidoCompra;
import br.com.nuvy.api.pedidocompra.model.SituacaoPedidoCompra;
import br.com.nuvy.common.base.dto.Dto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
public class PedidoCompraDto implements Dto<PedidoCompra> {

  private Long id;
  private UUID uuid;

  @Builder.Default
  private SituacaoPedidoCompra situacao = SituacaoPedidoCompra.RASCUNHO;
  private Long numeroPedido;

  private LocalDate dataSolicitacao;
  private LocalDate previsaoEntrega;

  @NotNull
  private Integer fornecedorId;
  private String fornecedorNome;
  private String fornecedorCpfCnpj;

  private Integer planoContaId;

  @JsonIgnore
  private String aplicacao;
  private Integer empresaId;
  private String empresaNome;

  @Valid
  private List<PedidoCompraItemDto> itens;

}