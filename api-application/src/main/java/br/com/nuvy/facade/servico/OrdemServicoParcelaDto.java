package br.com.nuvy.facade.servico;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrdemServicoParcelaDto {

  private Integer contaCorrenteId;
  private Integer formaRecebimentoId;
  private Integer numero;
  private BigDecimal valor;
  private LocalDateTime vencimento;
  private BigDecimal valorBruto;

}
