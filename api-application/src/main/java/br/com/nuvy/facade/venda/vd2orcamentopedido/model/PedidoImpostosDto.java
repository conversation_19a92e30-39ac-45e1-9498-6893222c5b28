package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedidoImpostosDto {

  /*private ModalidadeBaseCalculo modalidadeBaseCalculo;
  private BigDecimal aliquotaIcms;
  private BigDecimal aliquotaCreditoIcms;
  private BigDecimal aliquotaIcmsInterna;
  private BigDecimal aliquotaFcp;
  private BigDecimal aliquotaIpi;
  private BigDecimal aliquotaPis;
  private BigDecimal aliquotaCofins;
  private BigDecimal percentualBaseCalculoIcms;
  private BigDecimal percentualBaseCalculoIcmsPropria;
  private BigDecimal percentualMva;
  private BigDecimal percentualBaseCalculoSt;
  private BigDecimal percentualBaseCalculoIpi;
  private BigDecimal percentualBaseCalculoPis;
  private BigDecimal percentualBaseCalculoCofins;
  private BigDecimal percentualBaseCalculoDifal;
  private BigDecimal percentualFcpDifal;

  private BigDecimal valorIcmsItem;
  private BigDecimal valorIcmsStItem;
  private BigDecimal valorIpiItem;
  private BigDecimal valorPisItem;
  private BigDecimal valorCofinsItem;
  private BigDecimal valorFcpItem;
  private BigDecimal valorFcpStItem;

  private BigDecimal valorBaseCalculoIcmsItem;
  private BigDecimal valorBaseCalculoIcmsStItem;
  private BigDecimal valorBaseCalculoIpiItem;
  private BigDecimal valorBaseCalculoPisItem;
  private BigDecimal valorBaseCalculoCofinsItem;
  private BigDecimal valorBaseCalculoFcpItem;
  private BigDecimal valorCreditoSimplesNacionalItem;*/

  private BigDecimal valorIcms;
  private BigDecimal valorIcmsSt;
  private BigDecimal valorIpi;
  private BigDecimal valorPis;
  private BigDecimal valorCofins;
  private BigDecimal valorFcp;
  private BigDecimal valorFcpSt;

  private BigDecimal valorBaseCalculoIcms;
  private BigDecimal valorBaseCalculoIcmsSt;
  private BigDecimal valorBaseCalculoIpi;
  private BigDecimal valorBaseCalculoPis;
  private BigDecimal valorBaseCalculoCofins;
  private BigDecimal valorBaseCalculoFcp;
  private BigDecimal valorBaseCalculoCreditoIcmsSimplesNacional;
  private BigDecimal valorCreditoIcmsSimplesNacional;

}
