package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.enums.ModalidadeBaseCalculo;
import br.com.nuvy.api.enums.ModalidadeBaseCalculoSt;
import br.com.nuvy.api.enums.ModalidadeTipoCalculo;
import br.com.nuvy.api.enums.MotivoDesoneracaoIcms;
import br.com.nuvy.api.enums.TipoCalculoImposto;
import br.com.nuvy.api.enums.TipoRegimeTributario;
import br.com.nuvy.api.enums.TipoRegraCalculo;
import br.com.nuvy.api.venda.model.PedidoItem;
import br.com.nuvy.api.venda.model.PedidoItemEstoque;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.common.validator.Fci;
import br.com.nuvy.validation.groups.UnidadeTributavelGroup;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(callSuper = true)
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
// @GroupSequenceProvider(PedidoItemGroupSequenceProvider.class)
public class PedidoItemDto implements Dto<PedidoItem> {

  private UUID id;
  private UUID uuid;
  private String codigo;
  private String observacao;
  private BigDecimal quantidade;
  private BigDecimal precoUnitario;
  private BigDecimal precoTotal;
  private BigDecimal valorDesconto;
  private BigDecimal percentualDesconto;
  private BigDecimal valorTotal;
  private BigDecimal valorFrete;
  private BigDecimal valorSeguro;
  private BigDecimal valorOutrasDespesas;
  private BigDecimal valorTotalOutrasDespesas;
  private BigDecimal pesoBrutoTotalItems;
  private BigDecimal pesoLiquidoTotalItems;
  @Fci
  private String fci;
  @Builder.Default
  private Boolean pedidoCompraCliente = false;
  private String numeroPedidoCompraCliente;
  private String numeroItemPedidoCompraCliente;
  @Builder.Default
  private Boolean informarUnidadeQuantidadeTributavel = false;
  @NotNull(groups = UnidadeTributavelGroup.class)
  private BigDecimal quantidadeTributavel;
  @NotNull(groups = UnidadeTributavelGroup.class)
  private BigDecimal valorTributavel;
  private String eanTributavel;
  @Builder.Default
  private Boolean baixaEstoque = true;
  @Builder.Default
  private Boolean contaReceber = true;
  @NotNull
  private Integer produtoId;
  private UUID produtoUuid;
  private String produtoCodigo;
  @JsonProperty(access = Access.READ_ONLY)
  private String produtoDescricao;
  @JsonProperty(access = Access.READ_ONLY, value = "produtoUnidadeId")
  private Integer produtoUnidadeMedidaId;
  @JsonProperty(access = Access.READ_ONLY, value = "produtoUnidadeSigla")
  private String produtoUnidadeMedidaUnidade;
  private Integer kitId;
  private String kitCodigo;
  private String kitDescricao;
//  private Integer produtoPaiId;
//  @JsonProperty(access = Access.READ_ONLY)
//  private String produtoPaiDescricao;
  private Integer tabelaPrecoId;
  @JsonProperty(access = Access.READ_ONLY)
  private String tabelaPrecoNome;
  private Integer tipoReceitaId;
  @JsonProperty(access = Access.READ_ONLY)
  private String tipoReceitaNome;
  @NotNull(groups = UnidadeTributavelGroup.class)
  private Integer unidadeTributavelId;
  @JsonProperty(access = Access.READ_ONLY)
  private String unidadeTributavelDescricao;
  @NotNull
  private Integer nopId;
  private String nopNome;
  private String nopDescricao;
  private TipoRegimeTributario nopRegimeTributarioTipo;
  private List<PedidoItemEstoqueDto> itemsEstoque;
  private String cfopCodigo;
  private String cfopDescricao;
  private BigDecimal precoUnitarioBase;
  // MODAL ICMS
  private String csosnIcmsId;
  private String csosnIcmsDescricao;
  private String cstIcmsId;
  private String cstIcmsDescricao;
  private ModalidadeBaseCalculo modalidadeBaseCalculoIcms;
  private BigDecimal aliquotaFcpIcms;
  private BigDecimal percentualReducaoBaseCalculoIcms;
  private BigDecimal percentualDiferimentoIcms;
  private BigDecimal valorDiferimentoIcms;
  private BigDecimal aliquotaIcms;
  private BigDecimal valorIcms;
  private BigDecimal valorDesoneracaoIcms;
  private MotivoDesoneracaoIcms motivoDesoneracaoIcms;
  private BigDecimal valorBaseCalculoIcms;
  private BigDecimal valorFcpIcms;
  private BigDecimal valorBaseCalculoFcpIcms;
  @Builder.Default
  private Boolean impostoIcmsCusto = false;

  // MODAL ICMS ST
  private ModalidadeBaseCalculoSt modalidadeBaseCalculoIcmsSt;
  @JsonProperty("modalidadeTipoCalculoIcmsSt")
  private ModalidadeTipoCalculo modalidadeTipoCalculoIcmsSt;
  private BigDecimal aliquotaInterestadualIcmsSt;

  @JsonProperty("mva")
  private BigDecimal percentualMva;
  private BigDecimal aliquotaFcpSt;
  private BigDecimal aliquotaInternaIcmsSt;
  private BigDecimal percentualReducaoBaseCalculoIcmsSt;
  private BigDecimal valorBaseCalculoIcmsSt;
  private BigDecimal valorIcmsSt;
  private BigDecimal valorBaseCalculoFcpSt;
  private BigDecimal valorFcpSt;
  @Builder.Default
  private Boolean impostoIcmsStCusto = false;
  // MODAL IPI
  private String cstIpiCodigo;
  private String cstIpiDescricao;
  private TipoCalculoImposto tipoCalculoIpi;
  private BigDecimal aliquotaIpi;
  private BigDecimal valorBaseCalculoIpi;
  private BigDecimal valorIpi;
  private String enquadramentoIpiCodigo;
  private String enquadramentoIpiDescricao;
  @Builder.Default
  private Boolean impostoIpiCusto = false;
  // MODAL PIS
  private String cstPisCodigo;
  private String cstPisDescricao;
  private TipoCalculoImposto tipoCalculoPis;
  private BigDecimal aliquotaPis;
  private BigDecimal reducaoBaaseCalculoPis;
  private BigDecimal valorBaseCalculoPis;
  private BigDecimal quantidadeUnidadeTributavelPis;
  private BigDecimal valorUnidadeTributavelPis;
  private BigDecimal valorPis;
  @Builder.Default
  private Boolean impostoPisCusto = false;
  // Modal COFINS
  private String cstCofinsCodigo;
  private String cstCofinsDescricao;
  private TipoCalculoImposto tipoCalculoCofins;
  private BigDecimal aliquotaCofins;
  private BigDecimal reducaoBaaseCalculoCofins;
  private BigDecimal valorBaseCalculoCofins;
  private BigDecimal quantidadeUnidadeTributavelCofins;
  private BigDecimal valorUnidadeTributavelCofins;
  private BigDecimal valorCofins;
  @Builder.Default
  private Boolean impostoCofinsCusto = false;
  // MODAL ICMS DIFAL
  private TipoRegraCalculo tipoCalculoIcmsDifal;
  private BigDecimal percentualFcpIcmsDifal;
  private BigDecimal aliquotaInternaUfDestinaoIcmsDifal;
  private BigDecimal aliquotaInterestadualIcmsDifal;
  private BigDecimal percentualReducaoBaseCalculoDestinoIcmsDifal;
  private BigDecimal percentualInternaProdutoEstrangeiroIcmsDifal;
  private BigDecimal valorBaseCalculoIcmsDifal;
  private BigDecimal valorIcmsDifal;
  private BigDecimal valorBaseCalculoFcpIcmsDifal;
  private BigDecimal valorFcpIcmsDifal;
  @Builder.Default
  private Boolean impostoIcmsDifalCusto = false;

  private BigDecimal valorCreditoSimplesNacional;

  // OUTRAS NOTAS
  @Builder.Default
  private Boolean devolveIpi = false;
  private BigDecimal percentualMercadoriaDevolvida;
  private BigDecimal valorIpiDevolvido;
  private BigDecimal quantidadeDevolvida;

  private String descricaoComercial;
  private UUID produtoMarketPlaceId;
  private String produtoMarketPlaceCodigo;

  //AUXILIARES
  private Integer ordem;

  public boolean isBaixaEstoque() {
    return baixaEstoque != null && baixaEstoque;
  }

  public boolean isContaReceber() {
    return contaReceber != null && contaReceber;
  }

  public BigDecimal getValorDescontoOrZero() {
    return valorDesconto != null ? valorDesconto : BigDecimal.ZERO;
  }

  @Override
  public PedidoItem toEntity() {
    List<PedidoItemEstoque> itemsEstoque = new ArrayList<>();
    if (getItemsEstoque() != null) {
      for (var itemEstoqueDto : getItemsEstoque()) {
        var item = itemEstoqueDto.toEntity();
        itemsEstoque.add(item);
      }
    }
    var pedidoItem = ObjectUtils.convert(this, PedidoItem.class);
    pedidoItem.setItemsEstoque(itemsEstoque);

    if (this.getItemsEstoque() != null) {
      for (PedidoItemEstoque itemEstoque : pedidoItem.getItemsEstoque()) {
        itemEstoque.setPedidoItem(pedidoItem);
      }
    }
    return pedidoItem;
  }
}
