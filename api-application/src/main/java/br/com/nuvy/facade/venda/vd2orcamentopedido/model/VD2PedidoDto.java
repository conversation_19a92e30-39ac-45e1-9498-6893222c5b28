package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import java.util.List;

import br.com.nuvy.api.cadastro.dto.CondicaoPagamentoDto;
import br.com.nuvy.api.cadastro.dto.EnquadramentoIpiDto;
import br.com.nuvy.api.cadastro.dto.UnidadeMedidaDto;
import br.com.nuvy.api.enums.IndicadorPresenca;
import br.com.nuvy.api.enums.TipoIntermediador;
import br.com.nuvy.api.enums.TipoRegimeTributario;
import br.com.nuvy.api.financeiro.dto.CentroCustoCaracteristicaDto;
import br.com.nuvy.api.financeiro.dto.ContaBancariaResumoDto;
import br.com.nuvy.api.financeiro.dto.PlanoContaCaracteristicaDto;
import br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model.CsosnIcmsDto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstCofinsDto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstIcmsDto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstIpiDto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstPisDto;
import br.com.nuvy.facade.estoque.eq2eq3movimentoestoque.model.RastreabilidadeOutDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VD2PedidoDto {

  public TipoRegimeTributario tipoRegimeTributario;

  public List<NopDto> nopList;
  public List<PlanoContaCaracteristicaDto> tiposReceitaList;
  public List<CentroCustoCaracteristicaDto> centroCustoList;
  public List<DepositoResumoDto> depositoList;
  public List<UnidadeMedidaDto> unidadeTributavelList;
  public List<TipoFreteDto> tipoFreteList;
  public List<TipoVeiculoDto> tipoVeiculoList;
  public List<TipoCarroceriaDto> tipoCarroceriaList;
  public List<ContaBancariaResumoDto> contaCorrenteList;
  public List<CondicaoPagamentoDto> condicaoRecebimentoList;
  public List<FormaPagamentoDto> formaRecebimentoList;
  public List<IndicadorPresenca> indicadorPresencaList;
  public List<TipoIntermediador> tipoIntermediadorList;
  private List<CsosnIcmsDto> csosnIcmsList;
  private List<CstIcmsDto> cstIcmsList;
  private List<EnquadramentoIpiDto> enquadramentoIpiList;
  private List<RastreabilidadeOutDto> rastreabilidadeList;
  private List<CstPisDto> cstPisList;
  private List<CstCofinsDto> cstCofinsList;
  private List<CstIpiDto> cstIpiList;
}
