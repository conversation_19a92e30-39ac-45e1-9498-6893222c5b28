package br.com.nuvy.facade.venda.vd2orcamentopedido.model;

import br.com.nuvy.api.cadastro.model.EnderecoPessoa;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransportadoraEnderecoDto implements Dto<EnderecoPessoa> {

  private Integer id;
  private String uf;
  private String pais;
  private String ufNome;

  public static TransportadoraEnderecoDto from(EnderecoPessoa enderecoPessoa) {
    return ObjectUtils.convert(enderecoPessoa, TransportadoraEnderecoDto.class);
  }
}
