package br.com.nuvy.facade.cadastro.cd16pos.events;

import br.com.nuvy.api.cadastro.model.PosPdv;
import br.com.nuvy.api.cadastro.repository.PosPdvRepository;
import br.com.nuvy.api.integration.factory.PosPdvIntegradorFactory;
import br.com.nuvy.common.exception.PreconditionException;
import br.com.nuvy.facade.cadastro.cd16pos.pospdv.model.PosPdvEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class PosPdvListener implements ApplicationListener<PosPdvEvent> {

    private final PosPdvRepository posPdvRepository;
    private final PosPdvIntegradorFactory posPdvIntegradorFactory;

    @Override
    @Transactional
    public void onApplicationEvent(PosPdvEvent event) {
        var posPdv = posPdvRepository.findById(event.getPosPdv().getId())
            .orElseThrow(() -> new PreconditionException("PosPdv não encontrado"));
        posPdv.setDepositoIdOld(event.getPosPdv().getDepositoIdOld());
        posPdv.setNopIdOld(event.getPosPdv().getNopIdOld());
        log.info("Evento de PosPdv recebido: {}", posPdv);
        if (posPdv.isAtivo()) {
            sincronizarPosPdv(posPdv);
        }
    }

    public void sincronizarPosPdv(PosPdv posPdv) {
        log.info("Sincronizando PosPdv: {}", posPdv);
        posPdvIntegradorFactory.getPosPdvIntegrador(posPdv).sincronizaPosPdv(posPdv);
    }
}