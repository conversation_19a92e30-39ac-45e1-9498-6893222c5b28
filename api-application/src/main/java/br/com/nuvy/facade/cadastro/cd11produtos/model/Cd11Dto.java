package br.com.nuvy.facade.cadastro.cd11produtos.model;

import br.com.nuvy.api.cadastro.dto.OrigemMercadoriaDto;
import br.com.nuvy.api.cadastro.dto.ResumoCadastroProdutosDto;
import br.com.nuvy.api.cadastro.dto.TipoMercadoriaDto;
import br.com.nuvy.api.cadastro.dto.UnidadeMedidaDto;
import lombok.Builder;
import lombok.Value;

import java.util.List;

@Value
@Builder
public class Cd11Dto {
  private List<UnidadeMedidaDto> unidadesMedida;
  private List<TipoMercadoriaDto> tipoMercadorias;
  private List<OrigemMercadoriaDto> origemMercadorias;
  private ResumoCadastroProdutosDto resumoProdutos;
}
