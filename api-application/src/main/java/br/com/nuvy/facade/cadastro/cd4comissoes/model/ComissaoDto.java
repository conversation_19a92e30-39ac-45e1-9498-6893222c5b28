package br.com.nuvy.facade.cadastro.cd4comissoes.model;

import br.com.nuvy.api.venda.model.Comissao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.Column;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComissaoDto implements Dto<Comissao> {

  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private Integer id;
  private String nome;
  private Boolean icms;
  private Boolean ipi;
  private Boolean pis;
  private Boolean cofins;
  private Boolean icmsSt;
  private Boolean frete;
  private Boolean seguro;
  private Boolean outros;

  private Boolean issServico;
  private Boolean irServico;
  private Boolean inssServico;
  private Boolean pisServico;
  private Boolean cofinsServico;
  private Boolean csllServico;
  private Boolean descontosServico;

  @Digits(integer = 1, fraction = 9)
  @PositiveOrZero(message = "comissao.deve.ser.maior.que.zero")
  private BigDecimal percentualComissao;

  public static ComissaoDto from(Comissao entity) {
    return ObjectUtils.convert(entity, ComissaoDto.class);
  }
}
