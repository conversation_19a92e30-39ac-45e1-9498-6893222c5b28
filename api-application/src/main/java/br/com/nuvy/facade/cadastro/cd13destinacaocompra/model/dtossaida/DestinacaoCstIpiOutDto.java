package br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.dtossaida;

import br.com.nuvy.api.compras.model.DestinacaoCstIpi;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.facade.cadastro.cd13destinacaocompra.model.CstIpiDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DestinacaoCstIpiOutDto implements Dto<DestinacaoCstIpi> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;
  private CstIpiDto cstIpiOrigem;
  private CstIpiDto cstIpiEntrada;
  private CstIpiDto cstIpiDevolucao;
}
