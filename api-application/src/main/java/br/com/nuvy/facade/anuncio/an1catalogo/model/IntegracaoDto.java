package br.com.nuvy.facade.anuncio.an1catalogo.model;

import br.com.nuvy.api.anuncio.Integracao;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoIntegracao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class IntegracaoDto implements Dto<Integracao> {


  private Integer id;
  private Situacao situacao;
  private String nome;
  private String urlApi;
  private TipoIntegracao tipoIntegracao;
  private Integer origemId;

  public static IntegracaoDto from(Integracao integracao) {
    return ObjectUtils.convert(integracao, IntegracaoDto.class);
  }
}
