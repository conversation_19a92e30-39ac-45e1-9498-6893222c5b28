package br.com.nuvy.facade.venda.vd3tabelapreco;

import static org.springframework.web.bind.annotation.RequestMethod.HEAD;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.venda.filter.TabelaPrecoFilter;
import br.com.nuvy.common.discoverability.component.DiscovererComponent;
import br.com.nuvy.common.exception.ResourceConflictException;
import br.com.nuvy.facade.venda.vd3tabelapreco.model.TabelaPrecoDto;
import br.com.nuvy.facade.venda.vd3tabelapreco.model.TabelaPrecoResumoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "@Venda / VD3 - Tabela de Preços")
@RestController
@RequestMapping(value = "/venda/vd3")
@RequiredArgsConstructor
public class VD3TabelaPrecoController {

  private final VD3TabelaPrecoService service;
  private final DiscovererComponent discoverer;

  @Operation(
    summary = "Permite listar tabelas de preços para a tela VD3",
    description = "É a listagem completa de tabelas de preços com os campos necessários para a tela VD3"
  )
  @GetMapping(value = "/tabela-preco")
  @ResponseStatus(HttpStatus.OK)
  public List<TabelaPrecoResumoDto> findTabelaPreco(@ParameterObject TabelaPrecoFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findTabelaPreco(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite listar tabelas de preços para a tela VD3",
    description = "É a listagem completa de tabelas de preços com os campos necessários para a tela VD3"
  )
  @GetMapping(value = "/tabela-preco/consultar")
  @ResponseStatus(HttpStatus.OK)
  public List<TabelaPrecoResumoDto> findTabelaPrecoConsultas(@ParameterObject TabelaPrecoFilter filter,
    @ParameterObject Pageable pageable) {
    var result = service.findTabelaPrecoConsultas(filter, pageable);
    discoverer.handlePaginatedResults(pageable, result);
    return result.getContent();
  }

  @Operation(
    summary = "Permite buscar uma tabela de preço por ID",
    description = "É a busca de uma tabela de preço por ID para o facade"
  )
  @GetMapping(value = "/tabela-preco/{id}")
  @ResponseStatus(HttpStatus.OK)
  public TabelaPrecoDto findTabelaPrecoById(@PathVariable Integer id) {
    return service.findById(id);
  }

  @Operation(
    summary = "Permite criar uma nova Tabela de Preço",
    description = "É a criação de uma nova Tabela de Preço"
  )
  @PostMapping(value = "/tabela-preco")
  @ResponseStatus(HttpStatus.CREATED)
  public void createTabelaPreco(@RequestBody @Valid TabelaPrecoDto payload) {
    var id = service.createTabelaPreco(payload);
    discoverer.handleCreatedResource(id);
  }


  @Operation(
    summary = "Permite atualizar uma Tabela de Preço",
    description = "É a atualização de uma Tabela de Preço"
  )
  @PutMapping(value = "/tabela-preco/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateTabelaPreco(@PathVariable Integer id, @RequestBody TabelaPrecoDto payload) {
    service.updateTabelaPreco(id, payload);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Permite atualizar a situação de uma Tabela de Preço",
    description = "É a atualização de situação de uma Tabela de Preço"
  )
  @PatchMapping(value = "/tabela-preco/{id}")
  @ResponseStatus(HttpStatus.OK)
  public void updateStatusTabelaPreco(@PathVariable Integer id, @RequestParam Situacao situacao) {
    var tabelapreco = service.findById(id);
    tabelapreco.setSituacao(situacao);
    service.updateTabelaPreco(id, tabelapreco);
    discoverer.handleUpdatedResource(id);
  }

  @Operation(
    summary = "Permite excluir uma Tabela de Preço",
    description = "É a exclusão de uma Tabela de Preço"
  )
  @DeleteMapping(value = "/tabela-preco/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteTabelaPreco(@PathVariable Integer id) {
    service.deleteTabelaPreco(id);
  }

  @Operation(
    summary = "Permite verificar se um nome já está sendo utilizado",
    description = "Verifica se um nome está sendo utilizado por outra tabela de preço e se sim, retorna um erro"
  )
  @RequestMapping(method = HEAD, value = "/exists")
  @ResponseStatus(value = HttpStatus.OK)
  public void tabelaExist(@RequestParam String nomeTabela) {
    if (service.existsByNome(nomeTabela)) {
      throw new ResourceConflictException("tabela.preco.existente");
    }
  }
}
