package br.com.nuvy.facade.relatorio.rl3vendas.model;

import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoPedido;
import br.com.nuvy.common.base.filter.Filter;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.List;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PedidoRelatorioFilter implements Filter {

  @JsonProperty("empresaId")
  private List<Integer> empresasId;
  private LocalDate dataPedidoInicial;
  private LocalDate dataPedidoFinal;
  private LocalDate dataFaturamentoInicial;
  private LocalDate dataFaturamentoFinal;
  private List<SituacaoPedido> situacaoPedido;
  private TipoPedido tipoPedido;

  @Hidden
  private Boolean excluido;

}
