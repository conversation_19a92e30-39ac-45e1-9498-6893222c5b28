package br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model;

import java.util.Set;

import com.fasterxml.jackson.annotation.JsonProperty;

import br.com.nuvy.api.cadastro.model.nop.RegraIcmsItemExcecao;
import br.com.nuvy.api.enums.TipoRegraExcecao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.facade.cadastro.cd10naturezaoperacao.model.validation.ValidaExcecaoIcms;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ValidaExcecaoIcms
public class RegraIcmsItemExcecaoDto extends RegraIcmsItemDto implements Dto<RegraIcmsItemExcecao> {

  private Integer id;

  private TipoRegraExcecao tipoExcecao;

  private Integer produtoId;

  private String produtoCodigo;

  private String produtoDescricao;

  private String ncmId;

  private String ncmDescricao;

  @JsonProperty("cfopCodigo")
  private String variacaoCfopCodigo;

  @JsonProperty("cfopDescricao")
  private String variacaoCfopDescricao;

  @JsonProperty("cfopContribuinteCodigo")
  private String variacaoCfopConsumidorFinalCodigo;

  @JsonProperty("cfopContribuinteDescricao")
  private String variacaoCfopConsumidorFinalDescricao;

  @JsonProperty("cfopNaoContribuinteCodigo")
  private String variacaoCfopConsumidorFinalNcCodigo;

  @JsonProperty("cfopNaoContribuinteDescricao")
  private String variacaoCfopConsumidorFinalNcDescricao;

  @Singular
  private Set<String> ufs;
}
