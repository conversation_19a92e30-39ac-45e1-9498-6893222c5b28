package br.com.nuvy.reguacomunicacao.aplicacao.servico;

import br.com.nuvy.reguacomunicacao.aplicacao.comando.InsereHistoricoReguaCobranca;
import br.com.nuvy.reguacomunicacao.aplicacao.porta.saida.persistencia.RepositorioHistoricoReguaComunicacao;
import br.com.nuvy.reguacomunicacao.aplicacao.processador.ProcessadorHistoricoReguaComunicacao;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

@Service
class ServicoReguaComunicacao implements ProcessadorHistoricoReguaComunicacao {

  private final RepositorioHistoricoReguaComunicacao repositorioHistorico;

  private final TransactionTemplate transactions;

  ServicoReguaComunicacao(
    RepositorioHistoricoReguaComunicacao repositorioHistorico,
    PlatformTransactionManager transactionManager
  ) {
    this.repositorioHistorico = repositorioHistorico;

    var transactionDefinition = new DefaultTransactionDefinition();
    transactionDefinition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
    transactionDefinition.setReadOnly(false);
    this.transactions = new TransactionTemplate(transactionManager, transactionDefinition);
  }

  @Override
  public void processaComando(InsereHistoricoReguaCobranca comando) {
    transactions.executeWithoutResult(st -> {
      repositorioHistorico.insere(comando.criaEntidadeInserir());
    });
  }
}
