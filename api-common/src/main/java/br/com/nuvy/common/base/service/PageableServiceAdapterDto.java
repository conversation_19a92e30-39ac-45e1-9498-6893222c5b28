package br.com.nuvy.common.base.service;

import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.base.entity.Entidade;
import br.com.nuvy.common.base.filter.Filter;
import br.com.nuvy.common.base.repository.NuvyRepository;
import br.com.nuvy.common.exception.InternalServerErrorException;
import br.com.nuvy.common.exception.OperacaoNaoSuportadaException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.common.utils.ObjectUtils;
import com.google.common.reflect.TypeToken;
import java.io.Serializable;
import java.text.Collator;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

@SuppressWarnings({"SpringJavaInjectionPointsAutowiringInspection", "java:S1172", "java:S2201",
  "unchecked"})
public abstract class PageableServiceAdapterDto<E extends Entidade<I>, D extends Dto<E>, I extends Serializable, F extends Filter, R extends NuvyRepository<E, I>> implements
  PageableServiceDto<E, D, I, F> {

  @Autowired
  protected R repository;

  protected Specification<E> configureSpecification(F filter) {
    return (root, query, criteriaBuilder) -> PredicateBuilder.create(criteriaBuilder).and();
  }

  protected Specification<E> configureSpecification(String criterio) {
    return (root, query, criteriaBuilder) -> PredicateBuilder.create(criteriaBuilder).and();
  }

  @Transactional
  protected E beforeDelete(E entity) {
    return entity;
  }

  @Transactional
  protected void afterDelete(E entity) {
  }

  @Deprecated
  @Transactional
  protected E beforeCreateDto(D dto, E entity) {
    return beforeCreate(entity);
  }

  @Deprecated
  @Transactional
  protected E afterCreateDto(D dto, E entity) {
    return afterCreate(entity);
  }

  @Transactional
  protected E beforeCreate(E entity) {
    return entity;
  }

  @Transactional
  protected E afterCreate(E entity) {
    return entity;
  }

  @Transactional
  protected E beforeUpdate(E oldEntity, E newEntity) {
    return newEntity;
  }

  @Transactional
  protected E afterUpdate(E entity) {
    return entity;
  }

  @Transactional
  protected E save(E entity) {
    return repository.save(entity);
  }

  public List<E> findAll() {
    return repository.findAll();
  }

  @Override
  public Page<E> find(F filter, Pageable pageable) {
    Specification<E> specification = configureSpecification(filter);
    return repository.findAll(specification, pageable);
  }

  @Override
  public List<E> find(F filter) {
    Specification<E> specification = configureSpecification(filter);
    return repository.findAll(specification);
  }

  @Override
  public Page<E> find(String criterio, Pageable pageable) {
    Specification<E> specification = configureSpecification(criterio);
    return repository.findAll(specification, pageable);
  }

  @Override
  public Optional<E> findById(I id) {
    return repository.findById(id);
  }

  @Override
  public boolean exists(F filter) {
    Specification<E> specification = configureSpecification(filter);
    return repository.count(specification) > 0;
  }

  @Override
  public boolean notExists(F filter) {
    return !exists(filter);
  }

  @Transactional
  @Override
  public E create(E entity) {
    if (entity.getId() != null) {
      throw new OperacaoNaoSuportadaException("operacao.nao.suportada");
    }
    entity = beforeCreate(entity);
    entity = save(entity);
    entity = afterCreate(entity);
    return entity;
  }

  @Transactional
  @Override
  public E update(I id, E entity) {
    System.out.println("### PageableServiceAdapterDto.update() chamado para ID: " + id);
    return this.findById(id).map(oldEntity -> {
      E localEntity = entity;
      System.out.println("### Chamando beforeUpdate na classe base");
      localEntity = beforeUpdate(oldEntity, localEntity);
      localEntity.setId(id);
      System.out.println("### Salvando entidade na classe base");
      localEntity = save(localEntity);
      System.out.println("### Chamando afterUpdate na classe base");
      localEntity = afterUpdate(localEntity);
      System.out.println("### Retornando entidade atualizada da classe base");
      return localEntity;
    }).orElseThrow(ResourceNotFoundException::new);
  }

  public E patch(I id, E entity) {
    Optional<E> result = findById(id);
    if (result.isEmpty()) {
      throw new ResourceNotFoundException();
    }
    E target = result.get();
    ObjectUtils.merge(entity, target);
    return update(id, target);
  }

  @Transactional
  @Override
  public void delete(I id) {
    this.findById(id).map(entity -> {
      beforeDelete(entity);
      repository.deleteById(id);
      afterDelete(entity);
      return entity;
    }).orElseThrow(ResourceNotFoundException::new);
  }

  //------------------------------------------------
  //Deprecated methods

  @Override
  @Deprecated
  public Page<?> findDto(F filter, Pageable pageable) {
    return this.find(filter, pageable).map(e ->
      getDtoInstance().fromEntity(e));
  }

  @Override
  @Deprecated
  public Optional<Long> existsDto(F filter) {
    return Optional.of(this.exists(filter) ? 1l : 0l);
  }

  @Override
  @Deprecated
  public Optional<D> findByIdDto(I id) {
    final D dto = getDtoInstance();
    Optional<E> entity = this.findById(id);
    return (Optional<D>) entity.map(dto::fromEntity);
  }

  @Transactional
  @Override
  @Deprecated
  public D createDto(D payload) {
    E entity = payload.toEntity();
    entity = beforeCreateDto(payload, entity);
    entity = repository.save(entity);
    entity = afterCreateDto(payload, entity);
    return (D) getDtoInstance().fromEntity(entity);
  }

  @Transactional
  @Override
  @Deprecated
  public D updateDto(I id, D dto) {
    return this.findById(id).map(oldEntity -> {
      E entity = dto.toEntity();
      entity = beforeUpdate(oldEntity, entity);
      entity.setId(id);
      entity = repository.save(entity);
      entity = afterUpdate(entity);
      return (D) getDtoInstance().fromEntity(entity);
    }).orElseThrow(ResourceNotFoundException::new);
  }

  private D getDtoInstance() {
    try {
      return (D) new TypeToken<D>(getClass()) {
      }.getRawType().getDeclaredConstructor().newInstance();
    } catch (Exception e) {
      throw new InternalServerErrorException("empresa.mal.configurados");
    }
  }

  protected static <T, P> void sortListByProperty(List<T> list, Direction direction,
    Function<T, P> getPropertyFunction) {
    Collator collator = Collator.getInstance(Locale.getDefault());
    collator.setStrength(Collator.PRIMARY);
    list.sort((obj1, obj2) -> {
      P property1 = getPropertyFunction.apply(obj1);
      P property2 = getPropertyFunction.apply(obj2);

      if (property1 == null && property2 == null) {
        return 0;
      } else if (property1 == null) {
        return 1;
      } else if (property2 == null) {
        return -1;
      }

      int result = collator.compare(property1.toString(), property2.toString());

      if (direction.isDescending()) {
        result = -result;
      }
      return result;
    });
  }

  public E required(I id, String message) {
    Optional<E> value = findById(id);
    if (value.isEmpty()) {
      throw new ResourceNotFoundException(message);
    }
    return value.get();
  }

  public E required(I id) {
    return required(id, ResourceNotFoundException.DEFAULT_MESSAGE);
  }

  /**
   * @see PageableServiceAdapterDto#required(Serializable, String)
   */
  @Deprecated
  public E required(I id, RuntimeException runtimeException) {
    Optional<E> value = findById(id);
    if (value.isEmpty()) {
      throw runtimeException;
    }
    return value.get();
  }

  public E getReference(I id) {
    return repository.getReferenceById(id);
  }
}
