package br.com.nuvy.common.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Retention(RUNTIME)
@Repeatable(OnlyOneNotNulls.class)
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Constraint(validatedBy = OnlyOneNotNullValidator.class)
public @interface OnlyOneNotNull {
  String message() default "onlione.notnull";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  /**
   * Fields to validate against null.
   */
  String[] fields() default {};
}
