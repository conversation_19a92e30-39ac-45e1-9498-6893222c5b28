package br.com.nuvy.common.base.service;

import br.com.nuvy.common.base.entity.Entidade;
import br.com.nuvy.common.base.filter.Filter;
import br.com.nuvy.common.base.repository.NuvyRepository;
import br.com.nuvy.common.exception.OperacaoNaoSuportadaException;
import br.com.nuvy.common.exception.ResourceNotFoundException;
import br.com.nuvy.common.query.PredicateBuilder;
import br.com.nuvy.common.utils.ObjectUtils;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

@SuppressWarnings({"SpringJavaInjectionPointsAutowiringInspection", "java:S1172", "java:S2201",
  "unchecked"})
public abstract class PageableServiceAdapter<E extends Entidade<I>, I extends Serializable, F extends Filter, R extends NuvyRepository<E, I>> implements
  PageableService<E, I, F> {

  @Autowired
  protected R repository;

  protected Specification<E> configureSpecification(F filter) {
    return (root, query, criteriaBuilder) -> PredicateBuilder.create(criteriaBuilder).and();
  }

  protected Specification<E> configureSpecification(String criterio) {
    return (root, query, criteriaBuilder) -> PredicateBuilder.create(criteriaBuilder).and();
  }

  @Transactional
  protected E beforeDelete(E entity) {
    return entity;
  }

  @Transactional
  protected void afterDelete(E entity) {
  }

  @Transactional
  protected E beforeCreate(E entity) {
    return entity;
  }

  @Transactional
  protected E afterCreate(E entity) {
    return entity;
  }

  @Transactional
  protected E beforeUpdate(E oldEntity, E newEntity) {
    return newEntity;
  }

  @Transactional
  protected E afterUpdate(E entity) {
    return entity;
  }

  @Transactional
  protected E save(E entity) {
    return repository.save(entity);
  }

  public List<E> findAll() {
    return repository.findAll();
  }

  public Page<E> findAll(Pageable pageable) {
    return repository.findAll(pageable);
  }

  public List<E> findAll(Sort sort) {
    return repository.findAll(sort);
  }

  @Override
  public Page<E> find(F filter, Pageable pageable) {
    Specification<E> specification = configureSpecification(filter);
    return repository.findAll(specification, pageable);
  }

  @Override
  public Page<E> find(String criterio, Pageable pageable) {
    Specification<E> specification = configureSpecification(criterio);
    return repository.findAll(specification, pageable);
  }

  @Override
  public Optional<E> findById(I id) {
    return repository.findById(id);
  }

  @Override
  public boolean exists(F filter) {
    Specification<E> specification = configureSpecification(filter);
    return repository.count(specification) > 0;
  }

  @Transactional
  @Override
  public E create(E entity) {
    if (entity.getId() != null) {
      throw new OperacaoNaoSuportadaException("operacao.nao.suportada");
    }
    entity = beforeCreate(entity);
    entity = save(entity);
    entity = afterCreate(entity);
    return entity;
  }

  @Transactional
  @Override
  public E update(I id, E entity) {
    return this.findById(id).map(oldEntity -> {
      E localEntity = entity;
      localEntity = beforeUpdate(oldEntity, localEntity);
      localEntity.setId(id);
      localEntity = save(localEntity);
      localEntity = afterUpdate(localEntity);
      return localEntity;
    }).orElseThrow(ResourceNotFoundException::new);
  }

  public E patch(I id, E entity) {
    Optional<E> result = findById(id);
    if (result.isEmpty()) {
      throw new ResourceNotFoundException();
    }
    E target = result.get();
    ObjectUtils.merge(entity, target);
    return update(id, target);
  }

  @Transactional
  @Override
  public void delete(I id) {
    this.findById(id).map(entity -> {
      beforeDelete(entity);
      repository.deleteById(id);
      afterDelete(entity);
      return entity;
    }).orElseThrow(ResourceNotFoundException::new);
  }

  /**
   * @see PageableServiceAdapter#required(Serializable, String)
   */
  @Deprecated
  public E require(I id, String message) {
    return repository.required(id, message);
  }

  /**
   * @see PageableServiceAdapter#required(Serializable)
   */
  @Deprecated
  public E require(I id) {
    return require(id, ResourceNotFoundException.DEFAULT_MESSAGE);
  }

  public E required(I id, String message) {
    return repository.required(id, message);
  }

  public E required(I id) {
    return required(id, ResourceNotFoundException.DEFAULT_MESSAGE);
  }

  public Pageable newPageable(
    Pageable pageable, Map<String, String> propertys, String defaultSort
  ) {
    if (pageable == null || propertys == null || defaultSort == null) {
      return pageable;
    }
    if (pageable.getSort().isSorted()) {
      Optional<Order> order = pageable.getSort().get().findFirst();
      if (order.isPresent() && StringUtils.isNotBlank(propertys.get(order.get().getProperty()))) {
        pageable = ((PageRequest) pageable).withSort(
          order.get().getDirection(),
          propertys.get(order.get().getProperty())
        );
      }
    } else {
      pageable = PageRequest.of(
        pageable.getPageNumber(), pageable.getPageSize(),
        Sort.by(defaultSort)
      );
    }
    return pageable;
  }

  /**
   * @see PageableServiceAdapter#require(Serializable, String)
   */
  @Deprecated
  public E required(I id, RuntimeException runtimeException) {
    Optional<E> value = findById(id);
    if (value.isEmpty()) {
      throw runtimeException;
    }
    return value.get();
  }

  public E getReference(I id) {
    return repository.getReferenceById(id);
  }
}
