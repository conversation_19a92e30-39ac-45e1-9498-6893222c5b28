package br.com.nuvy.common.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Singular;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import java.util.List;
import java.util.Objects;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ErrorMessage {

  private final boolean error = true;
  private Integer status;
  private String code;
  private String message;
  @JsonIgnore
  private HttpStatus httpStatus;
  @Singular
  private List<ErrorDetail> details;

  public void setHttpStatus(HttpStatus httpStatus) {
    this.httpStatus = httpStatus;
    this.status = httpStatus.value();
  }

  public Integer getStatus() {
    if (Objects.nonNull(this.httpStatus)) {
      return this.httpStatus.value();
    }
    return status;
  }

  @Value
  @Builder
  public static class ErrorDetail {
    String field;
    List<String> fields;
    String code;
    String message;
    public String getCode() {
      return StringUtils.capitalize(code);
    }

    public String getMessage() {
       return StringUtils.capitalize(message);
    }

  }

}
