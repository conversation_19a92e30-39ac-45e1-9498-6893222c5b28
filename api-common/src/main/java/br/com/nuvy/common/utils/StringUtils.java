package br.com.nuvy.common.utils;

import java.nio.charset.Charset;
import java.text.Normalizer;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.passay.CharacterData;
import org.passay.CharacterRule;
import org.passay.PasswordGenerator;

public class StringUtils {

  private static final Pattern GTIN_PATTERN = Pattern.compile("^\\d{8}(\\d{4}(\\d{1,2})?)?$");

  private static PasswordGenerator generator = new PasswordGenerator();
  private static List<CharacterRule> passwordRules = Arrays.asList(
    new CharacterRule(NuvyCharactereData.UpperCase, 1),
    new CharacterRule(NuvyCharactereData.LowerCase, 1),
    new CharacterRule(NuvyCharactereData.Digit, 1),
    new CharacterRule(NuvyCharactereData.Special, 1)
  );
  private static List<CharacterRule> tokenRules = Arrays.asList(
    new CharacterRule(NuvyCharactereData.UpperCase, 1),
    new CharacterRule(NuvyCharactereData.Digit, 1)
  );

  public static String randomPassword(int length) {
    return generator.generatePassword(length, passwordRules);
  }

  public static String randomToken(int length) {
    return generator.generatePassword(length, tokenRules);
  }

  public static String removePunctuation(String string) {
    if (string != null) {
      return string.replaceAll("[^0-9]", "").trim();
    }
    return string;
  }

  public static String corrigeIdCnae(String id) {
    if (id == null) {
      return null;
    }
    id = id.replaceAll("[^0-9]", "").trim();
    if (id.length() == 6) {
      id = "0" + id;
    }
    return id;
  }

  public static String[] createArray(String string) {
    String[] array = string.split(",");
    for (int i = 0; i < array.length; i++) {
      array[i] = array[i].trim();
    }
    return array;
  }

  public static String stripAccents(String value) {
    value = Normalizer.normalize(value, Normalizer.Form.NFD);
    value = value.replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");
    return value;
  }

  public enum NuvyCharactereData implements CharacterData {
    LowerCase("INSUFFICIENT_LOWERCASE", "abcdefghijklmnopqrstuvwxyz"),
    UpperCase("INSUFFICIENT_UPPERCASE", "ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
    Digit("INSUFFICIENT_DIGIT", "0123456789"),
    Special("INSUFFICIENT_SPECIAL", "!#$%&()*+,-.:;<=>?@");

    private final String errorCode;
    private final String characters;

    private NuvyCharactereData(String code, String charString) {
      this.errorCode = code;
      this.characters = charString;
    }

    public String getErrorCode() {
      return this.errorCode;
    }

    public String getCharacters() {
      return this.characters;
    }
  }

  public static String formatCpfCnpj(String cpfCnpj) {
    if (cpfCnpj != null) {
      if (cpfCnpj.length() == 11) {
        return cpfCnpj.substring(0, 3) + "." + cpfCnpj.substring(3, 6) + "."
          + cpfCnpj.substring(6, 9) + "-" + cpfCnpj.substring(9, 11);
      } else if (cpfCnpj.length() == 14) {
        return cpfCnpj.substring(0, 2) + "." + cpfCnpj.substring(2, 5) + "."
          + cpfCnpj.substring(5, 8) + "/" + cpfCnpj.substring(8, 12) + "-"
          + cpfCnpj.substring(12, 14);
      }
    }
    return cpfCnpj;
  }

  public static boolean isCpf(String cpf) {
    if (cpf != null) {
      return cpf.length() == 11;
    }
    return false;
  }

  public static boolean isCnpj(String cnpj) {
    if (cnpj != null) {
      return cnpj.length() == 14;
    }
    return false;
  }

  public static String formatCep(String cep) {
    if (cep != null && (cep.length() == 8)) {
      return cep.substring(0, 5) + "-" + cep.substring(5, 8);

    }
    return cep;
  }

  public static String formataTelefone(String telefone) {
    if (telefone != null) {
      if (telefone.length() == 10) {
        return "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 6) + "-"
          + telefone.substring(6, 10);
      } else if (telefone.length() == 11) {
        return "(" + telefone.substring(0, 2) + ") " + telefone.substring(2, 7) + "-"
          + telefone.substring(7, 11);
      }
    }
    return telefone;
  }

  public static String replaceAll(String value) {
    if (value != null) {
      return value.replaceAll("[^0-9]+", "").trim();
    }
    return value;
  }

  public static boolean isValidCpf(String cpf) {
    if (cpf == null) {
      return false;
    }

    cpf = cpf.replaceAll("\\D", "");

    if (cpf.length() == 11) {
      int soma = 0;
      for (int i = 0; i < 9; i++) {
        soma += (10 - i) * (cpf.charAt(i) - '0');
      }
      soma = 11 - (soma % 11);
      if (soma > 9) {
        soma = 0;
      }
      if (soma == (cpf.charAt(9) - '0')) {
        soma = 0;
        for (int i = 0; i < 10; i++) {
          soma += (11 - i) * (cpf.charAt(i) - '0');
        }
        soma = 11 - (soma % 11);
        if (soma > 9) {
          soma = 0;
        }
        if (soma == (cpf.charAt(10) - '0')) {
          return true;
        }
      }
    }

    return false;
  }

  public static boolean isValidCnpj(String cnpj) {
    if (cnpj == null) {
      return false;
    }

    cnpj = cnpj.replaceAll("\\D", "");

    if (cnpj.length() == 14) {
      int soma = 0;
      int dig;

      String cnpjCalc = cnpj.substring(0, 12);

      char[] chr_cnpj = cnpj.toCharArray();

      // Primeira parte
      for (int i = 0; i < 4; i++) {
        if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9) {
          soma += (chr_cnpj[i] - 48) * (6 - (i + 1));
        }
      }
      for (int i = 0; i < 8; i++) {
        if (chr_cnpj[i + 4] - 48 >= 0 && chr_cnpj[i + 4] - 48 <= 9) {
          soma += (chr_cnpj[i + 4] - 48) * (10 - (i + 1));
        }
      }
      dig = 11 - (soma % 11);

      cnpjCalc += (dig == 10 || dig == 11) ? "0" : Integer.toString(dig);

      // Segunda parte
      soma = 0;
      for (int i = 0; i < 5; i++) {
        if (chr_cnpj[i] - 48 >= 0 && chr_cnpj[i] - 48 <= 9) {
          soma += (chr_cnpj[i] - 48) * (7 - (i + 1));
        }
      }
      for (int i = 0; i < 8; i++) {
        if (chr_cnpj[i + 5] - 48 >= 0 && chr_cnpj[i + 5] - 48 <= 9) {
          soma += (chr_cnpj[i + 5] - 48) * (10 - (i + 1));
        }
      }
      dig = 11 - (soma % 11);
      cnpjCalc += (dig == 10 || dig == 11) ? "0" : Integer.toString(dig);

      return cnpj.equals(cnpjCalc);
    }

    return false;
  }

  public static String getIlikeStringUpper(String string) {
    return "%" + string.toUpperCase() + "%";
  }

  public static String getIlikeStringLower(String string) {
    return "%" + string.toLowerCase()+ "%";
  }

  public static String getEncodedString(String string, Charset charsetEncoded, Charset charsetDecoded) {
    if(string == null)
      return null;
    return new String(string.getBytes(charsetEncoded), charsetDecoded);
  }

  public static String substring(final String str, int start, int end) {
    return org.apache.commons.lang3.StringUtils.substring(str, start, end);
  }

  public static boolean isBlank(CharSequence cs) {
    return org.apache.commons.lang3.StringUtils.isBlank(cs);
  }

  public static boolean isNotBlank(CharSequence cs) {
    return org.apache.commons.lang3.StringUtils.isNotBlank(cs);
  }

  public static String replaceAll(final String text, final String regex, final String replacement) {
    return org.apache.commons.lang3.RegExUtils.replaceAll(text, regex, replacement);
  }

  public String extractPathFromUrl(String url) {
    String resultado = url.split("\\?")[0];
    if (resultado.startsWith("http://") || resultado.startsWith("https://")) {
      String[] split = resultado.split("/");
      resultado = String.join("/", Arrays.copyOfRange(split, 3, split.length));
    }
    if (resultado.startsWith("/")) {
      resultado = resultado.substring(1);
    }
    return resultado;
  }

  public static boolean isGtinValido(String gtin) {
    if (gtin == null) {
      return false;
    }
    if (!GTIN_PATTERN.matcher(gtin).matches()) {
      return false;
    }
    String digito = gtin.substring(gtin.length() - 1);
    String gtinSemDigito = gtin.substring(0, gtin.length() - 1);
    int multiplicador = 3;
    int soma = 0;
    for (int i = gtinSemDigito.length() - 1; i >= 0; i--) {
      soma += Integer.parseInt(String.valueOf(gtinSemDigito.charAt(i))) * multiplicador;
      multiplicador = multiplicador == 3 ? 1 : 3;
    }
    int multiplo = (soma % 10 == 0) ? soma : ((soma / 10) * 10 + 10);
    String digitoCalculado = String.valueOf(multiplo - soma);
    return digito.equals(digitoCalculado);
  }

  public static String getGtinValido(String gtin) {
    if (isGtinValido(gtin)) {
      return gtin;
    }
    return "SEM GTIN";
  }

  /**
   * Formata o NCM para garantir que tenha 8 caracteres (remove o último dígito se tiver 9)
   *
   * @param ncm Código NCM
   * @return NCM formatado com 8 caracteres
   */
  public static String formatNcm(String ncm) {
    if (ncm == null) {
      return null;
    }

    // Se o NCM tiver 9 caracteres, remove o último
    if (ncm.length() == 9) {
      return ncm.substring(0, 8);
    }

    return ncm;
  }

  /**
   * Remove formatação da inscrição estadual, mantendo apenas números
   * Ex: "714.097.648.112" -> "714097648112"
   *
   * @param inscricaoEstadual Inscrição estadual com ou sem formatação
   * @return Inscrição estadual apenas com números
   */
  public static String limparInscricaoEstadual(String inscricaoEstadual) {
    if (inscricaoEstadual == null) {
      return null;
    }
    return inscricaoEstadual.replaceAll("[^0-9]", "").trim();
  }

  public static String formataHtml(String html) {
    if (html == null) {
      return null;
    }

    String tagPattern = "<[^>]+>";

    String semTags = html.replaceAll(tagPattern, "");

    String entityPattern = "&[^;]+;";

    Pattern entityRegex = Pattern.compile(entityPattern);
    Matcher matcher = entityRegex.matcher(semTags);
    StringBuilder sb = new StringBuilder();

    while (matcher.find()) {
      String entity = matcher.group();
      String replacement = converterEntidadeHtml(entity);
      matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
    }
    matcher.appendTail(sb);

    return sb.toString().replaceAll("\\s+", " ").trim();
  }

  private static String converterEntidadeHtml(String entity) {
    return switch (entity) {
      case "&nbsp;" -> " ";
      case "&lt;" -> "<";
      case "&gt;" -> ">";
      case "&amp;" -> "&";
      case "&quot;" -> "\"";
      case "&#39;" -> "'";
      case "&Ccedil;" -> "Ç";
      case "&ccedil;" -> "ç";
      case "&Aacute;" -> "Á";
      case "&aacute;" -> "á";
      case "&Eacute;" -> "É";
      case "&eacute;" -> "é";
      case "&Iacute;" -> "Í";
      case "&iacute;" -> "í";
      case "&Oacute;" -> "Ó";
      case "&oacute;" -> "ó";
      case "&Uacute;" -> "Ú";
      case "&uacute;" -> "ú";
      case "&Atilde;" -> "Ã";
      case "&atilde;" -> "ã";
      case "&Otilde;" -> "Õ";
      case "&otilde;" -> "õ";
      case "&Acirc;" -> "Â";
      case "&acirc;" -> "â";
      case "&Ecirc;" -> "Ê";
      case "&ecirc;" -> "ê";
      case "&Ocirc;" -> "Ô";
      case "&ocirc;" -> "ô";
      default -> entity;
    };
  }
}
