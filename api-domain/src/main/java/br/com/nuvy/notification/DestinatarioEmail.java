package br.com.nuvy.notification;

import br.com.nuvy.common.file.File;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DestinatarioEmail {

  private Set<String> emails;
  private String nomeCliente;
  private Set<String> copias;
  private Set<String> copiasOcultas;
  private List<File> anexos;

  public DestinatarioEmail(
    String email, String nomeCliente, Set<String> copias,
    Set<String> copiasOcultas, List<File> anexos
  ) {
    this.emails = Set.of(email);
    this.nomeCliente = nomeCliente;
    this.copias = copias;
    this.copiasOcultas = copiasOcultas;
    this.anexos = anexos;
  }
}
