package br.com.nuvy.events;

import br.com.nuvy.notification.DestinatarioEmail;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class OrdemServicoEmailEvent extends ApplicationEvent {

  private DestinatarioEmail destinatario;
  private Map<String, String> mailData;
  private String empresaNome;

  public OrdemServicoEmailEvent(Object source, DestinatarioEmail destinatario,
    Map<String, String> mailData, String empresaNome) {
    super(source);
    this.destinatario = destinatario;
    this.mailData = mailData;
    this.empresaNome = empresaNome;
  }

}
