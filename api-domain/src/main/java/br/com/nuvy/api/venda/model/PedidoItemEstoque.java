package br.com.nuvy.api.venda.model;

import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.api.estoque.model.Rastreabilidade;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@EqualsAndHashCode(of = {"pedidoItem", "deposito", "rastreabilidade"}, callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vd_pedido_item_etq")
public class PedidoItemEstoque implements Entidade<UUID> {

  @Id
  @Column(name = "id_pedido_item_etq")
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;
  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  private BigDecimal quantidade;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_pedido_item")
  private PedidoItem pedidoItem;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_rastreabilidade")
  private Rastreabilidade rastreabilidade;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_deposito", nullable = false)
  private Deposito deposito;

}
