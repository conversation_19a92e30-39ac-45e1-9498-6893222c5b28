package br.com.nuvy.api.seguranca.dto;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.seguranca.model.Perfil;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerfilResumoDto implements Dto<Perfil> {

  private Integer id;
  private String nome;
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;
  private Integer quantidadeUsuarios;

  public static PerfilResumoDto from(Perfil entidade) {
    return ObjectUtils.convert(entidade, PerfilResumoDto.class);
  }

  public static PerfilResumoDto toResumo(Perfil entidade) {
    return ObjectUtils.convert(entidade, PerfilResumoDto.class);
  }

  @Override
  public Perfil toEntity() {
    return ObjectUtils.convert(this, Perfil.class);
  }

}