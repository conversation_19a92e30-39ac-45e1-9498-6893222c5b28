package br.com.nuvy.api.estoque.model;

import static br.com.nuvy.common.utils.ObjectUtils.get;
import static br.com.nuvy.common.utils.ObjectUtils.getOrElse;
import static jakarta.persistence.CascadeType.ALL;
import static java.math.BigDecimal.ZERO;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.TipoCarroceria;
import br.com.nuvy.api.cadastro.model.TipoFrete;
import br.com.nuvy.api.cadastro.model.TipoVeiculo;
import br.com.nuvy.api.compras.model.DestinacaoCompra;
import br.com.nuvy.api.enums.IndicadorPresenca;
import br.com.nuvy.api.enums.OrigemNotaFiscal;
import br.com.nuvy.api.enums.SituacaoNotaEntrada;
import br.com.nuvy.api.enums.StatusManifestacao;
import br.com.nuvy.api.enums.TipoAmbienteEmissaoNota;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.enums.TipoEventoManifestacaoDestinatario;
import br.com.nuvy.api.enums.TipoIE;
import br.com.nuvy.api.enums.TipoNotaFiscal;
import br.com.nuvy.api.enums.TipoOperacao;
import br.com.nuvy.api.enums.TipoTransporte;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaItem.NotaFiscalItemDecorator;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import br.com.nuvy.common.utils.StringUtils;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@EqualsAndHashCode(of = "id", callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "eq_nf_entrada")
public class NotaFiscalEntrada extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id_nf_entrada")
  private UUID id;

  @Column(name = "cnpj", length = 14)
  private String cnpj;

  @Column(name = "razao_social", length = 150)
  private String razaoSocial;

  @Column(name = "nr_nf", length = 150)
  private String numeroNota;

  @Column(name = "dt_nf")
  private LocalDateTime dataEmissao;

  @Column(name = "dt_recebimento_nf")
  private LocalDateTime dataRecebimento;

  @Column(name = "vl_nota", columnDefinition = "numeric(12,2)")
  private BigDecimal valorTotal;

  @Column(name = "cnpj_transportador", length = 14)
  private String cnpjTransportador;

  @Column(name = "razao_social_transportador", length = 150)
  private String razaoSocialTransportador;

  @Column(name = "qtd_volumes", columnDefinition = "numeric(15,4)")
  private BigDecimal quantidadeVolumes;

  @Column(name = "tp_volume", length = 100)
  private String especieVolume;

  @Column(name = "peso_liquido", columnDefinition = "numeric(15,4)")
  private BigDecimal pesoLiquido;

  @Column(name = "peso_bruto", columnDefinition = "numeric(15,4)")
  private BigDecimal pesoBruto;

  @Enumerated(EnumType.STRING)
  @Column(name = "situacao", length = 50)
  private SituacaoNotaEntrada situacao;

  @Column(name = "chave_acesso", length = 100)
  private String chaveAcesso;

  @Enumerated(EnumType.STRING)
  @Column(name = "tp_endereco", length = 50)
  private TipoEndereco tipoEndereco;

  @Column(name = "cep", length = 12)
  private String cep;

  @Column(name = "uf", length = 2)
  private String uf;

  @Column(name = "uf_nome", length = 100)
  private String ufNome;

  @Column(name = "cod_uf", length = 10)
  private String codigoUf;

  @Column(name = "cidade", length = 150)
  private String cidade;

  @Column(name = "cod_cidade", length = 10)
  private String codigoCidade;

  @Column(name = "bairro", length = 150)
  private String bairro;

  @Column(name = "endereco")
  private String endereco;

  @Column(name = "numero", length = 25)
  @Builder.Default
  private String numero = "SN";

  @Column(name = "complemento", length = 150)
  private String complemento;

  @Column(name = "telefone", length = 15)
  private String telefone;

  @Column(name = "pais", length = 150)
  private String pais;

  @Column(name = "cod_pais", length = 100)
  private String codigoPais;

  @ManyToOne
  @JoinColumn(
    name = "id_tipo_frete",
    foreignKey = @ForeignKey(name = "fk_tp_frete_nf_entrada"))
  private TipoFrete tipoFrete;

  @ManyToOne
  @JoinColumn(
    name = "id_pessoa",
    foreignKey = @ForeignKey(name = "fk_pessoa_nf_entrada"))
  private Pessoa fornecedor;

  @OneToOne(cascade = ALL)
  @JoinColumn(
    name = "id_nf_entrada_pagto",
    foreignKey = @ForeignKey(name = "fk_nf_pagto_nf_entrada"))
  private NotaFiscalEntradaPagamento notaFiscalEntradaPagamento;

  @Builder.Default
  @OneToMany(mappedBy = "notaFiscalEntrada", cascade = ALL)
  private List<NotaFiscalEntradaItem> itens = new ArrayList<>();

  @Builder.Default
  @OneToMany(mappedBy = "notaFiscalEntrada", cascade = ALL, orphanRemoval = true)
  private List<NotaFiscalEntradaCentroCusto> centrosCustos = new ArrayList<>();

  @Builder.Default
  @OneToMany(mappedBy = "notaFiscalEntrada", cascade = ALL)
  private List<NotaFiscalEntradaHistorico> notaFiscalEntradaHistorico = new ArrayList<>();

  @ManyToOne
  @JoinColumn(
    name = "id_empresa",
    foreignKey = @ForeignKey(name = "fk_empresa_nf_entrada"),
    nullable = false)
  private Empresa empresa;

  @Enumerated(EnumType.STRING)
  @Column(name = "status_manifestacao", length = 20)
  private StatusManifestacao statusManifestacao;

  @Column(name = "motivo_manifestacao")
  private String motivoManifestacao;

  @Column(name = "caminho_xml")
  private String caminhoXml;

  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_eventomanifestacao", length = 50)
  private TipoEventoManifestacaoDestinatario tipoEventoManifestacao;

  @Enumerated(EnumType.STRING)
  @Column(name = "ambiente", length = 20)
  private TipoAmbienteEmissaoNota ambiente;

  @ManyToOne
  @JoinColumn(
    name = "id_plano_contas",
    foreignKey = @ForeignKey(name = "fk_nota_entrada_plano_conta"))
  private PlanoConta tipoDespesa;

  @ManyToOne
  @JoinColumn(
    name = "id_dest",
    foreignKey = @ForeignKey(name = "fk_nf_dest"))
  private DestinacaoCompra destinacaoCompra;

  @Column(name = "insc_estadual", length = 25)
  private String inscricaoEstadual;

  @Column(name = "insc_municipal", length = 25)
  private String inscricaoMunicipal;

  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_ie", length = 25)
  private TipoIE indicadorInscricaoEstadual;

//  campos novos para devolução

  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_nota", length = 50, updatable = false)
  private TipoNotaFiscal tipoNota;

  @Column(name = "motivo_devolucao", columnDefinition = "text")
  private String motivoDevolucao;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_nf_entrada_origem")
  private NotaFiscalEntrada notaFiscalOrigem;

  @Column(name = "email_envio_nf", length = 100)
  private String emailEnvioNf;

  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_transporte", length = 50)
  private TipoTransporte tipoTransporte;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_transportadora")
  private Pessoa transportadora;

  @Column(name = "veiculo_placa", length = 20)
  private String placa;

  @Column(name = "veiculo_uf", length = 2)
  private String ufVeiculo;

  @Column(name = "veiculo_rntcr", length = 100)
  private String rntcr;

  @Column(name = "dt_registro", updatable = false, columnDefinition = "timestamp")
  private LocalDateTime dataRegistro;

  @Column(name = "dt_entrega_prevista", columnDefinition = "timestamp")
  private LocalDateTime dataEntregaPrevista;

  @Column(name = "cod_rastreio", length = 150)
  private String codigoRastreio;

  @Column(name = "veiculo_ciot", length = 100)
  private String ciot;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_tipo_veiculo")
  private TipoVeiculo tipoVeiculo;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_tipo_carroceria")
  private TipoCarroceria tipoCarroceria;

  @Column(name = "veiculo_tara", columnDefinition = "numeric(15,4)")
  private BigDecimal tara;

  @Column(name = "veiculo_capacidade", columnDefinition = "numeric(15,4)")
  private BigDecimal capacidade;

  @Column(name = "condutor_nome", length = 150)
  private String nomeCondutor;

  @Column(name = "condutor_cpf", length = 11)
  private String cpfCondutor;

  @Column(name = "uf_carregamento", length = 2)
  private String ufCarregamento;

  @Column(name = "cidade_carregamento", length = 100)
  private String cidadeCarregamento;

  @Column(name = "uf_descarregamento", length = 2)
  private String ufDescarregamento;

  @Column(name = "cidade_descarregamento", length = 100)
  private String cidadeDescarregamento;

  @ElementCollection(targetClass = String.class, fetch = FetchType.EAGER)
  @CollectionTable(name = "eq_nf_entrada_uf_percurso", joinColumns = {
    @JoinColumn(name = "id_nf_entrada")})
  @Column(name = "uf")
  private Set<String> ufPercursos;

  @Column(name = "cpf_cnpj_destinatario", length = 14, updatable = false)
  private String cpfCnpjDestinatario;

  @Column(name = "obs_nota_fiscal", columnDefinition = "text")
  private String observacaoNotaFiscal;

  @Column(name = "serie_nota_fiscal", length = 10)
  private String serieNotaFiscal;

  @Column(name = "ind_consumidor_final", updatable = false)
  private Boolean consumidorFinal;

  @Column(name = "descricao_nop")
  private String nopDescricao;

  @Enumerated(EnumType.STRING)
  @Column(name = "indicador_presenca", updatable = false)
  private IndicadorPresenca indicadorPresenca;

  @ManyToOne(targetEntity = Usuario.class)
  @JoinColumn(name = "id_usuario", updatable = false)
  private Usuario usuario;

  @Column(name = "tipo_operacao", length = 60)
  @Enumerated(EnumType.STRING)
  private TipoOperacao tipoOperacao;

  @Column(name = "codigo_tela", length = 60, updatable = false)
  private String codigoTela;

  @OneToMany(mappedBy = "notaFiscalEntrada", cascade = ALL)
  private List<NotaFiscalEntradaEndereco> enderecos;

  @Column(name = "motivo_cancelamento")
  private String motivoCancelamento;

  @Column(name = "xml_nota_inutilizacao")
  private String xmlNotaInutilizacao;

  @Builder.Default
  @Enumerated(EnumType.STRING)
  @Column(name = "origem")
  private OrigemNotaFiscal origemNotaFiscal = OrigemNotaFiscal.NUVY;

  public static NotaFiscalEntrada of(UUID id) {
    if (id == null) {
      return null;
    }
    return NotaFiscalEntrada.builder()
      .id(id)
      .build();
  }

  @Getter
  @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
  public static class NotaFiscalDecorator {

    @Getter(AccessLevel.NONE)
    private final NotaFiscalEntrada notaFiscalEntrada;
    @Getter
    private final List<NotaFiscalItemDecorator> itens;
    private final NotaFiscalEntradaPagamento pagamento;

    public static NotaFiscalDecorator of(NotaFiscalEntrada notaFiscalEntrada) {
      ArrayList<NotaFiscalItemDecorator> itens = new ArrayList<NotaFiscalItemDecorator>();
      for (NotaFiscalEntradaItem item : notaFiscalEntrada.getItens()) {
        itens.add(NotaFiscalItemDecorator.of(item));
      }
      return new NotaFiscalDecorator(
        notaFiscalEntrada, itens, notaFiscalEntrada.getNotaFiscalEntradaPagamento()
      );
    }

    private static final BigDecimal VALOR_ZERO = ZERO.setScale(2, RoundingMode.HALF_UP);
    private static final BigDecimal PERCENTUAL_ZERO = ZERO.setScale(4, RoundingMode.HALF_UP);
    private static final BigDecimal QUANTIDADE_ZERO = ZERO.setScale(4, RoundingMode.HALF_UP);
    private static final int QUATORZE = 14;
    private static final int ONZE = 11;

    private static String parseString(String value) {
      if (StringUtils.isBlank(value)) {
        return null;
      }
      return value.trim();
    }

    private static BigDecimal scalePeso(BigDecimal value) {
      return value.setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal getPeso(Function<NotaFiscalEntrada, BigDecimal> getter) {
      return getOrElse(notaFiscalEntrada, getter, NotaFiscalDecorator::scalePeso, VALOR_ZERO);
    }

    private static BigDecimal scalePercentual(BigDecimal value) {
      return value.setScale(4, RoundingMode.HALF_UP);
    }

    private BigDecimal getPercentualPagamento(
      Function<NotaFiscalEntradaPagamento, BigDecimal> getter) {
      return getOrElse(pagamento, getter, NotaFiscalDecorator::scalePercentual, PERCENTUAL_ZERO);
    }

    private static BigDecimal scaleValor(BigDecimal value) {
      return value.setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal getValorPagamento(Function<NotaFiscalEntradaPagamento, BigDecimal> getter) {
      return getOrElse(pagamento, getter, NotaFiscalDecorator::scaleValor, VALOR_ZERO);
    }

    public UUID getId() {
      return notaFiscalEntrada.id;
    }

    public String getCpfCnpj() {
      return notaFiscalEntrada.cnpj;
    }

    public String getRazaoSocial() {
      return parseString(notaFiscalEntrada.razaoSocial);
    }

    public String getNumeroNota() {
      return parseString(notaFiscalEntrada.numeroNota);
    }

    public LocalDateTime getDataEmissao() {
      return notaFiscalEntrada.dataEmissao;
    }

    public LocalDateTime getDataRecebimento() {
      return notaFiscalEntrada.dataRecebimento;
    }

    public String getCnpjTransportador() {
      return parseString(notaFiscalEntrada.cnpjTransportador);
    }

    public String getRazaoSocialTransportador() {
      return parseString(notaFiscalEntrada.razaoSocialTransportador);
    }

    public BigDecimal getQuantidadeVolumes() {
      return notaFiscalEntrada.quantidadeVolumes;
    }

    public String getEspecieVolume() {
      return parseString(notaFiscalEntrada.especieVolume);
    }

    public BigDecimal getPesoLiquido() {
      return getPeso(NotaFiscalEntrada::getPesoLiquido);
    }

    public BigDecimal getPesoBruto() {
      return getPeso(NotaFiscalEntrada::getPesoBruto);
    }

    public SituacaoNotaEntrada getSituacao() {
      return notaFiscalEntrada.situacao;
    }

    public String getChaveAcesso() {
      return parseString(notaFiscalEntrada.chaveAcesso);
    }

    public TipoEndereco getTipoEndereco() {
      return notaFiscalEntrada.tipoEndereco;
    }

    public String getCep() {
      return parseString(notaFiscalEntrada.cep);
    }

    public String getUf() {
      return parseString(notaFiscalEntrada.uf);
    }

    public String getUfNome() {
      return parseString(notaFiscalEntrada.ufNome);
    }

    public String getCodigoUf() {
      return parseString(notaFiscalEntrada.codigoUf);
    }

    public String getCidade() {
      return parseString(notaFiscalEntrada.cidade);
    }

    public String getCodigoCidade() {
      return parseString(notaFiscalEntrada.codigoCidade);
    }

    public String getBairro() {
      return parseString(notaFiscalEntrada.bairro);
    }

    public String getEndereco() {
      return parseString(notaFiscalEntrada.endereco);
    }

    public String getNumero() {
      return parseString(notaFiscalEntrada.numero);
    }

    public String getComplemento() {
      return parseString(notaFiscalEntrada.complemento);
    }

    public String getTelefone() {
      return parseString(notaFiscalEntrada.telefone);
    }

    public String getPais() {
      return parseString(notaFiscalEntrada.pais);
    }

    public String getCodigoPais() {
      return parseString(notaFiscalEntrada.codigoPais);
    }

    public TipoFrete getTipoFrete() {
      return notaFiscalEntrada.tipoFrete;
    }

    public Pessoa getFornecedor() {
      return notaFiscalEntrada.fornecedor;
    }

    public NotaFiscalEntradaPagamento getNotaFiscalEntradaPagamento() {
      return notaFiscalEntrada.notaFiscalEntradaPagamento;
    }

    public List<NotaFiscalEntradaCentroCusto> getCentrosCustos() {
      return notaFiscalEntrada.centrosCustos;
    }

    public Empresa getEmpresa() {
      return notaFiscalEntrada.empresa;
    }

    public TipoAmbienteEmissaoNota getAmbiente() {
      return notaFiscalEntrada.ambiente;
    }

    public PlanoConta getTipoDespesa() {
      return notaFiscalEntrada.tipoDespesa;
    }

    public DestinacaoCompra getDestinacaoCompra() {
      return notaFiscalEntrada.destinacaoCompra;
    }

    public String getInscricaoEstadual() {
      return getOrElse(notaFiscalEntrada.inscricaoEstadual, "ISENTO");
    }

    public String getInscricaoMunicipal() {
      return parseString(notaFiscalEntrada.inscricaoMunicipal);
    }

    public TipoNotaFiscal getTipoNota() {
      return notaFiscalEntrada.tipoNota;
    }

    public String getMotivoDevolucao() {
      return parseString(notaFiscalEntrada.motivoDevolucao);
    }

    public String getChaveAcessoNotaOrigem() {
      return get(notaFiscalEntrada.notaFiscalOrigem, NotaFiscalEntrada::getChaveAcesso);
    }

    public String getEmailEnvioNf() {
      return getOrElse(parseString(notaFiscalEntrada.emailEnvioNf),
        notaFiscalEntrada.getFornecedor().getEmailNotaFiscal());
    }

    public TipoTransporte getTipoTransporte() {
      return notaFiscalEntrada.tipoTransporte;
    }

    public Pessoa getTransportadora() {
      return notaFiscalEntrada.transportadora;
    }

    public String getPlaca() {
      return parseString(notaFiscalEntrada.placa);
    }

    public String getUfVeiculo() {
      return parseString(notaFiscalEntrada.ufVeiculo);
    }

    public String getRntcr() {
      return parseString(notaFiscalEntrada.rntcr);
    }

    public LocalDateTime getDataRegistro() {
      return notaFiscalEntrada.dataRegistro;
    }

    public LocalDateTime getDataEntregaPrevista() {
      return notaFiscalEntrada.dataEntregaPrevista;
    }

    public String getCodigoRastreio() {
      return parseString(notaFiscalEntrada.codigoRastreio);
    }

    public String getCiot() {
      return parseString(notaFiscalEntrada.ciot);
    }

    public TipoVeiculo getTipoVeiculo() {
      return notaFiscalEntrada.tipoVeiculo;
    }

    public TipoCarroceria getTipoCarroceria() {
      return notaFiscalEntrada.tipoCarroceria;
    }

    public BigDecimal getTara() {
      return notaFiscalEntrada.tara;
    }

    public BigDecimal getCapacidade() {
      return notaFiscalEntrada.capacidade;
    }

    public String getNomeCondutor() {
      return parseString(notaFiscalEntrada.nomeCondutor);
    }

    public String getCpfCondutor() {
      return parseString(notaFiscalEntrada.cpfCondutor);
    }

    public String getUfCarregamento() {
      return parseString(notaFiscalEntrada.ufCarregamento);
    }

    public String getCidadeCarregamento() {
      return parseString(notaFiscalEntrada.cidadeCarregamento);
    }

    public String getUfDescarregamento() {
      return parseString(notaFiscalEntrada.ufDescarregamento);
    }

    public String getCidadeDescarregamento() {
      return parseString(notaFiscalEntrada.cidadeDescarregamento);
    }

    public Set<String> getUfPercursos() {
      return notaFiscalEntrada.ufPercursos;
    }

    public String getCpfCnpjDestinatario() {
      return parseString(notaFiscalEntrada.cpfCnpjDestinatario);
    }

    public String getObservacaoNotaFiscal() {
      return getOrElse(notaFiscalEntrada.observacaoNotaFiscal, "");
    }

    public String getSerieNotaFiscal() {
      return parseString(notaFiscalEntrada.serieNotaFiscal);
    }

    public Boolean getConsumidorFinal() {
      return notaFiscalEntrada.consumidorFinal;
    }

    public String getNopDescricao() {
      return parseString(notaFiscalEntrada.nopDescricao);
    }

    public IndicadorPresenca getIndicadorPresenca() {
      return notaFiscalEntrada.indicadorPresenca;
    }

    public Usuario getUsuario() {
      return notaFiscalEntrada.usuario;
    }

    public String getTipoOperacao() {
      return getOrElse(notaFiscalEntrada.tipoOperacao, TipoOperacao::getCodigo, "1");
    }

    public List<NotaFiscalEntradaEndereco> getEnderecos() {
      return notaFiscalEntrada.enderecos;
    }

    public BigDecimal getPercentualDesconto() {
      return getPercentualPagamento(NotaFiscalEntradaPagamento::getPercentualDesconto);
    }

    public String getFinalidadeNotaFiscal() {
      return get(notaFiscalEntrada, NotaFiscalEntrada::getTipoNota,
        TipoNotaFiscal::getFinalidadeNotaFiscal);
    }

    public String getTipoNotaFiscal() {
      return get(notaFiscalEntrada, NotaFiscalEntrada::getTipoNota,
        TipoNotaFiscal::getTipoNota);
    }

    public String getIndicadorPresencaCodigo() {
      return get(notaFiscalEntrada, NotaFiscalEntrada::getIndicadorPresenca,
        IndicadorPresenca::getCodigo);
    }

    public String getCpfEmpresa() {
      var cpf = parseString(getEmpresa().getCpfCnpj());
      return cpf != null && cpf.length() == ONZE ? cpf : null;
    }

    public String getCnpjEmpresa() {
      var cnpj = parseString(getEmpresa().getCpfCnpj());
      return cnpj != null && cnpj.length() == QUATORZE ? cnpj : null;
    }

    public BigDecimal getValorProdutos() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorProdutos);
    }


    public BigDecimal getValorTotal() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorTotal);
    }

    public BigDecimal getValorFrete() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorFrete);
    }

    public BigDecimal getValorDesconto() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorDesconto);
    }

    public BigDecimal getValorIcms() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorIcms);
    }

    public BigDecimal getValorBaseCalculoIcms() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorBaseCalculoIcms);
    }

    public BigDecimal getValorIcmsSt() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorIcmsSt);
    }

    public BigDecimal getValorBaseCalculoIcmsSt() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorBaseCalculoIcmsSt);
    }

    public BigDecimal getValorIpi() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorIpi);
    }

    public BigDecimal getValorPis() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorPis);
    }

    public BigDecimal getValorCofins() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorCofins);
    }

    public BigDecimal getValorOutrasDespesas() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorOutrasDespesas);
    }

    public BigDecimal getValorFcp() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorFcp);
    }

    public BigDecimal getValorBaseCalculoIpi() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorBaseCalculoIpi);
    }

    public BigDecimal getValorBaseCalculoPis() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorBaseCalculoPis);
    }

    public BigDecimal getValorBaseCalculoCofins() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorBaseCalculoCofins);
    }

    public BigDecimal getValorBaseCalculoCreditoIcmsSimplesNacional() {
      return getValorPagamento(
        NotaFiscalEntradaPagamento::getValorBaseCalculoCreditoIcmsSimplesNacional);
    }

    public BigDecimal getValorCreditoIcmsSimplesNacional() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorCreditoIcmsSimplesNacional);
    }

    public BigDecimal getValorFcpUfDestino() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorFcpUfDestino);
    }

    public BigDecimal getValorIcmsUfDestino() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorIcmsUfDestino);
    }

    public BigDecimal getValorSeguro() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorSeguro);
    }

    public String getCpfCliente() {
      return getCpfCnpj().length() == ONZE ? getCpfCnpj() : null;
    }

    public String getCnpjCliente() {
      return getCpfCnpj().length() == QUATORZE ? getCpfCnpj() : null;
    }

    public String getCodigoTipoIECliente() {
      return get(notaFiscalEntrada, NotaFiscalEntrada::getIndicadorInscricaoEstadual,
        TipoIE::getCodigo);
    }

    public String getSuframaCliente() {
      return get(notaFiscalEntrada.getFornecedor(), Pessoa::getSuframa);
    }

    public BigDecimal getAliquotaCreditoIcms() {
      var aliquota = get(notaFiscalEntrada.getEmpresa(), Empresa::getAliquotaCreditoIcms);
      return aliquota != null ? scalePercentual(aliquota) : null;
    }

    public BigDecimal getValorIpiDevolvido() {
      return getValorPagamento(NotaFiscalEntradaPagamento::getValorIpiDevolvido);
    }
  }
}
