package br.com.nuvy.api.cadastro.model.servico;

import br.com.nuvy.api.enums.SituacaoOrdemServico;
import br.com.nuvy.common.base.dto.JsonListWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@AllArgsConstructor
public final class OrdemServicoApiExterna {

  private UUID ordemServicoId;
  private BigInteger numeroRps;
  private LocalDateTime dataFaturamentoRps;
  private BigInteger codigo;
  private LocalDateTime dataServico;
  private SituacaoOrdemServico situacao;
  private UUID clienteId;
  private String observacao;
  private BigDecimal valor;
  private JsonListWrapper<Servico> servicos;
  private String tipoPagamento;
  private JsonListWrapper<Parcelas> parcelas;
  private JsonListWrapper<OrdemServicoNotaFiscal> ordemServicoNotaFiscal;

  public OrdemServicoApiExterna(
    UUID ordemServicoId,
    BigInteger numeroRps,
    LocalDateTime dataFaturamentoRps,
    BigInteger codigo,
    LocalDateTime dataServico,
    SituacaoOrdemServico situacao,
    UUID clienteId,
    String observacao,
    BigDecimal valor,
    String[] servicos,
    String tipoPagamento,
    String[] parcelas,
    String[] ordemServicoNotaFiscal
  ) {
    this.ordemServicoId = ordemServicoId;
    this.numeroRps = numeroRps;
    this.dataFaturamentoRps = dataFaturamentoRps;
    this.codigo = codigo;
    this.dataServico = dataServico;
    this.situacao = situacao;
    this.clienteId = clienteId;
    this.observacao = observacao;
    this.valor = valor;
    this.servicos = JsonListWrapper.of(servicos, Servico.class);
    this.tipoPagamento = tipoPagamento;
    this.parcelas = JsonListWrapper.of(parcelas, Parcelas.class);
    this.ordemServicoNotaFiscal = JsonListWrapper.of(ordemServicoNotaFiscal, OrdemServicoNotaFiscal.class);
  }

  @Data
  @AllArgsConstructor
  public static final class Servico {

    private String descricao;
    private BigDecimal quantidade;
    private UUID servicoId;
    private BigDecimal valor;
  }

  @Data
  @AllArgsConstructor
  public static final class Parcelas {

    private Integer numero;
    private BigDecimal valor;
    private LocalDate data;
    private String situacao;
  }

  @Data
  @AllArgsConstructor
  public static final class OrdemServicoNotaFiscal {
    private Integer numero;
    private String caminhoXml;
    private String caminhoPdf;
  }
}
