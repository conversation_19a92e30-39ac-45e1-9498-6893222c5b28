package br.com.nuvy.api.cadastro.event.categoria;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CategoriaAlterada extends CategoriaEvent {

  public CategoriaAlterada(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCategoria,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCategoria, ocorridoAs);
  }
}
