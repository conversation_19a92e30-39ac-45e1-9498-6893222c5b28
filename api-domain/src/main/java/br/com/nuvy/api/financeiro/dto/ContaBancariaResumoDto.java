package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContaBancariaResumoDto implements Dto<ContaBancaria> {

  private Integer id;
  private String nome;
  private Integer bancoId;
  private String bancoCodigo;
  private String bancoNome;
  private TipoContaDto tpConta;
  private BigDecimal saldo = BigDecimal.ZERO;
  private Situacao situacao;

  public static ContaBancariaResumoDto from(ContaBancaria entidade) {
    return ObjectUtils.convert(entidade, ContaBancariaResumoDto.class);
  }

}
