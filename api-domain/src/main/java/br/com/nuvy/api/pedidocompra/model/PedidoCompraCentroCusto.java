package br.com.nuvy.api.pedidocompra.model;

import br.com.nuvy.api.financeiro.model.CentroCusto;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.TenantId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"pedidoCompra", "centroCusto"}, callSuper = false)
@Entity
@Table(name = "pedido_centro_custo", schema = "pedidocompra")
public class PedidoCompraCentroCusto implements Entidade<Long> {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uuid", updatable = false)
    private UUID uuid;

    @TenantId
    @Column(name = "id_aplicacao")
    private String aplicacao;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_pedido")
    private PedidoCompra pedidoCompra;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_centro_custo")
    private CentroCusto centroCusto;

    @Column(name = "perc_rateio", precision = 10, scale = 9)
    private BigDecimal percentualRateio;

    @Column(name = "created_at", insertable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", insertable = false, updatable = false)
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;
}