package br.com.nuvy.api.cadastro.event.reguacomunicacao;

import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CobrancaReguaComunicacaoPendente extends EventoReguaComunicacao {

  private final Integer dias;
  private final Integer idContaBancaria;

  public CobrancaReguaComunicacaoPendente(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, UUID idReguaComunicacao,
    Integer dias, Integer idContaBancaria, LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idReguaComunicacao, ocorridoAs);
    this.dias = dias;
    this.idContaBancaria = idContaBancaria;
  }

  public CobrancaTituloReguaComunicacaoPendente createCobrancaTituloPendente(Integer idTitulo) {
    return new CobrancaTituloReguaComunicacaoPendente(
      UUID.randomUUID(), getIdAplicacao(), getIdEmpresa(), getIdUsuario(), getIdReguaComunicacao(),
      idTitulo, LocalDateTime.now()
    );
  }
}
