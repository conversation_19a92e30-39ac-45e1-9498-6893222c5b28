package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.PessoaImportacao;
import br.com.nuvy.api.enums.SituacaoImportacao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PessoaImportacaoDto implements Dto<PessoaImportacao> {

  private Integer id;
  private String nome;
  private String cpfCnpj;
  private String uf;
  private String cidade;
  private String telefone;
  private SituacaoImportacao situacao;
  private LocalDateTime dataRegistro;

  public static PessoaImportacaoDto from(PessoaImportacao entity) {
    return ObjectUtils.convert(entity, PessoaImportacaoDto.class);
  }

}
