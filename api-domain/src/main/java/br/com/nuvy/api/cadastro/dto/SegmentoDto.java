package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.Segmento;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter; import lombok.Setter;
import lombok.NoArgsConstructor;
import jakarta.validation.constraints.Size;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SegmentoDto implements Dto<Segmento> {

  private Integer id;
  @Size(max = 255)
  private String nome;
  @Size(max = 255)
  private String descricao;

  public static SegmentoDto from(Segmento entity) {
    return ObjectUtils.convert(entity, SegmentoDto.class);
  }
}
