package br.com.nuvy.api.financeiro.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferenciaContasResumoDto {

  private Integer id;
  private TranferenciaContasDto contaOrigem;
  private TranferenciaContasDto contaDestino;
  private BigDecimal valor;
  private LocalDateTime data;
}
