package br.com.nuvy.api.cadastro.event.pospdv;

import br.com.nuvy.base.NuvyErpEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class EventoPosPdv extends NuvyErpEvent {

  private final UUID idPosPdv;

  protected EventoPosPdv(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, UUID idPosPdv,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, ocorridoAs);
    this.idPosPdv = idPosPdv;
  }
}
