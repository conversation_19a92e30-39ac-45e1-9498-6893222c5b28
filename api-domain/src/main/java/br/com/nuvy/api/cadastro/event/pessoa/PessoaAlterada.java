package br.com.nuvy.api.cadastro.event.pessoa;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PessoaAlterada extends PessoaEvent {

  public PessoaAlterada(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idPessoa,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idPessoa, ocorridoAs);
  }
}
