package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormaPagamentoDto implements Dto<FormaPagamento> {

  private Integer id;
  @Size(max = 100)
  private String nome;
  @Size(max = 255)
  private String codigoMeioPagamentoSefaz;
  private Boolean ativo;

  public static FormaPagamentoDto from(FormaPagamento entidade) {
    return ObjectUtils.convert(entidade, FormaPagamentoDto.class);
  }

}
