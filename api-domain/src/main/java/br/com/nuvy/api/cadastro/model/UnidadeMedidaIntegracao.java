package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cd_unidade_medida_integracao")
public class UnidadeMedidaIntegracao extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

  @Size(max = 50)
  @Column(name = "name")
  private String nome;

  @Size(max = 40)
  @Column(name = "unittypeid")
  private String unittyPeId;

  @Column(name = "created_at", insertable = false, updatable = false)
  private LocalDateTime createdAt;

  @Column(name = "updated_at", insertable = false, updatable = false)
  private LocalDateTime updatedAt;

  public static UnidadeMedidaIntegracao of(UUID id) {
    if (id == null) {
      return null;
    }
    UnidadeMedidaIntegracao ret = new UnidadeMedidaIntegracao();
    ret.id = id;
    return ret;
  }
}
