package br.com.nuvy.api.estoque.comando;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RemoveMovimentoPorVendaPosPdv extends ComandoMovimentoPorProdutoDepositoRastreabilidade {

  private final List<Integer> idsPosPdvVendaItem;

  public RemoveMovimentoPorVendaPosPdv(
    UUID id,
    UUID idAplicacao, Integer idEmpresa, UUID idUsuario,
    Integer idProduto, String descricaoProduto,
    Integer idDeposito, boolean isPermiteEstoqueNegativo,
    Integer idRastreabilidade,
    List<Integer> idsPosPdvVendaItem,
    LocalDateTime emitidoAs
  ) {
    super(
      id,
      idAplicacao, idEmpresa, idUsuario,
      idProduto, descricaoProduto,
      idDeposito, isPermiteEstoqueNegativo,
      idRastreabilidade,
      emitidoAs
    );
    this.idsPosPdvVendaItem = idsPosPdvVendaItem;
  }

  public RemoveMovimentoPorVendaPosPdv(
    UUID id,
    UUID idAplicacao, Integer idEmpresa, UUID idUsuario,
    Integer idProduto, String descricaoProduto,
    Integer idDeposito, boolean isPermiteEstoqueNegativo,
    Integer idRastreabilidade,
    Integer idPosPdvVendaItem,
    LocalDateTime emitidoAs
  ) {
    this(
      id,
      idAplicacao, idEmpresa, idUsuario,
      idProduto, descricaoProduto,
      idDeposito, isPermiteEstoqueNegativo,
      idRastreabilidade,
      List.of(idPosPdvVendaItem),
      emitidoAs
    );
  }
}
