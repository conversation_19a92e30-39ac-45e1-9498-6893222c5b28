package br.com.nuvy.api.venda.dto;

import br.com.nuvy.api.venda.model.CanalVenda;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CanalVendaDto implements Dto<CanalVenda> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;
  private String nome;

  public static CanalVendaDto from(CanalVenda entity) {
    return ObjectUtils.convert(entity, CanalVendaDto.class);
  }
}
