package br.com.nuvy.api.estoque.dto;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoDeposito;
import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.validation.groups.DepositoTerceiroGroup;
import br.com.nuvy.validation.groups.DepositoTerceiroPoderGroup;
import br.com.nuvy.validation.groups.provider.DepositoGroupSequenceProvider;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.group.GroupSequenceProvider;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GroupSequenceProvider(DepositoGroupSequenceProvider.class)
public class DepositoDto implements Dto<Deposito> {

  private Integer id;
  @Size(max = 255)
  private String nome;
  @Size(max = 255)
  private String descricao;
  private TipoDeposito tipo;
  private Boolean venda = true;
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;
  @NotNull(groups = {DepositoTerceiroGroup.class, DepositoTerceiroPoderGroup.class})
  private Integer fornecedorId;
  @JsonProperty(access = Access.READ_ONLY)
  private String fornecedorNome;
  @JsonProperty(access = Access.READ_ONLY)
  private String fornecedorNomeInterno;
  @JsonProperty(access = Access.READ_ONLY)
  private String fornecedorCpfCnpj;
  @JsonProperty(access = Access.READ_ONLY)
  private Integer empresaId;
  @JsonProperty(access = Access.READ_ONLY)
  private String empresaNome;
  @JsonProperty(access = Access.READ_ONLY)
  private Boolean sistema = false;
  private Boolean permiteEstoqueNegativo;
  private boolean permiteReservaEstoque = false;

  public static DepositoDto from(Deposito entity) {
    return ObjectUtils.convert(entity, DepositoDto.class);
  }

  public void setAtivo(Boolean ativo) {
    if (Objects.nonNull(ativo)) {
      this.situacao = ativo ? Situacao.ATIVO : Situacao.INATIVO;
    }
  }

  public Boolean getAtivo() {
    return this.situacao == Situacao.ATIVO;
  }
}
