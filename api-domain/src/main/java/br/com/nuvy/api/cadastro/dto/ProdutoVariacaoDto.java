package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.enums.Situacao;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProdutoVariacaoDto {

  private Integer produtoId;
  private Situacao situacao;
  private Integer produtoPaiId;
  private String codigo;
  private String codigoEan;
  private BigDecimal preco;
  private BigDecimal quantidadeMinimaEstoque;
  private BigDecimal quantidadeMaximaEstoque;
  private BigDecimal quantidadeEstoqueAtual;
  private BigDecimal precoCustoInicial;
  private BigDecimal estoqueInicial;
  private BigDecimal pesoLiquido;
  private BigDecimal pesoBruto;

  private Integer variacaoIdUm;
  private String variacaoNomeUm;
  private Integer atributoIdUm;
  private String atributoNomeUm;

  private Integer variacaoIdDois;
  private String variacaoNomeDois;
  private Integer atributoIdDois;
  private String atributoNomeDois;

  private Integer variacaoIdTres;
  private String variacaoNomeTres;
  private Integer atributoIdTres;
  private String atributoNomeTres;

  private Integer variacaoIdQuatro;
  private String variacaoNomeQuatro;
  private Integer atributoIdQuatro;
  private String atributoNomeQuatro;

  private Integer empresaEstoqueInicialId;
  private Integer depositoEstoqueInicialId;

  @JsonProperty(access = JsonProperty.Access.READ_ONLY)
  private List<Imagem> imagens;

}
