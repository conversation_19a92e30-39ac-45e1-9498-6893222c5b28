package br.com.nuvy.api.venda.event.canalvenda;

import br.com.nuvy.api.enums.OrigemEventoHub;
import br.com.nuvy.api.notificacao.dto.NotificacaoHubDto;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
public class CanalVendaDepositoAlterado extends CanalVendaEvent {

  public CanalVendaDepositoAlterado(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCanalVenda,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idCanalVenda, ocorridoAs);
  }


  public NotificacaoHubDto buildNotificacaoHubDto() {
    return super.buildNotificacaoHubDto(null, OrigemEventoHub.CN_VENDA_ALTERADO_DEPOSITO);
  }
}
