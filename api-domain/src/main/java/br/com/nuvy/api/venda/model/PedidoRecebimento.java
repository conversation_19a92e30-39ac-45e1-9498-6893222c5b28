package br.com.nuvy.api.venda.model;

import br.com.nuvy.api.cadastro.model.CondicaoPagamento;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.FormaPagamento;
import br.com.nuvy.api.financeiro.model.Titulo;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vd_pedido_recebimento")
public class PedidoRecebimento extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @Column(name = "id_pedido_recebimento")
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;
  @Column(name = "nr_parcela")
  Integer numeroParcela;
  @Column(name = "dt_vencimento")
  private LocalDate dataVencimento;
  @Column(name = "vl_total_parcela")
  private BigDecimal valorTotalParcela;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_banco_conta")
  private ContaBancaria bancoConta;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_forma_pagamento")
  private FormaPagamento formaRecebimento;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_pedido")
  private Pedido pedido;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_cond_pagamento")
  private CondicaoPagamento condicaoRecebimento;

  @OneToMany(fetch = FetchType.LAZY)
  @JoinColumns({@JoinColumn(name = "id_pedido", referencedColumnName = "id_pedido"),
    @JoinColumn(name = "nr_parcela", referencedColumnName = "nr_parcela")})
  private List<Titulo> titulos;

  public PedidoRecebimento getDuplicado(Pedido pedido) {
    PedidoRecebimento pedidoRecebimento = new PedidoRecebimento();
    pedidoRecebimento.setPedido(pedido);
    pedidoRecebimento.setNumeroParcela(numeroParcela);
    pedidoRecebimento.setDataVencimento(dataVencimento);
    pedidoRecebimento.setValorTotalParcela(valorTotalParcela);
    pedidoRecebimento.setBancoConta(bancoConta);
    pedidoRecebimento.setFormaRecebimento(formaRecebimento);
    pedidoRecebimento.setCondicaoRecebimento(condicaoRecebimento);
    return pedidoRecebimento;
  }
}
