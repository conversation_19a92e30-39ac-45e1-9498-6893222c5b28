package br.com.nuvy.api.jornada;

import br.com.nuvy.api.enums.JornadaEnum.EtapaEnum;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "ad_jornada_etapa")
public class JornadaEtapa implements Entidade<UUID> {

  @Id
  @Column(name = "id_jornada_etapa")
  @GeneratedValue(strategy = GenerationType.UUID)
  private UUID id;

  @Column(name = "etapa", columnDefinition = "varchar(100)", nullable = false, updatable = false)
  @Enumerated(EnumType.STRING)
  private EtapaEnum etapaEnum;

  @ManyToOne(fetch = FetchType.LAZY, targetEntity = Jornada.class)
  @JoinColumn(name = "id_jornada", nullable = false, updatable = false)
  private Jornada jornada;

  @Column(name = "ind_concluido", nullable = false)
  private Boolean concluido;
}
