package br.com.nuvy.api.financeiro.event.titulo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TituloExcluido extends EventoTitulo {

  public TituloExcluido(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario,
    Integer idTitulo, LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idTitulo, ocorridoAs);
  }
}
