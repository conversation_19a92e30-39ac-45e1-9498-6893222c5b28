package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.financeiro.model.TipoPagamento;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TipoPagamentoDto implements Dto<TipoPagamento> {
  private String id;
  private String descricao;

  public static TipoPagamentoDto from(TipoPagamento entity) {
    return ObjectUtils.convert(entity, TipoPagamentoDto.class);
  }
}
