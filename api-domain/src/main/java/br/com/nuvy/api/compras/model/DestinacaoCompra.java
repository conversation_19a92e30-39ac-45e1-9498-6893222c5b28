package br.com.nuvy.api.compras.model;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.RegimeTributario;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoDestinacao;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.List;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cc_dest")
public class DestinacaoCompra implements Entidade<Integer> {

  @Id
  @Column(name = "id_dest")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  @Column(name = "destinacao")
  private String nome;
  @Column(name = "tp_dest")
  @Enumerated(EnumType.STRING)
  private TipoDestinacao tipo;
  @Column(name = "ind_gera_cp")
  @Builder.Default
  private Boolean geraContasPagar = false;
  @Column(name = "ind_estoque")
  private Boolean geraMovimentacaoEstoque = false;
  @Column(name = "ind_custo_icms")
  private Boolean custoIcms = false;
  @Column(name = "ind_custo_ipi")
  private Boolean custoIpi = false;
  @Column(name = "ind_custo_pis")
  private Boolean custoPis = false;
  @Column(name = "ind_custo_cofins")
  private Boolean custoCofins = false;
  @Column(name = "ind_icms_st")
  private Boolean icmsSt = false;
  @Column(name = "ind_custo_frete")
  private Boolean custoFrete = false;
  @Column(name = "ind_custo_outras_desp")
  private Boolean custoOutrasDespesas = false;
  @Column(name = "ind_custo_seguro")
  private Boolean custoSeguro = false;
  @Column(name = "situacao")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_regime_tributario")
  private RegimeTributario regimeTributario;
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_plano_conta")
  private PlanoConta planoConta;
  @ManyToMany
  @JoinTable(
    name = "cc_dest_empresa",
    joinColumns = @JoinColumn(name = "id_dest"),
    inverseJoinColumns = @JoinColumn(name = "id_empresa"))
  private List<Empresa> empresas;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCfop> cfops;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCstPis> pisCsts;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCstCofins> cofinsCsts;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCsosnIcms> icmsCsosns;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCstIcms> icmsCsts;
  @OneToMany(mappedBy = "destinacaoCompra", cascade = CascadeType.ALL)
  private List<DestinacaoCstIpi> ipiCsts;
  @Transient
  private Boolean isAtualizaItensRelacionados;

  public static DestinacaoCompra of(Integer id) {
    if (id == null) {
      return null;
    }
    return DestinacaoCompra.builder()
      .id(id)
      .build();
  }
}
