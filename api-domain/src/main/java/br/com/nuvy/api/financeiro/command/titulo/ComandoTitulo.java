package br.com.nuvy.api.financeiro.command.titulo;

import br.com.nuvy.base.NuvyErpCommand;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ComandoTitulo extends NuvyErpCommand {

  private final Integer idTitulo;

  protected ComandoTitulo(
    UUID id, UUID idAplicacao, Integer idEmpresa, Integer idTitulo, LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, null, emitidoAs);
    this.idTitulo = idTitulo;
  }
}
