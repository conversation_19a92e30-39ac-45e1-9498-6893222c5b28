package br.com.nuvy.api.cadastro.model.nop;

import java.math.BigDecimal;

import br.com.nuvy.api.enums.CstIcms;
import br.com.nuvy.api.enums.ModalidadeBaseCalculo;
import br.com.nuvy.api.enums.ModalidadeBaseCalculoSt;
import br.com.nuvy.api.enums.MotivoDesoneracaoIcms;
import br.com.nuvy.api.enums.ModalidadeTipoCalculo;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.SituacaoTributariaIcms;
import br.com.nuvy.api.enums.TipoRegraCalculo;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString(doNotUseGetters = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public abstract class RegraIcmsItem extends MultitenantEntity implements Entidade<Integer> {

  @Column(name = "info_complementar")
  private String informacaoComplementar;
  @Column(name = "situacao")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;

  // - Contribuinte
  // icms simples nacional
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_csosn_icms")
  private CsosnIcms csosn;

  // icms lucro real
  @Enumerated(EnumType.STRING)
  @Column(name = "cod_cst_icms", length = 6)
  private CstIcms cst;
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc", length = 30)
  private ModalidadeBaseCalculo modalidadeBaseDeCalculo;
  @Column(name = "aliquota_icms", precision = 10, scale = 9)
  private BigDecimal aliquotaIcms;
  @Column(name = "aliquota_icms_estrangeiro", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsEstrangeiro;
  @Column(name = "aliquota_fcp", precision = 10, scale = 9)
  private BigDecimal aliquotaFcp;
  @Column(name = "perc_reducao_bc", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculo;
  @Column(name = "percentual_deferimento", precision = 10, scale = 9)
  private BigDecimal percentualDiferimento;
  @Builder.Default
  @Column(name = "ind_icms_desonerado", nullable = false, columnDefinition = "boolean default false")
  private Boolean desonerado = false;
  @Enumerated(EnumType.STRING)
  @Column(name = "motivo_desoneracao", length = 200)
  private MotivoDesoneracaoIcms motivoDesoneracao;

  // icms st
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc_st", length = 50)
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoSt;
  @Column(name = "aliquota_interestadual_icms_st")
  private BigDecimal aliquotaInterestadualIcmsSt;
  @Column(name = "perc_mva_st")
  private BigDecimal percentualMvaSt;
  @Column(name = "aliquota_fcp_st")
  private BigDecimal aliquotaFcpSt;
  @Column(name = "aliquota_interna_icms_st")
  private BigDecimal aliquotaInternaIcmsSt;
  @Column(name = "perc_reducao_bc_st")
  private BigDecimal percentualReducaoBaseCalculoSt;
  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_calculo_st", length = 50)
  private ModalidadeTipoCalculo modalidadeTipoCalculoSt;

  // icms produto estrangeiro
  @Column(name = "perc_mva_estrangeiro_st")
  private BigDecimal percentualMvaEstrangeiroSt;
  @Column(name = "aliquota_fcp_estrangeiro_st")
  private BigDecimal aliquotaFcpEstrangeiroSt;
  @Column(name = "aliquota_icms_estrangeiro_st")
  private BigDecimal aliquotaIcmsEstrangeiroSt;
  @Column(name = "perc_reducao_bc_estrangeiro_st")
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroSt;

  // Consumidor Final - Contribuinte
  // icms simples nacional
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_csosn_cf")
  private CsosnIcms csosnConsumidorFinal;
  @Builder.Default
  @Column(name = "ind_zera_mva_st_cf")
  private Boolean zeraMvaStConsumidorFinal = false;

  // icms lucro real
  @Enumerated(EnumType.STRING)
  @Column(name = "cod_cst_cf", length = 6)
  private CstIcms cstConsumidorFinal;
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc_cf", length = 30)
  private ModalidadeBaseCalculo modalidadeBaseDeCalculoConsumidorFinal;
  @Column(name = "aliquota_icms_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsConsumidorFinal;
  @Column(name = "aliquota_icms_estrangeiro_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsEstrangeiroConsumidorFinal;
  @Column(name = "aliquota_fcp_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpConsumidorFinal;
  @Column(name = "perc_reducao_bc_cf", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoConsumidorFinal;
  @Column(name = "percentual_deferimento_cf", precision = 10, scale = 9)
  private BigDecimal percentualDiferimentoConsumidorFinal;
  @Builder.Default
  @Column(name = "ind_icms_desonerado_cf", nullable = false, columnDefinition = "boolean default false")
  private Boolean desoneradoConsumidorFinal = false;
  @Enumerated(EnumType.STRING)
  @Column(name = "motivo_desoneracao_cf", length = 200)
  private MotivoDesoneracaoIcms motivoDesoneracaoConsumidorFinal;

  // icms st
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc_st_cf", length = 50)
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoStConsumidorFinal;
  @Column(name = "aliquota_interestadual_icms_st_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaInterestadualIcmsStConsumidorFinal;
  @Column(name = "perc_mva_st_cf", precision = 10, scale = 9)
  private BigDecimal percentualMvaStConsumidorFinal;
  @Column(name = "aliquota_fcp_st_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpStConsumidorFinal;
  @Column(name = "aliquota_interna_icms_st_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaInternaIcmsStConsumidorFinal;
  @Column(name = "perc_reducao_bc_st_cf", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoStConsumidorFinal;
  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_calculo_st_cf", length = 50)
  private ModalidadeTipoCalculo modalidadeTipoCalculoStConsumidorFinal;

  // icms produto estrangeiro
  @Column(name = "perc_mva_estrangeiro_st_cf", precision = 10, scale = 9)
  private BigDecimal percentualMvaEstrangeiroStConsumidorFinal;
  @Column(name = "aliquota_fcp_estrangeiro_st_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpEstrangeiroStConsumidorFinal;
  @Column(name = "aliquota_icms_estrangeiro_st_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsEstrangeiroStConsumidorFinal;
  @Column(name = "perc_reducao_bc_estrangeiro_st_cf", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroStConsumidorFinal;

  // icms difal
  @Builder.Default
  @Column(name = "ind_calc_difal_st_cf", nullable = false, columnDefinition = "boolean default false")
  private Boolean calculaDifalStConsumidorFinal = false;
  @Column(name = "aliquota_fcp_difal_interestadual_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpDifalInterestadualConsumidorFinal;
  @Column(name = "aliquota_interna_uf_destino_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaInternaUfDestinoConsumidorFinal;
  @Column(name = "aliquota_interestadual_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaInterestadualConsumidorFinal;
  @Column(name = "perc_reducao_bc_destino_cf", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoDestinoConsumidorFinal;
  @Column(name = "aliquota_interestadual_estrangeiro_cf", precision = 10, scale = 9)
  private BigDecimal aliquotaInterestadualProdutoEstrangeiroConsumidorFinal;
  @Enumerated(EnumType.STRING)
  @Column(name = "regra_calculo_cf", length = 20)
  private TipoRegraCalculo regraCalculoConsumidorFinal;

  // - Consumidor Final - Nao Contribuinte
  // icms simples nacional
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_csosn_cf_nc")
  private CsosnIcms csosnConsumidorFinalNc;

  // icms lucro real
  @Enumerated(EnumType.STRING)
  @Column(name = "cod_cst_cf_nc", length = 6)
  private CstIcms cstConsumidorFinalNc;
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc_cf_nc", length = 30)
  private ModalidadeBaseCalculo modalidadeBaseDeCalculoConsumidorFinalNc;
  @Column(name = "aliquota_icms_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsConsumidorFinalNc;
  @Column(name = "aliquota_icms_estrangeiro_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsEstrangeiroConsumidorFinalNc;
  @Column(name = "aliquota_fcp_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpConsumidorFinalNc;
  @Column(name = "perc_reducao_bc_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoConsumidorFinalNc;
  @Column(name = "percentual_deferimento_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualDiferimentoConsumidorFinalNc;
  @Builder.Default
  @Column(name = "ind_icms_desonerado_cf_nc", nullable = false, columnDefinition = "boolean default false")
  private Boolean desoneradoConsumidorFinalNc = false;
  @Enumerated(EnumType.STRING)
  @Column(name = "motivo_desoneracao_cf_nc", length = 200)
  private MotivoDesoneracaoIcms motivoDesoneracaoConsumidorFinalNc;

  // icms st
  @Enumerated(EnumType.STRING)
  @Column(name = "modalide_bc_st_cf_nc", length = 50)
  private ModalidadeBaseCalculoSt modalidadeBaseDeCalculoStConsumidorFinalNc;
  @Column(name = "aliquota_interestadual_icms_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaInterestadualIcmsStConsumidorFinalNc;
  @Column(name = "perc_mva_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualMvaStConsumidorFinalNc;
  @Column(name = "aliquota_fcp_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpStConsumidorFinalNc;
  @Column(name = "aliquota_interna_icms_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaInternaIcmsStConsumidorFinalNc;
  @Column(name = "perc_reducao_bc_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoStConsumidorFinalNc;
  @Enumerated(EnumType.STRING)
  @Column(name = "tipo_calculo_st_cf_nc", length = 50)
  private ModalidadeTipoCalculo modalidadeTipoCalculoStConsumidorFinalNc;

  // icms produto estrangeiro
  @Column(name = "perc_mva_estrangeiro_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualMvaEstrangeiroStConsumidorFinalNc;
  @Column(name = "aliquota_fcp_estrangeiro_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaFcpEstrangeiroStConsumidorFinalNc;
  @Column(name = "aliquota_icms_estrangeiro_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal aliquotaIcmsEstrangeiroStConsumidorFinalNc;
  @Column(name = "perc_reducao_bc_estrangeiro_st_cf_nc", precision = 10, scale = 9)
  private BigDecimal percentualReducaoBaseCalculoEstrangeiroStConsumidorFinalNc;

  // icms difal
  @Column(name = "aliquota_fcp_difal_interestadual_cf_nc")
  private BigDecimal aliquotaFcpDifalInterestadualConsumidorFinalNc;
  @Column(name = "aliquota_interna_uf_destino_cf_nc")
  private BigDecimal aliquotaInternaUfDestinoConsumidorFinalNc;
  @Column(name = "aliquota_interestadual_icms_cf_nc")
  private BigDecimal aliquotaInterestadualIcmsConsumidorFinalNc;
  @Column(name = "perc_reducao_bc_destino_cf_nc")
  private BigDecimal percentualReducaoBaseCalculoDestinoConsumidorFinalNc;
  @Column(name = "aliquota_interestadual_icms_estrangeiro_cf_nc")
  private BigDecimal aliquotaInterestadualIcmsEstrangeiroConsumidorFinalNc;
  @Enumerated(EnumType.STRING)
  @Column(name = "tp_regra_calculo_cf_nc", length = 50)
  private TipoRegraCalculo tipoRegraCalculoConsumidorFinalNc;

  public SituacaoTributariaIcms getSituacaoTributaria() {
    if (cst == null && csosn == null) {
      return null;
    }
    return SituacaoTributariaIcms.of(this);
  }

  public boolean isDesonerado() {
    return desonerado != null && desonerado;
  }

  public SituacaoTributariaIcms getSituacaoTributariaConsumidorFinal() {
    if (cstConsumidorFinal == null && csosnConsumidorFinal == null) {
      return null;
    }
    return SituacaoTributariaIcms.ofConsumidorFinal(this);
  }

  public boolean isDesoneradoConsumidorFinal() {
    return desoneradoConsumidorFinal != null && desoneradoConsumidorFinal;
  }

  public boolean isZeraMvaStConsumidorFinal() {
    return zeraMvaStConsumidorFinal != null && zeraMvaStConsumidorFinal;
  }

  public boolean isCalculaDifalStConsumidorFinal() {
    return calculaDifalStConsumidorFinal != null && calculaDifalStConsumidorFinal;
  }

  public SituacaoTributariaIcms getSituacaoTributariaConsumidorFinalNc() {
    if (cstConsumidorFinalNc == null && csosnConsumidorFinalNc == null) {
      return null;
    }
    return SituacaoTributariaIcms.ofConsumidorFinalNc(this);
  }

  public boolean isDesoneradoConsumidorFinalNc() {
    return desoneradoConsumidorFinalNc != null && desoneradoConsumidorFinalNc;
  }
}
