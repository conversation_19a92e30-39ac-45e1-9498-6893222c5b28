package br.com.nuvy.api.notificacao.dto;

import static br.com.nuvy.common.utils.ObjectUtils.get;

import br.com.nuvy.api.enums.OrigemNotificacao;
import br.com.nuvy.api.notificacao.model.Notificacao;
import br.com.nuvy.api.seguranca.model.Usuario;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class NotificacaoFrontDto {

  private Integer notificacaoId;
  @NotNull
  private String tenantId;
  @NotNull
  private Integer empresaId;
  private UUID usuarioId;
  @NotNull
  private OrigemNotificacao origem;
  @NotNull
  private Object payload;

  public static NotificacaoFrontDto notificacaoToFront(Notificacao notificacao, Object payload) {
    return NotificacaoFrontDto.builder()
      .empresaId(notificacao.getEmpresa().getId())
      .usuarioId(get(notificacao, Notificacao::getUsuario, Usuario::getId))
      .tenantId(notificacao.getAplicacao())
      .notificacaoId(notificacao.getId())
      .origem(notificacao.getOrigem())
      .payload(payload)
      .build();
  }
}
