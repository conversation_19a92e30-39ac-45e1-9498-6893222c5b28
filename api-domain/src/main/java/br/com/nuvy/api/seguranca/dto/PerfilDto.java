package br.com.nuvy.api.seguranca.dto;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.seguranca.model.Perfil;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PerfilDto implements Dto<Perfil> {

  private Integer id;

  @NotNull
  @Size(min = 1, max = 150)
  private String nome;

  @NotNull
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;

  public static PerfilDto from(Perfil entidade) {
    return ObjectUtils.convert(entidade, PerfilDto.class);
  }

}