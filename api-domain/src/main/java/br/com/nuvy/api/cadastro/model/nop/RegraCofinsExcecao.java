package br.com.nuvy.api.cadastro.model.nop;

import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.model.OrigemMercadoria;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.TipoRegraExcecao;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.*;
import lombok.experimental.SuperBuilder;
import java.math.BigDecimal;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cd_regra_cofins_excecao")
public class RegraCofinsExcecao extends RegraCofins implements Entidade<Integer> {

  @Id
  @Column(name = "id_regra_cofins_excecao")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "tp_excecao")
  private TipoRegraExcecao tipoExcecao;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_regra_cofins")
  private RegraCofinsPadrao regra;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_produto")
  private Produto produto;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_ncm")
  private Ncm ncm;

  @Deprecated
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_origem_mercadoria")
  private OrigemMercadoria origemMercadoria;

  @ManyToMany
  @JoinTable(name = "cd_regra_cofins_excecao_orig_merc",
    joinColumns = @JoinColumn(name = "id_regra_cofins_excecao", referencedColumnName = "id_regra_cofins_excecao"),
    inverseJoinColumns = @JoinColumn(name = "id_origem_mercadoria", referencedColumnName = "id_origem_mercadoria"))
  private List<OrigemMercadoria> origensMercadoria;
}
