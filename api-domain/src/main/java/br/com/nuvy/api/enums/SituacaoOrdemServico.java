package br.com.nuvy.api.enums;

public enum SituacaoOrdemServico {
  FATURADO, ORCAMENTO, NF_EMITIDA, NF_CANCELADA, CANCELADO, APROVADO, RECORRENCIA, FINALIZADO, CONCLUIDO,NF_REJEITADA, EM_CANCELAMENTO;


  public static SituacaoOrdemServico of(String situacao) {
    for (SituacaoOrdemServico s : SituacaoOrdemServico.values()) {
      if (s.name().equalsIgnoreCase(situacao)) {
        return s;
      }
    }
    return null;
  }
}
