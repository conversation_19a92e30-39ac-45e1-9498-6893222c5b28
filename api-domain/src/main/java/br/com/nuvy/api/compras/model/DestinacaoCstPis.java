package br.com.nuvy.api.compras.model;

import br.com.nuvy.api.cadastro.model.nop.CstPis;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cc_dest_pis_cst")
public class DestinacaoCstPis implements Entidade<Integer> {

  @Id
  @Column(name = "id_dest_pis_cst")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  @ManyToOne
  @JoinColumn(name = "id_dest")
  private DestinacaoCompra destinacaoCompra;
  @ManyToOne
  @JoinColumn(name = "cod_cst_pis_origem")
  private CstPis cstPisOrigem;
  @ManyToOne
  @JoinColumn(name = "cod_cst_pis_entrada")
  private CstPis cstPisEntrada;
  @ManyToOne
  @JoinColumn(name = "cod_cst_pis_devolucao")
  private CstPis cstPisDevolucao;

}
