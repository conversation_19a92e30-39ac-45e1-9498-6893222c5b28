package br.com.nuvy.api.enums;

import lombok.Getter;

@Getter
public enum SituacaoTitulo {
  ABERTO("Aberto"),
  PAGO("Pago"),
  PAGO_PARCIAL("Pago parcial"),
  RECEBIDO("Recebido"),
  RECEBIDO_PARCIAL("Recebido parcial"),
  CONCILIADO("Conciliado"),
  AGENDADO_CNAB("Agendado CNAB"),
  VENCIDA("Vencida"),
  A_VENCER("A vencer"),
  VENCE_HOJE("Vence hoje");

  private final String descricao;

  SituacaoTitulo(String descricao) {
    this.descricao = descricao;
  }
}
