package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.enums.TipoTransacao;
import br.com.nuvy.api.financeiro.model.Transacao;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter; import lombok.Setter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransacaoDto implements Dto<Transacao> {

  private Integer id;
  private TipoTransacao tipo;
  private LocalDateTime dataTransacao;
  private BigDecimal valorTransacao;

  public static TransacaoDto from(Transacao entidade) {
    return ObjectUtils.convert(entidade, TransacaoDto.class);
  }
}
