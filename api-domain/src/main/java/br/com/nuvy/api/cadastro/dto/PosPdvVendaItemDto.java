package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.PosPdvVendaItem;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PosPdvVendaItemDto implements Dto<PosPdvVendaItem> {

  private Integer id;
  private UUID posPdvVendaId;
  private Integer produtoId;
  private String produtoDescricao;
  private String produtoCodigo;
  private Integer unidadeMedidaId;
  private String unidadeMedidaUnidade;
  private String produtoPosId;
  private Integer vendedorId;
  private BigDecimal quantidade;
  private BigDecimal precoVenda;
  private BigDecimal precoUnitario;
  private BigDecimal desconto;
  private BigDecimal acrescimo;
  private String nfceNcm;
  private String nfceCfop;
  private String nfceCst;
  private BigDecimal nfceAliqIcms;
  private String nfceCest;
  private String nfceCstPis;
  private BigDecimal nfceAliqPis;
  private String nfceCstCofins;
  private BigDecimal nfceAliqCofins;
  private BigDecimal nfceCodAnp;

  private Integer depositoId;
  private String depositoNome;

  private LocalDateTime createDat;
  private LocalDateTime updateDat;

  public static PosPdvVendaItemDto from(PosPdvVendaItem entity) {
    return ObjectUtils.convert(entity, PosPdvVendaItemDto.class);
  }
}
