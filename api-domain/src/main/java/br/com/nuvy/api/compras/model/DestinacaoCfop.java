package br.com.nuvy.api.compras.model;

import br.com.nuvy.api.cadastro.model.Cfop;
import br.com.nuvy.common.base.entity.Entidade;
import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cc_dest_cfop")
public class DestinacaoCfop implements Serializable, Entidade<Integer> {

  @Id
  @Column(name = "id_dest_cfop")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  @ManyToOne
  @JoinColumn(name = "id_dest")
  private DestinacaoCompra destinacaoCompra;
  @ManyToOne
  @JoinColumn(name = "cod_cfop_origem")
  private Cfop cfopOrigem;
  @ManyToOne
  @JoinColumn(name = "cod_cfop_entrada")
  private Cfop cfopEntrada;
  @ManyToOne
  @JoinColumn(name = "cod_cfop_devolucao")
  private Cfop cfopDevolucao;
}