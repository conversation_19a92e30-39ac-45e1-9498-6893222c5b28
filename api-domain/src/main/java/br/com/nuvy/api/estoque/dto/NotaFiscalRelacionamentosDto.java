package br.com.nuvy.api.estoque.dto;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.TipoFrete;
import br.com.nuvy.api.enums.TipoTransporte;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaItem;
import br.com.nuvy.api.estoque.model.NotaFiscalEntradaPagamento;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotaFiscalRelacionamentosDto {

  private String chaveAcesso;
  private String xml;
  private TipoFrete tipoFrete;
  private Pessoa fornecedor;
  private List<NotaFiscalEntradaItem> items;
  private NotaFiscalEntradaPagamento notaFiscalEntradaPagamento;
  private TipoTransporte tipoTransporte;

}
