package br.com.nuvy.api.venda.model;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.common.base.entity.Entidade;
import java.math.BigDecimal;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vd_meta_canal")
public class MetaCanalVenda implements Entidade<Integer> {

  @Id
  @Column(name = "id_meta_canal")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;
  @Column(name = "mes_referencia")
  private Integer mes;
  @Column(name = "ano_referencia")
  private Integer ano;
  @Column(name = "vl_meta", columnDefinition = "NUMERIC(21,10)")
  private BigDecimal valor;
  @ManyToOne(targetEntity = CanalVenda.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_canal")
  private CanalVenda canalVenda;
  @ManyToOne(targetEntity = Empresa.class)
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

}
