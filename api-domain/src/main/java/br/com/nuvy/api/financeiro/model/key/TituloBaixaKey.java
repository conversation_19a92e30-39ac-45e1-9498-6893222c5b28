package br.com.nuvy.api.financeiro.model.key;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Embeddable
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class TituloBaixaKey implements Serializable {

  @Column(name = "id_titulo", insertable = false, updatable = false)
  @EqualsAndHashCode.Include
  private Integer idTitulo;

  @Column(name = "id_baixa", insertable = false, updatable = false)
  @EqualsAndHashCode.Include
  private Integer idBaixa;

  public TituloBaixaKey() {
    super();
  }
}