package br.com.nuvy.api.cadastro.model;

import lombok.RequiredArgsConstructor;
import lombok.Value;
import java.util.List;

@Value
@RequiredArgsConstructor(staticName = "of")
public class ExtIpi {
  String id;
  public static List<ExtIpi> all() {
    return List.of(
      ExtIpi.of("01"),
      ExtIpi.of("02"),
      ExtIpi.of("03"),
      ExtIpi.of("04"),
      ExtIpi.of("05"),
      ExtIpi.of("06"),
      ExtIpi.of("07"),
      ExtIpi.of("08"),
      ExtIpi.of("09"),
      ExtIpi.of("10"),
      ExtIpi.of("11"),
      ExtIpi.of("99")
    );
  }
}
