package br.com.nuvy.api.enums;

import lombok.Getter;

@Getter
public enum TipoContratacaoFormaPagamento {
  BOLETO("1", "Boleto"),
  CARTAO_CREDITO("2", "Cartão de crédito"),

  DESCONHECIDO("", "Desconhecido");

  private final String codigo;
  private final String descricao;

  TipoContratacaoFormaPagamento(String codigo, String descricao) {
    this.codigo = codigo;
    this.descricao = descricao;
  }

  public static TipoContratacaoFormaPagamento of(String codigo) {
    for (var value : values()) {
      if (value.getCodigo().equals(codigo)) {
        return value;
      }
    }
    return DESCONHECIDO;
  }
}
