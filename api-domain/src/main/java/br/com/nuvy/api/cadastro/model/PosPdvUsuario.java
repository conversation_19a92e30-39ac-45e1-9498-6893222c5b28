package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cd_pos_pdv_usuario")
public class PosPdvUsuario implements Entidade<UUID> {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id")
  private UUID id;

  @TenantId
  @Column(
    name = "id_aplicacao",
    columnDefinition = "char(36)",
    updatable = false)
  private String aplicacao;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id_empresa", nullable = false)
  private Empresa empresa;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_usuario", nullable = false)
  private Usuario usuario;

  @Column(name = "created_at", insertable = false, nullable = false)
  private LocalDateTime createDat;

  @Column(name = "updated_at", insertable = false, nullable = false)
  private LocalDateTime updateDat;

  @Column(name = "ativo", nullable = false)
  private boolean ativo;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id_pospdv", nullable = false)
  private PosPdv posPdv;

  @Column(name = "id_externo", nullable = true, length = 255)
  private String idExterno;

  @Transient
  private Integer password;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    PosPdvUsuario posPdvUsuario = (PosPdvUsuario) o;
    return Objects.equals(id, posPdvUsuario.id);
  }

  @Override
  public int hashCode() {
    return id != null ? id.hashCode() : 0;
  }

}
