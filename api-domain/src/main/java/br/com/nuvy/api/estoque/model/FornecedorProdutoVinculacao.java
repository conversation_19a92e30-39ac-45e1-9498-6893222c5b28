package br.com.nuvy.api.estoque.model;

import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.*;

@Getter
@Setter
@Builder
@EqualsAndHashCode(of = {"fornecedor", "codigoProdutoFornecedor"}, callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(
  name = "eq_fornecedor_produto_vinculacao",
  uniqueConstraints = {
    @UniqueConstraint(
      name = "uk_fornecedor_codigo_produto",
      columnNames = {"id_fornecedor", "cod_produto_fornecedor", "id_aplicacao"}
    )
  }
)
public class FornecedorProdutoVinculacao extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id_vinculacao")
  private UUID id;

  @ManyToOne
  @JoinColumn(name = "id_fornecedor", nullable = false)
  private Pessoa fornecedor;

  @Column(name = "cod_produto_fornecedor", nullable = false, length = 100)
  private String codigoProdutoFornecedor;

  @ManyToOne
  @JoinColumn(name = "id_produto", nullable = false)
  private Produto produto;

  @Column(name = "descricao_produto_fornecedor", length = 255)
  private String descricaoProdutoFornecedor;

  @Column(name = "dt_ultima_atualizacao")
  private LocalDateTime dataUltimaAtualizacao;

  @Column(name = "created_at", insertable = false, updatable = false)
  private LocalDateTime createdAt;

  @Column(name = "updated_at", insertable = false, updatable = false)
  private LocalDateTime updatedAt;
}