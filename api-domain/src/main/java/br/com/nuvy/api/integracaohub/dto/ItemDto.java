package br.com.nuvy.api.integracaohub.dto;

import br.com.nuvy.api.integracaohub.enums.Status;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public  class ItemDto {

  private String title;
  private String categoryId;
  private BigDecimal price;
  private Status status;
  private String currencyId;
  private BigInteger availableQuantity;
  private String buyingMode;
  private String condition;
  private String listingTypeId;
  private List<SaleTermsDto> saleTerms;
  private List<PicturesDto> pictures;
  private List<AttributesDto> attributes;
  private DimensionDto dimension;
  private ShippingDto shipping;
  private List<VariationsDto> variations;

}
