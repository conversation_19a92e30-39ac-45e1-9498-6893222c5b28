package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.common.utils.ObjectUtils;
import java.math.BigDecimal;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProdutoRelatorioDto {

  //Principais
  private Situacao situacao;
  private String codigo;
  private String descricao;
  private String empresaNome;
  private BigDecimal precoVenda = BigDecimal.ZERO;
  private UnidadeMedidaDto unidadeMedida;
  private BigDecimal valorCustoMedio = BigDecimal.ZERO;

  //Adicionais
  private String codigoEan;
  private Integer diasCrossdocking;
  private Integer diasGarantia;
  private String descricaoCompleta;
  private String observacao;
  private Boolean controlaLoteValidade = false;
  private BigDecimal quantidadeMinimaEstoque;
  private BigDecimal precoCustoInicial;
  private NcmDto ncm;
  private CestDto cest;
  private TipoMercadoriaDto tipoMercadoria;
  private OrigemMercadoriaDto origemMercadoria;
  private BigDecimal largura;
  private BigDecimal altura;
  private BigDecimal profundidade;
  private BigDecimal pesoLiquido;
  private BigDecimal pesoBruto;
  private Set<QualificacaoProdutoDto> qualificacoes;
  private String marca;
  private String modelo;
  private CategoriaProdutoResumoDto categoriaProduto;

  private String atributoNomeUm;
  private String atributoNomeDois;
  private String atributoNomeTres;
  private String atributoNomeQuatro;

  public String getDescricao() {
    StringBuilder sb = new StringBuilder(descricao);

    if (atributoNomeUm != null && !atributoNomeUm.isEmpty()) {
      sb.append(" - ").append(atributoNomeUm);
    }

    if (atributoNomeDois != null && !atributoNomeDois.isEmpty()) {
      sb.append(" - ").append(atributoNomeDois);
    }

    if (atributoNomeTres != null && !atributoNomeTres.isEmpty()) {
      sb.append(" - ").append(atributoNomeTres);
    }

    if (atributoNomeQuatro != null && !atributoNomeQuatro.isEmpty()) {
      sb.append(" - ").append(atributoNomeQuatro);
    }

    return sb.toString();
  }

  public static ProdutoRelatorioDto from(Produto entity) {
    var dto = ObjectUtils.convert(entity, ProdutoRelatorioDto.class);

    var variacoes = entity.getVariacoes();
    for (int i = 0; i < variacoes.size(); i++) {
      if (i == 0) {
        dto.setAtributoNomeUm(variacoes.get(i).getVariacaoProdutoValor().getValor());
      } else if (i == 1) {
        dto.setAtributoNomeDois(variacoes.get(i).getVariacaoProdutoValor().getValor());
      } else if (i == 2) {
        dto.setAtributoNomeTres(variacoes.get(i).getVariacaoProdutoValor().getValor());
      } else if (i == 3) {
        dto.setAtributoNomeQuatro(variacoes.get(i).getVariacaoProdutoValor().getValor());
      }
    }
    return dto;
  }
}
