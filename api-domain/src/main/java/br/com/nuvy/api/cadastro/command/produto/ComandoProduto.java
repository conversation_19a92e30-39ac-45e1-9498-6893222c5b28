package br.com.nuvy.api.cadastro.command.produto;

import br.com.nuvy.base.NuvyErpCommand;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class ComandoProduto extends NuvyErpCommand {

  Integer idProduto;

  protected ComandoProduto(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idProduto,
    LocalDateTime emitidoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, emitidoAs);
    this.idProduto = idProduto;
  }
}
