package br.com.nuvy.api.enums;

import lombok.Getter;

@Getter
public enum OrigemNotificacao {

  MARKETPLACE_NOVA_VENDA("Nova venda"),

  PRODUTO_ESTOQUE_MINIMO("Estoque mínimo atingido"),

  EMISSAO_NOTA("Emissão de nota fiscal"),
  NOTA_FISCAL_REJEITADA("Nota fiscal rejeitada!"),
  NOTA_FISCAL_DENEGADA("Nota fiscal denegada!"),
  NOTA_FISCAL_EMITIDA("Nota fiscal emitida"),
  NOTA_FISCAL_RECEBIDA("Nota fiscal recebida"),
  NOTA_FISCAL_MANIFESTADA("Nota fiscal manifestada!"),
  NOTA_FISCAL_CANCELADA("Nota fiscal cancelada"),
  NOTA_FISCAL_DEVOLUCAO_EMITIDA("Nota fiscal devolução emitida!"),
  NOTA_FISCAL_RETORNO_EMITIDA("Nota fiscal retorno emitida!"),

  PORTAL_PARCEIRO_APROVADO("Parceiro aprovado no portal"),
  PORTAL_PARCEIRO_REJEITADO("Parceiro rejeitado no portal"),
  PORTAL_PARCEIRO_ANEXO("Novo anexo no portal"),
  PORTAL_TICKET_ABERTO("Novo ticket aberto no portal"),
  PORTAL_TICKET_ATUALIZADO("Ticket atualizado no portal"),
  PORTAL_VINCULO_REMOVIDO("Vínculo com parceiro removido"),

  IMPORTACAO_XML_PRODUTOS("Importação dos dados de produtos finalizada"),
  IMPORTACAO_XML_CLIENTE("Importação dos dados de clientes finalizada"),
  IMPORTACAO_XML_FORNECEDOR("Importação dos dados de fornecedores finalizada"),
  PLANILHA_IMPORTACAO("Importação de dados finalizada"),
  PLANILHA_PRODUTOS("Planilha de produtos importada"),
  PLANILHA_PARCEIROS("Planilha de parceiros importada"),
  PLANILHA_INVENTARIO("Planilha de inventário importada"),
  IMPORTACAO_CONTAS_PAGAR_RECEBER("Planilha de contas pagar/receber importada"),

  ORCAMENTO_APROVADO("Orçamento aprovado"),

  CALLFACE_LIGACAO_PERDIDA("Ligação perdida"),

  MENSALIDADE_NUVY("Mensalidade Nuvy"),

  PEDIDO_FATURADO("Pedido faturado"),
  VENDA_MARKETPLACE("Nova venda!"),
  NF_RECEBIDA("Nota fiscal recebida!"),
  RECIBO_SERVICO_EMITIDO("Recibo de Serviço Emitido"),
  NOTA_FISCAL_SERVICO_EMITIDA("Nota Fiscal de Serviço Emitida");

  private final String descricao;

  OrigemNotificacao(String descricao) {
    this.descricao = descricao;
  }
}
