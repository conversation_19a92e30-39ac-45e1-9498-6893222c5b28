package br.com.nuvy.api.venda.model;

import br.com.nuvy.api.anuncio.Catalogo;
import br.com.nuvy.api.anuncio.Integracao;
import br.com.nuvy.api.anuncio.PagamentoCanalVenda;
import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.enums.Marketplace;
import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.api.enums.SituacaoPedido;
import br.com.nuvy.api.enums.TipoAnuncio;
import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "aplicacao", "situacao", "nome"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vd_canal_venda")
public class CanalVenda implements Entidade<Integer> {

  @Id
  @Column(name = "id_canal")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @TenantId
  @Column(
    name = "id_aplicacao",
    columnDefinition = "char(36)",
    updatable = false)
  private String aplicacao;

  @Column(name = "situacao")
  @Enumerated(EnumType.STRING)
  private Situacao situacao;

  @Column(name = "nome")
  private String nome;

  @Column(name = "tp_anuncio")
  @Enumerated(EnumType.STRING)
  private TipoAnuncio tipoAnuncio;

  @Column(name = "descricao_nop")
  private String descricaoNop;

  @Column(name = "ind_faturamento")
  private Boolean faturamentoAutomatico;

  @Column(name = "percentual_protecao_estoque")
  private BigDecimal percentualProtecaoEstoque;

  /**
   * @deprecated
   */
  @Column(name = "ind_mapeamento_categorias")
  private Boolean mapeiaCategorias;

  @Column(name = "fase_venda")
  @Enumerated(EnumType.STRING)
  private SituacaoPedido faseVenda;

  @ManyToOne(targetEntity = CanalOrigem.class)
  @JoinColumn(name = "id_origem")
  private CanalOrigem origem;

  @ManyToOne(targetEntity = Integracao.class)
  @JoinColumn(name = "id_integracao")
  private Integracao integracao;

  @ManyToOne(targetEntity = TabelaPreco.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_tab_preco")
  private TabelaPreco tabelaPreco;

  @ManyToOne(targetEntity = Nop.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_nop")
  private Nop nop;

  @ManyToOne(targetEntity = Deposito.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_deposito")
  private Deposito deposito;

  @OneToMany(mappedBy = "canalVenda", fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST,
    CascadeType.MERGE})
  private List<PagamentoCanalVenda> pagamentoCanalVendas;

  @ManyToOne(targetEntity = Catalogo.class)
  @JoinColumn(name = "id_catalogo")
  private Catalogo catalogo;

  @ManyToOne(targetEntity = PlanoConta.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_plano_conta")
  private PlanoConta tipoReceita;

  @ManyToOne(targetEntity = ContaBancaria.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_conta_bancaria")
  private ContaBancaria contaBancaria;

  @ManyToOne
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

  @Column(name = "token_marketplace", length = 200)
  private String tokenMarketplace;

  @Column(name = "email")
  private String email;

  @Column(name = "username")
  private String username;

  @Column(name = "password")
  private String password;

  @Column(name = "store_id")
  private String storeId;

  @Column(name = "integration_key")
  private String integrationKey;

  @Column(name = "integrar_produtos_automaticamente")
  @Builder.Default
  private Boolean integrarProdutosAutomaticamente = false;

  public static CanalVenda of(Integer id) {
    if (id == null) {
      return null;
    }
    CanalVenda ret = new CanalVenda();
    ret.id = id;
    return ret;
  }

  public boolean isMapeiaCategorias() {
    return Marketplace.fromCanalOrigem(origem)
      .map(Marketplace::isUsaMapeamentoCategoria)
      .orElse(false);
  }
}
