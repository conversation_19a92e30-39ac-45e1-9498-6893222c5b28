package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.financeiro.model.Cnab;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

  @Getter
  @Setter
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public class CnabArquivoDto implements Dto<Cnab> {

    private Integer id;
    private String situacao;
    private Integer bancoId;
    private String bancoNome;
    private LocalDateTime dataCnab;

    public static CnabArquivoDto from(Cnab entity) {
      return ObjectUtils.convert(entity, CnabArquivoDto.class);
    }
  }