package br.com.nuvy.api.cadastro.dto;

import static br.com.nuvy.api.importacao.XlsService.*;
import br.com.nuvy.api.cadastro.model.Cnae;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.api.cadastro.model.RegimeTributario;
import br.com.nuvy.api.cadastro.model.Segmento;
import br.com.nuvy.api.cadastro.model.Setor;
import br.com.nuvy.api.enums.SituacaoParceiro;
import br.com.nuvy.api.enums.TipoEndereco;
import br.com.nuvy.api.enums.TipoIE;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.api.financeiro.model.Banco;
import br.com.nuvy.api.importacao.XlsDto;
import br.com.nuvy.api.importacao.XlsService;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import br.com.nuvy.validation.groups.PessoaFisicaGroup;
import br.com.nuvy.validation.groups.PessoaJuridicaGroup;
import br.com.nuvy.validation.groups.provider.PessoaXlsxSequenceProvider;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.poi.ss.usermodel.Cell;
import org.hibernate.validator.constraints.br.CNPJ;
import org.hibernate.validator.constraints.br.CPF;
import org.hibernate.validator.group.GroupSequenceProvider;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@GroupSequenceProvider(PessoaXlsxSequenceProvider.class)
public class PessoaXlsxDto extends XlsDto implements Dto<Pessoa>  {

  private Integer id;
  @NotNull(message = "[Tipo de pessoa] {jakarta.validation.constraints.NotNull.message}")
  private TipoPessoa tipo;

  @NotBlank(message = "[CPF/CNPJ] {jakarta.validation.constraints.NotBlank.message}")
  @CPF(groups = PessoaFisicaGroup.class, message = "[CPF/CNPJ] {org.hibernate.validator.constraints.br.CPF.message}")
  @CNPJ(groups = PessoaJuridicaGroup.class, message = "[CPF/CNPJ] {org.hibernate.validator.constraints.br.CNPJ.message}")
  private String cpfCnpj;

  //TODO verificar esta validacao
  @NotBlank(message = "[Nome/Raz\u00e3o Social] {jakarta.validation.constraints.NotBlank.message}")
  @Size(max = 255, message = "[Nome/Raz\u00e3o Social] {jakarta.validation.constraints.Size.message}")
  private String nome;

  @NotBlank(message = "[Nome Fantasia] {jakarta.validation.constraints.NotBlank.message}", groups = PessoaJuridicaGroup.class)
  @Size(max = 255, message = "[Nome Fantasia] {jakarta.validation.constraints.Size.message}")
  private String nomeFantasia;
  @NotBlank(message = "[Telefone/Celular] {jakarta.validation.constraints.NotBlank.message}")
  @Pattern(regexp="(\\d{11})|(\\d{10})", message = "[Telefone/Celular] formato.invalido")
  private String telefone;

  @Size(max = 150, message = "[Site] {jakarta.validation.constraints.Size.message}")
  private String site;

  @Size(min = 1, message = "[Rela\u00e7\u00e3o comercial] pelo.menos.uma.relacao.comercial.necessario")
  private Set<TipoRelacionamento> relacaoComercial = new HashSet<>();
  private List<String> qualificacaoNome;
  @Valid
  @NotEmpty(groups = {PessoaFisicaGroup.class, PessoaJuridicaGroup.class})
  private List<EnderecoPessoaDto> enderecos;

  @Valid
  private List<ContatoPessoaXlsxDto> contatos;
  private String nomeSetor;
  private String limiteCredito;
  @Size(max = 255, message = "[Nome do Vendedor] {jakarta.validation.constraints.Size.message}")
  private String nomeVendedor;
  @Size(max = 14, message = "[CPF/CNPJ do Vendedor] {jakarta.validation.constraints.Size.message}")
  private String cpfCnpjVendedor;
  private String observacoesInternas;
  @Size(max = 100, message = "[Email de Cobran\u00e7a] {jakarta.validation.constraints.Size.message}")
  @NotBlank(message = "[Email de Cobran\u00e7a] {jakarta.validation.constraints.NotBlank.message}")
  @Email(message = "[Email de Cobran\u00e7a] {jakarta.validation.constraints.Email.message}")
  private String emailCobranca;
  private boolean incluirDadosBancarios;

  @Size(max = 150, message = "[Nome do Titular] {jakarta.validation.constraints.Size.message}")
  @NotBlank(message = "[Nome do Titular] {jakarta.validation.constraints.NotBlank.message}")
  private String nomeTitularConta;
  @NotBlank(message = "[CPF/CNPJ do Titular] {jakarta.validation.constraints.NotBlank.message}")
  private String cpfTitularConta;
  private String chavePixConta;
  @NotBlank(message = "[Banco] {jakarta.validation.constraints.NotBlank.message}")
  @Pattern(regexp = "-?\\d+(\\.\\d+)?", message = "[Banco] apenas.numeros.sao.aceito")
  private String codigoBanco;
  @Size(max = 20, message = "[Ag\u00eancia] {jakarta.validation.constraints.Size.message}")
  @NotBlank(message = "[Ag\u00eancia] {jakarta.validation.constraints.NotBlank.message}")
  @Pattern(regexp = "-?\\d+(\\.\\d+)?", message = "[Ag\u00e2ncia] apenas.numeros.sao.aceito")
  private String agenciaConta;
  @Size(max = 20, message = "[Conta Corrente] {jakarta.validation.constraints.Size.message}")
  @NotBlank(message = "[Conta Corrente] {jakarta.validation.constraints.NotBlank.message}")
  @Pattern(regexp = "-?\\d+(\\.\\d+)?", message = "[Conta Corrente] apenas.numeros.sao.aceito")
  private String numeroConta;
  @Size(max = 25, message = "[Inscri\u00e7\u00e3o Estadual] {jakarta.validation.constraints.Size.message}")
  private String inscricaoEstadual;
  @Size(max = 25, message = "[Inscri\u00e7\u00e3o Municipal] {jakarta.validation.constraints.Size.message}")
  private String inscricaoMunicipal;
  private String nomeRegimeTributario;
  private String codigoCnae;
  private String nomeSegmento;
  @Size(max = 22, message = "[Inscri\u00e7\u00e3o Suframa] {jakarta.validation.constraints.Size.message}")
  private String suframa;
  private String observacaoNotaFiscal;
  private String relacCliente;
  private String relacFornecedor;
  private String relacTransportadora;
  private String relacColaborador;
  private String relacVendedor;
  @Email(message = "[Email para envio da nota fiscal] {jakarta.validation.constraints.Email.message}")
  @Size(max = 100, message = "[Email para envio da nota fiscal] {jakarta.validation.constraints.Size.message}")
  private String emailNotaFiscal;
  private Setor setor;
  private RegimeTributario regimeTributario;
  private Cnae cnae;
  private Segmento segmento;
  private Banco banco;
  private Set<QualificacaoPessoaDto> qualificacoes;
  private PessoaResumoDto vendedor;
  @NotNull(message = "[Contribuinte ICMS] {jakarta.validation.constraints.NotNull.message}")
  private TipoIE tipoIe;
  private SituacaoParceiro situacao;
  private boolean enderecoUnico;

  public PessoaXlsxDto XlsxDto(List<Cell> cells, List<Cell> header) {
    XlsService.cells = cells;
    XlsService.headerXlsNames = header;

    tipo = getEnum(TipoPessoa.class, "Tipo de pessoa");
    nome = getStringCell("Nome/Razão Social");
    cpfCnpj = getStringDigits("CPF/CNPJ", true);
    nomeFantasia = getStringCell("Nome Fantasia");
    telefone = getStringCell("Telefone/Celular").replace(" ", "");
    site = getStringCell("Site");
    situacao = SituacaoParceiro.ATIVO;

    validPutRelacionamento(getBooleanCell("Cliente"), TipoRelacionamento.CLIENTE);
    validPutRelacionamento(getBooleanCell("Fornecedor"), TipoRelacionamento.FORNECEDOR);
    validPutRelacionamento(getBooleanCell("Transportadora"), TipoRelacionamento.TRANSPORTADORA);
    validPutRelacionamento(getBooleanCell("Colaborador"), TipoRelacionamento.COLABORADOR);
    validPutRelacionamento(getBooleanCell("Vendedor"), TipoRelacionamento.VENDEDOR);

    qualificacaoNome = getStringList("Qualificação");

    List<EnderecoPessoaDto> pessoaEnderecos = new ArrayList<>();
    EnderecoPessoaDto cobranca = EnderecoPessoaDto.builder()
      .cep(getStringCellNull("CEP"))
      .uf(getStringCellNull("UF"))
      .cidade(getStringCellNull("Cidade"))
      .bairro(getStringCellNull("Bairro"))
      .endereco(getStringCellNull("Endereço"))
      .numero(getStringCellNull("Número"))
      .telefone(getStringCell("Telefone/Celular").replace(" ", ""))
      .complemento(getStringCell("Complemento"))
      .tipo(TipoEndereco.FISCAL)
      .build();
    pessoaEnderecos.add(cobranca);

    EnderecoPessoaDto entrega = new EnderecoPessoaDto();
    if (getBooleanCellMandatory("Este endereço é o mesmo que o de entrega?")) {
      enderecoUnico = true;
      entrega = cobranca;
    } else {
      entrega.setCpfCnpj(getStringDigits("CPF/CNPJ Entrega", true));
      entrega.setRazaoSocial(getStringCellNull("Nome/Razão Social Entrega"));
      entrega.setIncricaoEstadual(getStringDigits("Inscrição Estadual Entrega", false,true, true));
      entrega.setTelefone(getStringDigits("Telefone/Celular Entrega", false));
      entrega.setCep(getStringCellNull("CEP Entrega"));
      entrega.setUf(getStringCellNull("UF Entrega"));
      entrega.setCidade(getStringCellNull("Cidade Entrega"));
      entrega.setBairro(getStringCellNull("Bairro Entrega"));
      entrega.setEndereco(getStringCellNull("Endereço Entrega"));
      entrega.setNumero(getStringCellNull("Número Entrega"));
      entrega.setComplemento(getStringCell("Complemento Entrega"));
      entrega.setTipo(TipoEndereco.ENTREGA);
    }
    pessoaEnderecos.add(entrega);

    enderecos = pessoaEnderecos;

    String nomeDoContato = getStringCellNull("Nome do contato");
    String telefoneDoContato = getStringDigits("Telefone/Celular do contato", false);
    String emailDoContato = getStringCellNull("Email do contato");
    String observacoesDoContato = getStringCellNull("Observações do contato");

    if (!getStringCell("Nome do contato").isEmpty()) {

      if (verifyContactAndBuild(nomeDoContato, telefoneDoContato, emailDoContato)) {
      List<ContatoPessoaXlsxDto> pessoaContatos = new ArrayList<>();
        var contato = ContatoPessoaXlsxDto.builder()
          .nome(nomeDoContato)
          .telefone(telefoneDoContato)
          .email(emailDoContato)
          .observacao(observacoesDoContato)
          .build();
        pessoaContatos.add(contato);
        contatos = pessoaContatos;
      }
    }

    nomeSetor = getStringCell("Setor do contato");
    limiteCredito = getStringCell("Limite de Crédito");
    nomeVendedor = getStringCell("Nome do Vendedor");
    cpfCnpjVendedor = getStringDigits("CPF/CNPJ do Vendedor", false, true, true);
    observacoesInternas = getStringCell("Observações internas");
    emailCobranca = getStringCell("Email de Cobrança");

    incluirDadosBancarios = getBooleanCellMandatory("Incluir dados bancários?");
    if (incluirDadosBancarios) {
      nomeTitularConta = getStringCellNull("Nome do Titular");
      cpfTitularConta = getStringDigits("CPF/CNPJ do Titular", true);
      chavePixConta = getStringCell("Chave Pix");
      codigoBanco = getStringCell("Banco");
      agenciaConta = getStringCell("Agência");
      numeroConta = getStringCell("Conta Corrente");
    } else {
      // incluo esses valores fictícios para passar na validação, depois eles são apagados
      nomeTitularConta = "Nome modelo";
      cpfTitularConta = TipoPessoa.FISICA.equals(tipo) ? "92901450059" : "73243944000163";
      codigoBanco = "000";
      agenciaConta = "0000";
      numeroConta = "00000";
    }

    tipoIe = getEnum(TipoIE.class, "Contribuinte ICMS");
    inscricaoEstadual = getStringDigits("Inscrição Estadual", false);
    inscricaoMunicipal = getStringDigits("Inscrição Municipal", false);
    nomeRegimeTributario = getStringCellNull("Regime Tributário");
    codigoCnae = getStringCellNull("CNAE");
    nomeSegmento = getStringCell("Segmento");
    suframa = getStringDigits("Inscrição Suframa", false);
    observacaoNotaFiscal = getStringCell("Observações para a nota fiscal");
    emailNotaFiscal = getStringCell("Email para envio da nota fiscal");
    this.errorDetails = clearErrorDetails();
    return this;
  }

  private void validPutRelacionamento(boolean relacionamento, TipoRelacionamento tipoRelacionamento) {
    if (relacionamento)
      relacaoComercial.add(tipoRelacionamento);
  }

  @Override
  public Pessoa toEntity() {
    return ObjectUtils.convert(this, Pessoa.class);
  }

  public PessoaDto toDto() {
    return ObjectUtils.convert(this, PessoaDto.class);
  }

}


