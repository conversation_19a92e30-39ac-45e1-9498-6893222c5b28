package br.com.nuvy.api.enums;

import br.com.nuvy.validation.groups.ClienteGroup;
import br.com.nuvy.validation.groups.ColaboradorGroup;
import br.com.nuvy.validation.groups.FornecedorGroup;
import br.com.nuvy.validation.groups.TransportadoraGroup;
import br.com.nuvy.validation.groups.VendedorGroup;
import java.util.Arrays;

public enum TipoRelacionamento {
  CLIENTE(ClienteGroup.class),
  FORNECEDOR(FornecedorGroup.class),
  TRANSPORTADORA(TransportadoraGroup.class),
  COLABORADOR(ColaboradorGroup.class),
  VENDEDOR(VendedorGroup.class);

  private Class<?> group;

  TipoRelacionamento(Class<?> group) {
    this.group = group;
  }

  public Class<?> getGroup() {
    return group;
  }

  public static TipoRelacionamento of(String tipo) {
    return Arrays.stream(TipoRelacionamento.values())
      .filter(s -> tipo.equals(s.name()))
      .findFirst()
      .orElse(null);
  }
}
