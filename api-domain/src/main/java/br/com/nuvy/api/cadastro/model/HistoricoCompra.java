package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import java.time.LocalDateTime;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cd_produto_hist_compra")
public class HistoricoCompra extends MultitenantEntity implements Entidade<Integer> {

  @Id
  @Column(name = "id_produto_hist_compra")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "id_produto")
  private Integer idProduto;
  @Column(name = "dt_compra")
  private LocalDateTime dataCompra;
  @Column(name = "id_pessoa")
  private Integer idPessoa;
  @Column(name = "quantidade")
  private Integer quantidade;
  @Column(name = "preco_custo", columnDefinition = "NUMERIC")
  private Double precoCusto;
}
