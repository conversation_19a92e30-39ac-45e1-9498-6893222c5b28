package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.financeiro.model.CentroCusto;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CentroCustoCaracteristicaDto implements Dto<CentroCusto> {

  @JsonProperty(access = Access.READ_ONLY)
  private Integer id;
  private String nome;
  private String descricao;

  public static CentroCustoCaracteristicaDto from(CentroCusto entidade) {
    return ObjectUtils.convert(entidade, CentroCustoCaracteristicaDto.class);
  }
}
