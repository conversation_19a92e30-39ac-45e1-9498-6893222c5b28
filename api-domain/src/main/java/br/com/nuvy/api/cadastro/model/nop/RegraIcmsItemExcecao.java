package br.com.nuvy.api.cadastro.model.nop;

import br.com.nuvy.api.cadastro.model.Cfop;
import br.com.nuvy.api.cadastro.model.Ncm;
import br.com.nuvy.api.cadastro.model.Produto;
import br.com.nuvy.api.enums.TipoRegraExcecao;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "tipoExcecao", "produto", "ncm", "variacaoCfop", "variacaoCfopConsumidorFinal", "variacaoCfopConsumidorFinalNc", "ufs"}, callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cd_regra_icms_item_excecao")
public class RegraIcmsItemExcecao extends RegraIcmsItem {

  @Id
  @Column(name = "id_regra_icms_item_excecao")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_regra_icms_item")
  private RegraIcmsItemPadrao regraIcmsItem;

  @Column(name = "tp_excecao")
  private TipoRegraExcecao tipoExcecao;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_produto")
  private Produto produto;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_ncm")
  private Ncm ncm;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_variacao_cfop")
  private Cfop variacaoCfop;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_variacao_cfop_cf")
  private Cfop variacaoCfopConsumidorFinal;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_variacao_cfop_cf_nc")
  private Cfop variacaoCfopConsumidorFinalNc;

  @ElementCollection(targetClass = String.class)
  @CollectionTable(name = "cd_regra_icms_excecao_uf", joinColumns = {
    @JoinColumn(name = "id_regra_icms_item_excecao")
  })
  @Column(name = "uf")
  private Set<String> ufs;

  public static RegraIcmsItemExcecao of(Integer id) {
    if (id == null) {
      return null;
    }
    RegraIcmsItemExcecao ret = new RegraIcmsItemExcecao();
    ret.id = id;
    return ret;
  }
}
