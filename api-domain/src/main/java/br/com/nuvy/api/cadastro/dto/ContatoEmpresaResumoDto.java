package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.ContatoEmpresa;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter; import lombok.Setter;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContatoEmpresaResumoDto implements Dto<ContatoEmpresa> {

  private Integer id;
  @Size(max = 255)
  private String nome;
  @Size(max = 20)
  private String telefone;
  private Boolean ativo;
  private String nomeSetor;

  public static ContatoEmpresaResumoDto from(ContatoEmpresa entidade) {
    return ObjectUtils.convert(entidade, ContatoEmpresaResumoDto.class);
  }
}
