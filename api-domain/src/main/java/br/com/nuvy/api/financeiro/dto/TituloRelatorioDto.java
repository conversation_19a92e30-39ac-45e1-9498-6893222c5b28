package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.enums.CnabPagamento;
import br.com.nuvy.api.enums.SituacaoTitulo;
import br.com.nuvy.api.enums.StatusTitulo;
import br.com.nuvy.api.enums.TipoPessoa;
import br.com.nuvy.api.enums.TipoRelacionamento;
import br.com.nuvy.api.enums.TipoTitulo;
import br.com.nuvy.validation.groups.provider.CnabGroupSequenceProvider;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.group.GroupSequenceProvider;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GroupSequenceProvider(CnabGroupSequenceProvider.class)
public class TituloRelatorioDto {

  //Principais
  private Integer empresaId;
  private String empresaNome;
  @JsonProperty(value = "situacao", access = JsonProperty.Access.READ_ONLY)
  private StatusTitulo status;
  @JsonProperty(access = Access.WRITE_ONLY)
  private SituacaoTitulo situacao;
  private String fornecedorNome;
  private String fornecedorNomeInterno;
  private Integer tipoReceitaDespesaId;
  private String tipoReceitaDespesaNome;
  private List<TituloDespesaDto> despesas;
  private LocalDate dataVencimento;
  private Integer contaBancariaId;
  private String contaBancariaNome;
  private BigDecimal valorOriginal = BigDecimal.ZERO;
  private BigDecimal valorTotalImpostosRetidos = BigDecimal.ZERO;
  private BigDecimal valorJuros = BigDecimal.ZERO;
  private BigDecimal valorMultas = BigDecimal.ZERO;
  private BigDecimal valorDescontos = BigDecimal.ZERO;
  private BigDecimal valorAbatimentos = BigDecimal.ZERO;
  private BigDecimal valorTotal = BigDecimal.ZERO;
  private BigDecimal valorTotalPago = BigDecimal.ZERO;

  //Adicionais
  private String numeroNotaFiscal;
  private String observacao;
  private LocalDateTime dataEmissao;
  private List<TituloCentroCustosDto> centroCustos;
  private Integer formaPagamentoId;
  private String formaPagamentoNome;
  private String codigoBarras;
  private String numeroDocumento;
  private Boolean possuiRecorrencia;
  private Integer fornecedorId;
  private TipoPessoa fornecedorTipoPessoa;
  private String fornecedorCpfCnpj;
  private Set<TipoRelacionamento> fornecedorRelacaoComercial;
  private BigDecimal valorIss;
  private BigDecimal valorIr;
  private BigDecimal valorPis;
  private BigDecimal valorCofins;
  private BigDecimal valorCsll;
  private BigDecimal valorInss;
  private CnabPagamento cnabPagamento;
  private Integer instituicaoFinanceiraId;
  private String instituicaoFinanceiraCodigo;
  private String instituicaoFinanceiraNome;
  private String agenciaContaPagto;
  private String numeroContaPagto;
  private String cpfCnpjPagto;
  private String chavePixPagto;
  private String finalidadeTedId;
  private String finalidadeTedDescricao;
  private LocalDateTime dataGeracao;
  private LocalDate dataPagamento;
  private List<TituloHistoricoDto> historicos;
  private TipoTitulo tipo;
  private Integer numeroParcela;
  private Integer quantidadeParcelas;
}
