package br.com.nuvy.api.financeiro.model;

import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "fn_carteira_cobranca")
public class CarteiraCobranca implements Entidade<String> {

  @Id
  @Column(name = "cod_carteira_cobranca")
  private String id;

  @Size(max = 255)
  private String descricao;

}