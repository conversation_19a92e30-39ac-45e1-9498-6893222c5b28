package br.com.nuvy.api.enums;

import br.com.nuvy.api.venda.model.CanalOrigem;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Getter;

import static br.com.nuvy.common.utils.ObjectUtils.get;

@Getter
public enum Marketplace {

  MERCADO_LIVRE(2, false, false, true, false, false),
  NUVEMSHOP(10, true, true, true, false, false),
  VIRTUOL(17, true, true, false, false, true),
  NETSHOES(8, true, true, false, true, false),
  B2W(3, true, true, false, true, false),
  MAGALU(4, true, false, false, true, false),
  LEROY_MERLIN(16, true, true, false, false, false),
  VIA_VAREJO(5, true, true, false, false, false);

  private final Integer canalOrigemId;
  private final boolean usaCatalogo;
  private final boolean usaMapeamentoCategoria;
  private final boolean usaAutenticacao2FA;
  private final boolean enviaEmailNfEmitida = false;
  private final boolean mapeiaAtributosCategoria;
  private final boolean mapeiaAtributos;

  Marketplace(
    Integer canalOrigemId,
    boolean usaCatalogo,
    boolean usaMapeamentoCategoria,
    boolean usaAutenticacao2FA,
    boolean mapeiaAtributosCategoria,
    boolean mapeiaAtributos
  ) {
    this.canalOrigemId = canalOrigemId;
    this.usaCatalogo = usaCatalogo;
    this.usaMapeamentoCategoria = usaMapeamentoCategoria;
    this.usaAutenticacao2FA = usaAutenticacao2FA;
    this.mapeiaAtributosCategoria = mapeiaAtributosCategoria;
    this.mapeiaAtributos = mapeiaAtributos;
  }

  public static Optional<Marketplace> fromCanalOrigem(Integer canalOrigemId) {
    for (Marketplace marketplace : Marketplace.values()) {
      if (marketplace.getCanalOrigemId().equals(canalOrigemId)) {
        return Optional.of(marketplace);
      }
    }
    return Optional.empty();
  }

  public static Optional<Marketplace> fromCanalOrigem(CanalOrigem canalOrigem) {
    return fromCanalOrigem(get(canalOrigem, CanalOrigem::getId));
  }

  public static Stream<Marketplace> all() {
    return Arrays.stream(values());
  }

  public static Stream<Marketplace> allUsaCatalogo() {
    return Arrays.stream(values()).filter(Marketplace::isUsaCatalogo);
  }

}
