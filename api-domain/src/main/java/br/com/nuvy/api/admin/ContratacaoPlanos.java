package br.com.nuvy.api.admin;

import br.com.nuvy.api.enums.TipoContratacaoPlano;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "\"contratacaoPlanos\"", schema = "backoffice")
public class ContratacaoPlanos {

  @Id
  @Column(name = "id")
  private String id;

  @Column(name = "titulo", updatable = false)
  private String titulo;

  @Column(name = "valor", updatable = false)
  private BigDecimal valor;

  @Enumerated(EnumType.STRING)
  @Column(name = "tipo", updatable = false)
  private TipoContratacaoPlano tipo;
}
