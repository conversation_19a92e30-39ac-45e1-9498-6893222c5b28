package br.com.nuvy.api.financeiro.model;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.SituacaoBoleto;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "fn_banco_conta_homologacao_carteira")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class HomologacaoCarteira implements Entidade<Integer> {

  @Id
  @EqualsAndHashCode.Include
  @Column(name = "id_homolocacao_carteira")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @TenantId
  @Column(name = "id_aplicacao")
  private String aplicacao;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_banco_conta")
  private ContaBancaria contaBancaria;

  @Column(name = "id_servico_boleto")
  private Integer idServicoBoleto;

  @Column(name = "dt_recebimento")
  private LocalDateTime dataRecebimento;

  @Column(name = "situacao_boleto")
  @Enumerated(EnumType.STRING)
  private SituacaoBoleto situacao;

  @Column(name = "ind_envio_remessa")
  @Builder.Default
  private Boolean envioRemessa = false;

  @Column(name = "dt_recebimento_remessa")
  private LocalDateTime dataRecebimentoRemessa;

  @Column(name = "id_remessa_gerada")
  private Integer idRemessaGerada;

}