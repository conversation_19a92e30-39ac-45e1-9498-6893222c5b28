package br.com.nuvy.api.enums;

import java.util.Arrays;

public enum TipoOperacao {
  INTERNA("1"),
  INTERESTADUAL("2"),
  ESTRANGEIRA("3");

  private final String codigo;

  TipoOperacao(String codigo){
    this.codigo = codigo;
  }

  public String getCodigo() {
    return codigo;
  }

  public static TipoOperacao of(String tipo) {
    return Arrays.stream(TipoOperacao.values())
      .filter(s -> tipo.equals(s.name()))
      .findFirst().orElse(null);
  }

  public static TipoOperacao ofCodigo(String codigo) {
    return Arrays.stream(TipoOperacao.values())
      .filter(s -> s.getCodigo().equals(codigo))
      .findFirst().orElse(null);
  }
}
