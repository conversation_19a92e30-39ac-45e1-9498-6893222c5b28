package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id", "nome"})
@Entity
@Table(name = "cd_pos_pdv_venda_pagamento")
public class PosPdvVendaPagamento extends MultitenantEntity implements Entidade<Integer> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Integer id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_venda", nullable = false)
  private PosPdvVenda posPdvVenda;

  @Column(name = "created_at", insertable = false, updatable = false)
  private LocalDateTime createDat;

  @Column(name = "updated_at", updatable = false, insertable = false)
  private LocalDateTime updateDat;

  @Column(name = "pagamento_pos_id", length = 36)
  private String pagamentoPosId;

  @Column(name = "nome", length = 50)
  private String nome;

  @Column(name = "valor_pago", precision = 8, scale = 2)
  private BigDecimal valorPago;

  @Column(name = "tipo_operacao", length = 2)
  private String tipoOperacao;

  @Column(name = "codigo_autorizacao_tef", length = 30)
  private String codigoAutorizacaoTef;

  @Column(name = "data_hora_autorizacao", length = 25)
  private LocalDateTime dataHoraAutorizacao;

  @Column(name = "data_vencimento")
  private LocalDate dataVencimento;

  @Column(name = "rede_autorizacao_cartao", length = 8)
  private String redeAutorizacaoCartao;

  @Column(name = "bandeira_cartao", length = 8)
  private String bandeiraCartao;

  @Column(name = "quantidade_parcelas")
  private Integer quantidadeParcelas;

  @Column(name = "nsu_sitef", length = 8)
  private String nsuSitef;

  @ManyToOne
  @JoinColumn(name = "id_banco_conta")
  private ContaBancaria contaBancaria;

  public static PosPdvVendaPagamento of(Integer id) {
    if (id == null) {
      return null;
    }
    PosPdvVendaPagamento pagamento = new PosPdvVendaPagamento();
    pagamento.id = id;
    return pagamento;
  }
}
