package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "cd_tipo_carroceria")
public class TipoCarroceria implements Entidade<Integer> {

  @Id
  @Column(name = "id_tipo_carroceria")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Size(max = 100)
  @Column(name = "nm_tipo_carroceria")
  private String nome;

  public static TipoCarroceria of(Integer id) {
    if (id == null) {
      return null;
    }
    return TipoCarroceria.builder()
      .id(id)
      .build();
  }

}
