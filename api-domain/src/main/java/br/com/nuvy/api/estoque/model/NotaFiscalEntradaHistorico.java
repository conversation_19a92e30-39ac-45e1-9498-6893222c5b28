package br.com.nuvy.api.estoque.model;

import br.com.nuvy.api.enums.AcaoHistorico;
import br.com.nuvy.api.seguranca.model.Usuario;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "eq_nf_entrada_hist")
public class NotaFiscalEntradaHistorico extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Column(name = "id_nf_entrada_hist")
  private UUID id;

  @Column(name = "data_hist")
  private LocalDateTime dataHistorico;

  @Column(name = "descricao", columnDefinition = "TEXT")
  private String descricao;

  @Enumerated(EnumType.STRING)
  @Column(name = "acao", length = 50)
  private AcaoHistorico acao;

  @Column(name = "caminho_xml", columnDefinition = "TEXT")
  private String caminhoXml;

  @ManyToOne
  @JoinColumn(
    name = "id_usuario",
    foreignKey = @ForeignKey(name = "fk_nf_entrada_hist_usuario"),
    updatable = false)
  private Usuario usuario;

  @ManyToOne
  @JoinColumn(
    name = "id_nf_entrada",
    foreignKey = @ForeignKey(name = "fk_nf_entrada_hist_nf"),
    updatable = false)
  private NotaFiscalEntrada notaFiscalEntrada;

}
