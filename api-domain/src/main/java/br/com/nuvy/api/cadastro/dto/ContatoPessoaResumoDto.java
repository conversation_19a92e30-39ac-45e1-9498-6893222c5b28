package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.ContatoPessoa;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter; import lombok.Setter;
import lombok.NoArgsConstructor;

@Getter @Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContatoPessoaResumoDto implements Dto<ContatoPessoa> {

  private Integer id;
  private String nome;
  private String nomeSetor;
  private String email;
  private String telefone;
  private String ramal;
  private String observacao;
  private Boolean ativo;

  public static ContatoPessoaResumoDto from(ContatoPessoa contatoPessoa) {
    return ObjectUtils.convert(contatoPessoa, ContatoPessoaResumoDto.class);
  }
}
