package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.ContatoPessoa;
import br.com.nuvy.api.cadastro.model.Pessoa;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContatoPessoaXlsxDto implements Dto<ContatoPessoa> {

  private Integer id;

  @NotBlank
  @Size(max = 150)
  private String nome;

  @NotBlank
  @Size(max = 100)
  @Email
  private String email;

  private String observacao;

  @NotBlank
  @Pattern(regexp="(\\d{11})|(\\d{10})", message = "[Telefone/Celular] formato.invalido")
  private String telefone;

  private SetorDto setor;

  @JsonIgnore
  private Pessoa pessoa;

  public static ContatoPessoaXlsxDto from(ContatoPessoa entity) {
    return ObjectUtils.convert(entity, ContatoPessoaXlsxDto.class);
  }

}
