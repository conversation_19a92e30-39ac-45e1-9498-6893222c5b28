package br.com.nuvy.api.venda.event.canalvenda;

import br.com.nuvy.api.enums.OrigemEventoHub;
import br.com.nuvy.api.notificacao.dto.NotificacaoHubDto;
import br.com.nuvy.api.notificacao.dto.PayloadHubOnChange;
import br.com.nuvy.base.NuvyErpEvent;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
public abstract class CanalVendaEvent extends NuvyErpEvent {

  private final Integer idCanalVenda;

  protected CanalVendaEvent(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idCanalVenda,
    LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, ocorridoAs);
    this.idCanalVenda = idCanalVenda;
  }

  protected NotificacaoHubDto buildNotificacaoHubDto(Integer oldId,
    OrigemEventoHub origemEventoHub) {
    var oldIdString = oldId != null ? oldId.toString() : null;
    return NotificacaoHubDto.builder()
      .tenantId(getIdAplicacao().toString())
      .origem(origemEventoHub)
      .payload(new PayloadHubOnChange(idCanalVenda.toString(), origemEventoHub))
      .usuarioId(getIdUsuario() == null ? null : getIdUsuario())
      .oldId(oldIdString)
      .build();
  }
}
