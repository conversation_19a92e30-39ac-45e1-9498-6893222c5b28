package br.com.nuvy.api.financeiro.event.titulo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LembreteTituloEnviado extends EventoTitulo {

  private final LocalDate vencimento;
  private final UUID idReguaComunicacao;

  public LembreteTituloEnviado(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario,
    Integer idTitulo, LocalDate vencimento, UUID idReguaComunicacao, LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idTitulo, ocorridoAs);
    this.vencimento = vencimento;
    this.idReguaComunicacao = idReguaComunicacao;
  }
}
