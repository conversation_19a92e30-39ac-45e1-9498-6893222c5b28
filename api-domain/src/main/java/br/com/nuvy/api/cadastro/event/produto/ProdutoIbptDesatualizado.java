package br.com.nuvy.api.cadastro.event.produto;

import br.com.nuvy.api.cadastro.command.produto.ImportaIbptProduto;
import java.time.LocalDateTime;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ProdutoIbptDesatualizado extends ProdutoEvent {

  private final String uf;

  @JsonCreator
  public ProdutoIbptDesatualizado(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idProduto,
    String uf, LocalDateTime ocorridoAs
  ) {
    super(id, idAplicacao, idEmpresa, idUsuario, idProduto, ocorridoAs);
    this.uf = uf;
  }

  public ProdutoIbptDesatualizado(
    UUID id, UUID idAplicacao, Integer idEmpresa, UUID idUsuario, Integer idProduto,
    LocalDateTime ocorridoAs
  ) {
    this(id, idAplicacao, idEmpresa, idUsuario, idProduto, null, ocorridoAs);
  }

  public ImportaIbptProduto createImportaIbptProdutoCommand() {
    return new ImportaIbptProduto(
      UUID.randomUUID(), getIdAplicacao(), getIdEmpresa(), getIdUsuario(), getIdProduto(),
      uf, getOcorridoAs()
    );
  }
}
