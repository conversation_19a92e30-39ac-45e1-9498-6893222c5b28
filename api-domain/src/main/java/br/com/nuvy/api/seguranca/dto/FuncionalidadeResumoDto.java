package br.com.nuvy.api.seguranca.dto;

import br.com.nuvy.api.seguranca.model.Funcionalidade;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import lombok.Data;

@Data
public class FuncionalidadeResumoDto implements Dto<Funcionalidade> {

  private String id;
  private String nome;

  public static FuncionalidadeResumoDto from(Funcionalidade entidade) {
    return ObjectUtils.convert(entidade, FuncionalidadeResumoDto.class);
  }

}
