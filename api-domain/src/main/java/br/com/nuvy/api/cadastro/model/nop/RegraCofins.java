package br.com.nuvy.api.cadastro.model.nop;

import br.com.nuvy.api.enums.Situacao;
import br.com.nuvy.base.MultitenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import java.math.BigDecimal;

@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public class RegraCofins extends MultitenantEntity {

  @Column(name = "situacao")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private Situacao situacao = Situacao.ATIVO;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "cod_cst_cofins")
  private CstCofins cstCofins;

  @Column(name = "aliquota_cofins")
  private BigDecimal aliquota;

  @Column(name = "vl_cofins")
  private BigDecimal valor;

  @Column(name = "ind_reduz_icms_base_cofins")
  private Boolean reduzIcmsBaseCalculoCofins = false;

  @Column(name = "info_complementar")
  private String informacaoComplementar;
}
