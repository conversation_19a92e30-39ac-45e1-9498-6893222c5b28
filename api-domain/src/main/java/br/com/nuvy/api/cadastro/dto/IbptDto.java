package br.com.nuvy.api.cadastro.dto;

import br.com.nuvy.api.cadastro.model.Ibpt;
import br.com.nuvy.common.base.dto.Dto;
import br.com.nuvy.common.utils.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IbptDto {

  private Integer id;
  @JsonProperty("Codigo")
  private String codigo;
  @JsonProperty("Descricao")
  private String descricao;
  @JsonProperty("UF")
  private String uf;
  @JsonProperty("EX")
  private Integer ex;
  @JsonProperty("Nacional")
  private BigDecimal nacional;
  @JsonProperty("Estadual")
  private BigDecimal estadual;
  @JsonProperty("Importado")
  private BigDecimal importado;
  @JsonProperty("Municipal")
  private BigDecimal municipal;
  @JsonProperty("Tipo")
  private Integer tipo;
  @JsonProperty("VigenciaInicio")
  private String vigenciaInicio;
  @JsonProperty("VigenciaFim")
  private String vigenciaFim;
  @JsonProperty("Chave")
  private String chave;
  @JsonProperty("Fonte")
  private String fonte;
  @JsonProperty("Versao")
  private String versao;
  @JsonProperty("Valor")
  private String valor;
  @JsonProperty("ValorTributoNacional")
  private BigDecimal valorTributoNacional;
  @JsonProperty("ValorTributoEstadual")
  private BigDecimal valorTributoEstadual;
  @JsonProperty("ValorTributoImportado")
  private BigDecimal valorTributoImportado;
  @JsonProperty("ValorTributoMunicipal")
  private BigDecimal valorTributoMunicipal;
}
