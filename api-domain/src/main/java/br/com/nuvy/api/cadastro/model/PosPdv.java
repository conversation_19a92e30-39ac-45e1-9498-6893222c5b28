package br.com.nuvy.api.cadastro.model;

import br.com.nuvy.api.cadastro.model.nop.Nop;
import br.com.nuvy.api.estoque.model.Deposito;
import br.com.nuvy.api.financeiro.model.ContaBancaria;
import br.com.nuvy.api.financeiro.model.PlanoConta;
import br.com.nuvy.api.venda.model.TabelaPreco;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@ToString(of = {"id", "descricao", "posPdvIntegracao", "ativo", "createDat", "updateDat"})
@Table(name = "cd_pos_pdv")
public class PosPdv extends MultitenantEntity implements Entidade<UUID> {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id")
  private UUID id;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id_empresa", nullable = false)
  private Empresa empresa;

  @Column(name = "descricao", nullable = true, length = 100)
  private String descricao;

  @Column(name = "usuario", nullable = true, length = 100)
  private String usuario;

  @Column(name = "senha", nullable = true, length = 255)
  private String senha;

  @ManyToOne(targetEntity = Deposito.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_deposito", nullable = true)
  private Deposito deposito;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_nop", nullable = true)
  private Nop nop;

  @ManyToOne(targetEntity = PlanoConta.class, fetch = FetchType.LAZY)
  @JoinColumn(name = "id_plano_conta")
  private PlanoConta planoConta;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_conta_corrente", nullable = true)
  private ContaBancaria contaBancaria;

  @Column(name = "created_at", insertable = false, updatable = false)
  private LocalDateTime createDat;

  @Column(name = "updated_at", insertable = false, updatable = false)
  private LocalDateTime updateDat;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_integracao", nullable = false)
  private PosPdvIntegracao posPdvIntegracao;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_tab_preco", nullable = false)
  private TabelaPreco tabelaPreco;

  @Column(name = "token_pos", nullable = true)
  private String token;

  @Column(name = "data_token", nullable = true)
  private LocalDateTime dataToken;

  @Column(name = "ativo", nullable = false)
  private Boolean ativo = true;

  @Column(name = "ind_venda_atacado", nullable = false)
  private Boolean vendaAtacado = false;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "id_cliente")
  private Pessoa cliente;

  @Builder.Default
  @Column(name = "atualiza_preco_produto_venda", nullable = false)
  private Boolean atualizarPrecoVenda = false;

  @Builder.Default
  @Column(name = "atualiza_saldo_estoque_produto", nullable = false)
  private boolean atualizaSaldoEstoqueProduto = false;

  @Transient
  private Integer depositoIdOld = 0;

  @Transient
  private Integer nopIdOld =0;

  @Transient
  private Integer tabelaPrecoIdOld =0;

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    PosPdv posPdv = (PosPdv) o;
    return Objects.equals(id, posPdv.id);
  }

  @Override
  public int hashCode() {
    return id != null ? id.hashCode() : 0;
  }

  public static PosPdv of(UUID id) {
    if (id == null) {
      return null;
    }
    return PosPdv.builder()
      .id(id)
      .build();
  }

  public boolean isAtivo() {
    return ativo != null && ativo;
  }

  public boolean isVendaAtacado() {
    return vendaAtacado != null && vendaAtacado;
  }

  public boolean isAtualizarPrecoVenda() {
    return atualizarPrecoVenda != null && atualizarPrecoVenda;
  }

  public boolean isAtualizaSaldoEstoqueProduto() {
    // Como o campo é um primitivo boolean, não pode ser nulo
    // e sempre retornará seu valor atual (true ou false)
    return atualizaSaldoEstoqueProduto;
  }

  public boolean isMudouDeposito() {
    if((deposito == null || deposito.getId() == null || depositoIdOld == 0)){
      return false;
    }
    return !Objects.equals(deposito.getId(), depositoIdOld);
  }

  public boolean isMudouNop() {
    if(nop == null || nop.getId() == null || (nopIdOld !=null && nopIdOld == 0)){
      return false;
    }
    return !Objects.equals(nop.getId(), nopIdOld);
  }

  public boolean isMudouTabelaPreco() {
    if (tabelaPreco == null || tabelaPreco.getId() == null || (tabelaPrecoIdOld !=null && tabelaPrecoIdOld == 0)) {
      return false;
    }
    return !Objects.equals(tabelaPreco.getId(), tabelaPrecoIdOld);
  }

}
