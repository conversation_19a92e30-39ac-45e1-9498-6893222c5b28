package br.com.nuvy.api.financeiro.dto;

import br.com.nuvy.api.enums.FaturaStatus;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FaturamentoClienteDto {

  private UUID id;
  private LocalDateTime data;
  private String descricao;
  private BigDecimal valor;
  private FaturaStatus status;
  private String caminhoBoletoFatura;
  private String caminhoNotaFatura;
}
