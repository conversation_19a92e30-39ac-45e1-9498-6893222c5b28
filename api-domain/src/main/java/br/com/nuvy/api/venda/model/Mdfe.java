package br.com.nuvy.api.venda.model;

import br.com.nuvy.api.cadastro.model.Empresa;
import br.com.nuvy.api.enums.SituacaoMdfe;
import br.com.nuvy.base.MultitenantEntity;
import br.com.nuvy.common.base.entity.Entidade;
import jakarta.persistence.*;
import lombok.*;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id", callSuper = false)
@ToString(of = {"id"})
@Entity
@Table(name = "erp_mdfe", schema = "nfe")
public class Mdfe extends MultitenantEntity implements Entidade<Long> {

  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "id_empresa")
  private Empresa empresa;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private SituacaoMdfe status = SituacaoMdfe.PENDENTE;

  @Builder.Default
  @Column(name = "excluido")
  private Boolean excluido = false;

  @OneToMany(mappedBy = "mdfe", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
  private List<MdfeNotas> notas;

}
