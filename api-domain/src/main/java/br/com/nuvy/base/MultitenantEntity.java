package br.com.nuvy.base;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TenantId;

@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public abstract class MultitenantEntity {

  @TenantId
  @Column(
    name = "id_aplicacao",
    columnDefinition = "char(36)",
    updatable = false)
  private String aplicacao;

  public String getAplicacao() {
    return aplicacao;
  }

  public void setAplicacao(String aplicacao) {
    this.aplicacao = aplicacao;
  }
}
