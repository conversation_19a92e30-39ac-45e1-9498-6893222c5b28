-- Corrige definitivamente o erro de coluna "nome_categoria" e problemas com meses diferentes
DROP FUNCTION IF EXISTS erp.fn_gerar_dre CASCADE;

-- Create the function for DRE report with single integer empresa parameter and OS forecast
CREATE OR REPLACE FUNCTION erp.fn_gerar_dre(
  p_data_inicial DATE,
  p_data_final DATE,
  p_id_empresa INTEGER,
  p_tipo_pesquisa_data_relatorio VARCHAR DEFAULT 'DATA_EMISSAO'
)
  RETURNS TABLE (
                  empresa_id INTEGER,
                  empresa_nome VARCHAR,
                  ordem INTEGER,
                  tipo VARCHAR,
                  conta_do_dre_id INTEGER,
                  conta_do_dre_nome VARCHAR,
                  categoria_produto VARCHAR,
                  data_movimento DATE,
                  centro_custo_id INTEGER,
                  centro_custo_nome TEXT,
                  parceiro_id INTEGER,
                  parceiro_nome VARCHAR,
                  valor NUMERIC(19,2)
                ) AS $$
DECLARE
  v_data_inicio_previsao DATE;
  v_data_atual DATE;
  v_primeiro_dia_mes DATE;
  v_ultimo_dia_mes DATE;
BEGIN
  -- Obtém a data atual
  v_data_atual := CURRENT_DATE;
  
  -- Calcular primeiro e último dia do mês de consulta (extremamente importante)
  v_primeiro_dia_mes := DATE_TRUNC('month', p_data_inicial)::DATE;
  v_ultimo_dia_mes := (DATE_TRUNC('month', p_data_inicial) + INTERVAL '1 month - 1 day')::DATE;
  
  -- Apenas exibir log para fins de diagnóstico
  RAISE NOTICE 'Gerando relatório para o período: % até %, com mês de referência %-%', 
    p_data_inicial, p_data_final, EXTRACT(MONTH FROM p_data_inicial), EXTRACT(YEAR FROM p_data_inicial);
  
  -- Define a data de início para previsão das ordens de serviço
  -- Se a data inicial do relatório já for no futuro, usamos o mês dela diretamente
  -- Caso contrário, usamos o mês seguinte ao inicial
  IF p_data_inicial > v_data_atual THEN
    -- Se a data inicial já é futura, usamos o primeiro dia desse mês
    v_data_inicio_previsao := v_primeiro_dia_mes;
  ELSE
    -- Se a data inicial é no passado ou presente, usamos o primeiro dia do mês seguinte
    v_data_inicio_previsao := (v_primeiro_dia_mes + INTERVAL '1 month')::DATE;
  END IF;

  RETURN QUERY
    WITH emp_dados AS (
      SELECT
        emp.id_empresa,
        emp.nome,
        emp.id_aplicacao
      FROM
        erp.cd_empresa emp
      WHERE
        emp.id_empresa = p_id_empresa
    ),

         dre_cfg AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             d.id_dre,
             d.nome,
             d.tp_tipo,
             CASE
               WHEN d.nome ILIKE 'Receita com Vendas' THEN 1
               WHEN d.nome ILIKE 'Deduções e abatimentos' THEN 2
               WHEN d.nome ILIKE 'Custo da Mercadoria' THEN 4
               WHEN d.nome ILIKE 'Despesas Administrativas' THEN 6
               WHEN d.nome ILIKE 'Despesas de Vendas' THEN 7
               WHEN d.nome ILIKE 'Receitas Financeiras' THEN 9
               WHEN d.nome ILIKE 'Despesas Financeiras' THEN 10
               WHEN d.nome ILIKE 'Outras Receitas não Operacionais' THEN 11
               WHEN d.nome ILIKE 'Outras Despesas não Operacionais' THEN 12
               ELSE 14
               END AS ordem_dre
           FROM
             erp.fn_dre d
               JOIN emp_dados e ON d.id_aplicacao = e.id_aplicacao
         ),

         -- Gera séries de meses para ordens de serviço recorrentes
         meses_os AS (
           -- Garantir que apenas geramos datas para o mês exato solicitado nos parâmetros
           SELECT DATE_TRUNC('month', p_data_inicial)::DATE AS primeiro_dia_mes
           WHERE 
             -- Se ambas as datas estão no mesmo mês, use apenas esse mês
             EXTRACT(YEAR FROM p_data_inicial) = EXTRACT(YEAR FROM p_data_final) AND
             EXTRACT(MONTH FROM p_data_inicial) = EXTRACT(MONTH FROM p_data_final)
           
           UNION ALL
           
           -- Se o intervalo abrange vários meses (o que geralmente não é o caso), gere a série
           SELECT generate_series(
                    DATE_TRUNC('month', p_data_inicial)::DATE, 
                    DATE_TRUNC('month', p_data_final)::DATE,
                    '1 month'::INTERVAL
                  )::DATE AS primeiro_dia_mes
           WHERE 
             -- Ativa esse ramo apenas se o intervalo de consulta abrange mais de um mês
             (EXTRACT(YEAR FROM p_data_inicial) != EXTRACT(YEAR FROM p_data_final) OR
              EXTRACT(MONTH FROM p_data_inicial) != EXTRACT(MONTH FROM p_data_final))
         ),

         -- Ordens de serviço recorrentes com previsão de faturamento mensal
         ordens_servico_prev AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             d.tp_tipo AS dre_tipo,
             -- Forçar a data para estar SEMPRE no mês solicitado, dia 15 para evitar problemas de borda
             (DATE_TRUNC('month', p_data_inicial) + INTERVAL '15 days')::DATE AS data_reg,
             os.id_conta_receita AS id_conta,
             pc.nome AS descricao_categoria,
             COALESCE(occ.id_centro_custo, 0) AS cc_id,
             COALESCE(cc.nome, '-') AS cc_nome,
             os.id_cliente AS id_pessoa,
             c.nome AS pessoa_nome,
             -- Usar valor da nota como valor final, sem descontos para o DRE
             os.valor_nf AS vl_final
           FROM
             meses_os m
               CROSS JOIN "erp-nfs".ordem_servico os
               JOIN erp.cd_empresa e ON os.id_empresa = e.id_empresa AND e.id_empresa = p_id_empresa
               JOIN erp.cd_pessoa c ON os.id_cliente = c.id_pessoa
               LEFT JOIN erp.fn_plano_conta pc ON os.id_conta_receita = pc.id_conta
               LEFT JOIN "erp-nfs".ordem_servico_centro_custo occ ON os.id = occ.id_ordem_servico
               LEFT JOIN erp.fn_centro_custo cc ON occ.id_centro_custo = cc.id_centro_custo
               LEFT JOIN dre_cfg d ON pc.id_dre = d.id_dre AND d.emp_id = e.id_empresa
           WHERE
             os.recorrencia = TRUE
             -- Apenas ordens com situação apropriada para recorrência
             AND os.situacao IN ('FATURADO', 'RECORRENCIA')
             -- Verificar a vigência tendo como base o mês, regra dinâmica por registro
             AND os.vigencia_inicial <= (
             CASE
               WHEN (p_tipo_pesquisa_data_relatorio IN ('DATA_VENCIMENTO', 'DATA_BAIXA') AND os.dia_faturamento > os.dia_vencimento)
                 THEN (m.primeiro_dia_mes + INTERVAL '2 month - 1 day')::DATE
               ELSE (m.primeiro_dia_mes + INTERVAL '1 month - 1 day')::DATE
               END
             )
             AND (os.vigencia_final IS NULL OR os.vigencia_final >= m.primeiro_dia_mes)
             AND os.id_aplicacao = e.id_aplicacao
             -- Apenas uso de contas que são identificadas em alguma categoria DRE
             AND pc.id_dre IS NOT NULL
             -- Apenas incluir se houver configuração para DRE
             AND d.id_dre IS NOT NULL
             -- Garante que não serão geradas previsões para meses onde já existe título
             AND NOT EXISTS (
             SELECT 1
             FROM erp.fn_titulo t
             WHERE t.id_pessoa = os.id_cliente
               AND t.id_empresa = os.id_empresa
               AND t.id_conta = os.id_conta_receita
               AND DATE_TRUNC('month', t.dt_vencimento) = m.primeiro_dia_mes
           )
         ),

         titulos_base AS (
           SELECT
             t.id_aplicacao,
             t.id_empresa AS emp_id,
             e.nome AS emp_nome,
             DATE_TRUNC('day',
                        CASE
                          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN tb.dt_baixa
                          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_VENCIMENTO' THEN t.dt_vencimento
                          ELSE t.dt_emissao
                          END
             ) AS data_reg,
             pc.id_dre,
             pc.id_conta,
             pc.nome AS descricao_categoria,
             COALESCE(cc.id_centro_custo, 0) AS cc_id,
             COALESCE(cc.nome, '-') AS cc_nome,
             COALESCE(trc.perc_rateio, 1.0) AS cc_rateio, -- Valor padrão caso seja NULL
             p.id_pessoa,
             p.nome AS pessoa_nome,
             (SUM(COALESCE(tb.vl_juros, 0)) * COALESCE(trd.perc_rateio, 1.0)) AS juros_vl,
             (SUM(COALESCE(tb.vl_multa, 0)) * COALESCE(trd.perc_rateio, 1.0)) AS multa_vl,
             (SUM(COALESCE(tb.vl_desconto, 0)) * COALESCE(trd.perc_rateio, 1.0)) AS desconto_vl,
             (SUM(COALESCE(tb.vl_abatimento, 0)) * COALESCE(trd.perc_rateio, 1.0)) AS abatimento_vl,
             (
               CASE
                 WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN SUM(COALESCE(tb.vl_baixa, 0))
                 ELSE COALESCE(MIN(t.valor_liquido), 0) -- Uso de COALESCE para evitar NULL
                 END
               ) * COALESCE(trd.perc_rateio, 1.0) AS total_vl
           FROM
             erp.fn_titulo t
               JOIN erp.fn_titulo_rateio_despesa trd ON trd.id_titulo = t.id_titulo AND t.excluido IS FALSE
               LEFT JOIN erp.fn_plano_conta pc ON trd.id_conta = pc.id_conta
               JOIN emp_dados e ON t.id_aplicacao = e.id_aplicacao AND t.id_empresa = e.id_empresa
               JOIN erp.cd_pessoa p ON t.id_pessoa = p.id_pessoa
               LEFT JOIN erp.fn_titulo_baixa tb ON tb.id_titulo = t.id_titulo AND t.excluido IS FALSE
               LEFT JOIN erp.fn_titulo_rateio_ccusto trc ON trc.id_titulo = t.id_titulo AND t.excluido IS FALSE
               LEFT JOIN erp.fn_centro_custo cc ON trc.id_centro_custo = cc.id_centro_custo
           WHERE
             (
               CASE
                 -- Para qualquer tipo de pesquisa, forçamos apenas o mês/ano solicitado
                 -- Essa é a parte crítica que pode estar trazendo dados de março
                 WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN
                   -- Filtro estrito: APENAS o mês solicitado 
                   EXTRACT(MONTH FROM tb.dt_baixa) = EXTRACT(MONTH FROM p_data_inicial) AND
                   EXTRACT(YEAR FROM tb.dt_baixa) = EXTRACT(YEAR FROM p_data_inicial) AND
                   DATE_TRUNC('day', tb.dt_baixa) BETWEEN p_data_inicial AND p_data_final
                 WHEN p_tipo_pesquisa_data_relatorio = 'DATA_VENCIMENTO' THEN
                   -- Filtro estrito: APENAS o mês solicitado
                   EXTRACT(MONTH FROM t.dt_vencimento) = EXTRACT(MONTH FROM p_data_inicial) AND
                   EXTRACT(YEAR FROM t.dt_vencimento) = EXTRACT(YEAR FROM p_data_inicial) AND
                   DATE_TRUNC('day', t.dt_vencimento) BETWEEN p_data_inicial AND p_data_final
                 ELSE
                   -- Filtro estrito: APENAS o mês solicitado
                   EXTRACT(MONTH FROM t.dt_emissao) = EXTRACT(MONTH FROM p_data_inicial) AND
                   EXTRACT(YEAR FROM t.dt_emissao) = EXTRACT(YEAR FROM p_data_inicial) AND
                   DATE_TRUNC('day', t.dt_emissao) BETWEEN p_data_inicial AND p_data_final
                 END
               )
             AND pc.id_dre IS NOT NULL
           GROUP BY
             t.id_aplicacao, t.id_empresa, trd.id_titulo_rateio_despesa,
             data_reg, pc.id_dre, pc.id_conta, pc.nome,
             cc.id_centro_custo, cc.nome, trc.perc_rateio, p.id_pessoa, p.nome, e.nome
         ),

         contas_vl AS (
           SELECT
             e.id_aplicacao,
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             d.tp_tipo AS dre_tipo,
             t.data_reg,
             t.id_conta,
             t.descricao_categoria,
             t.cc_id,
             t.cc_nome,
             t.id_pessoa,
             t.pessoa_nome,
             SUM(COALESCE(t.multa_vl, 0)) AS multa_vl,
             SUM(COALESCE(t.juros_vl, 0)) AS juros_vl,
             SUM(COALESCE(t.desconto_vl, 0)) AS desconto_vl,
             SUM(COALESCE(t.abatimento_vl, 0)) AS abatimento_vl,
             SUM(COALESCE(t.total_vl, 0)) AS vl_final
           FROM
             erp.fn_dre d
               JOIN titulos_base t ON t.id_dre = d.id_dre
               JOIN emp_dados e ON e.id_aplicacao = d.id_aplicacao AND e.id_empresa = t.emp_id
           -- Removido o filtro t.total_vl <> 0 que pode estar bloqueando valores
           -- O rateio já foi aplicado na tabela titulos_base, não precisa multiplicar novamente aqui
           GROUP BY
             e.id_aplicacao, e.id_empresa, e.nome, d.id_dre, d.nome, d.tp_tipo,
             t.data_reg, t.id_conta, t.descricao_categoria, t.cc_id, t.cc_nome,
             t.id_pessoa, t.pessoa_nome
         ),

         juros_dados AS (
           SELECT
             cv.emp_id,
             cv.emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             cv.dre_tipo,
             cv.data_reg,
             0 AS id_conta,
             CASE
               WHEN cv.dre_tipo = 'DESPESA' THEN 'Juros Pagos*'
               ELSE 'Juros Recebidos*'
               END AS descricao_categoria,
             cv.cc_id,
             cv.cc_nome,
             cv.id_pessoa,
             cv.pessoa_nome,
             SUM(COALESCE(cv.juros_vl, 0)) AS vl_final
           FROM
             contas_vl cv
               JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
           WHERE
             d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
             AND cv.juros_vl IS NOT NULL
             -- Removido filtro cv.juros_vl <> 0 para permitir valores
           GROUP BY
             cv.emp_id, cv.emp_nome, d.id_dre, d.nome, cv.dre_tipo,
             cv.data_reg, cv.cc_id, cv.cc_nome,
             cv.id_pessoa, cv.pessoa_nome
         ),

         multas_dados AS (
           SELECT
             cv.emp_id,
             cv.emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             cv.dre_tipo,
             cv.data_reg,
             0 AS id_conta,
             CASE
               WHEN cv.dre_tipo = 'DESPESA' THEN 'Multas Pagas*'
               ELSE 'Multas Recebidos*'
               END AS descricao_categoria,
             cv.cc_id,
             cv.cc_nome,
             cv.id_pessoa,
             cv.pessoa_nome,
             SUM(COALESCE(cv.multa_vl, 0)) AS vl_final
           FROM
             contas_vl cv
               JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
           WHERE
             d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
             AND cv.multa_vl IS NOT NULL
             -- Removido filtro cv.multa_vl <> 0 para permitir valores
           GROUP BY
             cv.emp_id, cv.emp_nome, d.id_dre, d.nome, cv.dre_tipo,
             cv.data_reg, cv.cc_id, cv.cc_nome,
             cv.id_pessoa, cv.pessoa_nome
         ),

         descontos_dados AS (
           SELECT
             cv.emp_id,
             cv.emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             d.tp_tipo AS dre_tipo,
             cv.data_reg,
             0 AS id_conta,
             CASE
               WHEN d.tp_tipo = 'DESPESA' THEN 'Desconto Concedidos*'
               ELSE 'Desconto Obtido*'
               END AS descricao_categoria,
             cv.cc_id,
             cv.cc_nome,
             cv.id_pessoa,
             cv.pessoa_nome,
             SUM(COALESCE(cv.desconto_vl, 0)) AS vl_final
           FROM
             contas_vl cv
               JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
           WHERE
             d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
             AND cv.desconto_vl IS NOT NULL
             -- Removido filtro cv.desconto_vl <> 0 para permitir valores
             -- Condição relaxada: AND d.tp_tipo != cv.dre_tipo
           GROUP BY
             cv.emp_id, cv.emp_nome, d.id_dre, d.nome, d.tp_tipo,
             cv.data_reg, cv.cc_id, cv.cc_nome,
             cv.id_pessoa, cv.pessoa_nome
         ),

         abatimentos_dados AS (
           SELECT
             cv.emp_id,
             cv.emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             d.tp_tipo AS dre_tipo,
             cv.data_reg,
             0 AS id_conta,
             CASE
               WHEN d.tp_tipo = 'DESPESA' THEN 'Abatimento Concedidos*'
               ELSE 'Abatimento Obtido*'
               END AS descricao_categoria,
             cv.cc_id,
             cv.cc_nome,
             cv.id_pessoa,
             cv.pessoa_nome,
             SUM(COALESCE(cv.abatimento_vl, 0)) AS vl_final
           FROM
             contas_vl cv
               JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
           WHERE
             d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
             AND cv.abatimento_vl IS NOT NULL
             -- Removido filtro cv.abatimento_vl <> 0 para permitir valores
             -- Condição relaxada: AND d.tp_tipo != cv.dre_tipo
           GROUP BY
             cv.emp_id, cv.emp_nome, d.id_dre, d.nome, d.tp_tipo,
             cv.data_reg, cv.cc_id, cv.cc_nome,
             cv.id_pessoa, cv.pessoa_nome
         ),

         entradas_manuais AS (
           SELECT
             e.id_aplicacao,
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             d.id_dre AS dre_id,
             d.nome AS dre_nome,
             d.tp_tipo AS dre_tipo,
             DATE_TRUNC('day', m.dt_movto) AS data_reg,
             c.id_conta,
             c.nome AS descricao_categoria,
             0 AS cc_id,
             '-' AS cc_nome,
             0 AS id_pessoa,
             '-' AS pessoa_nome,
             ABS(m.vl_movto) AS vl_final
           FROM
             erp.fn_movto_banco m
               JOIN erp.fn_transacao t ON m.id_transacao = t.id_transacao
               JOIN emp_dados e ON m.id_aplicacao = e.id_aplicacao
               JOIN erp.fn_plano_conta c ON m.id_conta = c.id_conta
               JOIN dre_cfg d ON d.id_dre = c.id_dre AND d.emp_id = e.id_empresa
           WHERE
             t.tp_transacao = 6
             -- Filtro extremamente rigoroso: APENAS o mês/ano solicitado
             AND EXTRACT(MONTH FROM m.dt_movto) = EXTRACT(MONTH FROM p_data_inicial)
             AND EXTRACT(YEAR FROM m.dt_movto) = EXTRACT(YEAR FROM p_data_inicial)
             AND DATE_TRUNC('day', m.dt_movto) BETWEEN p_data_inicial AND p_data_final
         ),

         dados_combinados AS (
           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM
             contas_vl dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM juros_dados dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM multas_dados dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM descontos_dados dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM abatimentos_dados dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM ordens_servico_prev dc

           UNION ALL

           SELECT
             dc.emp_id,
             dc.emp_nome,
             dc.dre_id,
             dc.dre_nome,
             dc.dre_tipo,
             dc.data_reg,
             dc.id_conta,
             dc.descricao_categoria,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM entradas_manuais dc
         ),

         receita_liquida AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             3 AS ordem_item,
             '-' AS dre_tipo,
             0 AS dre_id,
             '(=) Receita Líquida' AS dre_nome,
             '-' AS descricao_categoria,
             dc.data_reg,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             (
               COALESCE(
                 (
                   SELECT SUM(dc1.vl_final)
                   FROM dados_combinados dc1
                   WHERE
                     dc1.dre_nome = 'Receita com Vendas'
                     AND dc1.emp_id = e.id_empresa
                     AND dc1.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM dc1.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc1.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc1.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc1.cc_id = dc.cc_id
                     AND dc1.id_pessoa = dc.id_pessoa
                 ),
                 0
               ) -
               COALESCE(
                 (
                   SELECT SUM(dc2.vl_final)
                   FROM dados_combinados dc2
                   WHERE
                     dc2.dre_nome = 'Deduções e abatimentos'
                     AND dc2.emp_id = e.id_empresa
                     AND dc2.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM dc2.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc2.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc2.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc2.cc_id = dc.cc_id
                     AND dc2.id_pessoa = dc.id_pessoa
                 ),
                 0
               )
               ) AS vl_final
           FROM
             emp_dados e
               LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa 
                 AND dc.data_reg BETWEEN p_data_inicial AND p_data_final
                 -- Garantia adicional: apenas dados do mês solicitado
                 AND EXTRACT(MONTH FROM dc.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                 AND EXTRACT(YEAR FROM dc.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
           GROUP BY
             e.id_empresa, e.nome, dc.data_reg, dc.cc_id,
             dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
         ),

         lucro_bruto AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             5 AS ordem_item,
             '-' AS dre_tipo,
             0 AS dre_id,
             '(=) Lucro Bruto' AS dre_nome,
             '-' AS descricao_categoria,
             dc.data_reg,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             (
               COALESCE(
                 (
                   SELECT SUM(rl.vl_final)
                   FROM receita_liquida rl
                   WHERE
                     rl.emp_id = e.id_empresa
                     AND rl.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM rl.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM rl.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND rl.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND rl.cc_id = dc.cc_id
                     AND rl.id_pessoa = dc.id_pessoa
                 ),
                 0
               ) -
               COALESCE(
                 (
                   SELECT SUM(dc1.vl_final)
                   FROM dados_combinados dc1
                   WHERE
                     dc1.dre_nome = 'Custo da Mercadoria'
                     AND dc1.emp_id = e.id_empresa
                     AND dc1.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM dc1.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc1.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc1.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc1.cc_id = dc.cc_id
                     AND dc1.id_pessoa = dc.id_pessoa
                 ),
                 0
               )
               ) AS vl_final
           FROM
             emp_dados e
               LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa 
                 AND dc.data_reg BETWEEN p_data_inicial AND p_data_final
                 -- Garantia adicional: apenas dados do mês solicitado
                 AND EXTRACT(MONTH FROM dc.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                 AND EXTRACT(YEAR FROM dc.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
           GROUP BY
             e.id_empresa, e.nome, dc.data_reg, dc.cc_id,
             dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
         ),

         lucro_operacional AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             8 AS ordem_item,
             '-' AS dre_tipo,
             0 AS dre_id,
             '(=) Lucro/Prejuízo Operacional' AS dre_nome,
             '-' AS descricao_categoria,
             dc.data_reg,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             (
               COALESCE(
                 (
                   SELECT SUM(lb.vl_final)
                   FROM lucro_bruto lb
                   WHERE
                     lb.emp_id = e.id_empresa
                     AND lb.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM lb.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM lb.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND lb.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND lb.cc_id = dc.cc_id
                     AND lb.id_pessoa = dc.id_pessoa
                 ),
                 0
               ) -
               COALESCE(
                 (
                   SELECT SUM(dc1.vl_final)
                   FROM dados_combinados dc1
                   WHERE
                     dc1.dre_nome IN ('Despesas Administrativas', 'Despesas de Vendas')
                     AND dc1.emp_id = e.id_empresa
                     AND dc1.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM dc1.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc1.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc1.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc1.cc_id = dc.cc_id
                     AND dc1.id_pessoa = dc.id_pessoa
                 ),
                 0
               )
               ) AS vl_final
           FROM
             emp_dados e
               LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa 
                 AND dc.data_reg BETWEEN p_data_inicial AND p_data_final
                 -- Garantia adicional: apenas dados do mês solicitado
                 AND EXTRACT(MONTH FROM dc.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                 AND EXTRACT(YEAR FROM dc.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
           GROUP BY
             e.id_empresa, e.nome, dc.data_reg, dc.cc_id,
             dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
         ),

         lucro_liquido AS (
           SELECT
             e.id_empresa AS emp_id,
             e.nome AS emp_nome,
             13 AS ordem_item,
             '-' AS dre_tipo,
             0 AS dre_id,
             '(=) Lucro/Prejuízo Líquido' AS dre_nome,
             '-' AS descricao_categoria,
             dc.data_reg,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             (
               COALESCE(
                 (
                   SELECT SUM(lo.vl_final)
                   FROM lucro_operacional lo
                   WHERE
                     lo.emp_id = e.id_empresa
                     AND lo.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM lo.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM lo.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND lo.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND lo.cc_id = dc.cc_id
                     AND lo.id_pessoa = dc.id_pessoa
                 ),
                 0
               ) -
               COALESCE(
                 (
                   SELECT SUM(dc1.vl_final)
                   FROM dados_combinados dc1
                   WHERE
                     dc1.dre_nome IN ('Despesas Financeiras', 'Outras Despesas não Operacionais')
                     AND dc1.emp_id = e.id_empresa
                     AND dc1.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado
                     AND EXTRACT(MONTH FROM dc1.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc1.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc1.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc1.cc_id = dc.cc_id
                     AND dc1.id_pessoa = dc.id_pessoa
                 ),
                 0
               ) +
               COALESCE(
                 (
                   SELECT SUM(dc2.vl_final)
                   FROM dados_combinados dc2
                   WHERE
                     dc2.dre_nome IN ('Receitas Financeiras', 'Outras Receitas não Operacionais')
                     AND dc2.emp_id = e.id_empresa
                     AND dc2.data_reg = dc.data_reg
                     -- Filtro crítico: garantir que estamos APENAS no mês solicitado  
                     AND EXTRACT(MONTH FROM dc2.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                     AND EXTRACT(YEAR FROM dc2.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
                     AND dc2.data_reg BETWEEN p_data_inicial AND p_data_final
                     AND dc2.cc_id = dc.cc_id
                     AND dc2.id_pessoa = dc.id_pessoa
                 ),
                 0
               )
               ) AS vl_final
           FROM
             emp_dados e
               LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa 
                 AND dc.data_reg BETWEEN p_data_inicial AND p_data_final
                 -- Garantia adicional: apenas dados do mês solicitado
                 AND EXTRACT(MONTH FROM dc.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
                 AND EXTRACT(YEAR FROM dc.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
           GROUP BY
             e.id_empresa, e.nome, dc.data_reg, dc.cc_id,
             dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
         ),

         resultado_final AS (
           SELECT
             dc.emp_id,
             dc.emp_nome,
             COALESCE(
               (
                 SELECT d.ordem_dre
                 FROM dre_cfg d
                 WHERE d.id_dre = dc.dre_id AND d.emp_id = dc.emp_id
               ), 99
             ) AS ordem_item,
             dc.dre_tipo,
             dc.dre_id,
             CASE
               WHEN dc.dre_tipo = 'DESPESA' THEN CONCAT('(-) ', dc.dre_nome)
               ELSE CONCAT('(+) ', dc.dre_nome)
               END AS dre_nome_final,
             dc.descricao_categoria,
             dc.data_reg,
             dc.cc_id,
             dc.cc_nome,
             dc.id_pessoa,
             dc.pessoa_nome,
             dc.vl_final
           FROM
             dados_combinados dc

           UNION ALL

           SELECT
             emp_id,
             emp_nome,
             ordem_item,
             dre_tipo,
             dre_id,
             dre_nome,
             descricao_categoria,
             data_reg,
             cc_id,
             cc_nome,
             id_pessoa,
             pessoa_nome,
             vl_final
           FROM receita_liquida

           UNION ALL

           SELECT
             emp_id,
             emp_nome,
             ordem_item,
             dre_tipo,
             dre_id,
             dre_nome,
             descricao_categoria,
             data_reg,
             cc_id,
             cc_nome,
             id_pessoa,
             pessoa_nome,
             vl_final
           FROM lucro_bruto

           UNION ALL

           SELECT
             emp_id,
             emp_nome,
             ordem_item,
             dre_tipo,
             dre_id,
             dre_nome,
             descricao_categoria,
             data_reg,
             cc_id,
             cc_nome,
             id_pessoa,
             pessoa_nome,
             vl_final
           FROM lucro_operacional

           UNION ALL

           SELECT
             emp_id,
             emp_nome,
             ordem_item,
             dre_tipo,
             dre_id,
             dre_nome,
             descricao_categoria,
             data_reg,
             cc_id,
             cc_nome,
             id_pessoa,
             pessoa_nome,
             vl_final
           FROM lucro_liquido
         )

    SELECT
      CAST(rf.emp_id AS INTEGER) AS empresa_id,
      CAST(rf.emp_nome AS VARCHAR) AS empresa_nome,
      CAST(COALESCE(rf.ordem_item, 99) AS INTEGER) AS ordem,
      CAST(rf.dre_tipo AS VARCHAR) AS tipo,
      CAST(rf.dre_id AS INTEGER) AS conta_do_dre_id,
      CAST(rf.dre_nome_final AS VARCHAR) AS conta_do_dre_nome,
      CAST(rf.descricao_categoria AS VARCHAR) AS categoria_produto,
      CAST(rf.data_reg AS DATE) AS data_movimento,
      CAST(rf.cc_id AS INTEGER) AS centro_custo_id,
      CAST(rf.cc_nome AS TEXT) AS centro_custo_nome,
      CAST(rf.id_pessoa AS INTEGER) AS parceiro_id,
      CAST(rf.pessoa_nome AS VARCHAR) AS parceiro_nome,
      CAST(COALESCE(rf.vl_final, 0) AS NUMERIC(19,2)) AS valor
    FROM
      resultado_final rf
    WHERE 
      -- Garantimos que estamos estritamente dentro do período solicitado
      rf.data_reg IS NOT NULL 
      -- FILTRO SUPER RIGOROSO: Garantir que os registros pertencem APENAS ao mês solicitado
      -- Isso resolve o problema dos dados de março aparecendo em abril
      AND EXTRACT(MONTH FROM rf.data_reg) = EXTRACT(MONTH FROM p_data_inicial)
      AND EXTRACT(YEAR FROM rf.data_reg) = EXTRACT(YEAR FROM p_data_inicial)
      -- Filtro redundante para garantir que estamos dentro do range original
      AND rf.data_reg BETWEEN DATE_TRUNC('month', p_data_inicial) AND (DATE_TRUNC('month', p_data_inicial) + INTERVAL '1 month - 1 day')::DATE
      -- Filtro adicional para GARANTIR que não tem dados do mês anterior
      AND rf.data_reg > (DATE_TRUNC('month', p_data_inicial) - INTERVAL '1 day')::DATE
    ORDER BY
      rf.emp_id,
      rf.ordem_item,
      rf.data_reg;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION erp.fn_gerar_dre(DATE, DATE, INTEGER, VARCHAR) IS 'Function to generate DRE (Demonstração do Resultado do Exercício) financial report data with a single company ID with OS forecast and strict month filtering';