-- Função para consultar contas a pagar e receber, incluindo ordens de serviço recorrentes
DROP FUNCTION IF EXISTS fn_consulta_conta_pagar_receber_v2;

CREATE OR REPLACE FUNCTION fn_consulta_conta_pagar_receber_v2(
  p_tipo_titulo VARCHAR,
  p_id_aplicacao VARCHAR
) RETURNS TABLE (
                  empresaId INTEGER,
                  empresaNome VARCHAR,
                  situacao VARCHAR,
                  fornecedorNome VARCHAR,
                  fornecedorNomeInterno VARCHAR,
                  planoContaId INTEGER,
                  planoContaNome VARCHAR,
                  percentual_despesa NUMERIC(19,6),
                  valorCalculado_despesa NUMERIC(19,2),
                  percentual_cc NUMERIC(19,6),
                  valorCalculado_cc NUMERIC(19,2),
                  dataVencimento DATE,
                  contaBancariaId INTEGER,
                  contaBancariaNome VARCHAR,
                  impostosRetidos NUMERIC(19,2),
                  totalValorPis NUMERIC(19,2),
                  totalValorCofins NUMERIC(19,2),
                  totalValorInss NUMERIC(19,2),
                  totalValorCsll NUMERIC(19,2),
                  totalValorIss NUMERIC(19,2),
                  totalValorIr NUMERIC(19,2),
                  valorTotalImpostosRetidos NUMERIC(19,2),
                  valorOriginal NUMERIC(19,2),
                  valorTotal NUMERIC(19,2),
                  valorJuros NUMERIC(19,2),
                  valorMultas NUMERIC(19,2),
                  valorDescontos NUMERIC(19,2),
                  valorAbatimentos NUMERIC(19,2),
                  valorTotalPago NUMERIC(19,2),
                  dataEmissao TIMESTAMP,
                  centroCustoId INTEGER,
                  centroCustoDescricao VARCHAR,
                  tipoTitulo VARCHAR,
                  possuiRecorrencia BOOLEAN,
                  fornecedorId INTEGER,
                  fornecedorTipoPessoa VARCHAR,
                  fornecedorCpfCnpj VARCHAR,
                  valorPis NUMERIC(19,2),
                  valorCofins NUMERIC(19,2),
                  valorInss NUMERIC(19,2),
                  valorCsll NUMERIC(19,2),
                  valorIss NUMERIC(19,2),
                  valorIr NUMERIC(19,2),
                  dataGeracao TIMESTAMP,
                  dataPagamento DATE,
                  tipo VARCHAR,
                  dataHistorico TIMESTAMP,
                  acao VARCHAR,
                  usuarioId VARCHAR,
                  usuarioNome VARCHAR,
                  numeroNotaFiscal VARCHAR,
                  observacao VARCHAR,
                  codigoDeBarras VARCHAR,
                  numeroDocumento VARCHAR,
                  numeroParcela INTEGER,
                  parcela_texto VARCHAR,
                  formaPagamentoId INTEGER,
                  formaPagamentoNome VARCHAR
                ) AS $$
BEGIN
  RETURN QUERY
    WITH
      -- Define a data de início para ordens de serviço
      data_filtro AS (
        SELECT
          -- Usamos a data atual como base
          CURRENT_DATE AS data_base,
          -- Para ordens de serviço futuras, usamos o primeiro dia do próximo mês
          DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month')::DATE AS data_inicio_os,
          -- Para data final, usamos 6 meses a partir de hoje
          (CURRENT_DATE + INTERVAL '6 months')::DATE AS data_fim
      ),

      -- Melhorada CTE para empresa, garantindo que sempre tenha nome
      info_empresa AS (
        SELECT
          id_empresa,
          id_aplicacao,
          COALESCE(
            NULLIF(nome_fantasia,''),
            NULLIF(nome,''),
            'Empresa ' || id_empresa::TEXT
          ) AS nome_empresa
        FROM
          erp.cd_empresa
      ),

      -- Títulos com suas baixas
      titulo_baixa_count AS (
        SELECT
          id_titulo,
          COUNT(*) as count_baixas
        FROM
          erp.fn_titulo_baixa
        GROUP BY
          id_titulo
      ),
      titulo_recorrencia AS (
        SELECT
          id_titulo_referencia,
          TRUE AS tem_recorrencia
        FROM
          erp.fn_titulo
        GROUP BY
          id_titulo_referencia
        HAVING
          COUNT(*) > 1
      ),
      titulo_baixa_info AS (
        SELECT
          id_titulo,
          MAX(dt_baixa) AS dt_baixa,
          SUM(vl_juros) AS vl_juros,
          SUM(vl_multa) AS vl_multa,
          SUM(vl_desconto) AS vl_desconto,
          SUM(vl_abatimento) AS vl_abatimento,
          SUM(vl_baixa) AS vl_baixa
        FROM
          erp.fn_titulo_baixa
        GROUP BY
          id_titulo
      ),

      -- Gera series de meses para ordens de serviço recorrentes
      meses_os AS (
        SELECT generate_series(
                 (SELECT data_inicio_os FROM data_filtro),
                 (SELECT data_fim FROM data_filtro),
                 '1 month'::INTERVAL
               )::DATE AS primeiro_dia_mes
      ),

      -- Ordens de serviço recorrentes FATURADAS - uma para cada mês no período
      ordens_servico_recorrentes AS (
        SELECT
          e.id_empresa::INTEGER AS empresaId,
          COALESCE(ie.nome_empresa, 'Empresa ' || e.id_empresa::TEXT)::VARCHAR AS empresaNome,
          'PREVISTO'::VARCHAR AS situacao,
          c.nome::VARCHAR AS fornecedorNome,
          c.nome_interno::VARCHAR AS fornecedorNomeInterno,
          os.id_conta_receita::INTEGER AS planoContaId,
          pc.nome::VARCHAR AS planoContaNome,
          1.0::NUMERIC(19,6) AS percentual_despesa,
          os.total_servicos::NUMERIC(19,2) AS valorCalculado_despesa,
          NULL::NUMERIC(19,6) AS percentual_cc,
          NULL::NUMERIC(19,2) AS valorCalculado_cc,
          CASE
            -- Se tem vencimento no próximo mês
            WHEN os.vencimento_proximo_mes THEN
              (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '1 month' +
               ((LEAST(
                   COALESCE(os.dia_faturamento, os.dia_vencimento, EXTRACT(DAY FROM os.vigencia_inicial)::INTEGER),
                   EXTRACT(DAY FROM (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '2 month - 1 day'))::INTEGER
                 ) - 1)::TEXT || ' days')::INTERVAL)::DATE
            ELSE
              -- Calcula para o mês atual
              (DATE_TRUNC('month', m.primeiro_dia_mes) +
               ((LEAST(
                   COALESCE(os.dia_faturamento, os.dia_vencimento, EXTRACT(DAY FROM os.vigencia_inicial)::INTEGER),
                   EXTRACT(DAY FROM (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '1 month - 1 day'))::INTEGER
                 ) - 1)::TEXT || ' days')::INTERVAL)::DATE
            END AS dataVencimento,

          COALESCE(os.id_conta_corrente, (
            SELECT MIN(bc.id_banco_conta)
            FROM erp.fn_banco_conta bc
                   JOIN erp.cd_empresa emp ON bc.id_aplicacao = emp.id_aplicacao
            WHERE emp.id_empresa = e.id_empresa
              AND bc.id_aplicacao = p_id_aplicacao
            LIMIT 1
          ))::INTEGER AS contaBancariaId,
          cb.nome::VARCHAR AS contaBancariaNome,
          0.00::NUMERIC(19,2) AS impostosRetidos,
          0.00::NUMERIC(19,2) AS totalValorPis,
          0.00::NUMERIC(19,2) AS totalValorCofins,
          0.00::NUMERIC(19,2) AS totalValorInss,
          0.00::NUMERIC(19,2) AS totalValorCsll,
          0.00::NUMERIC(19,2) AS totalValorIss,
          0.00::NUMERIC(19,2) AS totalValorIr,
          0.00::NUMERIC(19,2) AS valorTotalImpostosRetidos,
          os.valor_nf::NUMERIC(19,2) AS valorOriginal,
          os.valor_nf::NUMERIC(19,2) AS valorTotal,
          0.00::NUMERIC(19,2) AS valorJuros,
          0.00::NUMERIC(19,2) AS valorMultas,
          os.total_descontos::NUMERIC(19,2) AS valorDescontos,
          0.00::NUMERIC(19,2) AS valorAbatimentos,
          0.00::NUMERIC(19,2) AS valorTotalPago,
          os.data_criacao::TIMESTAMP AS dataEmissao,
          NULL::INTEGER AS centroCustoId,
          NULL::VARCHAR as centroCustoDescricao,
          'RECEBER'::VARCHAR AS tipoTitulo,
          TRUE::BOOLEAN AS possuiRecorrencia,
          c.id_pessoa::INTEGER AS fornecedorId,
          c.tp_pessoa::VARCHAR as fornecedorTipoPessoa,
          c.cpf_cnpj::VARCHAR as fornecedorCpfCnpj,
          0.00::NUMERIC(19,2) AS valorPis,
          0.00::NUMERIC(19,2) AS valorCofins,
          0.00::NUMERIC(19,2) AS valorInss,
          0.00::NUMERIC(19,2) AS valorCsll,
          0.00::NUMERIC(19,2) AS valorIss,
          0.00::NUMERIC(19,2) AS valorIr,
          CAST(NOW() AS TIMESTAMP) AS dataGeracao,
          NULL::DATE AS dataPagamento,
          'RECEBER'::VARCHAR AS tipo,
          NULL::TIMESTAMP AS dataHistorico,
          NULL::VARCHAR AS acao,
          NULL::VARCHAR AS usuarioId,
          NULL::VARCHAR AS usuarioNome,
          NULL::VARCHAR AS numeroNotaFiscal,
          os.observacoes::VARCHAR AS observacao,
          NULL::VARCHAR AS codigoDeBarras,
          os.codigo::VARCHAR AS numeroDocumento,
          COALESCE(NULL, 1)::INTEGER AS numeroParcela,
          CAST('-' AS VARCHAR) AS parcela_texto,
          os.id_forma_recebimento::INTEGER AS formaPagamentoId,
          fp.nome::VARCHAR AS formaPagamentoNome
        FROM
          meses_os m
            CROSS JOIN "erp-nfs".ordem_servico os
            JOIN erp.cd_empresa e ON os.id_empresa = e.id_empresa
            -- Usando LEFT JOIN com a CTE e adicionando condição de id_aplicacao
            LEFT JOIN info_empresa ie ON ie.id_empresa = e.id_empresa AND ie.id_aplicacao = e.id_aplicacao
            JOIN erp.cd_pessoa c ON os.id_cliente = c.id_pessoa
            LEFT JOIN erp.fn_plano_conta pc ON os.id_conta_receita = pc.id_conta
            LEFT JOIN erp.fn_banco_conta cb ON os.id_conta_corrente = cb.id_banco_conta
            LEFT JOIN erp.cd_forma_pagamento fp ON os.id_forma_recebimento = fp.id_forma_pagamento
        WHERE
          os.recorrencia = TRUE
          AND os.situacao IN ('FATURADO')
          -- Verificar a vigência tendo como base o mês
          AND os.vigencia_inicial <= (m.primeiro_dia_mes + INTERVAL '1 month - 1 day')::DATE
          AND (os.vigencia_final IS NULL OR os.vigencia_final >= m.primeiro_dia_mes)
          AND os.id_aplicacao = p_id_aplicacao
          -- Apenas incluir se estivermos olhando para contas a receber ou todos os tipos
          AND (p_tipo_titulo IS NULL OR p_tipo_titulo = 'RECEBER')
      ),

      -- Ordens de serviço recorrentes NÃO FATURADAS - uma para cada mês no período
      ordens_servico_recorrentes_nao_faturadas AS (
        SELECT
          e.id_empresa::INTEGER AS empresaId,
          COALESCE(ie.nome_empresa, 'Empresa ' || e.id_empresa::TEXT)::VARCHAR AS empresaNome,
          'ORDEM DE SERVIÇO NÃO FATURADA'::VARCHAR AS situacao,
          c.nome::VARCHAR AS fornecedorNome,
          c.nome_interno::VARCHAR AS fornecedorNomeInterno,
          os.id_conta_receita::INTEGER AS planoContaId,
          pc.nome::VARCHAR AS planoContaNome,
          1.0::NUMERIC(19,6) AS percentual_despesa,
          os.total_servicos::NUMERIC(19,2) AS valorCalculado_despesa,
          NULL::NUMERIC(19,6) AS percentual_cc,
          NULL::NUMERIC(19,2) AS valorCalculado_cc,
          CASE
            -- Se tem vencimento no próximo mês
            WHEN os.vencimento_proximo_mes THEN
              (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '1 month' +
               ((LEAST(
                   COALESCE(os.dia_faturamento, os.dia_vencimento, EXTRACT(DAY FROM os.vigencia_inicial)::INTEGER),
                   EXTRACT(DAY FROM (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '2 month - 1 day'))::INTEGER
                 ) - 1)::TEXT || ' days')::INTERVAL)::DATE
            ELSE
              -- Calcula para o mês atual
              (DATE_TRUNC('month', m.primeiro_dia_mes) +
               ((LEAST(
                   COALESCE(os.dia_faturamento, os.dia_vencimento, EXTRACT(DAY FROM os.vigencia_inicial)::INTEGER),
                   EXTRACT(DAY FROM (DATE_TRUNC('month', m.primeiro_dia_mes) + INTERVAL '1 month - 1 day'))::INTEGER
                 ) - 1)::TEXT || ' days')::INTERVAL)::DATE
            END AS dataVencimento,

          COALESCE(os.id_conta_corrente, (
            SELECT MIN(bc.id_banco_conta)
            FROM erp.fn_banco_conta bc
                   JOIN erp.cd_empresa emp ON bc.id_aplicacao = emp.id_aplicacao
            WHERE emp.id_empresa = e.id_empresa
              AND bc.id_aplicacao = p_id_aplicacao
            LIMIT 1
          ))::INTEGER AS contaBancariaId,
          cb.nome::VARCHAR AS contaBancariaNome,
          0.00::NUMERIC(19,2) AS impostosRetidos,
          0.00::NUMERIC(19,2) AS totalValorPis,
          0.00::NUMERIC(19,2) AS totalValorCofins,
          0.00::NUMERIC(19,2) AS totalValorInss,
          0.00::NUMERIC(19,2) AS totalValorCsll,
          0.00::NUMERIC(19,2) AS totalValorIss,
          0.00::NUMERIC(19,2) AS totalValorIr,
          0.00::NUMERIC(19,2) AS valorTotalImpostosRetidos,
          os.valor_nf::NUMERIC(19,2) AS valorOriginal,
          os.valor_nf::NUMERIC(19,2) AS valorTotal,
          0.00::NUMERIC(19,2) AS valorJuros,
          0.00::NUMERIC(19,2) AS valorMultas,
          os.total_descontos::NUMERIC(19,2) AS valorDescontos,
          0.00::NUMERIC(19,2) AS valorAbatimentos,
          0.00::NUMERIC(19,2) AS valorTotalPago,
          os.data_criacao::TIMESTAMP AS dataEmissao,
          NULL::INTEGER AS centroCustoId,
          NULL::VARCHAR as centroCustoDescricao,
          'RECEBER'::VARCHAR AS tipoTitulo,
          TRUE::BOOLEAN AS possuiRecorrencia,
          c.id_pessoa::INTEGER AS fornecedorId,
          c.tp_pessoa::VARCHAR as fornecedorTipoPessoa,
          c.cpf_cnpj::VARCHAR as fornecedorCpfCnpj,
          0.00::NUMERIC(19,2) AS valorPis,
          0.00::NUMERIC(19,2) AS valorCofins,
          0.00::NUMERIC(19,2) AS valorInss,
          0.00::NUMERIC(19,2) AS valorCsll,
          0.00::NUMERIC(19,2) AS valorIss,
          0.00::NUMERIC(19,2) AS valorIr,
          CAST(NOW() AS TIMESTAMP) AS dataGeracao,
          NULL::DATE AS dataPagamento,
          'RECEBER'::VARCHAR AS tipo,
          NULL::TIMESTAMP AS dataHistorico,
          NULL::VARCHAR AS acao,
          NULL::VARCHAR AS usuarioId,
          NULL::VARCHAR AS usuarioNome,
          NULL::VARCHAR AS numeroNotaFiscal,
          os.observacoes::VARCHAR AS observacao,
          NULL::VARCHAR AS codigoDeBarras,
          os.codigo::VARCHAR AS numeroDocumento,
          COALESCE(NULL, 1)::INTEGER AS numeroParcela,
          CAST('-' AS VARCHAR) AS parcela_texto,
          os.id_forma_recebimento::INTEGER AS formaPagamentoId,
          fp.nome::VARCHAR AS formaPagamentoNome
        FROM
          meses_os m
            CROSS JOIN "erp-nfs".ordem_servico os
            JOIN erp.cd_empresa e ON os.id_empresa = e.id_empresa
            -- Usando LEFT JOIN com a CTE e adicionando condição de id_aplicacao
            LEFT JOIN info_empresa ie ON ie.id_empresa = e.id_empresa AND ie.id_aplicacao = e.id_aplicacao
            JOIN erp.cd_pessoa c ON os.id_cliente = c.id_pessoa
            LEFT JOIN erp.fn_plano_conta pc ON os.id_conta_receita = pc.id_conta
            LEFT JOIN erp.fn_banco_conta cb ON os.id_conta_corrente = cb.id_banco_conta
            LEFT JOIN erp.cd_forma_pagamento fp ON os.id_forma_recebimento = fp.id_forma_pagamento
        WHERE
          os.recorrencia = TRUE
          AND os.situacao IN ('RECORRENCIA')
          -- Verificar a vigência tendo como base o mês
          AND os.vigencia_inicial <= (m.primeiro_dia_mes + INTERVAL '1 month - 1 day')::DATE
          AND (os.vigencia_final IS NULL OR os.vigencia_final >= m.primeiro_dia_mes)
          AND os.id_aplicacao = p_id_aplicacao
          -- Apenas incluir se estivermos olhando para contas a receber ou todos os tipos
          AND (p_tipo_titulo IS NULL OR p_tipo_titulo = 'RECEBER')
      ),

      -- Ordens de serviço não recorrentes dentro do período especificado
      ordens_servico_periodo AS (
        SELECT
          e.id_empresa::INTEGER AS empresaId,
          COALESCE(ie.nome_empresa, 'Empresa ' || e.id_empresa::TEXT)::VARCHAR AS empresaNome,
          os.situacao::VARCHAR AS situacao,
          c.nome::VARCHAR AS fornecedorNome,
          c.nome_interno::VARCHAR AS fornecedorNomeInterno,
          os.id_conta_receita::INTEGER AS planoContaId,
          pc.nome::VARCHAR AS planoContaNome,
          1.0::NUMERIC(19,6) AS percentual_despesa,
          os.total_servicos::NUMERIC(19,2) AS valorCalculado_despesa,
          NULL::NUMERIC(19,6) AS percentual_cc,
          NULL::NUMERIC(19,2) AS valorCalculado_cc,
          -- Para ordens não recorrentes, usamos as parcelas para definir as datas de vencimento
          osp.vencimento::DATE AS dataVencimento,
          COALESCE(os.id_conta_corrente, (
            SELECT MIN(bc.id_banco_conta)
            FROM erp.fn_banco_conta bc
                   JOIN erp.cd_empresa emp ON bc.id_aplicacao = emp.id_aplicacao
            WHERE emp.id_empresa = e.id_empresa
              AND bc.id_aplicacao = p_id_aplicacao
            LIMIT 1
          ))::INTEGER AS contaBancariaId,
          cb.nome::VARCHAR AS contaBancariaNome,
          os.total_impostos_retidos::NUMERIC(19,2) AS impostosRetidos,
          0.00::NUMERIC(19,2) AS totalValorPis,
          0.00::NUMERIC(19,2) AS totalValorCofins,
          0.00::NUMERIC(19,2) AS totalValorInss,
          0.00::NUMERIC(19,2) AS totalValorCsll,
          0.00::NUMERIC(19,2) AS totalValorIss,
          0.00::NUMERIC(19,2) AS totalValorIr,
          os.total_impostos_retidos::NUMERIC(19,2) AS valorTotalImpostosRetidos,
          osp.valor::NUMERIC(19,2) AS valorOriginal,
          osp.valor::NUMERIC(19,2) AS valorTotal,
          0.00::NUMERIC(19,2) AS valorJuros,
          0.00::NUMERIC(19,2) AS valorMultas,
          (os.total_descontos * (osp.valor / os.valor_nf))::NUMERIC(19,2) AS valorDescontos,
          0.00::NUMERIC(19,2) AS valorAbatimentos,
          0.00::NUMERIC(19,2) AS valorTotalPago,
          os.data_criacao::TIMESTAMP AS dataEmissao,
          NULL::INTEGER AS centroCustoId,
          NULL::VARCHAR as centroCustoDescricao,
          'RECEBER'::VARCHAR AS tipoTitulo,
          FALSE::BOOLEAN AS possuiRecorrencia,
          c.id_pessoa::INTEGER AS fornecedorId,
          c.tp_pessoa::VARCHAR as fornecedorTipoPessoa,
          c.cpf_cnpj::VARCHAR as fornecedorCpfCnpj,
          0.00::NUMERIC(19,2) AS valorPis,
          0.00::NUMERIC(19,2) AS valorCofins,
          0.00::NUMERIC(19,2) AS valorInss,
          0.00::NUMERIC(19,2) AS valorCsll,
          0.00::NUMERIC(19,2) AS valorIss,
          0.00::NUMERIC(19,2) AS valorIr,
          CAST(NOW() AS TIMESTAMP) AS dataGeracao,
          NULL::DATE AS dataPagamento,
          'RECEBER'::VARCHAR AS tipo,
          NULL::TIMESTAMP AS dataHistorico,
          NULL::VARCHAR AS acao,
          NULL::VARCHAR AS usuarioId,
          NULL::VARCHAR AS usuarioNome,
          osn.numero_nfse::VARCHAR as numeroNotaFiscal,
          os.observacoes::VARCHAR AS observacao,
          NULL::VARCHAR AS codigoDeBarras,
          os.codigo::VARCHAR AS numeroDocumento,
          COALESCE(osp.numero, 1)::INTEGER AS numeroParcela,
          CAST('-' AS VARCHAR) AS parcela_texto,
          osp.id_forma_recebimento::INTEGER AS formaPagamentoId,
          fp.nome::VARCHAR AS formaPagamentoNome
        FROM
          "erp-nfs".ordem_servico os
            JOIN "erp-nfs".ordem_servico_parcela osp ON os.id = osp.id_ordem_servico
            JOIN erp.cd_empresa e ON os.id_empresa = e.id_empresa
            -- Usando LEFT JOIN com a CTE e adicionando condição de id_aplicacao
            LEFT JOIN info_empresa ie ON ie.id_empresa = e.id_empresa AND ie.id_aplicacao = e.id_aplicacao
            JOIN erp.cd_pessoa c ON os.id_cliente = c.id_pessoa
            LEFT JOIN erp.fn_plano_conta pc ON os.id_conta_receita = pc.id_conta
            LEFT JOIN erp.fn_banco_conta cb ON osp.id_conta_corrente = cb.id_banco_conta
            LEFT JOIN erp.cd_forma_pagamento fp ON osp.id_forma_recebimento = fp.id_forma_pagamento
            LEFT JOIN "erp-nfs".ordem_servico_nota_fiscal osn ON os.id_nota_fiscal = osn.id
        WHERE
          os.recorrencia = FALSE
          AND not os.situacao IN ('APROVADO', 'FATURADO', 'FINALIZADO')
          AND os.id_aplicacao = p_id_aplicacao
          AND (p_tipo_titulo IS NULL OR p_tipo_titulo = 'RECEBER')
          AND (
          -- Usar a data de início específica para ordens de serviço (primeiro dia do próximo mês)
          -- E limitar até a data final definida (6 meses a partir de hoje)
          (osp.vencimento::DATE >= (SELECT data_inicio_os FROM data_filtro))
            AND
          (osp.vencimento::DATE <= (SELECT data_fim FROM data_filtro))
          )
      )
    -- Títulos normais
    SELECT
      EMP.id_empresa::INTEGER AS empresaId,
      -- CORREÇÃO AQUI - Usar CTE info_empresa com COALESCE
      COALESCE(IE.nome_empresa, 'Empresa ' || EMP.id_empresa::TEXT)::VARCHAR AS empresaNome,
      TIT.situacao::VARCHAR,
      FORN.nome::VARCHAR AS fornecedorNome,
      FORN.nome_interno::VARCHAR AS fornecedorNomeInterno,
      RDE.id_conta::INTEGER AS planoContaId,
      FPC.nome::VARCHAR AS planoContaNome,
      RDE.perc_rateio::NUMERIC(19,6) as percentual_despesa,
      (TIT.vl_total_titulo * RDE.perc_rateio)::NUMERIC(19,2) AS valorCalculado_despesa,
      TAC.perc_rateio::NUMERIC(19,6) as percentual_cc,
      (TIT.vl_total_titulo * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1))::NUMERIC(19,2) AS valorCalculado_cc,
      TIT.dt_vencimento::DATE AS dataVencimento,
      FBC.id_banco_conta::INTEGER AS contaBancariaId,
      FBC.nome::VARCHAR AS contaBancariaNome,
      ((
        CASE WHEN TIT.ind_pis_retido = TRUE THEN COALESCE(TIT.vl_pis, 0) ELSE 0 END
          + CASE WHEN TIT.ind_cofins_retido = TRUE THEN COALESCE(TIT.vl_cofins, 0) ELSE 0 END
          + CASE WHEN TIT.ind_inss_retido = TRUE THEN COALESCE(TIT.vl_inss, 0) ELSE 0 END
          + CASE WHEN TIT.ind_csll_retido = TRUE THEN COALESCE(TIT.vl_csll, 0) ELSE 0 END
          + CASE WHEN TIT.ind_iss_retido = TRUE THEN COALESCE(TIT.vl_iss, 0) ELSE 0 END
          + CASE WHEN TIT.ind_ir_retido = TRUE THEN COALESCE(TIT.vl_ir, 0) ELSE 0 END
        ) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1))::NUMERIC(19,2) AS impostosRetidos,
      CASE WHEN TIT.ind_pis_retido = TRUE THEN COALESCE(TIT.vl_pis, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorPis,
      CASE WHEN TIT.ind_cofins_retido = TRUE THEN COALESCE(TIT.vl_cofins, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorCofins,
      CASE WHEN TIT.ind_inss_retido = TRUE THEN COALESCE(TIT.vl_inss, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorInss,
      CASE WHEN TIT.ind_csll_retido = TRUE THEN COALESCE(TIT.vl_csll, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorCsll,
      CASE WHEN TIT.ind_iss_retido = TRUE THEN COALESCE(TIT.vl_iss, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorIss,
      CASE WHEN TIT.ind_ir_retido = TRUE THEN COALESCE(TIT.vl_ir, 0) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END::NUMERIC(19,2) AS totalValorIr,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          ((
            CASE WHEN TIT.ind_pis_retido = TRUE THEN COALESCE(TIT.vl_pis, 0) ELSE 0 END
              + CASE WHEN TIT.ind_cofins_retido = TRUE THEN COALESCE(TIT.vl_cofins, 0) ELSE 0 END
              + CASE WHEN TIT.ind_inss_retido = TRUE THEN COALESCE(TIT.vl_inss, 0) ELSE 0 END
              + CASE WHEN TIT.ind_csll_retido = TRUE THEN COALESCE(TIT.vl_csll, 0) ELSE 0 END
              + CASE WHEN TIT.ind_iss_retido = TRUE THEN COALESCE(TIT.vl_iss, 0) ELSE 0 END
              + CASE WHEN TIT.ind_ir_retido = TRUE THEN COALESCE(TIT.vl_ir, 0) ELSE 0 END
            ) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1)
        ELSE 0
        END::NUMERIC(19,2) AS valorTotalImpostosRetidos,
      CASE
        WHEN TBC.count_baixas > 0 THEN TIT.vl_total_titulo / NULLIF(TBC.count_baixas, 0)
        ELSE TIT.vl_total_titulo
        END::NUMERIC(19,2) AS valorOriginal,
      CASE
        WHEN TBC.count_baixas > 0 THEN TIT.valor_liquido / NULLIF(TBC.count_baixas, 0)
        ELSE TIT.valor_liquido
        END::NUMERIC(19,2) AS valorTotal,
      COALESCE(TBI.vl_juros, 0)::NUMERIC(19,2) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) as valorJuros,
      COALESCE(TBI.vl_multa, 0)::NUMERIC(19,2) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) as valorMultas,
      COALESCE(TBI.vl_desconto, 0)::NUMERIC(19,2) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) as valorDescontos,
      COALESCE(TBI.vl_abatimento, 0)::NUMERIC(19,2) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) as valorAbatimentos,
      COALESCE(TBI.vl_baixa, 0)::NUMERIC(19,2) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) as valorTotalPago,
      TIT.dt_emissao::TIMESTAMP as dataEmissao,
      CCU.id_centro_custo::INTEGER as centroCustoId,
      CCU.nome::VARCHAR as centroCustoDescricao,
      TIT.tp_titulo::VARCHAR as tipoTitulo,
      COALESCE(TR.tem_recorrencia, FALSE) AS possuiRecorrencia,
      FORN.id_pessoa::INTEGER AS fornecedorId,
      FORN.tp_pessoa::VARCHAR as fornecedorTipoPessoa,
      FORN.cpf_cnpj::VARCHAR as fornecedorCpfCnpj,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_pis_retido THEN (COALESCE(TIT.vl_pis, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_pis_retido THEN (COALESCE(TIT.vl_pis, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorPis,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_cofins_retido THEN (COALESCE(TIT.vl_cofins, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_cofins_retido THEN (COALESCE(TIT.vl_cofins, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorCofins,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_inss_retido THEN (COALESCE(TIT.vl_inss, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_inss_retido THEN (COALESCE(TIT.vl_inss, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorInss,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_csll_retido THEN (COALESCE(TIT.vl_csll, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_csll_retido THEN (COALESCE(TIT.vl_csll, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorCsll,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_iss_retido THEN (COALESCE(TIT.vl_iss, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_iss_retido THEN (COALESCE(TIT.vl_iss, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorIss,
      CASE
        WHEN TBC.count_baixas > 0 THEN
          CASE WHEN TIT.ind_ir_retido THEN (COALESCE(TIT.vl_ir, 0) / NULLIF(TBC.count_baixas, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        ELSE
          CASE WHEN TIT.ind_ir_retido THEN (COALESCE(TIT.vl_ir, 0)) * RDE.perc_rateio * COALESCE(TAC.perc_rateio, 1) ELSE 0 END
        END::NUMERIC(19,2) AS valorIr,
      TIT.dt_geracao::TIMESTAMP as dataGeracao,
      CAST(TBI.dt_baixa AS DATE) AS dataPagamento,
      TIT.tp_titulo::VARCHAR AS tipo,
      HIS.dt_historico::TIMESTAMP AS dataHistorico,
      HIS.acao::VARCHAR AS acao,
      CAST(HIS.id_usuario AS VARCHAR) as usuarioId,
      USU.nome::VARCHAR AS usuarioNome,
      TIT.nr_nota_fiscal::VARCHAR as numeroNotaFiscal,
      TIT.observacao::VARCHAR as observacao,
      TIT.cod_barras::VARCHAR as codigoDeBarras,
      TIT.nr_documento::VARCHAR as numeroDocumento,
      COALESCE(TIT.nr_parcela, 1)::INTEGER as numeroParcela,
      CAST((CAST(COALESCE(TIT.nr_parcela, 1) AS VARCHAR) || ' de ' || CAST(COALESCE(TIT.qtd_parcelas, 1) AS VARCHAR)) AS VARCHAR) AS parcela_texto,
      TIT.id_forma_pagamento::INTEGER as formaPagamentoId,
      FPG.nome::VARCHAR as formaPagamentoNome
    FROM
      erp.ad_aplicacao APL
        INNER JOIN erp.cd_empresa EMP ON APL.id_aplicacao = EMP.id_aplicacao
        -- CORREÇÃO AQUI - Usar LEFT JOIN com info_empresa ao invés de usar nome_fantasia diretamente
        LEFT JOIN info_empresa IE ON IE.id_empresa = EMP.id_empresa AND IE.id_aplicacao = EMP.id_aplicacao
        INNER JOIN erp.fn_titulo TIT ON EMP.id_empresa = TIT.id_empresa
        INNER JOIN erp.cd_pessoa FORN ON TIT.id_pessoa = FORN.id_pessoa
        INNER JOIN erp.fn_titulo_rateio_despesa RDE ON TIT.id_titulo = RDE.id_titulo
        INNER JOIN erp.fn_plano_conta FPC ON RDE.id_conta = FPC.id_conta
        INNER JOIN erp.fn_banco_conta FBC ON TIT.id_banco_conta = FBC.id_banco_conta
        LEFT JOIN titulo_baixa_count TBC ON TIT.id_titulo = TBC.id_titulo
        LEFT JOIN titulo_baixa_info TBI ON TIT.id_titulo = TBI.id_titulo
        LEFT JOIN titulo_recorrencia TR ON TIT.id_titulo_referencia = TR.id_titulo_referencia
        LEFT JOIN erp.fn_titulo_rateio_ccusto TAC ON TIT.id_titulo = TAC.id_titulo
        LEFT JOIN erp.fn_centro_custo CCU ON TAC.id_centro_custo = CCU.id_centro_custo
        LEFT JOIN (
        SELECT id_titulo, MAX(dt_historico) as max_dt_historico
        FROM erp.fn_titulo_hist
        GROUP BY id_titulo
      ) HIS_MAX ON TIT.id_titulo = HIS_MAX.id_titulo
        LEFT JOIN erp.fn_titulo_hist HIS
                  ON HIS_MAX.id_titulo = HIS.id_titulo
                    AND HIS_MAX.max_dt_historico = HIS.dt_historico
        LEFT JOIN erp.sg_usuario USU ON HIS.id_usuario = USU.id_usuario
        LEFT JOIN erp.cd_forma_pagamento FPG ON TIT.id_forma_pagamento = FPG.id_forma_pagamento
    WHERE
      APL.situacao = 'ATIVO'
      AND EMP.situacao = 'ATIVO'
      AND TIT.excluido = false
      AND (p_tipo_titulo IS NULL OR TIT.tp_titulo = p_tipo_titulo)
      AND (
      -- Incluir títulos dentro do período padrão (da data atual até 6 meses no futuro)
      (
        (TIT.dt_vencimento >= (SELECT data_base FROM data_filtro))
          AND
        (TIT.dt_vencimento <= (SELECT data_fim FROM data_filtro))
        )
        OR
        -- Incluir títulos com situação ABERTO independentemente da data de vencimento
      (TIT.situacao IN ('ABERTO', 'A_VENCER', 'VENCE_HOJE', 'VENCIDA'))
        OR
        -- Incluir títulos que não possuem baixas associadas (independente da data)
      (TIT.id_titulo NOT IN (SELECT id_titulo FROM erp.fn_titulo_baixa))
      )
      AND APL.id_aplicacao = p_id_aplicacao

    --Adicionar as ordens de serviço recorrentes não faturadas
    UNION ALL
    SELECT * FROM ordens_servico_recorrentes_nao_faturadas

    -- Adicionar as ordens de serviço não recorrentes no período
    UNION ALL
    SELECT * FROM ordens_servico_periodo

    -- Adicionar as ordens de serviço recorrentes faturadas
    UNION ALL
    SELECT * FROM ordens_servico_recorrentes

    ORDER BY
      dataVencimento ASC,
      empresaId,
      valorTotal DESC;
END;
$$ LANGUAGE plpgsql;