-- Adicionar campos de auditoria (created_at e updated_at) nas tabelas de ordem de serviço

-- Tabela ordem_servico (já possui data_criacao e data_alteracao, mapear para created_at e updated_at)
ALTER TABLE "erp-nfs".ordem_servico
ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- <PERSON><PERSON><PERSON><PERSON> created_at com data_criacao e updated_at com data_alteracao
UPDATE "erp-nfs".ordem_servico
SET created_at = data_criacao,
    updated_at = data_alteracao;

-- Tabela ordem_servico_item
ALTER TABLE "erp-nfs".ordem_servico_item
ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- Tabela ordem_servico_parcela
ALTER TABLE "erp-nfs".ordem_servico_parcela
ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- Tabela ordem_servico_nota_fiscal (já possui data_emissao, usar como base para created_at)
ALTER TABLE "erp-nfs".ordem_servico_nota_fiscal
ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- Inicializar created_at com data_emissao quando disponível
UPDATE "erp-nfs".ordem_servico_nota_fiscal
SET created_at = COALESCE(data_emissao, CURRENT_TIMESTAMP),
    updated_at = COALESCE(data_emissao, CURRENT_TIMESTAMP);

-- Tabela ordem_servico_nota_fiscal_item
ALTER TABLE "erp-nfs".ordem_servico_nota_fiscal_item
ADD COLUMN created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL;

-- Criar triggers para atualizar updated_at em cada tabela usando função existente

-- Trigger para ordem_servico
CREATE TRIGGER trg_atualizar_updated_at_ordem_servico
    BEFORE UPDATE ON "erp-nfs".ordem_servico
    FOR EACH ROW
    EXECUTE FUNCTION atualiza_updated_at();

-- Trigger para ordem_servico_item
CREATE TRIGGER trg_atualizar_updated_at_ordem_servico_item
    BEFORE UPDATE ON "erp-nfs".ordem_servico_item
    FOR EACH ROW
    EXECUTE FUNCTION atualiza_updated_at();

-- Trigger para ordem_servico_parcela
CREATE TRIGGER trg_atualizar_updated_at_ordem_servico_parcela
    BEFORE UPDATE ON "erp-nfs".ordem_servico_parcela
    FOR EACH ROW
    EXECUTE FUNCTION atualiza_updated_at();

-- Trigger para ordem_servico_nota_fiscal
CREATE TRIGGER trg_atualizar_updated_at_ordem_servico_nota_fiscal
    BEFORE UPDATE ON "erp-nfs".ordem_servico_nota_fiscal
    FOR EACH ROW
    EXECUTE FUNCTION atualiza_updated_at();

-- Trigger para ordem_servico_nota_fiscal_item
CREATE TRIGGER trg_atualizar_updated_at_ordem_servico_nota_fiscal_item
    BEFORE UPDATE ON "erp-nfs".ordem_servico_nota_fiscal_item
    FOR EACH ROW
    EXECUTE FUNCTION atualiza_updated_at();