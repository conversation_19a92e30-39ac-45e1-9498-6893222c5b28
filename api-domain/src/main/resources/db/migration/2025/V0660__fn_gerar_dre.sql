-- Remove any existing functions with the same name
DROP FUNCTION IF EXISTS erp.fn_gerar_dre CASCADE;

-- Create the function for DRE report with single integer empresa parameter
CREATE OR REPLACE FUNCTION erp.fn_gerar_dre(
  p_data_inicial DATE,
  p_data_final DATE,
  p_id_empresa INTEGER,
  p_tipo_pesquisa_data_relatorio VARCHAR DEFAULT 'DATA_EMISSAO'
) 
RETURNS TABLE (
  empresa_id INTEGER,
  empresa_nome VARCHAR,
  ordem INTEGER,
  tipo VARCHAR,
  conta_do_dre_id INTEGER,
  conta_do_dre_nome VARCHAR,
  categoria_produto VARCHAR,
  data_movimento DATE,
  centro_custo_id INTEGER,
  centro_custo_nome TEXT,
  parceiro_id INTEGER,
  parceiro_nome VARCHAR,
  valor NUMERIC(19,2)
) AS $$
DECLARE
  v_cat_produto VARCHAR := '-';
BEGIN
  RETURN QUERY
  WITH emp_dados AS (
    SELECT 
      emp.id_empresa, 
      emp.nome,
      emp.id_aplicacao
    FROM 
      erp.cd_empresa emp
    WHERE 
      emp.id_empresa = p_id_empresa
  ),
  
  dre_cfg AS (
    SELECT 
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      d.id_dre,
      d.nome,
      d.tp_tipo,
      CASE
        WHEN d.nome ILIKE 'Receita com Vendas' THEN 1
        WHEN d.nome ILIKE 'Deduções e abatimentos' THEN 2
        WHEN d.nome ILIKE 'Custo da Mercadoria' THEN 4
        WHEN d.nome ILIKE 'Despesas Administrativas' THEN 6
        WHEN d.nome ILIKE 'Despesas de Vendas' THEN 7
        WHEN d.nome ILIKE 'Receitas Financeiras' THEN 9
        WHEN d.nome ILIKE 'Despesas Financeiras' THEN 10
        WHEN d.nome ILIKE 'Outras Receitas não Operacionais' THEN 11
        WHEN d.nome ILIKE 'Outras Despesas não Operacionais' THEN 12
        ELSE 14
      END AS ordem_dre
    FROM 
      erp.fn_dre d
      JOIN emp_dados e ON d.id_aplicacao = e.id_aplicacao
  ),
  
  titulos_base AS (
    SELECT 
      t.id_aplicacao,
      t.id_empresa,
      DATE_TRUNC('day',
        CASE
          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN tb.dt_baixa
          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_VENCIMENTO' THEN t.dt_vencimento
          ELSE t.dt_emissao
        END
      ) AS data_reg,
      pc.id_dre,
      pc.id_conta,
      pc.nome AS nome_conta,
      COALESCE(cc.id_centro_custo, 0) AS cc_id,
      COALESCE(cc.nome, '-') AS cc_nome,
      COALESCE(trc.perc_rateio, 1) AS cc_rateio,
      p.id_pessoa,
      p.nome AS pessoa_nome,
      (SUM(COALESCE(tb.vl_juros, 0)) * trd.perc_rateio) AS juros_vl,
      (SUM(COALESCE(tb.vl_multa, 0)) * trd.perc_rateio) AS multa_vl,
      (SUM(COALESCE(tb.vl_desconto, 0)) * trd.perc_rateio) AS desconto_vl,
      (SUM(COALESCE(tb.vl_abatimento, 0)) * trd.perc_rateio) AS abatimento_vl,
      (
        CASE
          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN SUM(COALESCE(tb.vl_baixa, 0))
          ELSE MIN(COALESCE(t.valor_liquido, 0))
        END
      ) * trd.perc_rateio AS total_vl
    FROM 
      erp.fn_titulo t
      JOIN erp.fn_titulo_rateio_despesa trd ON trd.id_titulo = t.id_titulo AND t.excluido IS FALSE
      LEFT JOIN erp.fn_plano_conta pc ON trd.id_conta = pc.id_conta
      JOIN emp_dados e ON t.id_aplicacao = e.id_aplicacao AND t.id_empresa = e.id_empresa
      JOIN erp.cd_pessoa p ON t.id_pessoa = p.id_pessoa
      LEFT JOIN erp.fn_titulo_baixa tb ON tb.id_titulo = t.id_titulo AND t.excluido IS FALSE
      LEFT JOIN erp.fn_titulo_rateio_ccusto trc ON trc.id_titulo = t.id_titulo AND t.excluido IS FALSE
      LEFT JOIN erp.fn_centro_custo cc ON trc.id_centro_custo = cc.id_centro_custo
    WHERE 
      (
        CASE
          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_BAIXA' THEN 
            DATE_TRUNC('day', tb.dt_baixa) BETWEEN p_data_inicial AND p_data_final
          WHEN p_tipo_pesquisa_data_relatorio = 'DATA_VENCIMENTO' THEN 
            DATE_TRUNC('day', t.dt_vencimento) BETWEEN p_data_inicial AND p_data_final
          ELSE 
            DATE_TRUNC('day', t.dt_emissao) BETWEEN p_data_inicial AND p_data_final
        END
      )
      AND pc.id_dre IS NOT NULL
    GROUP BY 
      t.id_aplicacao, t.id_empresa, trd.id_titulo_rateio_despesa, 
      data_reg, pc.id_dre, pc.id_conta, pc.nome, 
      cc.id_centro_custo, cc.nome, trc.perc_rateio, p.id_pessoa, p.nome
  ),
  
  contas_vl AS (
    SELECT 
      e.id_aplicacao,
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      d.tp_tipo AS dre_tipo,
      t.data_reg,
      t.id_conta,
      t.nome_conta,
      t.cc_id,
      t.cc_nome,
      t.id_pessoa,
      t.pessoa_nome,
      SUM(COALESCE(t.multa_vl, 0) * t.cc_rateio) AS multa_vl,
      SUM(COALESCE(t.juros_vl, 0) * t.cc_rateio) AS juros_vl,
      SUM(COALESCE(t.desconto_vl, 0) * t.cc_rateio) AS desconto_vl,
      SUM(COALESCE(t.abatimento_vl, 0) * t.cc_rateio) AS abatimento_vl,
      SUM(COALESCE(t.total_vl, 0) * t.cc_rateio) AS vl_final
    FROM 
      erp.fn_dre d
      JOIN titulos_base t ON t.id_dre = d.id_dre
      JOIN emp_dados e ON e.id_aplicacao = d.id_aplicacao AND e.id_empresa = t.id_empresa
    WHERE 
      t.total_vl <> 0
    GROUP BY 
      e.id_aplicacao, e.id_empresa, e.nome, d.id_dre, d.nome, d.tp_tipo,
      t.data_reg, t.id_conta, t.nome_conta, t.cc_id, t.cc_nome,
      t.id_pessoa, t.pessoa_nome
  ),
  
  juros_dados AS (
    SELECT 
      cv.emp_id,
      cv.emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      cv.dre_tipo,
      cv.data_reg,
      0 AS id_conta,
      CASE
        WHEN cv.dre_tipo = 'DESPESA' THEN 'Juros Pagos*'
        ELSE 'Juros Recebidos*'
      END AS nome_conta,
      cv.cc_id,
      cv.cc_nome,
      cv.id_pessoa,
      cv.pessoa_nome,
      SUM(COALESCE(cv.juros_vl, 0)) AS vl_final
    FROM 
      contas_vl cv
      JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
    WHERE 
      d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
      AND cv.juros_vl IS NOT NULL
      AND cv.juros_vl <> 0
    GROUP BY 
      cv.emp_id, cv.emp_nome, d.id_dre, d.nome, cv.dre_tipo,
      cv.data_reg, cv.cc_id, cv.cc_nome,
      cv.id_pessoa, cv.pessoa_nome
  ),
  
  multas_dados AS (
    SELECT 
      cv.emp_id,
      cv.emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      cv.dre_tipo,
      cv.data_reg,
      0 AS id_conta,
      CASE
        WHEN cv.dre_tipo = 'DESPESA' THEN 'Multas Pagas*'
        ELSE 'Multas Recebidos*'
      END AS nome_conta,
      cv.cc_id,
      cv.cc_nome,
      cv.id_pessoa,
      cv.pessoa_nome,
      SUM(COALESCE(cv.multa_vl, 0)) AS vl_final
    FROM 
      contas_vl cv
      JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
    WHERE 
      d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
      AND cv.multa_vl IS NOT NULL
      AND cv.multa_vl <> 0
    GROUP BY 
      cv.emp_id, cv.emp_nome, d.id_dre, d.nome, cv.dre_tipo,
      cv.data_reg, cv.cc_id, cv.cc_nome,
      cv.id_pessoa, cv.pessoa_nome
  ),
  
  descontos_dados AS (
    SELECT 
      cv.emp_id,
      cv.emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      d.tp_tipo AS dre_tipo,
      cv.data_reg,
      0 AS id_conta,
      CASE
        WHEN d.tp_tipo = 'DESPESA' THEN 'Desconto Concedidos*'
        ELSE 'Desconto Obtido*'
      END AS nome_conta,
      cv.cc_id,
      cv.cc_nome,
      cv.id_pessoa,
      cv.pessoa_nome,
      SUM(COALESCE(cv.desconto_vl, 0)) AS vl_final
    FROM 
      contas_vl cv
      JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
    WHERE 
      d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
      AND cv.desconto_vl IS NOT NULL
      AND cv.desconto_vl <> 0
      AND d.tp_tipo != cv.dre_tipo
    GROUP BY 
      cv.emp_id, cv.emp_nome, d.id_dre, d.nome, d.tp_tipo,
      cv.data_reg, cv.cc_id, cv.cc_nome,
      cv.id_pessoa, cv.pessoa_nome
  ),
  
  abatimentos_dados AS (
    SELECT 
      cv.emp_id,
      cv.emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      d.tp_tipo AS dre_tipo,
      cv.data_reg,
      0 AS id_conta,
      CASE
        WHEN d.tp_tipo = 'DESPESA' THEN 'Abatimento Concedidos*'
        ELSE 'Abatimento Obtido*'
      END AS nome_conta,
      cv.cc_id,
      cv.cc_nome,
      cv.id_pessoa,
      cv.pessoa_nome,
      SUM(COALESCE(cv.abatimento_vl, 0)) AS vl_final
    FROM 
      contas_vl cv
      JOIN dre_cfg d ON d.id_dre = cv.dre_id AND d.emp_id = cv.emp_id
    WHERE 
      d.nome IN ('Receitas Financeiras', 'Despesas Financeiras')
      AND cv.abatimento_vl IS NOT NULL
      AND cv.abatimento_vl <> 0
      AND d.tp_tipo != cv.dre_tipo
    GROUP BY 
      cv.emp_id, cv.emp_nome, d.id_dre, d.nome, d.tp_tipo,
      cv.data_reg, cv.cc_id, cv.cc_nome,
      cv.id_pessoa, cv.pessoa_nome
  ),
  
  entradas_manuais AS (
    SELECT 
      e.id_aplicacao,
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      d.id_dre AS dre_id,
      d.nome AS dre_nome,
      d.tp_tipo AS dre_tipo,
      DATE_TRUNC('day', m.dt_movto) AS data_reg,
      c.id_conta,
      c.nome AS nome_conta,
      0 AS cc_id,
      '-' AS cc_nome,
      0 AS id_pessoa,
      '-' AS pessoa_nome,
      ABS(m.vl_movto) AS vl_final
    FROM 
      erp.fn_movto_banco m
      JOIN erp.fn_transacao t ON m.id_transacao = t.id_transacao
      JOIN emp_dados e ON m.id_aplicacao = e.id_aplicacao
      JOIN erp.fn_plano_conta c ON m.id_conta = c.id_conta
      JOIN dre_cfg d ON d.id_dre = c.id_dre AND d.emp_id = e.id_empresa
    WHERE 
      t.tp_transacao = 6
      AND DATE_TRUNC('day', m.dt_movto) BETWEEN p_data_inicial AND p_data_final
  ),
  
  dados_combinados AS (
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM 
      contas_vl
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM juros_dados
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM multas_dados
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM descontos_dados
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM abatimentos_dados
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      dre_id,
      dre_nome,
      dre_tipo,
      data_reg,
      id_conta,
      nome_conta AS nome_categoria,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM entradas_manuais
  ),
  
  receita_liquida AS (
    SELECT 
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      3 AS ordem_item,
      '-' AS dre_tipo,
      0 AS dre_id,
      '(=) Receita Líquida' AS dre_nome,
      v_cat_produto AS nome_categoria,
      dc.data_reg,
      dc.cc_id,
      dc.cc_nome,
      dc.id_pessoa,
      dc.pessoa_nome,
      (
        COALESCE(
          (
            SELECT SUM(dc1.vl_final)
            FROM dados_combinados dc1
            WHERE 
              dc1.dre_nome = 'Receita com Vendas'
              AND dc1.emp_id = e.id_empresa
              AND dc1.data_reg = dc.data_reg
              AND dc1.cc_id = dc.cc_id
              AND dc1.id_pessoa = dc.id_pessoa
          ), 
          0
        ) -
        COALESCE(
          (
            SELECT SUM(dc2.vl_final)
            FROM dados_combinados dc2
            WHERE 
              dc2.dre_nome = 'Deduções e abatimentos'
              AND dc2.emp_id = e.id_empresa
              AND dc2.data_reg = dc.data_reg
              AND dc2.cc_id = dc.cc_id
              AND dc2.id_pessoa = dc.id_pessoa
          ), 
          0
        )
      ) AS vl_final
    FROM 
      emp_dados e
      LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa
    GROUP BY 
      e.id_empresa, e.nome, dc.data_reg, dc.cc_id, 
      dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
  ),
  
  lucro_bruto AS (
    SELECT 
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      5 AS ordem_item,
      '-' AS dre_tipo,
      0 AS dre_id,
      '(=) Lucro Bruto' AS dre_nome,
      v_cat_produto AS nome_categoria,
      dc.data_reg,
      dc.cc_id,
      dc.cc_nome,
      dc.id_pessoa,
      dc.pessoa_nome,
      (
        COALESCE(
          (
            SELECT rl.vl_final
            FROM receita_liquida rl
            WHERE 
              rl.emp_id = e.id_empresa
              AND rl.data_reg = dc.data_reg
              AND rl.cc_id = dc.cc_id
              AND rl.id_pessoa = dc.id_pessoa
          ), 
          0
        ) -
        COALESCE(
          (
            SELECT SUM(dc1.vl_final)
            FROM dados_combinados dc1
            WHERE 
              dc1.dre_nome = 'Custo da Mercadoria'
              AND dc1.emp_id = e.id_empresa
              AND dc1.data_reg = dc.data_reg
              AND dc1.cc_id = dc.cc_id
              AND dc1.id_pessoa = dc.id_pessoa
          ), 
          0
        )
      ) AS vl_final
    FROM 
      emp_dados e
      LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa
    GROUP BY 
      e.id_empresa, e.nome, dc.data_reg, dc.cc_id, 
      dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
  ),
  
  lucro_operacional AS (
    SELECT 
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      8 AS ordem_item,
      '-' AS dre_tipo,
      0 AS dre_id,
      '(=) Lucro/Prejuízo Operacional' AS dre_nome,
      v_cat_produto AS nome_categoria,
      dc.data_reg,
      dc.cc_id,
      dc.cc_nome,
      dc.id_pessoa,
      dc.pessoa_nome,
      (
        COALESCE(
          (
            SELECT lb.vl_final
            FROM lucro_bruto lb
            WHERE 
              lb.emp_id = e.id_empresa
              AND lb.data_reg = dc.data_reg
              AND lb.cc_id = dc.cc_id
              AND lb.id_pessoa = dc.id_pessoa
          ), 
          0
        ) -
        COALESCE(
          (
            SELECT SUM(dc1.vl_final)
            FROM dados_combinados dc1
            WHERE 
              dc1.dre_nome IN ('Despesas Administrativas', 'Despesas de Vendas')
              AND dc1.emp_id = e.id_empresa
              AND dc1.data_reg = dc.data_reg
              AND dc1.cc_id = dc.cc_id
              AND dc1.id_pessoa = dc.id_pessoa
          ), 
          0
        )
      ) AS vl_final
    FROM 
      emp_dados e
      LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa
    GROUP BY 
      e.id_empresa, e.nome, dc.data_reg, dc.cc_id, 
      dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
  ),
  
  lucro_liquido AS (
    SELECT 
      e.id_empresa AS emp_id,
      e.nome AS emp_nome,
      13 AS ordem_item,
      '-' AS dre_tipo,
      0 AS dre_id,
      '(=) Lucro/Prejuízo Líquido' AS dre_nome,
      v_cat_produto AS nome_categoria,
      dc.data_reg,
      dc.cc_id,
      dc.cc_nome,
      dc.id_pessoa,
      dc.pessoa_nome,
      (
        COALESCE(
          (
            SELECT lo.vl_final
            FROM lucro_operacional lo
            WHERE 
              lo.emp_id = e.id_empresa
              AND lo.data_reg = dc.data_reg
              AND lo.cc_id = dc.cc_id
              AND lo.id_pessoa = dc.id_pessoa
          ), 
          0
        ) -
        COALESCE(
          (
            SELECT SUM(dc1.vl_final)
            FROM dados_combinados dc1
            WHERE 
              dc1.dre_nome IN ('Despesas Financeiras', 'Outras Despesas não Operacionais')
              AND dc1.emp_id = e.id_empresa
              AND dc1.data_reg = dc.data_reg
              AND dc1.cc_id = dc.cc_id
              AND dc1.id_pessoa = dc.id_pessoa
          ), 
          0
        ) +
        COALESCE(
          (
            SELECT SUM(dc2.vl_final)
            FROM dados_combinados dc2
            WHERE 
              dc2.dre_nome IN ('Receitas Financeiras', 'Outras Receitas não Operacionais')
              AND dc2.emp_id = e.id_empresa
              AND dc2.data_reg = dc.data_reg
              AND dc2.cc_id = dc.cc_id
              AND dc2.id_pessoa = dc.id_pessoa
          ), 
          0
        )
      ) AS vl_final
    FROM 
      emp_dados e
      LEFT JOIN dados_combinados dc ON dc.emp_id = e.id_empresa
    GROUP BY 
      e.id_empresa, e.nome, dc.data_reg, dc.cc_id, 
      dc.cc_nome, dc.id_pessoa, dc.pessoa_nome
  ),
  
  resultado_final AS (
    SELECT
      dc.emp_id,
      dc.emp_nome,
      COALESCE(
        (
          SELECT d.ordem_dre
          FROM dre_cfg d
          WHERE d.id_dre = dc.dre_id AND d.emp_id = dc.emp_id
        ), 99
      ) AS ordem_item,
      dc.dre_tipo,
      dc.dre_id,
      CASE
        WHEN dc.dre_tipo = 'DESPESA' THEN CONCAT('(-) ', dc.dre_nome)
        ELSE CONCAT('(+) ', dc.dre_nome)
      END AS dre_nome_final,
      dc.nome_categoria,
      dc.data_reg,
      dc.cc_id,
      dc.cc_nome,
      dc.id_pessoa,
      dc.pessoa_nome,
      dc.vl_final
    FROM 
      dados_combinados dc
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      ordem_item, 
      dre_tipo,
      dre_id,
      dre_nome,
      nome_categoria,
      data_reg,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM receita_liquida
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      ordem_item,
      dre_tipo,
      dre_id,
      dre_nome,
      nome_categoria,
      data_reg,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM lucro_bruto
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      ordem_item,
      dre_tipo,
      dre_id,
      dre_nome,
      nome_categoria,
      data_reg,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM lucro_operacional
    
    UNION ALL
    
    SELECT
      emp_id,
      emp_nome,
      ordem_item,
      dre_tipo,
      dre_id,
      dre_nome,
      nome_categoria,
      data_reg,
      cc_id,
      cc_nome,
      id_pessoa,
      pessoa_nome,
      vl_final
    FROM lucro_liquido
  )
  
  SELECT
    CAST(rf.emp_id AS INTEGER) AS empresa_id,
    CAST(rf.emp_nome AS VARCHAR) AS empresa_nome,
    CAST(COALESCE(rf.ordem_item, 99) AS INTEGER) AS ordem,
    CAST(rf.dre_tipo AS VARCHAR) AS tipo,
    CAST(rf.dre_id AS INTEGER) AS conta_do_dre_id,
    CAST(rf.dre_nome_final AS VARCHAR) AS conta_do_dre_nome,
    CAST(rf.nome_categoria AS VARCHAR) AS categoria_produto,
    CAST(rf.data_reg AS DATE) AS data_movimento,
    CAST(rf.cc_id AS INTEGER) AS centro_custo_id,
    CAST(rf.cc_nome AS TEXT) AS centro_custo_nome,
    CAST(rf.id_pessoa AS INTEGER) AS parceiro_id,
    CAST(rf.pessoa_nome AS VARCHAR) AS parceiro_nome,
    CAST(rf.vl_final AS NUMERIC(19,2)) AS valor
  FROM 
    resultado_final rf
  ORDER BY 
    rf.emp_id, 
    rf.ordem_item, 
    rf.data_reg;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION erp.fn_gerar_dre(DATE, DATE, INTEGER, VARCHAR) IS 'Function to generate DRE (Demonstração do Resultado do Exercício) financial report data with a single company ID';