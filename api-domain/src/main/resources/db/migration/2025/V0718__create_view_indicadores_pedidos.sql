-- View para consolidar dados dos pedidos de venda com itens e rateios
DROP VIEW IF EXISTS erp.vw_indicadores_pedidos;

CREATE VIEW erp.vw_indicadores_pedidos AS
WITH rateio_centro_custo AS (
    -- CTE para calcular rateios por centro de custo
    SELECT
        p.id_pedido,
        p.id_aplicacao,
        rcc.id_centro_custo,
        rcc.perc_rateio AS perc_rateio_ccusto,
        cc.nome AS nome_centro_custo,
        cc.descricao AS descricao_centro_custo
    FROM erp.vd_pedido p
    LEFT JOIN erp.vd_pedido_centro_custo rcc ON p.id_pedido = rcc.id_pedido
    LEFT JOIN erp.fn_centro_custo cc ON rcc.id_centro_custo = cc.id_centro_custo
    WHERE p.excluido = false
),
pedido_itens_agregados AS (
    -- CTE para agregar dados dos itens do pedido
    SELECT
        pi.id_pedido,
        COUNT(*) AS qtd_itens,
        SUM(pi.quantidade) AS qtd_total_produtos,
        SUM(pi.vl_total) AS vl_total_itens,
        SUM(pi.vl_desconto) AS vl_total_desconto_itens,
        SUM(pi.vl_icms) AS vl_total_icms_itens,
        SUM(pi.vl_icms_st) AS vl_total_icms_st_itens,
        SUM(pi.vl_ipi) AS vl_total_ipi_itens,
        SUM(pi.vl_pis) AS vl_total_pis_itens,
        SUM(pi.vl_cofins) AS vl_total_cofins_itens,
        SUM(pi.vl_frete) AS vl_total_frete_itens,
        SUM(pi.vl_seguro) AS vl_total_seguro_itens,
        SUM(pi.vl_outras_despesas) AS vl_total_outras_despesas_itens,
        SUM(pi.vl_custo_medio * pi.quantidade) AS vl_total_custo_itens
    FROM erp.vd_pedido_item pi
    GROUP BY pi.id_pedido
),
pedido_pagamentos AS (
    -- CTE para agrupar formas de pagamento do pedido
    SELECT
        pp.id_pedido,
        COUNT(*) AS qtd_formas_pagamento,
        SUM(pp.vl_total_parcela) AS vl_total_pagamentos,
        STRING_AGG(DISTINCT fp.nome, ', ') AS formas_pagamento
    FROM erp.vd_pedido_pagto pp
    LEFT JOIN erp.cd_forma_pagamento fp ON pp.id_forma_pagamento = fp.id_forma_pagamento
    GROUP BY pp.id_pedido
)

SELECT
    -- Dados principais do pedido
    p.id_pedido,
    p.id_aplicacao,
    p.id_empresa,
    p.id_pedido_pai,
    p.codigo_tela,
    p.situacao,
    p.tp_pedido,
    p.tp_operacao,
    p.tp_ambiente,
    p.tp_comprovante,
    p.ind_consumidor_final,
    p.ind_gera_cr,
    p.ind_baixa_estoque,
    p.ind_endereco_unico,
    p.ind_peso_manual,
    p.excluido,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,

    -- Chaves estrangeiras
    p.id_cliente,
    p.id_vendedor,
    p.id_nop,
    p.id_conta,
    p.id_banco_conta,
    p.id_cond_pagamento,
    p.id_forma_pagamento,
    p.id_canal_venda,
    p.id_transportadora,
    p.id_tipo_frete,
    p.id_tipo_veiculo,
    p.id_tipo_carroceria,
    p.id_ordem_servico_conjugado,
    p.usuario,

    -- Datas importantes
    p.dt_pedido,
    p.dt_faturamento,
    p.dt_entrega_prevista,
    p.dt_nfe_emitida,

    -- Valores principais do pedido
    p.vl_total_produtos,
    p.vl_desconto,
    p.vl_nota_fiscal,
    p.vl_outras_despesas,
    p.vl_total_outras_despesas,
    p.vl_frete,
    p.vl_seguro_frete,
    p.vl_outras_despesas_frete,

    -- Valores de impostos
    p.vl_icms,
    p.vl_icms_st,
    p.vl_ipi,
    p.vl_pis,
    p.vl_cofins,
    p.vl_fcp,
    p.vl_fcp_st,
    p.vl_desoneracao_icms,
    p.vl_fcp_icms_difal,
    p.vl_icms_difal,
    p.vl_ipi_devolvido,

    -- Bases de cálculo
    p.vl_bc_icms,
    p.vl_bc_icms_st,
    p.vl_bc_ipi,
    p.vl_bc_pis,
    p.vl_bc_cofins,
    p.vl_bc_credito_icms_sn,

    -- Percentuais e configurações
    p.perc_desconto,
    p.perc_credito_icms_sn,
    p.vl_credito_icms_sn,
    p.aliquota_funrural,

    -- Dados da nota fiscal
    p.nr_nota_fiscal,
    p.serie_nota_fiscal,
    p.chave_notafiscal,
    p.chave_notafiscal_referenciada,
    p.xml_nota_fiscal,
    p.xml_nota_fiscal_inutilizacao,

    -- Dados de transporte
    p.veiculo_placa,
    p.veiculo_uf,
    p.veiculo_rntcr,
    p.veiculo_ciot,
    p.veiculo_tara,
    p.veiculo_capacidade,
    p.condutor_nome,
    p.condutor_cpf,
    p.nome_transportadora,

    -- Dados de entrega
    p.uf_carregamento,
    p.cidade_carregamento,
    p.uf_descarregamento,
    p.cidade_descarregamento,
    p.qtd_volume,
    p.tp_volume,
    p.peso_liquido,
    p.peso_bruto,
    p.cod_rastreio,
    p.url_rastreio,

    -- Dados de contato
    p.email_cobranca,
    p.email_nf,

    -- Dados de marketplace/integração
    p.orderid,
    p.integrationorderid,
    p.deliverylogistictype,
    p.tipo_logistica_marketplace,

    -- Observações e motivos
    p.obs_pedido,
    p.obs_nota_fiscal,
    p.motivo_cancelamento,
    p.motivo_devolucao,

    -- Dados de intermediação
    p.indicador_presenca,
    p.tp_intermediador,
    p.nome_intermediador,
    p.cnpj_intermediador,
    p.tp_transporte,
    -- Dados agregados dos itens
    pia.qtd_itens,
    pia.qtd_total_produtos,
    pia.vl_total_itens,
    pia.vl_total_desconto_itens,
    pia.vl_total_icms_itens,
    pia.vl_total_icms_st_itens,
    pia.vl_total_ipi_itens,
    pia.vl_total_pis_itens,
    pia.vl_total_cofins_itens,
    pia.vl_total_frete_itens,
    pia.vl_total_seguro_itens,
    pia.vl_total_outras_despesas_itens,
    pia.vl_total_custo_itens,

    -- Margem bruta calculada
    CASE
        WHEN pia.vl_total_custo_itens > 0 AND pia.vl_total_itens > 0 THEN
            ((pia.vl_total_itens - pia.vl_total_custo_itens) / pia.vl_total_itens) * 100
    END AS perc_margem_bruta,

    -- Dados de pagamento agregados
    pp.qtd_formas_pagamento,
    pp.vl_total_pagamentos,
    pp.formas_pagamento,

    -- Dados de rateio por centro de custo
    rcc.id_centro_custo,
    rcc.perc_rateio_ccusto,
    rcc.nome_centro_custo,
    rcc.descricao_centro_custo,

    -- Valores rateados do pedido
    p.vl_total_produtos * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_total_produtos_rateio,
    p.vl_desconto * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_desconto_rateio,
    p.vl_nota_fiscal * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_nota_fiscal_rateio,
    p.vl_icms * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_icms_rateio,
    p.vl_ipi * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_ipi_rateio,
    p.vl_pis * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_pis_rateio,
    p.vl_cofins * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_cofins_rateio,
    pia.vl_total_custo_itens * COALESCE(rcc.perc_rateio_ccusto, 1.0) AS vl_total_custo_itens_rateio,

    -- Dados do cliente
    cli.nome AS nome_cliente,
    cli.cpf_cnpj AS cpf_cnpj_cliente,
    cli.email AS email_cliente,
    cli.tp_pessoa AS tp_pessoa_cliente,

    -- Dados do vendedor
    vend.nome AS nome_vendedor,
    vend.cpf_cnpj AS cpf_cnpj_vendedor,
    vend.email AS email_vendedor,

    -- Dados da transportadora
    transp.nome AS nome_transportadora_pessoa,
    transp.cpf_cnpj AS cpf_cnpj_transportadora,

    -- Dados da aplicação
    a.nome AS nome_aplicacao,
    a.situacao AS situacao_aplicacao,
    a.origem AS origem_aplicacao,

    -- Dados da empresa
    e.nome AS nome_empresa,
    e.cpf_cnpj AS cpf_cnpj_empresa,
    e.situacao AS situacao_empresa,

    -- Dados do banco conta
    bc.nome AS nome_banco_conta,
    bc.agencia AS agencia_banco_conta,
    bc.numero_conta,
    b.nome AS nome_banco,
    b.cod_banco,

    -- Dados da condição de pagamento
    cp.nome AS nome_cond_pagamento,
    cp.qtd_parcelas AS qtd_parcelas_cond_pagamento,

    -- Dados da forma de pagamento
    fp.nome AS nome_forma_pagamento,

    -- Dados do canal de venda
    cv.nome AS nome_canal_venda,

    -- Dados da NOP
    nop.descricao AS descricao_nop,

    -- Dados do plano de conta
    pc.nome AS nome_plano_conta,
    pc.tp_conta AS tipo_plano_conta,

    -- Dados do tipo de frete
    tf.nm_tipo_frete AS nome_tipo_frete,
    tf.compoe_valor_icms AS frete_compoe_valor_icms

FROM erp.vd_pedido p
    -- JOINs com CTEs
    LEFT JOIN rateio_centro_custo rcc ON p.id_pedido = rcc.id_pedido
    LEFT JOIN pedido_itens_agregados pia ON p.id_pedido = pia.id_pedido
    LEFT JOIN pedido_pagamentos pp ON p.id_pedido = pp.id_pedido

    -- JOINs com entidades relacionadas
    INNER JOIN erp.ad_aplicacao a ON p.id_aplicacao = a.id_aplicacao
    INNER JOIN erp.cd_empresa e ON p.id_empresa = e.id_empresa
    LEFT JOIN erp.cd_pessoa cli ON p.id_cliente = cli.id_pessoa
    LEFT JOIN erp.cd_pessoa vend ON p.id_vendedor = vend.id_pessoa
    LEFT JOIN erp.cd_pessoa transp ON p.id_transportadora = transp.id_pessoa
    LEFT JOIN erp.fn_banco_conta bc ON p.id_banco_conta = bc.id_banco_conta
    LEFT JOIN erp.fn_banco b ON bc.id_banco = b.id_banco
    LEFT JOIN erp.cd_cond_pagamento cp ON p.id_cond_pagamento = cp.id_cond_pagamento
    LEFT JOIN erp.cd_forma_pagamento fp ON p.id_forma_pagamento = fp.id_forma_pagamento
    LEFT JOIN erp.vd_canal_venda cv ON p.id_canal_venda = cv.id_canal
    LEFT JOIN erp.cd_nop nop ON p.id_nop = nop.id_nop
    LEFT JOIN erp.fn_plano_conta pc ON p.id_conta = pc.id_conta
    LEFT JOIN erp.cd_tipo_frete tf ON p.id_tipo_frete = tf.id_tipo_frete

WHERE p.excluido = false
  AND a.situacao = 'ATIVO'
  AND e.situacao = 'ATIVO'

ORDER BY p.dt_pedido DESC, p.id_pedido;

-- Comentário da view
COMMENT ON VIEW erp.vw_indicadores_pedidos IS 'View consolidada com dados dos pedidos de venda incluindo itens agregados, rateios por centro de custo, valores rateados e dados das entidades relacionadas para indicadores e relatórios';