create or replace function revoga_comissao(p_id_venda integer, p_id_item integer) returns void
  language plpgsql
as
$$
DECLARE
  venda RECORD;
  item_record erp.cd_pos_pdv_venda_item%ROWTYPE; -- Record para armazenar o resultado
  cur_itens REFCURSOR; -- Cursor para armazenar a consulta
  _valor_calculado numeric(21,10);
BEGIN
  -- comissão padrão para testes caso não tenha nada configurado
  SELECT *
  INTO venda
  FROM erp.cd_pos_pdv_venda
  where id = p_id_venda;

  -- pegar todos os itens da venda
  IF p_id_item IS NULL THEN
    OPEN cur_itens FOR
      SELECT *
      FROM erp.cd_pos_pdv_venda_item pi
      WHERE pi.id_venda = p_id_venda;
  ELSE
    OPEN cur_itens FOR
      SELECT *
      FROM erp.cd_pos_pdv_venda_item pi
      WHERE pi.id_venda = p_id_venda
        AND pi.id = p_id_item;
  END IF;

  -- pegar todos os itens da venda
  LOOP
    FETCH cur_itens INTO item_record;
    EXIT WHEN NOT FOUND;
    BEGIN
      -- atualizar a comissão como excluída
      UPDATE erp.vd_comissao_vendedor
      SET excluido = true
      where id_aplicacao = venda.id_aplicacao
        and id_empresa = venda.id_empresa
        and id_vendedor = item_record.id_vendedor
        and id_produto = item_record.id_produto
        and id_pospdv_venda_item = item_record.id;

      -- atualizar a comissão mensal
      BEGIN
        SELECT valor_calculado
        INTO _valor_calculado
        FROM erp.vd_comissao_vendedor
        where id_aplicacao = venda.id_aplicacao
          and id_empresa = venda.id_empresa
          and id_vendedor = item_record.id_vendedor
          and id_produto = item_record.id_produto
          and id_pospdv_venda_item = item_record.id;

        -- if para pegar registros que foram removidos
        update erp.vd_comissao_vendedor_acumulado
        set valor_acumulado = valor_acumulado - _valor_calculado
        where id_aplicacao = venda.id_aplicacao
          and id_empresa = venda.id_empresa
          and id_vendedor = venda.id_vendedor
          and mes_acumulado = (DATE_TRUNC('month', CURRENT_DATE));
      END;
    END;
  END LOOP;
END;
$$;
alter function revoga_comissao(integer, integer) owner to postgres;

create or replace function gera_comissao(p_id_venda integer, p_id_item integer) returns void
  language plpgsql
as
$$
DECLARE
  venda RECORD;
  comissao_usuario numeric(21,10);
  item_record erp.cd_pos_pdv_venda_item%ROWTYPE; -- Record para armazenar o resultado
  cur_itens REFCURSOR; -- Cursor para armazenar a consulta
  _valor_old_valor_acumulado numeric(21,10);
  _valor_acumulado numeric(21,10);
  _comissao_calculada_item numeric(21,10);
  _novo_valor_acumulado_vendedor numeric(21,10);
BEGIN
  -- comissão padrão para testes caso não tenha nada configurado
  comissao_usuario := 0;
  _novo_valor_acumulado_vendedor := 0;
  _comissao_calculada_item := 0;
  _valor_acumulado := 0;

  SELECT *
  INTO venda
  FROM erp.cd_pos_pdv_venda
  where id = p_id_venda;

  IF p_id_item IS NULL THEN
    OPEN cur_itens FOR
      SELECT *
      FROM erp.cd_pos_pdv_venda_item pi
      WHERE pi.id_venda = p_id_venda;
  ELSE
    OPEN cur_itens FOR
      SELECT *
      FROM erp.cd_pos_pdv_venda_item pi
      WHERE pi.id_venda = p_id_venda
        AND pi.id = p_id_item;
  END IF;

  -- pegar todos os itens da venda
  LOOP
    FETCH cur_itens INTO item_record;
    EXIT WHEN NOT FOUND;
    BEGIN
      -- % comissão produto/vendedor/padrao
      SELECT vdc.comissao
      INTO comissao_usuario
      FROM erp.vd_comissao_produto vdc
      where vdc.id_aplicacao = venda.id_aplicacao
        and vdc.id_empresa = venda.id_empresa
        and vdc.id_produto = item_record.id_produto;

      IF NOT FOUND THEN
        comissao_usuario := 0;
        BEGIN
          -- % comissão do usuario
          SELECT vce.perc_comissao
          INTO comissao_usuario
          FROM erp.vd_comissao_excecao vce
          where vce.id_aplicacao = venda.id_aplicacao
            and vce.id_empresa = venda.id_empresa;

          IF NOT FOUND THEN
            comissao_usuario := 0;
            -- % comissão padrão
            BEGIN
              SELECT vc.perc_comissao
              INTO comissao_usuario
              FROM erp.vd_comissao vc
              where vc.id_aplicacao = venda.id_aplicacao
                and vc.id_empresa = venda.id_empresa;

              IF NOT FOUND THEN
                comissao_usuario := 0;
              END IF;
            END;
          END IF;
        END;
      END IF;
    END;

    IF comissao_usuario > 0 THEN
      -- inserir a comissão
--       _comissao_calculada_item = (((item_record.preco_venda * item_record.quantidade) - item_record.desconto) * comissao_usuario);
      _comissao_calculada_item = ((item_record.preco_venda * item_record.quantidade) * comissao_usuario);

      INSERT INTO erp.vd_comissao_vendedor
      (id_aplicacao, id_empresa, id_vendedor, id_produto, id_pospdv_venda_item,
       valor_calculado, comissao_aplicada, tipo_comissao)
      values
        (venda.id_aplicacao, venda.id_empresa, item_record.id_vendedor, item_record.id_produto,
         item_record.id, _comissao_calculada_item, comissao_usuario, 'PDV');

      -- atualizar a comissão mensal
      BEGIN
        SELECT vcva.valor_acumulado
        INTO _valor_acumulado
        FROM ERP.vd_comissao_vendedor_acumulado vcva
        WHERE vcva.id_aplicacao = venda.id_aplicacao
          and vcva.id_empresa = venda.id_empresa
          and vcva.id_vendedor = item_record.id_vendedor
          and TO_CHAR(vcva.mes_acumulado, 'MM-YYYY') = TO_CHAR(current_date, 'MM-YYYY');

        IF NOT FOUND THEN
          _valor_acumulado := 0;
          insert into erp.vd_comissao_vendedor_acumulado
          (id_aplicacao, id_empresa, id_vendedor,
           valor_acumulado, mes_acumulado)
          values (venda.id_aplicacao, venda.id_empresa, item_record.id_vendedor,
                  (_valor_acumulado + _comissao_calculada_item),
                  (DATE_TRUNC('month', CURRENT_DATE))
                 );
        ELSE
          SELECT va.valor_acumulado
          INTO _valor_old_valor_acumulado
          from vd_comissao_vendedor_acumulado va
          where id_vendedor = venda.id_vendedor;

          _novo_valor_acumulado_vendedor = ROUND(_valor_old_valor_acumulado + _comissao_calculada_item, 4);

          -- if para pegar registros que foram removidos
          update erp.vd_comissao_vendedor_acumulado
          set valor_acumulado = _novo_valor_acumulado_vendedor
          where id_aplicacao = venda.id_aplicacao
            and id_empresa = venda.id_empresa
            and id_vendedor = venda.id_vendedor
            and mes_acumulado = (DATE_TRUNC('month', CURRENT_DATE));
        END IF;
      END;
    END IF;
  END LOOP;
END;
$$;
alter function gera_comissao(integer, integer) owner to postgres;