-- Função corrigida que será executada pela trigger
CREATE OR REPLACE FUNCTION erp.verificar_campo_termo()
  RETURNS TRIGGER AS $$
BEGIN
  -- Só verifica se o campo ip_termo_aceite estiver sendo alterado
  IF OLD.ip_termo_aceite IS DISTINCT FROM NEW.ip_termo_aceite THEN
    -- Verifica apenas se o valor antigo não era nulo/vazio e o novo está sendo definido como nulo/vazio
    IF OLD.ip_termo_aceite IS NOT NULL AND OLD.ip_termo_aceite <> '' AND (NEW.ip_termo_aceite IS NULL OR NEW.ip_termo_aceite = '') THEN
      RAISE EXCEPTION 'Não é permitido remover o valor do campo ip_termo_aceite. Operação cancelada.';
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON><PERSON><PERSON> da trigger
DROP TRIGGER IF EXISTS verificar_termo_sg_usuario_aplicacao ON erp.sg_usuario_aplicacao;
CREATE TRIGGER verificar_termo_sg_usuario_aplicacao
  BEFORE UPDATE ON erp.sg_usuario_aplicacao
  FOR EACH ROW
EXECUTE FUNCTION erp.verificar_campo_termo();