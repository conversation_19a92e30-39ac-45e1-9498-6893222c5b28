create table erp.vd_pedido_arquivos
(
  id bigserial not null,
  id_uuid uuid default gen_random_uuid() not null,
  id_aplicacao varchar(36) not null constraint cd_pessoa_arquivos_ad_aplicacao_id_aplicacao_fk references erp.ad_aplicacao,
  id_pedido uuid not null constraint vd_pedido_arquivos_vd_pedido_id_pedido_fk references erp.vd_pedido,
  url_s3 varchar(200) not null,
  file_type varchar(30) not null,
  created_at timestamp default current_timestamp  not null,
  updated_at timestamp default current_timestamp  not null,
  created_by varchar default 'nuvy erp' not null,
  updated_by varchar default 'nuvy erp' not null
);
create unique index vd_pedido_arquivos_id_id_uuid_uindex on erp.vd_pedido_arquivos (id, id_uuid);
create index vd_pedido_arquivos_id_index on erp.vd_pedido_arquivos (id);
alter table erp.vd_pedido_arquivos add constraint vd_pedido_arquivos_pk primary key (id);
