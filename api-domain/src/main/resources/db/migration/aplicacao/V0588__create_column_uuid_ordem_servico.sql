alter table if exists "erp-nfs".ordem_servico
  add column if not exists uuid_ordem_servico uuid default gen_random_uuid() not null;
create unique index if not exists ordem_servico_uuid_uindex
  on "erp-nfs".ordem_servico (uuid_ordem_servico);

alter table if exists "erp-nfs".servico
  add column if not exists uuid_servico uuid default gen_random_uuid() not null;
create unique index if not exists servico_uuid_uindex
  on "erp-nfs".servico (uuid_servico);