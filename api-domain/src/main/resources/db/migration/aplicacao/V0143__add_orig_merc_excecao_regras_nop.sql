---
create table if not exists cd_regra_ipi_excecao_orig_merc (
	id_regra_ipi_excecao bigint not null,
    id_origem_mercadoria bigint not null,
    constraint cd_regra_ipi_excecao_orig_merc_pkey primary key (id_regra_ipi_excecao, id_origem_mercadoria),
    constraint cd_regra_ipi_excecao_orig_merc_id_excecao_fkey foreign key (id_regra_ipi_excecao) references cd_regra_ipi_excecao on delete restrict on update restrict,
    constraint cd_regra_ipi_excecao_orig_merc_id_origem_fkey foreign key (id_origem_mercadoria) references cd_origem_mercadoria on delete restrict on update restrict
);
---
ALTER TABLE cd_regra_ipi_excecao RENAME COLUMN tp_aquisicao to tp_aquisicao_int;
ALTER TABLE cd_regra_ipi_excecao ADD COLUMN tp_aquisicao VARCHAR(50);
UPDATE cd_regra_ipi_excecao
SET tp_aquisicao = CASE tp_aquisicao_int
  WHEN 0 THEN 'COMPRADO'
  WHEN 1 THEN 'FABRICADO'
  WHEN 2 THEN 'AMBOS'
END;
ALTER TABLE cd_regra_ipi_excecao DROP COLUMN tp_aquisicao_int;
---
create table if not exists cd_regra_pis_excecao_orig_merc (
	id_regra_pis_excecao bigint not null,
    id_origem_mercadoria bigint not null,
    constraint cd_regra_pis_excecao_orig_merc_pkey primary key (id_regra_pis_excecao, id_origem_mercadoria),
    constraint cd_regra_pis_excecao_orig_merc_id_excecao_fkey foreign key (id_regra_pis_excecao) references cd_regra_pis_excecao on delete restrict on update restrict,
    constraint cd_regra_pis_excecao_orig_merc_id_origem_fkey foreign key (id_origem_mercadoria) references cd_origem_mercadoria on delete restrict on update restrict
);
---
create table if not exists cd_regra_cofins_excecao_orig_merc (
	id_regra_cofins_excecao bigint not null,
    id_origem_mercadoria bigint not null,
    constraint cd_regra_cofins_excecao_orig_merc_pkey primary key (id_regra_cofins_excecao, id_origem_mercadoria),
    constraint cd_regra_cofins_excecao_orig_merc_id_excecao_fkey foreign key (id_regra_cofins_excecao) references cd_regra_cofins_excecao on delete restrict on update restrict,
    constraint cd_regra_cofins_excecao_orig_merc_id_origem_fkey foreign key (id_origem_mercadoria) references cd_origem_mercadoria on delete restrict on update restrict
);
---