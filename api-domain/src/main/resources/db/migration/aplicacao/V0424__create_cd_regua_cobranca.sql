create table if not exists erp.cd_regua_comunicacao
(
  id uuid default public.uuid_generate_v4() not null constraint cd_regua_comunicacao_pk primary key,
  id_aplicacao uuid not null,
  id_banco_conta integer constraint cd_regua_comunicacao_fn_banco_conta_id_banco_conta_fk references erp.fn_banco_conta,
  situacao varchar(20) default 'ATIVO' not null,
  nome varchar(300) not null,
  ind_lembrete_vencimento boolean default false not null,
  ind_cobrancas_vencidas  boolean default false not null,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null,
  created_by varchar(100) default 'sistema' not null,
  updated_by varchar(100) default 'sistema' not null
);

-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_cd_regua_comunicacao_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.cd_regua_comunicacao SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_cd_regua_comunicacao_updated_at
  AFTER UPDATE ON erp.cd_regua_comunicacao
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_cd_regua_comunicacao_updated_at();

create table erp.cd_regua_comunicacao_lembrete
(
  id uuid default public.uuid_generate_v4() not null constraint cd_regua_comunicacao_lembrete_pk primary key,
  id_regua_comunicacao uuid not null constraint cd_regua_comunicacao_lembrete_cd_regua_comunicacao_id_fk references erp.cd_regua_comunicacao,
  dia_lembrete integer not null,
  situacao varchar(20) default 'ATIVO' not null,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null,
  created_by varchar(100) default 'sistema' not null,
  updated_by varchar(100) default 'sistema' not null
);
comment on column erp.cd_regua_comunicacao_lembrete.dia_lembrete is 'Quantidade de dias antes enviar o aviso';
create unique index cd_regua_comunicacao_lembrete_uindex on erp.cd_regua_comunicacao_lembrete (id_regua_comunicacao, dia_lembrete);

-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_cd_regua_comunicacao_lembrete_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.cd_regua_comunicacao_lembrete SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_cd_regua_comunicacao_lembrete_updated_at
  AFTER UPDATE ON erp.cd_regua_comunicacao_lembrete
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_cd_regua_comunicacao_lembrete_updated_at();

create table erp.cd_regua_comunicacao_vencida
(
  id uuid default public.uuid_generate_v4() not null constraint cd_regua_comunicacao_vencida_pk primary key,
  id_regua_comunicacao uuid not null constraint cd_regua_comunicacao_vencida_cd_regua_comunicacao_id_fk references erp.cd_regua_comunicacao,
  dia_aviso integer not null,
  situacao varchar(20) default 'ATIVO' not null,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null,
  created_by varchar(100) default 'sistema' not null,
  updated_by varchar(100) default 'sistema' not null
);
comment on column erp.cd_regua_comunicacao_vencida.dia_aviso is 'Quantidade após vencimento para enviar cobrança';
create unique index cd_regua_comunicacao_vencida_uindex on erp.cd_regua_comunicacao_vencida (id_regua_comunicacao, dia_aviso);

-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_cd_regua_comunicacao_vencida_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.cd_regua_comunicacao_vencida SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_cd_regua_comunicacao_vencida_updated_at
  AFTER UPDATE ON erp.cd_regua_comunicacao_vencida
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_cd_regua_comunicacao_vencida_updated_at();

create table erp.cd_regua_comunicacao_excecao
(
  id uuid default public.uuid_generate_v4() not null constraint cd_regua_comunicacao_excecao_pk primary key,
  id_regua_comunicacao uuid not null constraint cd_regua_comunicacao_excecao_cd_regua_comunicacao_id_fk references erp.cd_regua_comunicacao,
  id_pessoa bigint not null constraint cd_regua_comunicacao_excecao_cd_pessoa_id_fk references erp.cd_pessoa,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null,
  created_by varchar(100) default 'sistema' not null,
  updated_by varchar(100) default 'sistema' not null
);
create unique index cd_regua_comunicacao_excecao_uindex on erp.cd_regua_comunicacao_excecao (id_regua_comunicacao, id_pessoa);

-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_cd_regua_comunicacao_excecao_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.cd_regua_comunicacao_excecao SET updated_at = CURRENT_TIMESTAMP WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_cd_regua_comunicacao_excecao_updated_at
  AFTER UPDATE ON erp.cd_regua_comunicacao_excecao
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_cd_regua_comunicacao_excecao_updated_at();