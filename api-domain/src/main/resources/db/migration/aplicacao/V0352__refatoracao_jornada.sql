drop table if exists ad_jornada_empresa;
drop table if exists ad_jornada_segmento;
drop table if exists ad_jornada_etapa;
drop table if exists ad_jornada;

create table erp.ad_jornada (
        id_jornada uuid primary key not null default public.uuid_generate_v4(),
        descricao character varying(100),
        situacao character varying(50)
);

create table erp.ad_jornada_etapa (
        id_jornada_etapa uuid primary key not null default public.uuid_generate_v4(),
        id_jornada uuid,
        descricao character varying(150),
        situacao character varying(50)
);

alter table erp.ad_jornada_etapa
  add constraint ad_jornada_etapa_ad_jornada_id_jornada_fk
    foreign key (id_jornada) references erp.ad_jornada;


create table erp.ad_jornada_empresa (
      id_jornada_empresa uuid primary key not null default public.uuid_generate_v4(),
      id_aplicacao character varying(36) not null,
      id_empresa bigint not null,
      id_jornada_etapa uuid,
      atingida boolean
);
alter table erp.ad_jornada_empresa
  add constraint ad_jornada_empresa_ad_aplicacao_id_aplicacao_fk
    foreign key (id_aplicacao) references erp.ad_aplicacao;
alter table erp.ad_jornada_empresa
  add constraint ad_jornada_empresa_cd_empresa_id_empresaep_fk
    foreign key (id_empresa) references erp.cd_empresa;
alter table erp.ad_jornada_empresa
  add constraint ad_jornada_empresa_ad_jornada_id_jornada_etapa_fk
    foreign key (id_jornada_etapa) references erp.ad_jornada;
