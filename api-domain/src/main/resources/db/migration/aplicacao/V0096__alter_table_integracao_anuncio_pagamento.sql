CREATE TABLE an_canal_pagamento_conta
(
  id_canal_pagamento_conta INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_canal                 INT8,
  id_aplicacao               CHAR(36),
  id_forma_pagamento         INT8,
  id_banco_conta             INT8,
  FOREIG<PERSON> KEY (id_canal) REFERENCES vd_canal_venda (id_canal),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao),
  FOREIGN KEY (id_forma_pagamento) REFERENCES cd_forma_pagamento (id_forma_pagamento),
  FOREIGN KEY (id_banco_conta) REFERENCES fn_banco_conta (id_banco_conta)
);