create schema if not exists logusuarioserp;

create table logusuarioserp.logs_usuarios_erp
(
  id_log uuid default public.uuid_generate_v4() not null constraint logs_usuarios_erp_pk primary key,
  id_aplicacao char(36) constraint logs_usuarios_erp_ad_aplicacao_id_aplicacao_fk references erp.ad_aplicacao,
  id_empresa integer not null constraint logs_usuarios_erp_cd_empresa_id_empresa_fk references erp.cd_empresa,
  id_usuario uuid not null constraint logs_usuarios_erp_sg_usuario_id_usuario_fk references erp.sg_usuario,
  url_front varchar(300),
  url_api varchar(30),
  createdat timestamp default now() not null
);
