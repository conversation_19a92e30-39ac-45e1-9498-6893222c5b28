CREATE TABLE utilidades.produto_importacao (
	id_produto_importacao int8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
	id_aplicacao bpchar(36) NULL,
	cod_produto master.citext NULL,
	cod_ean master.citext NULL,
	descricao master.citext NULL,
	vl_preco_venda numeric(21, 10) NULL,
	situacao varchar(50) NULL,
	id_ncm varchar(15) NULL,
	id_unidade_medida int8 null,
	ind_importado bool NULL,
	dt_registro timestamp NULL,

  FOREIGN KEY (id_unidade_medida) REFERENCES erp.cd_unidade_medida (id_unidade_medida),
  FOREIGN KEY (id_ncm) REFERENCES erp.cd_ncm (id_ncm),
  FOREIGN KEY (id_aplicacao) REFERENCES erp.ad_aplicacao (id_aplicacao)
);


CREATE TABLE utilidades.pessoa_importacao (
	id_pessoa_importacao int8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
	id_aplicacao bpchar(36) NULL,
	cpf_cnpj varchar(14) NULL,
	insc_estadual varchar(25) NULL,
	insc_municipal varchar(25) NULL,
	nome varchar(255) NULL,
	nome_fantasia varchar(255) NULL,
	email_cobranca master.citext NULL,
	cep varchar(12) NULL,
	uf varchar(2) NULL,
	cidade varchar(150) NULL,
	bairro varchar(150) NULL,
	endereco varchar(150) NULL,
	numero varchar(60) NULL,
	complemento varchar(100) NULL,
	telefone varchar(15) NULL,
	pais varchar(150) NULL,
	situacao varchar(50) NULL,
	id_cnae varchar(20) NULL,
	id_regime_tributario int8 NULL,
	ind_importado bool NULL,
	dt_registro timestamp NULL,
	tp_pessoa_importacao varchar(50) NULL,

  FOREIGN KEY (id_regime_tributario) REFERENCES erp.cd_regime_tributario (id_regime_tributario),
  FOREIGN KEY (id_cnae) REFERENCES erp.cd_cnae (id_cnae),
  FOREIGN KEY (id_aplicacao) REFERENCES erp.ad_aplicacao (id_aplicacao)
);

ALTER TABLE erp.cd_produto ADD COLUMN ind_importado bool;
ALTER TABLE erp.cd_pessoa ADD COLUMN ind_importado bool;