alter table erp.an_catalogo alter column id_empresa set not null;
create index an_catalogo_id_aplicacao_index on erp.an_catalogo (id_aplicacao);
create index an_catalogo_id_empresa_index on erp.an_catalogo (id_empresa);
alter table erp.an_catalogo_produto alter column id_catalogo set not null;
alter table erp.an_catalogo add created_at timestamp default current_timestamp not null;
alter table erp.an_catalogo add updated_at timestamp default current_timestamp not null;

-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_an_catalogo_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.an_catalogo SET updated_at = CURRENT_TIMESTAMP WHERE id_catalogo = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_an_catalogo_updated_at
  AFTER UPDATE ON erp.an_catalogo
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_an_catalogo_updated_at();

alter table erp.an_catalogo_produto add created_at timestamp default current_timestamp not null;
alter table erp.an_catalogo_produto add updated_at timestamp default current_timestamp not null;
-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_an_catalogo_produto_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.an_catalogo_produto SET updated_at = CURRENT_TIMESTAMP WHERE id_catalogo = OLD.id and id_produto = OLD.id_produto and id_produto_catalogo = OLD.id_produto_catalogo;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_an_catalogo_produto_updated_at
  AFTER UPDATE ON erp.an_catalogo_produto
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_an_catalogo_produto_updated_at();

alter table erp.an_catalogo_categoria add created_at timestamp default current_timestamp not null;
alter table erp.an_catalogo_categoria add updated_at timestamp default current_timestamp not null;
-- Criação da função de trigger
CREATE OR REPLACE FUNCTION update_an_catalogo_categoria_updated_at()
  RETURNS TRIGGER AS $$
BEGIN
  UPDATE erp.an_catalogo_categoria SET updated_at = CURRENT_TIMESTAMP WHERE id_catalogo = OLD.id and id_catalogo_categoria = OLD.id_catalogo_categoria;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criação da trigger para executar a função após a atualização de um registro
CREATE OR REPLACE TRIGGER update_an_catalogo_categoria_updated_at
  AFTER UPDATE ON erp.an_catalogo_produto
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)  -- !
EXECUTE FUNCTION update_an_catalogo_categoria_updated_at();

