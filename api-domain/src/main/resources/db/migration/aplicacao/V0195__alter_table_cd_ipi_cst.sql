ALTER TABLE cd_ipi_cst
ADD COLUMN cod_cst_ipi_inverso VARCHAR(2);
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '50' WHERE cod_cst_ipi = '00';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '51' WHERE cod_cst_ipi = '01';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '52' WHERE cod_cst_ipi = '02';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '53' WHERE cod_cst_ipi = '03';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '54' WHERE cod_cst_ipi = '04';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '55' WHERE cod_cst_ipi = '05';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '99' WHERE cod_cst_ipi = '49';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '00' WHERE cod_cst_ipi = '50';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '01' WHERE cod_cst_ipi = '51';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '02' WHERE cod_cst_ipi = '52';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '03' WHERE cod_cst_ipi = '53';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '04' WHERE cod_cst_ipi = '54';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '05' WHERE cod_cst_ipi = '55';
UPDATE cd_ipi_cst SET cod_cst_ipi_inverso = '49' WHERE cod_cst_ipi = '99';
ALTER TABLE cd_ipi_cst
ALTER COLUMN cod_cst_ipi_inverso SET NOT NULL;