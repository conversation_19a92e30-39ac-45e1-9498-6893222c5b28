alter table if exists eq_nf_entrada
  add column if not exists tipo_nota              varchar(50),
  add column if not exists motivo_devolucao       text,
  add column if not exists email_envio_nf         varchar(100),
  add column if not exists tipo_transporte        varchar(50),
  add column if not exists id_transportadora      int references cd_pessoa (id_pessoa),
  add column if not exists veiculo_placa          varchar(20),
  add column if not exists veiculo_uf             varchar(2),
  add column if not exists veiculo_rntcr          varchar(20),
  add column if not exists dt_registro            timestamp,
  add column if not exists dt_entrega_prevista    timestamp,
  add column if not exists cod_rastreio           varchar(150),
  add column if not exists veiculo_ciot           varchar(100),
  add column if not exists veiculo_tara           numeric(15, 4),
  add column if not exists veiculo_capacidade     numeric(15, 4),
  add column if not exists condutor_nome          varchar(150),
  add column if not exists condutor_cpf           varchar(11),
  add column if not exists uf_carregamento        varchar(2),
  add column if not exists cidade_carregamento    varchar(100),
  add column if not exists uf_descarregamento     varchar(2),
  add column if not exists cidade_descarregamento varchar(100),
  add column if not exists id_tipo_veiculo        int references cd_tipo_veiculo (id_tipo_veiculo),
  add column if not exists id_tipo_carroceria     int references cd_tipo_carroceria (id_tipo_carroceria),
  add column if not exists id_nf_entrada_origem   uuid references eq_nf_entrada (id_nf_entrada),
  add column if not exists cpf_cnpj_destinatario  varchar(14);

alter table if exists eq_nf_entrada_pagto
  add column if not exists perc_desconto      numeric(10, 9),
  add column if not exists vl_seguro          numeric(12, 2),
  add column if not exists vl_outras_despesas numeric(12, 2);


UPDATE eq_nf_entrada
SET tipo_nota = 'ENTRADA'
WHERE tipo_nota IS NULL;

alter table if exists eq_nf_entrada
  alter column tipo_nota set not null;

create table if not exists eq_nf_entrada_uf_percurso
(
  uf            varchar(2),
  id_nf_entrada uuid references eq_nf_entrada (id_nf_entrada),
  primary key (uf, id_nf_entrada)
);

alter table if exists eq_nf_entrada_item
add column if not exists qtd_devolvida numeric(15, 4);