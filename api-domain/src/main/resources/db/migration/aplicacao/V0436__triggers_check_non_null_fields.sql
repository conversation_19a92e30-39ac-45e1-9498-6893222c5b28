create function erp.prevent_null_fields_numero_serie_nfe_pedido() returns trigger
  language plpgsql
as
$$
BEGIN
  IF OLD.id_pessoa IS NOT NULL AND NEW.id_pessoa IS NULL THEN
    RAISE EXCEPTION 'Não é permitido atualizar id_pessoa para NULL';
  END IF;
  RETURN NEW;
END;
$$;
alter function erp.prevent_null_fields_numero_serie_nfe_pedido() owner to postgres;
create trigger prevent_null_fields_numero_serie_nfe_pedido
  before update
    of id_pessoa
  on erp.eq_nf_entrada
  for each row
  when (old.id_pessoa IS DISTINCT FROM new.id_pessoa)
execute procedure erp.prevent_null_fields_numero_serie_nfe_pedido();