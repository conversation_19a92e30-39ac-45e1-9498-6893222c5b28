--atualizar os kits existentes para o ncm do primeiro produto do kit

DO
$$
  DECLARE
    produto RECORD;
    ncm INTEGER;
  BEGIN
    FOR produto IN
      SELECT *
      FROM erp.cd_produto
      WHERE ind_kit = true
      LOOP
        ncm := NULL;
        BEGIN
          SELECT p.id_ncm
          INTO ncm
          FROM erp.cd_produto_kit ck
                 JOIN erp.cd_produto p ON p.id_produto = ck.id_produto
          WHERE ck.id_produto_pai = produto.id_produto
            AND p.id_aplicacao = ck.id_aplicacao
          ORDER BY ck.id_produto_kit DESC
          LIMIT 1;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            ncm := NULL;
        END;

        UPDATE erp.cd_produto
        SET id_ncm = ncm
        WHERE id_produto = produto.id_produto
          AND id_aplicacao = produto.id_aplicacao;
      END LOOP;
  END;
$$;

CREATE OR REPLACE FUNCTION atualizar_ncm_produto_pai()
  RETURNS TRIGGER AS
$$
DECLARE
  ncm INTEGER;
BEGIN
  ncm := NULL;

  SELECT p.id_ncm
  INTO ncm
  FROM erp.cd_produto_kit ck
  JOIN erp.cd_produto p ON p.id_produto = ck.id_produto
  WHERE ck.id_produto_pai = NEW.id_produto_pai
    AND p.id_aplicacao = ck.id_aplicacao
  ORDER BY ck.id_produto_kit DESC
  LIMIT 1;

  UPDATE erp.cd_produto
  SET id_ncm = ncm
  WHERE id_produto = NEW.id_produto_pai
    AND id_aplicacao = NEW.id_aplicacao;
  RETURN NULL;
END;
$$
  LANGUAGE plpgsql;


CREATE or replace TRIGGER trg_atualizar_ncm_produto_pai
  AFTER INSERT OR DELETE OR UPDATE
  ON erp.cd_produto_kit
  FOR EACH ROW
EXECUTE FUNCTION atualizar_ncm_produto_pai();
