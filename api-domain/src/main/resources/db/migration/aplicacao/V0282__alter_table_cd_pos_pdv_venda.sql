alter table erp.cd_pos_pdv_venda drop column if exists payment_type_id;
alter table erp.cd_pos_pdv_venda drop column if exists name;
alter table erp.cd_pos_pdv_venda drop column if exists paid_amount;
alter table erp.cd_pos_pdv_venda drop column if exists tef_type;
alter table erp.cd_pos_pdv_venda drop column if exists tef_autorizacao;
alter table erp.cd_pos_pdv_venda drop column if exists tef_rede;
alter table erp.cd_pos_pdv_venda drop column if exists tef_data_hora;
alter table erp.cd_pos_pdv_venda drop column if exists tef_cartao;
alter table erp.cd_pos_pdv_venda drop column if exists tef_nsu_sitef;
alter table erp.cd_pos_pdv_venda drop column if exists tef_nsu_host;
alter table erp.cd_pos_pdv_venda drop column if exists tef_parcelas;
alter table erp.cd_pos_pdv_venda_pagamento add if not exists payment_type_id varchar(36);
comment on column erp.cd_pos_pdv_venda_pagamento.payment_type_id is 'Identifier da Forma de Pagamento';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists name varchar(50);
comment on column erp.cd_pos_pdv_venda_pagamento.name is 'Descricao da forma de pagamento';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists paid_amount decimal(8,2);
comment on column erp.cd_pos_pdv_venda_pagamento.paid_amount is 'Valor Pago';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_type varchar(2);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_type is 'Tipo de operação TEF -- Vide tabela.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_autorizacao varchar(30);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_autorizacao is 'Código de autorização TEF.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_data_hora varchar(25);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_data_hora is 'Data hora da autorização. YYYYMMDDHHMMSS';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_rede varchar(8);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_rede is 'Rede de autorização do Cartão. -- Vide tabela.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_cartao varchar(8);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_cartao is 'Bandeira do cartão. -- Vide tabela.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_nsu_sitef varchar(100);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_nsu_sitef is 'NSU do Sitef. -- Vide tabela.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_nsu_host varchar(100);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_nsu_host is 'NSU do HOST. -- Vide tabela.';
alter table erp.cd_pos_pdv_venda_pagamento add if not exists tef_parcelas varchar(4);
comment on column erp.cd_pos_pdv_venda_pagamento.tef_parcelas is 'Quantidade de parcelas. -- Vide tabela.';