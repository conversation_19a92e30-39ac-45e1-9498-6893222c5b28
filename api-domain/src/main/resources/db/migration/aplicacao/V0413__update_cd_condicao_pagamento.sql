alter table erp.cd_cond_pagamento add ordem integer;

UPDATE erp.cd_cond_pagamento SET ordem = 24 WHERE id_cond_pagamento = 22;
UPDATE erp.cd_cond_pagamento SET ordem = 11 WHERE id_cond_pagamento = 10;
UPDATE erp.cd_cond_pagamento SET ordem = 20 WHERE id_cond_pagamento = 18;
UPDATE erp.cd_cond_pagamento SET ordem = 2  WHERE id_cond_pagamento = 2;
UPDATE erp.cd_cond_pagamento SET ordem = 21 WHERE id_cond_pagamento = 19;
UPDATE erp.cd_cond_pagamento SET ordem = 5  WHERE id_cond_pagamento = 4;
UPDATE erp.cd_cond_pagamento SET ordem = 23 WHERE id_cond_pagamento = 21;
UPDATE erp.cd_cond_pagamento SET ordem = 25 WHERE id_cond_pagamento = 23;
UPDATE erp.cd_cond_pagamento SET ordem = 19 WHERE id_cond_pagamento = 17;
UPDATE erp.cd_cond_pagamento SET ordem = 1  WHERE id_cond_pagamento = 1;
UPDATE erp.cd_cond_pagamento SET ordem = 12 WHERE id_cond_pagamento = 11;
UPDATE erp.cd_cond_pagamento SET ordem = 22 WHERE id_cond_pagamento = 20;
UPDATE erp.cd_cond_pagamento SET ordem = 17 WHERE id_cond_pagamento = 15;
UPDATE erp.cd_cond_pagamento SET ordem = 15 WHERE id_cond_pagamento = 25;
UPDATE erp.cd_cond_pagamento SET ordem = 26 WHERE id_cond_pagamento = 24;
UPDATE erp.cd_cond_pagamento SET ordem = 18 WHERE id_cond_pagamento = 16;
UPDATE erp.cd_cond_pagamento SET ordem = 10 WHERE id_cond_pagamento = 9;
UPDATE erp.cd_cond_pagamento SET ordem = 6  WHERE id_cond_pagamento = 5;
UPDATE erp.cd_cond_pagamento SET ordem = 14 WHERE id_cond_pagamento = 13;
UPDATE erp.cd_cond_pagamento SET ordem = 4  WHERE id_cond_pagamento = 3;
UPDATE erp.cd_cond_pagamento SET ordem = 13 WHERE id_cond_pagamento = 12;
UPDATE erp.cd_cond_pagamento SET ordem = 7  WHERE id_cond_pagamento = 6;
UPDATE erp.cd_cond_pagamento SET ordem = 9  WHERE id_cond_pagamento = 8;
UPDATE erp.cd_cond_pagamento SET ordem = 16 WHERE id_cond_pagamento = 14;
UPDATE erp.cd_cond_pagamento SET ordem = 8  WHERE id_cond_pagamento = 7;

alter table erp.cd_cond_pagamento alter column ordem set not null;
create unique index cd_cond_pagamento_ordem_uindex on erp.cd_cond_pagamento (ordem);