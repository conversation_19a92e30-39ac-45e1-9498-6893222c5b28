create unique index if not exists vd_meta_geral_id_aplicacao_id_empresa_ano_referencia_mes_referencia_uindex
  on vd_meta_geral (id_aplicacao, id_empresa, ano_referencia, mes_referencia);

create unique index if not exists vd_meta_canal_id_aplicacao_id_empresa_id_canal_ano_referencia_mes_referencia_uindex
  on vd_meta_canal (id_aplicacao, id_empresa, id_canal, ano_referencia, mes_referencia);

create unique index if not exists vd_meta_vendedor_id_aplicacao_id_empresa_id_pessoa_id_empresa_ano_referencia_mes_referencia_uindex
  on vd_meta_vendedor (id_aplicacao, id_empresa, id_pessoa, id_empresa, ano_referencia, mes_referencia);
