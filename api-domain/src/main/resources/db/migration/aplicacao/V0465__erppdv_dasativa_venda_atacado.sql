CREATE OR REPLACE FUNCTION atualizar_produtos_atacado()
  RETURNS TRIGGER AS $$
BEGIN
  IF NEW.ind_venda_atacado = false THEN
    UPDATE erp.cd_produto
    SET qtd_minima_atacado = 0,
        vl_atacado = 0
    WHERE id_aplicacao = NEW.id_aplicacao;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_atualizar_produtos_atacado
  AFTER UPDATE OF ind_venda_atacado
  ON erp.cd_pos_pdv
  FOR EACH ROW
EXECUTE FUNCTION atualizar_produtos_atacado();
