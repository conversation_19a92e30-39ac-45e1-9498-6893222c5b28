CREATE TABLE fn_banco_conta_homologacao_carteira
(
  id_homolocacao_carteira   INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao       CHAR(36)                              NOT NULL,
  id_empresa         INT8                                  NOT NULL,
  id_banco_conta     INT8                                  NOT NULL,
  id_servico_boleto  BIGINT                                NOT NULL,
  dt_recebimento     TIMESTAMP,
  situacao_boleto    CHAR(25),
  ind_envio_remessa  BOOLEAN DEFAULT false,
  dt_recebimento_remessa     TIMESTAMP,
  id_remessa_gerada  BIGINT,
  FOREIGN KEY (id_empresa) REFERENCES cd_empresa (id_empresa),
  FOREIGN KEY (id_banco_conta) REFERENCES fn_banco_conta (id_banco_conta),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);