DROP VIEW IF EXISTS ERP.VW_EXTRATO_CONCILIACAO;
CREATE OR REPLACE VIEW ERP.VW_EXTRATO_CONCILIACAO AS
SELECT MVT.id_movto       AS id,
       MVT.id_banco_conta AS contaBancariaId,
       TRA.id_transacao   AS transacaoId,
       BAN.id_banco       AS bancoId,
       BAN.cod_banco      AS bancoCodigo,
       BAN.nome           AS bancoNome,
       CASE
         WHEN TRA.tp_transacao IN (4, 5) THEN EMP.id_empresa
         ELSE PES.id_pessoa
         END              AS parceiroId,
       CASE
         WHEN TRA.tp_transacao IN (4, 5) THEN EMP.nome
         ELSE PES.nome
         END              AS parceiroNome,
       CASE
         WHEN TRA.tp_transacao IN (4, 5) THEN EMP.cpf_cnpj
         ELSE PES.cpf_cnpj
         END              AS parceiroCpfCnpj,
       MVT.vl_movto       AS valorBaixa,
       CASE
         WHEN TRA.tp_transacao = 0 AND TIT.tp_titulo = 'PAGAR' THEN 'DESPESA'
         WHEN TRA.tp_transacao = 0 AND TIT.tp_titulo = 'RECEBER' THEN 'RECEITA'
         WHEN TRA.tp_transacao = 4 THEN 'TRANSFERENCIA_CONTAS'
         WHEN TRA.tp_transacao = 5 THEN 'SALDO_INICIAL'
         END              AS tipo,
       DATE(MVT.dt_movto) AS dataBaixa,
       CASE
         WHEN TRA.tp_transacao = 0 THEN TIT.dt_vencimento
         ELSE DATE(MVT.dt_movto)
         END              AS vencimento,
       MVT.situacao       AS status
FROM ERP.FN_MOVTO_BANCO MVT
       INNER JOIN
     ERP.FN_TRANSACAO TRA ON MVT.id_transacao = TRA.id_transacao
       LEFT JOIN
     ERP.FN_TITULO_BAIXA TTB ON TRA.id_transacao = TTB.id_transacao
       LEFT JOIN
     ERP.FN_TITULO TIT ON TTB.id_titulo = TIT.id_titulo
       LEFT JOIN
     ERP.cd_pessoa PES ON TIT.id_pessoa = PES.id_pessoa
       LEFT JOIN
     ERP.FN_TRANSACAO_TRANSFERENCIA TRF ON TRA.id_transacao = TRF.id_transacao
       LEFT JOIN
     ERP.FN_BANCO_CONTA BCO ON MVT.id_banco_conta = BCO.id_banco_conta
       LEFT JOIN
     ERP.FN_BANCO BAN ON BCO.id_banco = BAN.id_banco
       LEFT JOIN
     ERP.cd_empresa_banco_conta EBC ON BCO.id_banco_conta = EBC.id_banco_conta
       LEFT JOIN
     ERP.cd_empresa EMP ON EBC.id_empresa = EMP.id_empresa
ORDER BY MVT.dt_movto, parceiroNome;