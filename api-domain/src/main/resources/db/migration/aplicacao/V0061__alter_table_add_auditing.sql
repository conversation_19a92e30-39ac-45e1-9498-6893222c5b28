ALTER TABLE ad_aplicacao
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VA<PERSON><PERSON><PERSON>(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VA<PERSON><PERSON><PERSON>(100);

ALTER TABLE cd_pessoa
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VA<PERSON><PERSON><PERSON>(100);

ALTER TABLE sg_perfil
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VA<PERSON>HA<PERSON>(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);

ALTER TABLE sg_perfil_funcionalidade
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by <PERSON><PERSON><PERSON><PERSON>(100);

ALTER TABLE cd_empresa
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by <PERSON><PERSON><PERSON><PERSON>(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by <PERSON><PERSON><PERSON>R(100);

ALTER TABLE cd_empresa_contato
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);

ALTER TABLE nf_serie
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);

ALTER TABLE eq_deposito
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);

ALTER TABLE sg_usuario_aplicacao
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);

ALTER TABLE sg_usuario
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);



ALTER TABLE fn_plano_conta
  ADD COLUMN created_at TIMESTAMP,
  ADD COLUMN created_by VARCHAR(100),
  ADD COLUMN updated_at TIMESTAMP,
  ADD COLUMN updated_by VARCHAR(100);