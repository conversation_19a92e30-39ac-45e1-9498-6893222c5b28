---
ALTER TABLE cd_regra_icms_item RENAME COLUMN modalide_bc to modalide_bc_int;
ALTER TABLE cd_regra_icms_item ADD COLUMN modalide_bc VARCHAR(30);
UPDATE cd_regra_icms_item
SET modalide_bc = CASE modalide_bc_int
  WHEN 0 THEN 'PRECO_TABELADO'
  WHEN 1 THEN 'LISTA_NEGATIVA'
  WHEN 2 THEN 'LISTA_POSITIVA'
  WHEN 3 THEN 'LISTA_NEUTRA'
  WHEN 4 THEN 'MARGEM_VALOR_AGREGADO'
  WHEN 5 THEN 'PAUTA'
  WHEN 6 THEN 'VALOR_OPERACAO'
  WHEN 7 THEN 'NENHUM'
END;
ALTER TABLE cd_regra_icms_item DROP COLUMN modalide_bc_int;
---
alter table if exists cd_regra_icms_item 
    add column modalide_bc_st varchar(30);
update cd_regra_icms_item
SET modalide_bc_st = modalide_bc;
update cd_regra_icms_item
SET modalide_bc = null;
---
alter table if exists cd_regra_icms_item 
    add column aliquota_fcp numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_estrangeiro numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column cod_cst_icms varchar(6);
alter table if exists cd_regra_icms_item 
    add column ind_icms_desonerado boolean default false not null;
alter table if exists cd_regra_icms_item 
    add column motivo_desoneracao varchar(200);
alter table if exists cd_regra_icms_item 
    add column percentual_deferimento numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_reducao_bc numeric(10,9);
---
ALTER TABLE cd_regra_icms_item_excecao RENAME COLUMN modalide_bc to modalide_bc_int;
ALTER TABLE cd_regra_icms_item_excecao ADD COLUMN modalide_bc VARCHAR(30);
UPDATE cd_regra_icms_item_excecao
SET modalide_bc = CASE modalide_bc_int
  WHEN 0 THEN 'PRECO_TABELADO'
  WHEN 1 THEN 'LISTA_NEGATIVA'
  WHEN 2 THEN 'LISTA_POSITIVA'
  WHEN 3 THEN 'LISTA_NEUTRA'
  WHEN 4 THEN 'MARGEM_VALOR_AGREGADO'
  WHEN 5 THEN 'PAUTA'
  WHEN 6 THEN 'VALOR_OPERACAO'
  WHEN 7 THEN 'NENHUM'
END;
ALTER TABLE cd_regra_icms_item_excecao DROP COLUMN modalide_bc_int;
---
ALTER TABLE cd_regra_icms_item_excecao
    ADD COLUMN modalide_bc_st VARCHAR(30);

UPDATE cd_regra_icms_item_excecao
SET modalide_bc = modalide_bc_st;
UPDATE cd_regra_icms_item_excecao
SET modalide_bc_st = null;
---
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_fcp numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_estrangeiro numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column cod_cst_icms varchar(6);
alter table if exists cd_regra_icms_item_excecao 
    add column ind_icms_desonerado boolean default false not null;
alter table if exists cd_regra_icms_item_excecao 
    add column motivo_desoneracao varchar(200);
alter table if exists cd_regra_icms_item_excecao 
    add column percentual_deferimento numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_reducao_bc numeric(10,9);
---