CREATE TABLE eq_transacao
(
  id_transacao INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao CHAR(36)                             NOT NULL,
  dt_transacao TIMESTAMP,
  tp_transacao INT8,
  FOREI<PERSON><PERSON> KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE eq_rastreabilidade
(
  id_rastreabilidade INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao       CHAR(36)                             NOT NULL,
  lote               VARCHAR(20),
  dt_fabricacao      DATE,
  dt_validade        DATE,
  nr_serie           VARCHAR(50),
  id_produto         INT8                                  NOT NULL,
  cod_agregacao      VARCHAR(20),
  FOREIGN KEY (id_produto) REFERENCES cd_produto (id_produto),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE eq_saldo
(
  id_saldo           INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao       CHAR(36)                             NOT NULL,
  qtd_saldo          NUMERIC(15, 4),
  id_rastreabilidade INT8,
  id_deposito        INT8,
  id_produto         INT8                                  NOT NULL,
  FOREIGN KEY (id_rastreabilidade) REFERENCES eq_rastreabilidade (id_rastreabilidade),
  FOREIGN KEY (id_deposito) REFERENCES eq_deposito (id_deposito),
  FOREIGN KEY (id_produto) REFERENCES cd_produto (id_produto),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE eq_movto_estoque
(
  id_movto_estoque   INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao       CHAR(36)                             NOT NULL,
  dt_movto           TIMESTAMP,
  qtd_movto          NUMERIC(15, 4)                       NOT NULL,
  qtd_informada      NUMERIC(15, 4),
  tp_movto           INT8,
  vl_custo           NUMERIC(21, 10),
  observacao         TEXT,
  id_usuario         UUID,
  id_deposito        INT8,
  id_rastreabilidade INT8,
  id_produto         INT8                                  NOT NULL,
  id_transacao       INT8,
  FOREIGN KEY (id_deposito) REFERENCES eq_deposito (id_deposito),
  FOREIGN KEY (id_rastreabilidade) REFERENCES eq_rastreabilidade (id_rastreabilidade),
  FOREIGN KEY (id_produto) REFERENCES cd_produto (id_produto),
  FOREIGN KEY (id_transacao) REFERENCES eq_transacao (id_transacao),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);