ALTER TABLE sg_usuario_aplicacao ADD COLUMN dt_vencimento_callface TIMESTAMP;
CREATE TABLE IF NOT EXISTS utilidades.ht_callface_empresa_cobranca (
                                                                     id_aplicacao char(36) NOT NULL,
                                                                     id_empresa int8 NOT NULL,
                                                                     qtd_usuario int8 NOT NULL,
                                                                     mes int8 NOT NULL,
                                                                     ano int8 NOT NULL,
                                                                     ind_integrado BOOL NOT null,
                                                                     id_callface_empresa_cobranca uuid NOT NULL,
                                                                     CONSTRAINT ht_callface_empresa_cobranca_pk PRIMARY KEY (id_callface_empresa_cobranca),
                                                                     CONSTRAINT ht_callface_empresa_cobranca_id_aplicacao_fkey FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao(id_aplicacao),
                                                                     CONSTRAINT ht_callface_empresa_cobranca_id_empresa_fkey FOREIGN KEY (id_empresa) REFERENCES cd_empresa(id_empresa)
);