drop table if exists erp.cd_pos_pdv_venda_pagamento;
drop table if exists erp.cd_pos_pdv_venda_item;
drop table if exists erp.cd_pos_pdv_venda;

create table if not exists erp.cd_pos_pdv_venda (
	id int8 generated by default as identity not null primary key,
	id_empresa int8 not null,
	id_pospdv uuid not null,
	id_usuario uuid null,
	id_aplicacao bpchar(36) NOT NULL,
	venda_id varchar(36) null,
	data_venda timestamp null,
	situacao varchar(60) null,
	cpf_cnpj varchar(18) null,
	num_nfce varchar(10) null,
	serie_nfce varchar(3) null,
	chave_nfce varchar(50) null,
	protocolo varchar(50) null,
	data_recebimento timestamp null,
	qr_code varchar(500) null,
	url_sefaz varchar(500) null,
	url_nfce varchar(500) null,
	valor_total_tributos varchar(12) null,
	identificador varchar(30) null,
	numero_propriedade_terminal varchar(5) null,
	ambiente varchar(20) null,
	total_item numeric(21, 10) null,
	valor_total numeric(21, 10) null,
	valor_total_desconto numeric(21, 10) null,
	createdat timestamp not null default now(),
	updatedat timestamp null,
	constraint cd_pos_pdv_venda_cd_pos_pdv_id_fk foreign key (id_empresa) references erp.cd_empresa(id_empresa),
	constraint cd_pos_pdv_venda_id_aplicacao_fk foreign key (id_aplicacao) references erp.ad_aplicacao(id_aplicacao),
	constraint cd_pos_pdv_venda_id_pospdv_fk foreign key (id_pospdv) references erp.cd_pos_pdv(id),
	constraint cd_pos_pdv_venda_id_usuario_fk foreign key (id_usuario) references erp.sg_usuario(id_usuario)
);


create table if not exists erp.cd_pos_pdv_venda_item (
	id int8 generated by default as identity not null primary key,
	id_venda int8 not null,
	id_produto int8 not null,
	id_aplicacao bpchar(36) NOT NULL,
	produto_pos_id varchar(36) null,
	quantidade numeric(5, 2) null,
	preco_venda numeric(7, 2) null,
	preco_unitario numeric(7, 2) null,
	desconto numeric(7, 2) null,
	nfce_ncm varchar(8) null,
	nfce_cfop varchar(4) null,
	nfce_cst varchar(4) null,
	nfce_aliq_icms numeric(4, 2) null,
	nfce_cest varchar(7) null,
	nfce_cst_pis varchar(4) null,
	nfce_aliq_pis numeric(4, 2) null,
	nfce_cst_cofins varchar(4) null,
	nfce_aliq_cofins numeric(4, 2) null,
	nfce_cod_anp numeric(4, 2) null,
	createdat timestamp not null default now(),
	updatedat timestamp null,
	constraint cd_pos_pdv_venda_item_cd_pos_pdv_id_fk foreign key (id_venda) references erp.cd_pos_pdv_venda(id),
	constraint cd_pos_pdv_venda_item_id_aplicacao_fk foreign key (id_aplicacao) references erp.ad_aplicacao(id_aplicacao),
	constraint cd_pos_pdv_venda_item_id_produto_fkey foreign key (id_produto) references erp.cd_produto(id_produto)
);

create table if not exists erp.cd_pos_pdv_venda_pagamento (
	id int8 generated by default as identity not null primary key,
	id_venda int8 not null,
	id_aplicacao bpchar(36) NOT NULL,
	pagamento_pos_id varchar(36) null,
	nome varchar(50) null,
	valor_pago numeric(8, 2) null,
	tipo_operacao varchar(20) null,
	codigo_autorizacao_tef varchar(30) null,
	data_hora_autorizacao timestamp null,
	rede_autorizacao_cartao varchar(8) null,
	bandeira_cartao varchar(8) null,
	quantidade_parcelas varchar(8) null,
	nsu_sitef varchar(8) null,
	createdat timestamp not null default now(),
	updatedat timestamp null,
	constraint cd_pos_pdv_venda_pagamento_id_aplicacao_fk foreign key (id_aplicacao) references erp.ad_aplicacao(id_aplicacao),
	constraint cd_pos_pdv_venda_pagamento_cd_pos_pdv_id_fk foreign key (id_venda) references erp.cd_pos_pdv_venda(id)
);