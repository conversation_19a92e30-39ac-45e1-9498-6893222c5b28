ALTER TABLE cd_pessoa_endereco
  ADD COLUMN IF NOT EXISTS pais_cod VARCHAR(100),
  ADD COLUMN IF NOT EXISTS pais VARCHAR(100);

ALTER TABLE vd_pedido_endereco
  ADD COLUMN IF NOT EXISTS pais_cod VARCHAR(100),
  ADD COLUMN IF NOT EXISTS pais VARCHAR(100);

ALTER TABLE cd_empresa
  ADD COLUMN IF NOT EXISTS pais_cod VARCHAR(100),
  ADD COLUMN IF NOT EXISTS pais VARCHAR(100);

ALTER TABLE nf_recebida
  ADD COLUMN IF NOT EXISTS pais_cod VARCHAR(100),
  ADD COLUMN IF NOT EXISTS pais VARCHAR(100);

ALTER TABLE vd_pedido
  ADD COLUMN IF NOT EXISTS tp_operacao VARCHAR(10),
  ADD COLUMN IF NOT EXISTS valor_fcp_uf_destino NUMERIC(21,10),
  ADD COLUMN IF NOT EXISTS valor_fcp_icms_destino NUMERIC(21,10);

ALTER TABLE vd_pedido_item
  ADD COLUMN IF NOT EXISTS ext_ipi VARCHAR(10);