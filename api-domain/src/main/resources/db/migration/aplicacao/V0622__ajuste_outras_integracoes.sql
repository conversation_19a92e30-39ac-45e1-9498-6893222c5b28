-- Add foreign key column first, then the constraint separately
ALTER TABLE erp.cd_outras_integracoes_empresa
  ADD COLUMN IF NOT EXISTS id_plano_conta INTEGER;
-- Now add the foreign key constraint
ALTER TABLE erp.cd_outras_integracoes_empresa
  ADD CONSTRAINT cd_outras_integracoes_empresa_id_plano_conta_fk
    FOREIGN KEY (id_plano_conta) REFERENCES erp.fn_plano_conta;

-- The rest of your SQL looks fine:
ALTER TABLE erp.cd_outras_integracoes_servico
  DROP CONSTRAINT IF EXISTS cd_outras_integracoes_servico_ordem_servico_id_fk;

ALTER TABLE erp.cd_outras_integracoes_servico
  ADD CONSTRAINT cd_outras_integracoes_servico_servico_id_fk
    FOREIGN KEY (id_servico) REFERENCES "erp-nfs".servico;