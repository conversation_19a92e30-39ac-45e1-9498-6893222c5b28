drop view logusuarioserp.url_por_empresa;
create or replace view logusuarioserp.url_por_empresa(id, url_front, "Cd Empresa - ID Empresa__nome") as
SELECT
  "logusuarioserp"."logs_usuarios_erp"."id_log" AS "id",
  "logusuarioserp"."logs_usuarios_erp"."url_front" AS "url_front",
  "Cd Empresa - ID Empresa"."nome" AS "Cd Empresa - ID Empresa__nome"
FROM
  "logusuarioserp"."logs_usuarios_erp"

    LEFT JOIN "erp"."cd_empresa" AS "Cd Empresa - ID Empresa" ON "logusuarioserp"."logs_usuarios_erp"."id_empresa" = "Cd Empresa - ID Empresa"."id_empresa"
    LEFT JOIN "erp"."cd_segmento" AS "Cd Segmento - ID Segmento" ON "Cd Empresa - ID Empresa"."id_segmento" = "Cd Segmento - ID Segmento"."id_segmento"
    LEFT JOIN "backoffice"."cliente" AS "Cliente - ID Aplicacao" ON "Cd Empresa - ID Empresa"."id_aplicacao" = "Cliente - ID Aplicacao"."id"
    LEFT JOIN "backoffice"."contratacao" AS "Contratacao" ON "Cliente - ID Aplicacao"."id" = "Contratacao"."clienteId"
    LEFT JOIN "backoffice"."contratacaoPlanos" AS "ContratacaoPlanos" ON "Contratacao"."planoId" = "ContratacaoPlanos"."id"
WHERE
  (
      (
          "Cd Empresa - ID Empresa"."nome" <> 'Music Tech LTDA'
        )

      OR ("Cd Empresa - ID Empresa"."nome" IS NULL)
    )

  AND (
    (
        "Cd Empresa - ID Empresa"."nome" <> 'Marcondes Teste'
      )
    OR ("Cd Empresa - ID Empresa"."nome" IS NULL)
  )
  AND (
    (
        "Cd Empresa - ID Empresa"."nome" <> 'teste davi 1128'
      )
    OR ("Cd Empresa - ID Empresa"."nome" IS NULL)
  )
  AND ("Cd Empresa - ID Empresa"."situacao" = 'ATIVO')
  AND (
    NOT (
        LOWER("logusuarioserp"."logs_usuarios_erp"."url_front") LIKE '%/editar%'
      )
    OR (
      "logusuarioserp"."logs_usuarios_erp"."url_front" IS NULL
      )
  )
  AND (
    NOT (
        LOWER("logusuarioserp"."logs_usuarios_erp"."url_front") LIKE '%?%'
      )
    OR (
      "logusuarioserp"."logs_usuarios_erp"."url_front" IS NULL
      )
  )
UNION ALL
SELECT
  "tmp".id,
  "tmp"."url" AS "url",
  '-' AS "Cd Empresa - ID Empresa__nome"
FROM
  "logusuarioserp"."tmp_urls" "tmp"
WHERE
    "tmp"."url" NOT IN (
    SELECT "logs"."url_front"
    FROM "logusuarioserp"."logs_usuarios_erp" "logs"
    WHERE "logs"."url_front" LIKE '%' || "tmp"."url" || '%'
  );

alter table logusuarioserp.url_por_empresa owner to postgres;