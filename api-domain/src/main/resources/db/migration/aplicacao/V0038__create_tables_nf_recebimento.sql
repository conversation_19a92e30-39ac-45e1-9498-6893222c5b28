CREATE TABLE nf_recebida
(
  id_nf_recebida             INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao               CHAR(36)                             NOT NULL,
  cnpj                       VARCHAR(14),
  razao_social               VARCHAR(150),
  nr_nf                      VARCHAR(150),
  dt_nf                      TIMESTAMP,
  dt_recebimento_nf          TIMESTAMP,
  vl_nota                    NUMERIC(21, 10),
  cnpj_transportador         VARCHAR(14),
  razao_social_transportador VARCHAR(150),
  tp_frete                   INT8,
  qtd_volumes                NUMERIC(15, 4),
  tp_volume                  VARCHAR(100),
  peso_liquido               NUMERIC(15, 4),
  peso_bruto                 NUMERIC(15, 4),
  id_pessoa                  INT8,
  situacao                   VARCHAR(50),
  chave_acesso               VARCHAR(100),
  xml                        TEXT,
  tp_endereco                INT8,
  cep                        VARCHAR(12),
  uf                         VARCHAR(2),
  uf_nome                    VARCHAR(100),
  cidade                     VARCHAR(150),
  cidade_cod                 VARCHAR(10),
  bairro                     VARCHAR(150),
  endereco                   VARCHAR(150),
  numero                     VARCHAR(60),
  complemento                VARCHAR(100),
  telefone                   VARCHAR(15),
  pais                       VARCHAR(150),

  FOREIGN KEY (id_pessoa) REFERENCES cd_pessoa (id_pessoa),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_centro_custo
(
  id_nf_recebida_centro_custo INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao                CHAR(36)                             NOT NULL,
  id_nf_recebida              INT8                                  NOT NULL,
  id_centro_custo             INT8                                  NOT NULL,
  percentual                  NUMERIC(10, 9),

  FOREIGN KEY (id_nf_recebida) REFERENCES nf_recebida (id_nf_recebida),
  FOREIGN KEY (id_centro_custo) REFERENCES fn_centro_custo (id_centro_custo),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_hist
(
  id_nf_recebida_hist INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao        CHAR(36)                             NOT NULL,
  id_nf_recebida      INT8                                  NOT NULL,
  dt_hist             TIMESTAMP,
  descricao           TEXT,
  id_usuario          UUID,

  FOREIGN KEY (id_nf_recebida) REFERENCES nf_recebida (id_nf_recebida),
  FOREIGN KEY (id_usuario) REFERENCES sg_usuario (id_usuario),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_pagto
(
  id_nf_recebida_pagto INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao         CHAR(36)                             NOT NULL,
  id_nf_recebida       INT8                                  NOT NULL,
  id_banco_conta       INT8,
  id_cond_pagamento    INT8,
  id_forma_pagamento   INT8                                  NOT NULL,

  FOREIGN KEY (id_nf_recebida) REFERENCES nf_recebida (id_nf_recebida),
  FOREIGN KEY (id_banco_conta) REFERENCES fn_banco_conta (id_banco_conta),
  FOREIGN KEY (id_cond_pagamento) REFERENCES cd_cond_pagamento (id_cond_pagamento),
  FOREIGN KEY (id_forma_pagamento) REFERENCES cd_forma_pagamento (id_forma_pagamento),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_pagto_parcela
(
  id_nf_recebida_pagto_parcela INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao                 CHAR(36)                             NOT NULL,
  id_nf_recebida_pagto         INT8                                  NOT NULL,
  id_banco_conta               INT8,
  id_forma_pagamento           INT8,
  vl_parcela                   NUMERIC(21, 10),
  dt_vencimento                TIMESTAMP,

  FOREIGN KEY (id_nf_recebida_pagto) REFERENCES nf_recebida_pagto (id_nf_recebida_pagto),
  FOREIGN KEY (id_banco_conta) REFERENCES fn_banco_conta (id_banco_conta),
  FOREIGN KEY (id_forma_pagamento) REFERENCES cd_forma_pagamento (id_forma_pagamento),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE cd_produto_fornecedor
(
  id_produto_fornecedor  INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao           CHAR(36)                             NOT NULL,
  cod_produto_fornecedor INT8,
  id_produto             INT8                                  NOT NULL,
  id_fornecedor          INT8                                  NOT NULL,
  unidade_nf             VARCHAR(6),
  fator_conv_unidade     NUMERIC(15, 4),

  FOREIGN KEY (id_produto) REFERENCES cd_produto (id_produto),
  FOREIGN KEY (id_fornecedor) REFERENCES cd_pessoa (id_pessoa),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_item
(
  id_nf_recebida_item    INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao           CHAR(36)                             NOT NULL,
  id_nf_recebida         INT8                                  NOT NULL,
  id_rastreabilidade     INT8,
  id_produto             INT8,
  ind_entrada            BOOL,
  cod_fornecedor         VARCHAR(100),
  descricao              VARCHAR(150),
  cod_produto            VARCHAR(100),
  id_ncm                 VARCHAR(15),
  id_dest                INT8,
  cod_cfop_compra        VARCHAR(50),
  cod_cfop_entrada       VARCHAR(50),
  ind_icms               BOOL,
  ind_ipi                BOOL,
  ind_pis                BOOL,
  ind_cofins             BOOL,
  ind_icms_st            BOOL,
  ind_frete              BOOL,
  ind_outras_despesas    BOOL,
  ind_seguro             BOOL,
  cod_cst_icms           VARCHAR(2),
  cod_cst_pis            VARCHAR(2),
  cod_cst_ipi            VARCHAR(2),
  cod_cst_cofins         VARCHAR(2),
  vl_custo_medio_entrada NUMERIC(21, 10),
  vl_seguro              NUMERIC(21, 10),
  vl_frete               NUMERIC(21, 10),
  vl_outras_despesas     NUMERIC(21, 10),
  vl_icms                NUMERIC(21, 10),
  vl_icms_st             NUMERIC(21, 10),
  vl_fcp                 NUMERIC(21, 10),
  vl_ipi                 NUMERIC(21, 10),
  vl_pis                 NUMERIC(21, 10),
  vl_cofins              NUMERIC(21, 10),
  aliquota_icms          NUMERIC(10, 9),
  aliquota_credito_icms  NUMERIC(10, 9),
  aliquota_icms_interna  NUMERIC(10, 9),
  aliquota_fcp           NUMERIC(10, 9),
  aliquota_ipi           NUMERIC(10, 9),
  aliquota_pis           NUMERIC(10, 9),
  aliquota_cofins        NUMERIC(10, 9),
  perc_bc_icms           NUMERIC(10, 9),
  perc_bc_icms_propria   NUMERIC(10, 9),
  perc_mva               NUMERIC(10, 9),
  perc_bc_st             NUMERIC(10, 9),
  perc_bc_fcp            NUMERIC(10, 9),
  perc_bc_ipi            NUMERIC(10, 9),
  perc_bc_pis            NUMERIC(10, 9),
  perc_bc_cofins         NUMERIC(10, 9),
  vl_credito_icms        NUMERIC(21, 10),
  vl_icms_interna        NUMERIC(21, 10),
  vl_bc_icms             NUMERIC(21, 10),
  vl_bc_icms_propria     NUMERIC(21, 10),
  vl_mva                 NUMERIC(21, 10),
  vl_bc_st               NUMERIC(21, 10),
  vl_bc_fcp              NUMERIC(21, 10),
  vl_bc_ipi              NUMERIC(21, 10),
  vl_bc_pis              NUMERIC(21, 10),
  vl_bc_cofins           NUMERIC(21, 10),
  unidade_nf             VARCHAR(6),
  qtd_nf                 NUMERIC(15, 4),
  quantidade             NUMERIC(15, 4),
  id_unidade_medida      INT8,

  FOREIGN KEY (id_nf_recebida) REFERENCES nf_recebida (id_nf_recebida),
  FOREIGN KEY (id_rastreabilidade) REFERENCES eq_rastreabilidade (id_rastreabilidade),
  FOREIGN KEY (id_produto) REFERENCES cd_produto (id_produto),
  FOREIGN KEY (id_ncm) REFERENCES cd_ncm (id_ncm),
  FOREIGN KEY (id_dest) REFERENCES cc_dest (id_dest),
  FOREIGN KEY (cod_cfop_compra) REFERENCES cd_cfop (cod_cfop),
  FOREIGN KEY (cod_cfop_entrada) REFERENCES cd_cfop (cod_cfop),
  FOREIGN KEY (cod_cst_icms) REFERENCES cd_icms_cst (cod_cst_icms),
  FOREIGN KEY (cod_cst_pis) REFERENCES cd_pis_cst (cod_cst_pis),
  FOREIGN KEY (cod_cst_ipi) REFERENCES cd_ipi_cst (cod_cst_ipi),
  FOREIGN KEY (cod_cst_cofins) REFERENCES cd_cofins_cst (cod_cst_cofins),
  FOREIGN KEY (id_unidade_medida) REFERENCES cd_unidade_medida (id_unidade_medida),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);

CREATE TABLE nf_recebida_item_etq
(
  id_nf_recebida_item_etq INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao            CHAR(36)                             NOT NULL,
  id_nf_recebida_item     INT8,
  id_rastreabilidade      INT8,
  id_deposito             INT8,
  quantidde               INT8,

  FOREIGN KEY (id_nf_recebida_item) REFERENCES nf_recebida_item (id_nf_recebida_item),
  FOREIGN KEY (id_rastreabilidade) REFERENCES eq_rastreabilidade (id_rastreabilidade),
  FOREIGN KEY (id_deposito) REFERENCES eq_deposito (id_deposito),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);