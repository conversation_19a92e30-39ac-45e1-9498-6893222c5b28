create table if not exists eq_nf_entrada_pagto_xml
(
  id_nf_entrada_pagto_xml uuid     not null,
  id_aplicacao            char(36) not null,
  vl_desconto             numeric(12, 2),
  vl_frete                numeric(12, 2),
  vl_icms_st              numeric(12, 2),
  vl_ipi                  numeric(12, 2),
  vl_produtos             numeric(12, 2),
  vl_total                numeric(12, 2),
  id_nf_entrada           uuid,
  primary key (id_nf_entrada_pagto_xml)
);

create table if not exists eq_nf_entrada_parcela_xml
(
  id_nf_entrada_parcela_xml serial   not null,
  id_aplicacao              char(36) not null,
  dt_vencimento             date,
  numero_parcela            varchar(100),
  vl_parcela                numeric(12, 2),
  id_nf_entrada_pagto_xml   uuid,
  primary key (id_nf_entrada_parcela_xml)
);

alter table if exists eq_nf_entrada_pagto_xml
  add constraint fk_nf_item_nf_entrada
    foreign key (id_nf_entrada)
      references eq_nf_entrada (id_nf_entrada);

alter table if exists eq_nf_entrada_parcela_xml
  add constraint fk_nf_parcela_nf_pagto_xml
    foreign key (id_nf_entrada_pagto_xml)
      references eq_nf_entrada_pagto_xml (id_nf_entrada_pagto_xml);

alter table if exists nf_recebida
  drop constraint if exists nf_recebida_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_id_pessoa_fkey;

alter table if exists nf_recebida_centro_custo
  drop constraint if exists nf_recebida_centro_custo_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_centro_custo_id_centro_custo_fkey,
  drop constraint if exists nf_recebida_centro_custo_id_nf_recebida_fkey;

alter table if exists nf_recebida_hist
  drop constraint if exists nf_recebida_hist_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_hist_id_nf_recebida_fkey,
  drop constraint if exists nf_recebida_hist_id_usuario_fkey;

alter table if exists nf_recebida_item
  drop constraint if exists nf_recebida_item_cod_cfop_compra_fkey,
  drop constraint if exists nf_recebida_item_cod_cfop_entrada_fkey,
  drop constraint if exists nf_recebida_item_cod_cst_cofins_fkey,
  drop constraint if exists nf_recebida_item_cod_cst_icms_fkey,
  drop constraint if exists nf_recebida_item_cod_cst_ipi_fkey,
  drop constraint if exists nf_recebida_item_cod_cst_pis_fkey,
  drop constraint if exists nf_recebida_item_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_item_id_dest_fkey,
  drop constraint if exists nf_recebida_item_id_ncm_fkey,
  drop constraint if exists nf_recebida_item_id_nf_recebida_fkey,
  drop constraint if exists nf_recebida_item_id_produto_fkey,
  drop constraint if exists nf_recebida_item_id_rastreabilidade_fkey,
  drop constraint if exists nf_recebida_item_id_unidade_medida_fkey;

alter table if exists nf_recebida_item_etq
  drop constraint if exists nf_recebida_item_etq_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_item_etq_id_aplicacao_fkey1,
  drop constraint if exists nf_recebida_item_etq_id_deposito_fkey,
  drop constraint if exists nf_recebida_item_etq_id_nf_recebida_item_fkey,
  drop constraint if exists nf_recebida_item_etq_id_rastreabilidade_fkey;

alter table if exists nf_recebida_pagto
  drop constraint if exists nf_recebida_pagto_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_pagto_id_banco_conta_fkey,
  drop constraint if exists nf_recebida_pagto_id_cond_pagamento_fkey,
  drop constraint if exists nf_recebida_pagto_id_forma_pagamento_fkey,
  drop constraint if exists nf_recebida_pagto_id_nf_recebida_fkey;

alter table if exists nf_recebida_pagto_parcela
  drop constraint if exists nf_recebida_pagto_parcela_id_aplicacao_fkey,
  drop constraint if exists nf_recebida_pagto_parcela_id_banco_conta_fkey,
  drop constraint if exists nf_recebida_pagto_parcela_id_forma_pagamento_fkey,
  drop constraint if exists nf_recebida_pagto_parcela_id_nf_recebida_pagto_fkey;

drop table if exists nf_recebida;
drop table if exists nf_recebida_centro_custo;
drop table if exists nf_recebida_hist;
drop table if exists nf_recebida_item;
drop table if exists nf_recebida_item_etq;
drop table if exists nf_recebida_pagto;
drop table if exists nf_recebida_pagto_parcela;