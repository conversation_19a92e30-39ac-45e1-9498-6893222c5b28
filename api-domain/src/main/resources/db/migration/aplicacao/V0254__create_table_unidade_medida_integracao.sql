alter table erp.cd_pos_pdv rename column chace_api to chave_api;
create table erp.cd_unidade_medida_empresa
(
  id uuid default public.uuid_generate_v4() not null constraint cd_unidade_medida_empresa_pk primary key,
  id_unidade_integracao uuid not null constraint cd_unidade_medida_empresa_cd_unidade_medida_integracao_id_fk references erp.cd_unidade_medida_integracao,
  id_unidade_medida bigint not null constraint cd_unidade_medida_empresa_cd_unidade_medida_id_fk references erp.cd_unidade_medida,
  id_aplicacao char(36) constraint cd_unidade_medida_empresa_ad_aplicacao_id_aplicacao_fk references erp.ad_aplicacao,
  id_empresa integer not null constraint cd_unidade_medida_empresa_cd_empresa_id_empresa_fk references erp.cd_empresa,
  createdat timestamp default now() not null,
  updatedat timestamp
);

