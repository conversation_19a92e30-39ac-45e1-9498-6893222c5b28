create table erp.vd_comissao_vendedor
(
  id_comissao_vendedor uuid default gen_random_uuid() not null
    constraint vd_comissao_vendedor_pk
      primary key,
  valor_calculado numeric(21, 10) not null,
  valor_ajustado numeric(21, 10),
  id_aplicacao char(36) not null
    constraint vd_comissao_vendedor_ad_aplicacao_id_aplicacao_fk
      references erp.ad_aplicacao,
  id_empresa bigint not null
    constraint vd_comissao_vendedor_cd_empresa_id_empresa_fk
      references erp.cd_empresa,
  id_vendedor bigint
    constraint vd_comissao_vendedor_cd_pessoa_id_pessoa_fk
      references erp.cd_pessoa,
  id_produto bigint
    constraint vd_comissao_vendedor_cd_produto_id_produto_fk
      references erp.cd_produto,
  id_pedido_item uuid
    constraint vd_comissao_vendedor_vd_pedido_item_id_pedido_item_fk
      references erp.vd_pedido_item,
  id_pospdv_venda_item bigint
    constraint vd_comissao_vendedor_cd_pos_pdv_venda_item_id_fk
      references erp.cd_pos_pdv_venda_item,
  id_ordemservico_item bigint
    constraint vd_comissao_vendedor_ordem_servico_item_id_fk
      references "erp-nfs".ordem_servico_item,
  id_comissao bigint
    constraint vd_comissao_vendedor_vd_comissao_id_comissao_fk
      references erp.vd_comissao,
  id_comissao_excecao bigint
    constraint vd_comissao_vendedor_vd_comissao_excecao_id_comissao_fk
      references erp.vd_comissao_excecao,
  id_comissao_produto uuid
    constraint vd_comissao_vendedor_vd_comissao_produto_id_comissao_fk
      references erp.vd_comissao_produto,
  comissao_aplicada numeric(21, 10) not null,
  tipo_comissao varchar(255) not null, --PADRAO, EXCECAO_VENDEDOR, EXCECAO_PRODUTO
  ind_icms boolean not null default false,
  ind_pis boolean not null default false,
  ind_icms_st boolean not null default false,
  ind_outras_despesas boolean not null default false,
  ind_ipi boolean not null default false,
  ind_cofins boolean not null default false,
  ind_frete boolean not null default false,
  ind_seguro boolean not null default false,
  ind_os_iss boolean not null default false,
  ind_os_ir boolean not null default false,
  ind_os_inss boolean not null default false,
  ind_os_pis boolean not null default false,
  ind_os_cofins boolean not null default false,
  ind_os_csll boolean not null default false,
  ind_os_desconto boolean not null default false,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null
);