alter table erp.vd_pedido_item add if not exists ordem integer;
comment on column erp.vd_pedido_item.ordem is 'ordem do item no pedido';

alter table erp.vd_pedido_item drop column if exists created_at;
alter table erp.vd_pedido_item drop column if exists updated_at;
alter table erp.vd_pedido_item add if not exists created_at timestamp default now() not null;
alter table erp.vd_pedido_item add if not exists updated_at timestamp;
update erp.vd_pedido_item set updated_at = CURRENT_TIMESTAMP where updated_at is null;
alter table erp.vd_pedido_item alter column updated_at set default now();
alter table erp.vd_pedido_item alter column updated_at set not null;

create index if not exists vd_pedido_item_id_aplicacao_index on erp.vd_pedido_item (id_aplicacao desc);
create index if not exists vd_pedido_item_id_pedido_index on erp.vd_pedido_item (id_pedido desc);
create index if not exists vd_pedido_item_id_produto_index on erp.vd_pedido_item (id_produto desc);

-- Trigger para atualizar a data updated_at do item do pedido atualizado
CREATE OR REPLACE FUNCTION atualizar_data_pedido_item()
  RETURNS TRIGGER language plpgsql AS $$
BEGIN
  UPDATE erp.vd_pedido_item SET updated_at = CURRENT_TIMESTAMP WHERE vd_pedido_item.id_pedido_item = OLD.id_pedido_item;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER atualizar_data_pedido_item
  AFTER UPDATE ON erp.vd_pedido_item
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)
EXECUTE procedure atualizar_data_pedido_item();


-- Trigger para atualizar o próprio registro após a inserção
CREATE OR REPLACE FUNCTION salvar_ordem_pedido_item()
  RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  vOrdem INT;
BEGIN
  select CASE WHEN max(ordem) is null THEN 1 ELSE max(ordem) + 1 END INTO vOrdem from erp.vd_pedido_item where id_pedido = NEW.id_pedido;
  NEW.ordem = vOrdem;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER salvar_ordem_pedido_item
  BEFORE INSERT ON erp.vd_pedido_item
  FOR EACH ROW
EXECUTE PROCEDURE salvar_ordem_pedido_item();

--vd_pedido
update erp.vd_pedido set created_at = CURRENT_TIMESTAMP where created_at is null;
alter table erp.vd_pedido alter column created_at set not null;
alter table erp.vd_pedido alter column created_at set default CURRENT_TIMESTAMP;
update erp.vd_pedido set updated_at = CURRENT_TIMESTAMP where updated_at is null;
alter table erp.vd_pedido alter column updated_at set default now();
alter table erp.vd_pedido alter column updated_at set not null;

CREATE OR REPLACE FUNCTION atualizar_data_pedido()
  RETURNS TRIGGER language plpgsql AS $$
BEGIN
  UPDATE erp.vd_pedido SET updated_at = CURRENT_TIMESTAMP WHERE vd_pedido.id_pedido = OLD.id_pedido;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER atualizar_data_pedido
  AFTER UPDATE ON erp.vd_pedido
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)
EXECUTE procedure atualizar_data_pedido();

--sg_usuario_aplicacao
update erp.sg_usuario_aplicacao set created_at = CURRENT_TIMESTAMP where created_at is null;
alter table erp.sg_usuario_aplicacao alter column created_at set not null;
alter table erp.sg_usuario_aplicacao alter column created_at set default CURRENT_TIMESTAMP;
update erp.sg_usuario_aplicacao set updated_at = CURRENT_TIMESTAMP where updated_at is null;
alter table erp.sg_usuario_aplicacao alter column updated_at set default now();
alter table erp.sg_usuario_aplicacao alter column updated_at set not null;

CREATE OR REPLACE FUNCTION atualizar_data_registro_sg_usuario_aplicacao()
  RETURNS TRIGGER language plpgsql AS $$
BEGIN
  UPDATE erp.sg_usuario_aplicacao SET updated_at = CURRENT_TIMESTAMP WHERE sg_usuario_aplicacao.id_usuario_aplicacao = OLD.id_usuario_aplicacao;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER atualizar_data_registro_sg_usuario_aplicacao
  AFTER UPDATE ON erp.sg_usuario_aplicacao
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)
EXECUTE procedure atualizar_data_registro_sg_usuario_aplicacao();

--sg_usuario
update erp.sg_usuario set created_at = CURRENT_TIMESTAMP where created_at is null;
alter table erp.sg_usuario alter column created_at set not null;
alter table erp.sg_usuario alter column created_at set default CURRENT_TIMESTAMP;
update erp.sg_usuario set updated_at = CURRENT_TIMESTAMP where updated_at is null;
alter table erp.sg_usuario alter column updated_at set default now();
alter table erp.sg_usuario alter column updated_at set not null;

CREATE OR REPLACE FUNCTION atualizar_data_registro_sg_usuario()
  RETURNS TRIGGER language plpgsql AS $$
BEGIN
  UPDATE erp.sg_usuario SET updated_at = CURRENT_TIMESTAMP WHERE sg_usuario.id_usuario = OLD.id_usuario;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER atualizar_data_registro_sg_usuario
  AFTER UPDATE ON erp.sg_usuario
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)
EXECUTE procedure atualizar_data_registro_sg_usuario();

-- cd_pos_pdv_venda_pagamento
alter table erp.cd_pos_pdv_venda_pagamento rename column createdat to created_at;
alter table erp.cd_pos_pdv_venda_pagamento  rename column updatedat to updated_at;
update erp.cd_pos_pdv_venda_pagamento set created_at = CURRENT_TIMESTAMP where created_at is null;
alter table erp.cd_pos_pdv_venda_pagamento alter column created_at set not null;
alter table erp.cd_pos_pdv_venda_pagamento alter column created_at set default CURRENT_TIMESTAMP;
update erp.cd_pos_pdv_venda_pagamento set updated_at = CURRENT_TIMESTAMP where updated_at is null;
alter table erp.cd_pos_pdv_venda_pagamento alter column updated_at set default now();
alter table erp.cd_pos_pdv_venda_pagamento alter column updated_at set not null;

CREATE OR REPLACE FUNCTION atualizar_data_registro_cd_pos_pdv_venda_pagamento()
  RETURNS TRIGGER language plpgsql AS $$
BEGIN
  UPDATE erp.cd_pos_pdv_venda_pagamento SET updated_at = CURRENT_TIMESTAMP WHERE cd_pos_pdv_venda_pagamento.id = OLD.id;
  RETURN NEW;
END
$$;
CREATE OR REPLACE TRIGGER atualizar_data_registro_cd_pos_pdv_venda_pagamento
  AFTER UPDATE ON erp.cd_pos_pdv_venda_pagamento
  FOR EACH ROW
  WHEN (pg_trigger_depth() < 1)
EXECUTE procedure atualizar_data_registro_cd_pos_pdv_venda_pagamento();
