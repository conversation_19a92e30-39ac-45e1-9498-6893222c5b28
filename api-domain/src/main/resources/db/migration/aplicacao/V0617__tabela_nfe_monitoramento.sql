create table erp.nf_serie_monitor
(
  id_monitor uuid default gen_random_uuid() not null
    constraint nf_serie_monitor_pk
      primary key,
  id_aplicacao char(36) not null
    constraint nf_serie_monitor_ad_aplicacao_id_aplicacao_fk
      references erp.ad_aplicacao,
  id_empresa bigint not null
    constraint nf_serie_monitor_cd_empresa_id_empresa_fk
      references erp.cd_empresa,
  ativo boolean default false not null,
  data_ativo timestamp default current_timestamp not null,
  ultimo_monitoramento timestamp,
  created_at timestamp default current_timestamp not null,
  updated_at timestamp default current_timestamp not null
);

-- 3. <PERSON><PERSON>r a trigger que dispara ao atualizar qualquer registro
CREATE TRIGGER trigger_atualiza_nf_serie_monitor_updated_at
  BEFORE UPDATE ON erp.nf_serie_monitor
  FOR EACH ROW
EXECUTE FUNCTION atualiza_updated_at();