create or replace function revoga_comissao(id_venda integer) returns void
  language plpgsql
as
$$
DECLARE
  venda RECORD;
  itens RECORD;
  _valor_calculado numeric(21,10);
BEGIN
  -- comissão padrão para testes caso não tenha nada configurado
  RAISE NOTICE 'VALORES';

  SELECT *
  INTO venda
  FROM erp.cd_pos_pdv_venda
  where id = id_venda;

  -- pegar todos os itens da venda
  FOR itens IN
    SELECT *
    FROM erp.cd_pos_pdv_venda_item pi
    where pi.id_venda = venda.id
    LOOP
      BEGIN
        -- atualizar a comissão como excluída
        UPDATE erp.vd_comissao_vendedor
        SET excluido = true
        where id_aplicacao = venda.id_aplicacao
          and id_empresa = venda.id_empresa
          and id_vendedor = itens.id_vendedor
          and id_produto = itens.id_produto
          and id_pospdv_venda_item = itens.id;
        RAISE NOTICE 'VALORES % % % % %', venda.id_aplicacao, venda.id_empresa, itens.id_vendedor, itens.id_produto, itens.id;

        -- atualizar a comissão mensal
        BEGIN
          SELECT valor_calculado
          INTO _valor_calculado
          FROM erp.vd_comissao_vendedor
          where id_aplicacao = venda.id_aplicacao
            and id_empresa = venda.id_empresa
            and id_vendedor = itens.id_vendedor
            and id_produto = itens.id_produto
            and id_pospdv_venda_item = itens.id;

          -- if para pegar registros que foram removidos
          update erp.vd_comissao_vendedor_acumulado
          set valor_acumulado = valor_acumulado - _valor_calculado
          where id_aplicacao = venda.id_aplicacao
            and id_empresa = venda.id_empresa
            and id_vendedor = venda.id_vendedor
            and mes_acumulado = (DATE_TRUNC('month', CURRENT_DATE));
        END;
      END;
    END LOOP;
END;
$$;

alter function revoga_comissao(integer) owner to postgres;