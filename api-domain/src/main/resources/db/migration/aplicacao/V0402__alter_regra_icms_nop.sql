alter table if exists cd_regra_icms_item 
    add column aliquota_fcp_cf numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_fcp_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_fcp_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_fcp_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_cf numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_estrangeiro_cf numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_estrangeiro_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_icms_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_interestadual_icms_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column aliquota_interna_icms_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column ind_icms_desonerado_cf boolean default false not null;
alter table if exists cd_regra_icms_item 
    add column ind_icms_desonerado_cf_nc boolean default false not null;
alter table if exists cd_regra_icms_item 
    add column modalide_bc_cf_nc varchar(30);
alter table if exists cd_regra_icms_item 
    add column modalide_bc_st_cf_nc varchar(30);
alter table if exists cd_regra_icms_item 
    add column motivo_desoneracao_cf varchar(200);
alter table if exists cd_regra_icms_item 
    add column motivo_desoneracao_cf_nc varchar(200);
alter table if exists cd_regra_icms_item 
    add column percentual_deferimento_cf numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column percentual_deferimento_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_mva_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_mva_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_reducao_bc_cf numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_reducao_bc_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_reducao_bc_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item 
    add column perc_reducao_bc_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_fcp_cf numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_fcp_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_fcp_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_fcp_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_cf numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_estrangeiro_cf numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_estrangeiro_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_icms_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_interestadual_icms_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column aliquota_interna_icms_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column ind_icms_desonerado_cf boolean default false not null;
alter table if exists cd_regra_icms_item_excecao 
    add column ind_icms_desonerado_cf_nc boolean default false not null;
alter table if exists cd_regra_icms_item_excecao 
    add column modalide_bc_cf_nc varchar(30);
alter table if exists cd_regra_icms_item_excecao 
    add column modalide_bc_st_cf_nc varchar(30);
alter table if exists cd_regra_icms_item_excecao 
    add column motivo_desoneracao_cf varchar(200);
alter table if exists cd_regra_icms_item_excecao 
    add column motivo_desoneracao_cf_nc varchar(200);
alter table if exists cd_regra_icms_item_excecao 
    add column percentual_deferimento_cf numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column percentual_deferimento_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_mva_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_mva_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_reducao_bc_cf numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_reducao_bc_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_reducao_bc_estrangeiro_st_cf_nc numeric(10,9);
alter table if exists cd_regra_icms_item_excecao 
    add column perc_reducao_bc_st_cf_nc numeric(10,9);