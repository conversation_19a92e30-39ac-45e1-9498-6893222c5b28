delete from vd_pedido_hist_email_arquivo;
delete from vd_pedido_hist_email;
delete from vd_pedido_hist;
alter table vd_pedido_hist_email_arquivo drop column if exists id_pedido_historico_email_arquivo;
alter table vd_pedido_hist_email_arquivo add if not exists id_pedido_historico_email_arquivo uuid not null;
alter table vd_pedido_hist_email_arquivo add primary key (id_pedido_historico_email_arquivo);
alter table vd_pedido_hist_email_arquivo drop constraint if exists vd_pedido_hist_email_arquivo_pkey;
drop index if exists vd_pedido_hist_email_arquivo_pkey;
alter table vd_pedido_hist_email_arquivo drop constraint vd_pedido_hist_email_arquivo_id_pedido_historico_email_fkey;
alter table vd_pedido_hist_email drop constraint vd_pedido_hist_email_pkey;
alter table vd_pedido_hist_email drop column id_pedido_historico_email;
alter table vd_pedido_hist_email add id_pedido_historico_email uuid;
alter table vd_pedido_hist_email add primary key (id_pedido_historico_email);
alter table vd_pedido_hist_email_arquivo drop column id_pedido_historico_email;
alter table vd_pedido_hist_email_arquivo add id_pedido_historico_email uuid;
alter table vd_pedido_hist_email_arquivo add constraint vd_pedido_hist_email_arquivo_pk primary key (id_pedido_historico_email_arquivo);
alter table vd_pedido_hist_email_arquivo add constraint vd_pedido_hist_email_arquivo_id_pedido_historico_email_fkey foreign key (id_pedido_historico_email) references erp.vd_pedido_hist_email;
alter table vd_pedido_hist drop column id_pedido_hist;
drop index if exists vd_pedido_hist_pkey;
alter table vd_pedido_hist drop constraint if exists vd_pedido_hist_pkey;
alter table vd_pedido_hist add id_pedido_hist uuid;
alter table vd_pedido_hist add primary key (id_pedido_hist);