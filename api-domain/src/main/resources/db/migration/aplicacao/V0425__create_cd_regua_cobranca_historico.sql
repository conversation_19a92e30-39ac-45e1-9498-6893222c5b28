create table if not exists erp.cd_regua_comunicacao_historico
(
  id uuid default public.uuid_generate_v4() not null constraint cd_regua_comunicacao_historico_pk primary key,
  id_aplicacao char(36) not null constraint cd_regua_comunicacao_historico_ad_aplicacao_id_aplicacao_fk references erp.ad_aplicacao,
  tipo varchar(10) not null,
  id_titulo bigint not null constraint cd_regua_comunicacao_historico_fn_titulo_id_titulo_fk references erp.fn_titulo,
  created_at timestamp default current_timestamp not null
);
comment on column erp.cd_regua_comunicacao_historico.tipo is 'AVISO/COBRANCA';

alter table erp.cd_regua_comunicacao_historico add id_regua_comunicacao uuid not null;
alter table erp.cd_regua_comunicacao_historico add constraint cd_regua_comunicacao_historico_cd_regua_comunicacao_id_fk foreign key (id_regua_comunicacao) references erp.cd_regua_comunicacao;