-- Autor: <PERSON>
--------------------------------------------------------------------------------------------

-- Cria uma tabela para armazenar as cartas de correção de uma nota fiscal de entrada
CREATE TABLE IF NOT EXISTS eq_nf_entrada_carta_correcao
(
  id_carta_correcao  UUID          NOT NULL
    PRIMARY KEY,
  texto_correcao     VARCHAR(1000) NOT NULL,
  caminho_xml        VARCHAR(255)  NOT NULL,
  data_hora_registro TIMESTAMP     NOT NULL,
  id_usuario         UUID          NOT NULL
    REFERENCES sg_usuario (id_usuario),
  id_aplicacao       char(36)          NOT NULL
    REFERENCES ad_aplicacao (id_aplicacao),
  situacao           VARCHAR(50)   NOT NULL,
  id_nf_entrada      UUID          NOT NULL
    REFERENCES eq_nf_entrada (id_nf_entrada)
);