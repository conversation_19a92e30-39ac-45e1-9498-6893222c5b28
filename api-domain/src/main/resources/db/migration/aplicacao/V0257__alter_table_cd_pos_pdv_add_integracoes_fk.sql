create unique index if not exists cd_pos_pdv_integracoes_id_integracao_uindex
  on erp.cd_pos_pdv_integracoes (id_integracao);

alter table erp.cd_pos_pdv_integracoes
  drop constraint if exists cd_pos_pdv_integracoes_pk;
alter table erp.cd_pos_pdv_integracoes
  add constraint cd_pos_pdv_integracoes_pk
    primary key (id_integracao);

drop table erp.cd_pos_pdv;

create table erp.cd_pos_pdv
(
  id                uuid      default public.uuid_generate_v4() not null
    constraint cd_pos_pdv_pk
      primary key,
  id_aplicacao      char(36)                                 not null
    constraint cd_pos_pdv_ad_aplicacao_id_aplicacao_fk
      references erp.ad_aplicacao,
  id_empresa        integer                                  not null
    constraint cd_pos_pdv_cd_empresa_id_empresa_fk
      references erp.cd_empresa,
  descricao         varchar(100),
  usuario           varchar(100),
  senha             integer,
  chave_api         varchar(50),
  id_deposito       integer,
  id_nop            integer,
  id_tipo_receita   integer,
  id_conta_corrente integer,
  createdat         timestamp default now()                  not null,
  updatedat         timestamp,
  id_integracao     uuid                                     not null
);

alter table erp.cd_pos_pdv
  owner to postgres;

create unique index cd_pos_pdv_id_uindex
  on erp.cd_pos_pdv (id);