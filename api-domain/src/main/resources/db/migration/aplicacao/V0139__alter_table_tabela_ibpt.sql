alter table cd_empresa add column token_ibpt VARCHAR(255);

alter table cd_tablela_ibpt add column nacional numeric(10, 9),
add column estadual numeric(21, 10),
add column importado numeric(21, 10),
add column municipal numeric(21, 10),
add column id_produto integer;

ALTER TABLE cd_tablela_ibpt RENAME COLUMN aliq_nac_ibpt TO valor_trib_nacional;
ALTER TABLE cd_tablela_ibpt ALTER COLUMN valor_trib_nacional TYPE numeric(21, 10);
ALTER TABLE cd_tablela_ibpt RENAME COLUMN aliq_estadual TO valor_trib_estadual;
ALTER TABLE cd_tablela_ibpt ALTER COLUMN valor_trib_estadual TYPE numeric(21, 10);
ALTER TABLE cd_tablela_ibpt RENAME COLUMN aliq_imp_ibpt TO valor_trib_importado;
ALTER TABLE cd_tablela_ibpt ALTER COLUMN valor_trib_importado TYPE numeric(21, 10);
ALTER TABLE cd_tablela_ibpt RENAME COLUMN aliq_municipal TO valor_trib_municipal;
ALTER TABLE cd_tablela_ibpt ALTER COLUMN valor_trib_municipal TYPE numeric(21, 10);

alter table cd_tablela_ibpt add constraint fk_produto foreign key (id_produto) references cd_produto (id_produto);

alter table cd_tablela_ibpt alter column id_produto set not null;

alter table if exists cd_tablela_ibpt rename to cd_tabela_ibpt;
