create table erp.cd_unidade_medida_integracao (
  id uuid default public.uuid_generate_v4() not null constraint cd_unidade_medida_integracao_pk primary key,
  id_aplicacao char(36) not null constraint cd_unidade_medida_integracao_ad_aplicacao_id_aplicacao_fk references erp.ad_aplicacao,
  id_empresa integer not null constraint cd_unidade_medida_integracao_cd_empresa_id_empresa_fk references erp.cd_empresa,
  name varchar(50) not null,
  unittypeid varchar(40) not null,
  createdat timestamp default now() not null,
  updatedat timestamp
);

