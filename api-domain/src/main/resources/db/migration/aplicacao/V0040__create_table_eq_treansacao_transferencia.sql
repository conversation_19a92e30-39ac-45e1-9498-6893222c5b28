CREATE TABLE eq_transacao_transferencia
(
  id_transacao             INT8 GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
  id_aplicacao             CHAR(36)                              NOT NULL,
  id_movto_estoque_origem  INT8                                  NOT NULL,
  id_movto_estoque_destino INT8                                  NOT NULL,
  dt_transferencia         TIMESTAMP,

  FOREIGN KEY (id_movto_estoque_origem) REFERENCES eq_movto_estoque (id_movto_estoque),
  FOREIGN KEY (id_movto_estoque_destino) REFERENCES eq_movto_estoque (id_movto_estoque),
  FOREIGN KEY (id_aplicacao) REFERENCES ad_aplicacao (id_aplicacao)
);