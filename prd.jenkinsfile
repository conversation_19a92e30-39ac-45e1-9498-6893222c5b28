#!/usr/bin/env groovy
pipeline {
  agent any
  stages {
    stage ('Build') {
      steps {
        sh '''
        bash -c '
        echo "HOME is set to $HOME"
        export SDKMAN_DIR="$HOME/.sdkman"
        echo "SDKMAN_DIR is set to $SDKMAN_DIR"
        if [ -s "$SDKMAN_DIR/bin/sdkman-init.sh" ]; then
          source "$SDKMAN_DIR/bin/sdkman-init.sh"
        else
          echo "SDKMAN! initialization script not found."
          exit 1
        fi
        sdk use java 21.0.4-tem
        ./mvnw -DskipTests=true -Duser.language=pt -Duser.country=BR -Duser.timezone=GMT-3 -P api-doc clean install
        '
        '''
        sh """
        docker build --file ./prd.dockerfile --tag nuvy-erp-api:prd .
        docker tag nuvy-erp-api:prd 941002391451.dkr.ecr.us-east-1.amazonaws.com/nuvy-erp-api:prd-$BUILD_NUMBER
        """
      }
    }
    stage ('Login') {
      steps {
        sh 'docker login -u AWS -p $(aws ecr get-login-password --region us-east-1) 941002391451.dkr.ecr.us-east-1.amazonaws.com'
      }
    }
    stage ('Push') {
      steps {
        sh "docker push 941002391451.dkr.ecr.us-east-1.amazonaws.com/nuvy-erp-api:prd-$BUILD_NUMBER"
      }
    }
    stage ('Deploy') {
      steps {
        sh """
        kubectl\
        --context=arn:aws:eks:us-east-1:941002391451:cluster/nuvy-erp-prd\
        set image deployment/nuvy-erp-service\
        -n nuvy-erp\
        nuvy-erp-service=941002391451.dkr.ecr.us-east-1.amazonaws.com/nuvy-erp-api:prd-$BUILD_NUMBER
        """
      }
    }
    stage ('Clean') {
      steps {
        sh "docker image rm 941002391451.dkr.ecr.us-east-1.amazonaws.com/nuvy-erp-api:prd-$BUILD_NUMBER"
        sh "docker image rm nuvy-erp-api:prd"
        sh "docker builder prune -f"
        sh "./mvnw clean"
      }
    }
  }
}