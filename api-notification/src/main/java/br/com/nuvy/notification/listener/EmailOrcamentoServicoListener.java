package br.com.nuvy.notification.listener;

import static br.com.nuvy.notification.Constants.TEMPLATE_EMAIL_OS_ORCAMENTO;
import static java.util.stream.Collectors.toMap;

import br.com.nuvy.events.OrcamentoServicoEmailEvent;
import br.com.nuvy.notification.service.BrevoService;
import java.util.Map;
import java.util.Map.Entry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class EmailOrcamentoServicoListener implements
  ApplicationListener<OrcamentoServicoEmailEvent> {

  private final BrevoService service;

  @Override
  public void onApplicationEvent(OrcamentoServicoEmailEvent event) {
    service.preparaAndSendOrcamento(event.getDestinatario(), event.getMailData().entrySet().stream()
        .collect(toMap(Map.Entry::getKey, Entry::getValue)),
      TEMPLATE_EMAIL_OS_ORCAMENTO, event.getEmpresaNome());
  }
}
