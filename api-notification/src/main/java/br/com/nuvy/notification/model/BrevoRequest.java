package br.com.nuvy.notification.model;

import org.springframework.util.CollectionUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public record BrevoRequest(
  BrevoEmail sender,
  BrevoEmail replyTo,
  Collection<BrevoEmail> to,
  Integer templateId,
  Map<String, Object> params,
  String subject,
  List<BrevoAnexo> attachment,
  Collection<BrevoEmail> cc,
  Collection<BrevoEmail> bcc,
  String htmlContent,
  String textContent
) {

  public BrevoRequest {
    if (CollectionUtils.isEmpty(cc)) {
      cc = null;
    }
    if (CollectionUtils.isEmpty(bcc)) {
      bcc = null;
    }
  }

  public BrevoRequest(
    BrevoEmail sender,
    BrevoEmail replyTo,
    Collection<BrevoEmail> to,
    Integer templateId,
    Map<String, Object> params,
    String subject,
    List<BrevoAnexo> attachment,
    Collection<BrevoEmail> cc,
    Collection<BrevoEmail> bcc
  ) {
    this(
      sender, replyTo, to, templateId, params, subject, attachment.isEmpty() ? null : attachment,
      cc.isEmpty() ? null : cc, bcc.isEmpty() ? null : bcc,
      null, null
    );
  }

  public BrevoRequest(
    BrevoEmail sender,
    BrevoEmail replyTo,
    Collection<BrevoEmail> to,
    Integer templateId,
    Map<String, Object> params
  ) {
    this(
      sender, replyTo, to, templateId, params, null, List.of(),
      List.of(), List.of()
    );
  }

  public BrevoRequest(
    Collection<BrevoEmail> to,
    Integer templateId,
    Map<String, Object> params
  ) {
    this(BrevoEmail.fromNuvy(), BrevoEmail.naoResponder(), to, templateId, params);
  }
}
