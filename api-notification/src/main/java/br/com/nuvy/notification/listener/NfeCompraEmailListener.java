package br.com.nuvy.notification.listener;

import static br.com.nuvy.notification.Constants.TEXTO_UPLOAD_XML;
import static java.util.stream.Collectors.toMap;

import br.com.nuvy.events.ImportNfeCompraEmailEvent;
import br.com.nuvy.notification.service.BrevoService;
import java.util.Map;
import java.util.Map.Entry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class NfeCompraEmailListener implements ApplicationListener<ImportNfeCompraEmailEvent> {

  private final BrevoService service;

  @Override
  public void onApplicationEvent(ImportNfeCompraEmailEvent event) {
    try {
      log.info("==>> [SUCESSO-UPLOAD] Enviando email de sucesso de upload para: {}",
        event.getDestinatario().getEmail());
      service.preparaAndSend(event.getDestinatario(), event.getMailData().entrySet().stream()
        .collect(toMap(Map.Entry::getKey, Entry::getValue)), TEXTO_UPLOAD_XML);
      log.info("==>> [SUCESSO-UPLOAD] Email de sucesso enviado para: {}",
        event.getDestinatario().getEmail());
    } catch (Exception e) {
      log.error("==>> [ERRO-UPLOAD] Erro ao enviar email para {}",
        event.getDestinatario().getEmail(), e);
    }
  }
}
