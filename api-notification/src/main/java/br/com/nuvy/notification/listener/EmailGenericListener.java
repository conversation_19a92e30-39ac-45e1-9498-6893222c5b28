package br.com.nuvy.notification.listener;

import br.com.nuvy.events.EmailGenericEvent;
import br.com.nuvy.notification.DestinatarioEmail;
import br.com.nuvy.notification.service.BrevoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class EmailGenericListener implements ApplicationListener<EmailGenericEvent> {

  private final BrevoService service;

  @Override
  public void onApplicationEvent(EmailGenericEvent event) {
    service.preparaAndSendGenericEmail(
      event.getMensagem(),
      new DestinatarioEmail(
        event.getEmailDestinatario(), null, event.getCopias(), event.getCopiasOcultas(), null
      ),
      event.getAssunto()
    );
  }
}
