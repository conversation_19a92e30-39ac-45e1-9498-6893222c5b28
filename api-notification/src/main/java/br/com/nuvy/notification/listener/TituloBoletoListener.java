package br.com.nuvy.notification.listener;

import br.com.nuvy.events.TituloBoletoEmailEvent;
import br.com.nuvy.notification.service.BrevoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.Map.Entry;

import static br.com.nuvy.notification.Constants.TEMPLATE_EMAIL_BOLETO_CHEGOU;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class TituloBoletoListener implements ApplicationListener<TituloBoletoEmailEvent> {

  private final BrevoService service;

  @Override
  public void onApplicationEvent(TituloBoletoEmailEvent event) {
    log.info("Evento de envio de email de boletos automaticos");
    log.info(event.getEmpresaNome());
    service.preparaAndSendBoleto(event.getDestinatario(), event.getMailData().entrySet().stream()
        .collect(toMap(Map.Entry::getKey, Entry::getValue)),
      TEMPLATE_EMAIL_BOLETO_CHEGOU, event.getEmpresaNome());
  }

}
